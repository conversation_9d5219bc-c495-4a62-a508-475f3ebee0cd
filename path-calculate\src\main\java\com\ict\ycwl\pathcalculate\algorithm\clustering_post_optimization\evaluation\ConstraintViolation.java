package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import lombok.Builder;
import lombok.Data;

/**
 * 约束违反项
 * 
 * 表示单个路线的约束违反情况
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ConstraintViolation {
    
    /**
     * 路线索引
     */
    private int routeIndex;
    
    /**
     * 违反类型描述
     */
    private String violationType;
    
    /**
     * 当前值
     */
    private double currentValue;
    
    /**
     * 约束限制值
     */
    private double constraintLimit;
    
    /**
     * 违反量（当前值 - 限制值）
     */
    private double violationAmount;
    
    /**
     * 违反比例（违反量 / 限制值）
     */
    private double violationRatio;
    
    /**
     * 违反严重程度枚举
     */
    private ViolationSeverity severity;
    
    /**
     * 获取违反严重程度
     */
    public ViolationSeverity getSeverity() {
        if (severity != null) {
            return severity;
        }
        
        // 根据违反比例自动计算严重程度
        if (violationRatio <= 0.05) {
            return ViolationSeverity.MINOR;
        } else if (violationRatio <= 0.1) {
            return ViolationSeverity.MODERATE;
        } else if (violationRatio <= 0.2) {
            return ViolationSeverity.SIGNIFICANT;
        } else if (violationRatio <= 0.3) {
            return ViolationSeverity.SEVERE;
        } else {
            return ViolationSeverity.CRITICAL;
        }
    }
    
    /**
     * 是否为严重违反
     */
    public boolean isCriticalViolation() {
        return getSeverity().ordinal() >= ViolationSeverity.SEVERE.ordinal();
    }
    
    /**
     * 获取违反描述
     */
    public String getViolationDescription() {
        return String.format("%s: 当前%.1f，限制%.1f，超出%.1f (%.1f%%)",
            violationType, currentValue, constraintLimit, 
            violationAmount, violationRatio * 100);
    }
    
    /**
     * 违反严重程度枚举
     */
    public enum ViolationSeverity {
        MINOR("轻微", 1, "可接受的小幅违反"),
        MODERATE("中等", 2, "需要关注的违反"),
        SIGNIFICANT("显著", 3, "需要优化的违反"),
        SEVERE("严重", 4, "必须修复的违反"),
        CRITICAL("危急", 5, "严重影响系统的违反");
        
        private final String description;
        private final int level;
        private final String detail;
        
        ViolationSeverity(String description, int level, String detail) {
            this.description = description;
            this.level = level;
            this.detail = detail;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getLevel() {
            return level;
        }
        
        public String getDetail() {
            return detail;
        }
    }
}