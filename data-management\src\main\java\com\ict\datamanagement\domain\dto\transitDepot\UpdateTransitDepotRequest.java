package com.ict.datamanagement.domain.dto.transitDepot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("修改中转站表单")
@AllArgsConstructor
@NoArgsConstructor
public class UpdateTransitDepotRequest extends AddTransitDepot{
    @ApiModelProperty(value = "中转站id",dataType = "int")
    private int transitDepotId;
}
