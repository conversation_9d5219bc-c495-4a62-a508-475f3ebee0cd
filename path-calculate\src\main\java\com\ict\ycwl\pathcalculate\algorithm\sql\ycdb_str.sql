/*
 Navicat Premium Data Transfer

 Source Server         : xmlhs
 Source Server Type    : MySQL
 Source Server Version : 90001
 Source Host           : localhost:3306
 Source Schema         : ycdb

 Target Server Type    : MySQL
 Target Server Version : 90001
 File Encoding         : 65001

 Date: 17/07/2025 21:38:25
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for accumulation
-- ----------------------------
DROP TABLE IF EXISTS `accumulation`;
CREATE TABLE `accumulation`  (
  `accumulation_id` bigint NOT NULL COMMENT '聚集区id',
  `leader_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '聚集区负责人名称',
  `accumulation_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '聚集区名称',
  `leader_phone` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '聚集区负责人联系电话',
  `longitude` double NULL DEFAULT NULL COMMENT '聚集区经度',
  `latitude` double NULL DEFAULT NULL COMMENT '聚集区纬度',
  `area_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所属大区',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否软删除（0：否；1：是）',
  `accumulation_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '聚集区地址',
  `route_id` bigint NULL DEFAULT NULL COMMENT '路线id',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '所属中转站id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '大区Id',
  PRIMARY KEY (`accumulation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for accumulation_back
-- ----------------------------
DROP TABLE IF EXISTS `accumulation_back`;
CREATE TABLE `accumulation_back`  (
  `accumulation_id` bigint NOT NULL COMMENT '聚集区id',
  `leader_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '聚集区负责人名称',
  `accumulation_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '聚集区名称',
  `leader_phone` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '聚集区负责人联系电话',
  `longitude` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '聚集区经度',
  `latitude` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '聚集区纬度',
  PRIMARY KEY (`accumulation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for area
-- ----------------------------
DROP TABLE IF EXISTS `area`;
CREATE TABLE `area`  (
  `area_id` bigint NOT NULL AUTO_INCREMENT COMMENT '大区id',
  `area_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '大区名称',
  PRIMARY KEY (`area_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for car
-- ----------------------------
DROP TABLE IF EXISTS `car`;
CREATE TABLE `car`  (
  `car_id` bigint NOT NULL AUTO_INCREMENT COMMENT '车辆id',
  `license_plate_number` char(10) CHARACTER SET ujis COLLATE ujis_japanese_ci NULL DEFAULT NULL COMMENT '车牌号(七位)',
  `max_load` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '最大载重（吨）',
  `max_distance` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '最大行驶距离（米）',
  `integral` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '积分',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '状态（0：异常；1：正常）',
  `delivery_time` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '最长可工作时长（单位：米）[ 暂定字段 ]',
  `area_id` bigint NULL DEFAULT NULL COMMENT '所属大区id',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '中转站id',
  `car_driver_id` bigint NULL DEFAULT NULL COMMENT '驾驶人id',
  `is_delete` int NULL DEFAULT 0 COMMENT '0保留1删除',
  `actual_load` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '实际载货量',
  `actual_time` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '实际工作时间',
  `week` varchar(255) CHARACTER SET ujis COLLATE ujis_japanese_ci NULL DEFAULT NULL COMMENT '星期',
  `date` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '日期',
  `delivery_area_id` int NULL DEFAULT NULL COMMENT '配送域id',
  `route_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线',
  `is_fact` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '0车辆基本信息，1实情',
  PRIMARY KEY (`car_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for car_back
-- ----------------------------
DROP TABLE IF EXISTS `car_back`;
CREATE TABLE `car_back`  (
  `car_id` bigint NOT NULL COMMENT '车辆id',
  `license_plate_number` char(10) CHARACTER SET ujis COLLATE ujis_japanese_ci NULL DEFAULT NULL COMMENT '车牌号(七位)',
  `max_load` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '最大载重（吨）',
  `max_distance` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '最大行驶距离（米）',
  `integral` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '积分',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '状态（0：异常；1：正常）',
  `delivery_time` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '最长可工作时长（单位：米）[ 暂定字段 ]',
  `area_id` bigint NULL DEFAULT NULL COMMENT '所属大区id',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '中转站id',
  PRIMARY KEY (`car_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for centerdistance
-- ----------------------------
DROP TABLE IF EXISTS `centerdistance`;
CREATE TABLE `centerdistance`  (
  `cenStore_id` bigint NOT NULL AUTO_INCREMENT COMMENT '商铺归属中心点id',
  `cenStore_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中心点名称，用于计算商铺是城镇还是乡村',
  `lng` double NULL DEFAULT NULL COMMENT '中心点经度',
  `lat` double NULL DEFAULT NULL COMMENT '中心点纬度',
  `radius` double NULL DEFAULT NULL COMMENT '半径单位m',
  PRIMARY KEY (`cenStore_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for delivery_area
-- ----------------------------
DROP TABLE IF EXISTS `delivery_area`;
CREATE TABLE `delivery_area`  (
  `delivery_area_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配送域id',
  `delivery_area_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '配送域名称',
  `team_id` bigint NULL DEFAULT NULL COMMENT '所属班组id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '所属行政区id',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '中转站id',
  `delivery_type_id` bigint NULL DEFAULT NULL COMMENT '配送类型id',
  `route_number` int NULL DEFAULT NULL COMMENT '路径数',
  `car_number` int NULL DEFAULT NULL COMMENT '车辆数',
  `is_delete` int NULL DEFAULT 0 COMMENT '0保留，1删除',
  PRIMARY KEY (`delivery_area_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for delivery_type
-- ----------------------------
DROP TABLE IF EXISTS `delivery_type`;
CREATE TABLE `delivery_type`  (
  `delivery_type_id` bigint NOT NULL COMMENT '配送类型id',
  `delivery_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '配送类型名称',
  PRIMARY KEY (`delivery_type_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dist
-- ----------------------------
DROP TABLE IF EXISTS `dist`;
CREATE TABLE `dist`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `origin` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `destination` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `dist` double NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_origin_dest`(`origin` ASC, `destination` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 128450 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for error_point
-- ----------------------------
DROP TABLE IF EXISTS `error_point`;
CREATE TABLE `error_point`  (
  `error_point_id` bigint NOT NULL AUTO_INCREMENT COMMENT '错误点id',
  `current_store_longitude` double NULL DEFAULT NULL COMMENT '当前商铺经度',
  `current_store_latitude` double NULL DEFAULT NULL COMMENT '当前商铺纬度',
  `pairing_store_longitude` double NULL DEFAULT NULL COMMENT '配对商铺经度(聚集区、打卡点的经纬度)',
  `pairing_store_latitude` double NULL DEFAULT NULL COMMENT '配对商铺纬度（聚集区打卡点的经纬度）',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否软删除（0：否；1：是）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`error_point_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for error_point_back
-- ----------------------------
DROP TABLE IF EXISTS `error_point_back`;
CREATE TABLE `error_point_back`  (
  `error_point_id` bigint NOT NULL COMMENT '错误点id',
  `current_store_longitude` double NULL DEFAULT NULL COMMENT '当前商铺经度',
  `current_store_latitude` double NULL DEFAULT NULL COMMENT '当前商铺纬度',
  `pairing_store_longitude` double NULL DEFAULT NULL COMMENT '配对商铺经度',
  `pairing_store_latitude` double NULL DEFAULT NULL COMMENT '配对商铺纬度',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否软删除（0：否；1：是）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_exist` tinyint(1) NULL DEFAULT 0 COMMENT '是否存在这种情况（0：否；1：是）',
  PRIMARY KEY (`error_point_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for feedback
-- ----------------------------
DROP TABLE IF EXISTS `feedback`;
CREATE TABLE `feedback`  (
  `feedback_id` bigint NOT NULL AUTO_INCREMENT COMMENT '反馈id',
  `customer_code` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户编码',
  `route_id` bigint NULL DEFAULT NULL COMMENT '送货路径id',
  `route_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '送货路径',
  `order_date` datetime NULL DEFAULT NULL COMMENT '订单日期',
  `delivery_work_number` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '送货员工号',
  `delivery_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '送货员',
  `customer_manager_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户专员',
  `manager_work_number` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户专员工号',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者id',
  `feedback_information` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '反馈异常信息',
  `feedback_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '反馈类型（1：物流反馈；2：营销反馈）',
  `feedback_status` int NULL DEFAULT NULL COMMENT '是否处理（0：未处理；1：处理中；2：已处理；3：无需处理）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `area_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '大区名称',
  `update_time` datetime NULL DEFAULT NULL COMMENT '最近更新时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '最近更新人id',
  PRIMARY KEY (`feedback_id`) USING BTREE,
  INDEX `create_by`(`create_by` ASC) USING BTREE,
  INDEX `customer_code`(`customer_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1927915818520674307 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for feedback_file
-- ----------------------------
DROP TABLE IF EXISTS `feedback_file`;
CREATE TABLE `feedback_file`  (
  `feedback_file_id` bigint NOT NULL AUTO_INCREMENT COMMENT '异常反馈信息文件id',
  `feedback_file_path` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '异常反馈信息文件路径',
  `feedback_file_real_path` varchar(125) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '异常反馈信息文件真实路径',
  `feedback_id` bigint NULL DEFAULT NULL COMMENT '异常反馈信息id',
  PRIMARY KEY (`feedback_file_id`) USING BTREE,
  INDEX `feedback_file_ibfk_1`(`feedback_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1927915818558423042 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for feedback_reply
-- ----------------------------
DROP TABLE IF EXISTS `feedback_reply`;
CREATE TABLE `feedback_reply`  (
  `reply_id` bigint NOT NULL AUTO_INCREMENT COMMENT '回复id',
  `reply_content` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '回复内容',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `reply_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '回复类型（1：送货部；2：营销部）',
  `feedback_id` bigint NULL DEFAULT NULL COMMENT '反馈信息id',
  PRIMARY KEY (`reply_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1900177217546694659 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for feedback_reply_file
-- ----------------------------
DROP TABLE IF EXISTS `feedback_reply_file`;
CREATE TABLE `feedback_reply_file`  (
  `reply_file_id` bigint NOT NULL AUTO_INCREMENT COMMENT '回复信息文件id',
  `reply_file_path` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '回复信息文件路径',
  `reply_file_real_path` varchar(125) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '回复信息文件真实路径',
  `reply_id` bigint NULL DEFAULT NULL COMMENT '回复id',
  PRIMARY KEY (`reply_file_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1900177217592832003 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for file_import_logs
-- ----------------------------
DROP TABLE IF EXISTS `file_import_logs`;
CREATE TABLE `file_import_logs`  (
  `file_id` int NOT NULL AUTO_INCREMENT COMMENT '文件id',
  `file_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '文件名',
  `file_size` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '文件大小单位kb',
  `import_time` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '导入时间',
  `user_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户',
  `status` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '导入状态',
  `store_or_car` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '0表示该表格是商铺表，1表示该表格是车辆实情表',
  PRIMARY KEY (`file_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 218 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gear
-- ----------------------------
DROP TABLE IF EXISTS `gear`;
CREATE TABLE `gear`  (
  `gear_id` bigint NOT NULL COMMENT '档位id',
  `gear_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '具体档位',
  `cargo_weight` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '档位对应的载货量',
  PRIMARY KEY (`gear_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for group
-- ----------------------------
DROP TABLE IF EXISTS `group`;
CREATE TABLE `group`  (
  `group_id` bigint NOT NULL COMMENT '班组id',
  `group_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '班组名称',
  `area_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for group_areas
-- ----------------------------
DROP TABLE IF EXISTS `group_areas`;
CREATE TABLE `group_areas`  (
  `group_id` bigint NOT NULL COMMENT '班组Id',
  `area_id` bigint NOT NULL COMMENT '大区Id',
  PRIMARY KEY (`area_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operation
-- ----------------------------
DROP TABLE IF EXISTS `operation`;
CREATE TABLE `operation`  (
  `operation_id` bigint NOT NULL COMMENT '权限点id',
  `operation_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '权限点名称',
  `operation_state` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '权限点描述',
  PRIMARY KEY (`operation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pickup_user
-- ----------------------------
DROP TABLE IF EXISTS `pickup_user`;
CREATE TABLE `pickup_user`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '取货户id',
  `customer_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户编码',
  `contact_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `store_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商店名称',
  `customer_manager_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `contact_phone` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '订货电话',
  `store_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商铺地址',
  `road_grade` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '道路等级0城区1乡镇',
  `gear` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '档位',
  `delivery_distance` double NULL DEFAULT NULL COMMENT '配送距离',
  `pickup_containers` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '取货柜地址',
  `type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '取货柜类型',
  `weights` double NULL DEFAULT NULL COMMENT '权值',
  `locks` int(1) UNSIGNED ZEROFILL NOT NULL DEFAULT 0 COMMENT '1加锁0不加锁',
  `color` int NULL DEFAULT NULL COMMENT '1普通商户未分配，2普通商户分配，3定点取货户未分配，4定点取货户已分配',
  `longitude` double NULL DEFAULT NULL COMMENT '经度',
  `latitude` double NULL DEFAULT NULL COMMENT '纬度',
  `accumulation_id` bigint NULL DEFAULT NULL COMMENT '聚集区id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `customer_code`(`customer_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2306510 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pickup_user_import
-- ----------------------------
DROP TABLE IF EXISTS `pickup_user_import`;
CREATE TABLE `pickup_user_import`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '表格记录',
  `customer_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '客户编码',
  `contact_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户名称',
  `store_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商店名称',
  `customer_manager_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `store_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商铺地址',
  `pickup_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '取货柜类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1578536 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pickup_user_parameter
-- ----------------------------
DROP TABLE IF EXISTS `pickup_user_parameter`;
CREATE TABLE `pickup_user_parameter`  (
  `id` int NOT NULL COMMENT '主键id',
  `gear` double NULL DEFAULT NULL COMMENT '客户档位',
  `road_grade` double NULL DEFAULT NULL COMMENT '道路等级',
  `avg_distance` double NULL DEFAULT NULL COMMENT '平均送货距离',
  `level_param` double NULL DEFAULT NULL COMMENT '定级参数',
  `exclude_gear` int NULL DEFAULT NULL COMMENT '大于等于这个档位的取货户不参与计算',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for point_distance
-- ----------------------------
DROP TABLE IF EXISTS `point_distance`;
CREATE TABLE `point_distance`  (
  `point_distance_id` bigint NOT NULL COMMENT '路段id',
  `distance` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路段距离',
  `polyline` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '路段坐标点串',
  `origin` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路段起始点',
  `destination` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路路段终点',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否软删除（0：否；1：是）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '所属中转站id',
  `type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路段类型（ordinary：普通；bridge：桥）',
  PRIMARY KEY (`point_distance_id`) USING BTREE,
  UNIQUE INDEX `uniq_origin_destination`(`origin` ASC, `destination` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `role_id` bigint NOT NULL COMMENT '角色id',
  `role_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '角色名字',
  `role_state` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '角色描述',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for role_operation
-- ----------------------------
DROP TABLE IF EXISTS `role_operation`;
CREATE TABLE `role_operation`  (
  `role_id` bigint NOT NULL COMMENT '角色id',
  `operation_id` bigint NOT NULL COMMENT '权限点id',
  `status` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '0' COMMENT '角色权限状态：默认0为未开启权限点   1为开启权限点',
  PRIMARY KEY (`role_id`, `operation_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for route
-- ----------------------------
DROP TABLE IF EXISTS `route`;
CREATE TABLE `route`  (
  `route_id` bigint NOT NULL COMMENT '路线id',
  `route_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '路线名称',
  `distance` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线距离',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '中转站id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '大区id',
  `polyline` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '路线坐标点串',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否软删除（0：否；1：是）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cargo_weight` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '载货量',
  `version_number` int NULL DEFAULT NULL COMMENT '版本号',
  `convex` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '凸包坐标点串',
  `work_time` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '工作时长',
  PRIMARY KEY (`route_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for route_accumulation
-- ----------------------------
DROP TABLE IF EXISTS `route_accumulation`;
CREATE TABLE `route_accumulation`  (
  `route_id` bigint NOT NULL COMMENT '路线id',
  `accumulation_id` bigint NOT NULL COMMENT '聚集区id',
  PRIMARY KEY (`route_id`, `accumulation_id`) USING BTREE,
  INDEX `fk_route_accumulation_accumulation`(`accumulation_id` ASC) USING BTREE,
  CONSTRAINT `fk_route_accumulation_accumulation` FOREIGN KEY (`accumulation_id`) REFERENCES `accumulation_back` (`accumulation_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_route_accumulation_route` FOREIGN KEY (`route_id`) REFERENCES `route_back` (`route_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for route_back
-- ----------------------------
DROP TABLE IF EXISTS `route_back`;
CREATE TABLE `route_back`  (
  `route_id` bigint NOT NULL COMMENT '路线id',
  `route_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线名称',
  `distance` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线距离',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '中转站id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '大区id',
  PRIMARY KEY (`route_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for route_copy_522
-- ----------------------------
DROP TABLE IF EXISTS `route_copy_522`;
CREATE TABLE `route_copy_522`  (
  `route_id` bigint NOT NULL COMMENT '路线id',
  `route_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '路线名称',
  `distance` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线距离',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '中转站id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '大区id',
  `polyline` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '路线坐标点串',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否软删除（0：否；1：是）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cargo_weight` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '载货量',
  `version_number` int NULL DEFAULT NULL COMMENT '版本号',
  `convex` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '凸包坐标点串',
  `work_time` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '工作时长',
  PRIMARY KEY (`route_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for route_detail
-- ----------------------------
DROP TABLE IF EXISTS `route_detail`;
CREATE TABLE `route_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `accumulation_count` int NULL DEFAULT NULL COMMENT '打卡点/聚集区时长',
  `city_count` int NULL DEFAULT NULL COMMENT '城区商铺个数',
  `country_count` int NULL DEFAULT NULL COMMENT '乡镇商铺个数',
  `loading_time` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '装车时长',
  `transit_time` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '途中时长',
  `delivery_time` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '卸货配送时长',
  `total_time` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '总时长',
  `route_id` bigint NULL DEFAULT NULL COMMENT '路线Id',
  `freeewat_dist` double NULL DEFAULT NULL COMMENT '高速公路行驶里程m',
  `uraban_roads_dist` double NULL DEFAULT NULL COMMENT '城区公路行驶里程m',
  `township_roads_dist` double NULL DEFAULT NULL COMMENT '乡镇公路行驶里程m',
  `second_transit_time` double NULL DEFAULT NULL COMMENT '二次中转站时长分钟',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4275 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for route_user
-- ----------------------------
DROP TABLE IF EXISTS `route_user`;
CREATE TABLE `route_user`  (
  `user_id` bigint NOT NULL,
  `route_id` bigint NOT NULL,
  PRIMARY KEY (`route_id`, `user_id`) USING BTREE,
  INDEX `fk_route_user_user`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_route_user_route` FOREIGN KEY (`route_id`) REFERENCES `route_back` (`route_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_route_user_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for scheduling
-- ----------------------------
DROP TABLE IF EXISTS `scheduling`;
CREATE TABLE `scheduling`  (
  `scheduling_id` bigint NOT NULL COMMENT '排班表id',
  `day` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '星期几（1,2,3,4,5）',
  `car_id` bigint NULL DEFAULT NULL COMMENT '车辆id',
  `route_id` bigint NULL DEFAULT NULL COMMENT '路线id',
  PRIMARY KEY (`scheduling_id`) USING BTREE,
  INDEX `car_id`(`car_id` ASC) USING BTREE,
  INDEX `route_id`(`route_id` ASC) USING BTREE,
  INDEX `scheduling_id`(`scheduling_id` ASC) USING BTREE,
  CONSTRAINT `scheduling_ibfk_1` FOREIGN KEY (`car_id`) REFERENCES `car_back` (`car_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `scheduling_ibfk_2` FOREIGN KEY (`route_id`) REFERENCES `route_back` (`route_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for scheduling_user
-- ----------------------------
DROP TABLE IF EXISTS `scheduling_user`;
CREATE TABLE `scheduling_user`  (
  `scheduling_id` bigint NOT NULL COMMENT '排班表id',
  `user_id` bigint NOT NULL COMMENT '配送员id',
  PRIMARY KEY (`scheduling_id`, `user_id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `scheduling_user_ibfk_1` FOREIGN KEY (`scheduling_id`) REFERENCES `scheduling` (`scheduling_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `scheduling_user_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for second_transit
-- ----------------------------
DROP TABLE IF EXISTS `second_transit`;
CREATE TABLE `second_transit`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '二次中转单条记录id',
  `second_transit_id` bigint NULL DEFAULT NULL COMMENT '二次中转站id',
  `transit_time` double NULL DEFAULT NULL COMMENT '二次中转时长',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for site_selection
-- ----------------------------
DROP TABLE IF EXISTS `site_selection`;
CREATE TABLE `site_selection`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pickup_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '取货地名称',
  `pickup_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `longitude` double NULL DEFAULT NULL COMMENT '经度',
  `latitude` double NULL DEFAULT NULL COMMENT '纬度',
  `type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '取货地类型',
  `status` int NULL DEFAULT NULL COMMENT '状态(1禁用2启用未分配商户3启用已分配商户）',
  `city` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '市',
  `district` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '区/县/县级市',
  `town` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '镇',
  `village` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '村/村委会',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for site_store
-- ----------------------------
DROP TABLE IF EXISTS `site_store`;
CREATE TABLE `site_store`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '选址and商户表id',
  `site_selection_id` int NOT NULL COMMENT '选址id',
  `store_id` int NULL DEFAULT NULL COMMENT '商户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 97227 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for store
-- ----------------------------
DROP TABLE IF EXISTS `store`;
CREATE TABLE `store`  (
  `store_id` bigint NOT NULL AUTO_INCREMENT COMMENT '店铺id',
  `customer_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '客户编码',
  `store_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `store_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺经营地址',
  `longitude` double NULL DEFAULT NULL COMMENT '店铺经度',
  `latitude` double NULL DEFAULT NULL COMMENT '店铺纬度',
  `type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商圈类型',
  `order_cycle` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '订货周期',
  `district` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺所属行政区',
  `area_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺所属大区',
  `contact_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺联系人名称',
  `contact_phone` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺联系人电话号码',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '状态（0：异常；1：正常）',
  `customer_manager_id` bigint NULL DEFAULT NULL COMMENT '客户专员id',
  `accumulation_id` bigint NULL DEFAULT NULL COMMENT '聚集区id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '大区id',
  `route_id` bigint NULL DEFAULT NULL COMMENT '路线id',
  `route_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线名称',
  `customer_manager_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户专员名称',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否软删除（0：不是；1：是）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `gear` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户档位',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `group_id` bigint NULL DEFAULT NULL COMMENT '班组Id',
  `location_type` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '0：城区；1：乡镇',
  `delivery_area` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `is_special` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '是否是特殊点0不是1是',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '特殊点备注',
  `special_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '特殊点类型',
  PRIMARY KEY (`store_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11437 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for store_back1
-- ----------------------------
DROP TABLE IF EXISTS `store_back1`;
CREATE TABLE `store_back1`  (
  `store_id` bigint NOT NULL COMMENT '店铺id',
  `customer_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '客户编码',
  `store_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `store_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺经营地址',
  `longitude` double NULL DEFAULT NULL COMMENT '店铺经度',
  `latitude` double NULL DEFAULT NULL COMMENT '店铺纬度',
  `type` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '商圈类型',
  `order_cycle` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '订货周期',
  `district` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '店铺所属行政区',
  `area_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺所属大区',
  `contact_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺联系人名称',
  `contact_phone` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '店铺联系人电话号码',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '状态（0：异常；1：正常）',
  `customer_manager_id` bigint NULL DEFAULT NULL COMMENT '客户专员id',
  `accumulation_id` bigint NULL DEFAULT NULL COMMENT '聚集区id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '大区id',
  `route_id` bigint NULL DEFAULT NULL COMMENT '路线id',
  `route_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线名称',
  `customer_manager_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户专员名称',
  `is_delete` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '是否软删除（0：不是；1：是）',
  `creat_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`store_id`) USING BTREE,
  INDEX `customer_code`(`customer_code` ASC) USING BTREE,
  INDEX `store_id`(`store_id` ASC) USING BTREE,
  INDEX `customer_manager_id`(`customer_manager_id` ASC) USING BTREE,
  CONSTRAINT `store_back1_ibfk_1` FOREIGN KEY (`customer_manager_id`) REFERENCES `user` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for store_back2
-- ----------------------------
DROP TABLE IF EXISTS `store_back2`;
CREATE TABLE `store_back2`  (
  `store_id` bigint NOT NULL COMMENT '店铺id',
  `customer_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '客户编码',
  `store_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `store_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺经营地址',
  `longitude` double NULL DEFAULT NULL COMMENT '店铺经度',
  `latitude` double NULL DEFAULT NULL COMMENT '店铺纬度',
  `type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商圈类型',
  `order_cycle` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '订货周期',
  `district` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺所属行政区',
  `area_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺所属大区',
  `contact_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺联系人名称',
  `contact_phone` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '店铺联系人电话号码',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '状态（0：异常；1：正常）',
  `customer_manager_id` bigint NULL DEFAULT NULL COMMENT '客户专员id',
  `accumulation_id` bigint NULL DEFAULT NULL COMMENT '聚集区id',
  `area_id` bigint NULL DEFAULT NULL COMMENT '大区id',
  `route_id` bigint NULL DEFAULT NULL COMMENT '路线id',
  `route_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线名称',
  `customer_manager_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户专员名称',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '是否软删除（0：不是；1：是）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`store_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for store_time
-- ----------------------------
DROP TABLE IF EXISTS `store_time`;
CREATE TABLE `store_time`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `longitude` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT ' 经度',
  `latitude` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `time` double NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ind_longitude_latitude`(`longitude` ASC, `latitude` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1671 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for store_two
-- ----------------------------
DROP TABLE IF EXISTS `store_two`;
CREATE TABLE `store_two`  (
  `store_id` bigint NOT NULL COMMENT '商铺id',
  `head` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `spare_phone` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备用收货电话',
  `receiving_phone` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '收货电话',
  `resale_cycle` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '返销周期',
  PRIMARY KEY (`store_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_parameter
-- ----------------------------
DROP TABLE IF EXISTS `system_parameter`;
CREATE TABLE `system_parameter`  (
  `id` int NOT NULL COMMENT '系统参数记录id',
  `accumulation_intensity` double NULL DEFAULT NULL COMMENT '聚集区密集度系数',
  `shore_unload_city_time` double NULL DEFAULT NULL COMMENT '商铺平均卸货时长(小时)城区',
  `shore_unload_township_time` double NULL DEFAULT NULL COMMENT '商铺平均卸货时长(小时)乡村',
  `freeway` double NULL DEFAULT NULL COMMENT '车辆时速(千米每时)-高速公路',
  `urban_roads` double NULL DEFAULT NULL COMMENT '车辆时速(千米每时)-城区公路',
  `township_roads` double NULL DEFAULT NULL COMMENT '车辆时速(千米每时)-乡镇公路',
  `loading_time` double NULL DEFAULT NULL COMMENT '装车时长',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for team
-- ----------------------------
DROP TABLE IF EXISTS `team`;
CREATE TABLE `team`  (
  `team_id` bigint NOT NULL AUTO_INCREMENT COMMENT '班组id',
  `team_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '班组名称',
  `delivery_area_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '配送域名称',
  `transit_depot_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中转站名称',
  `car_sum` int NULL DEFAULT NULL COMMENT '车辆总数',
  `route_sum` int NULL DEFAULT NULL COMMENT '路线总数',
  `is_delete` int NULL DEFAULT 0 COMMENT '0否，1删除',
  PRIMARY KEY (`team_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for transit_delivery
-- ----------------------------
DROP TABLE IF EXISTS `transit_delivery`;
CREATE TABLE `transit_delivery`  (
  `transit_delivery_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `transit_depot_id` bigint NULL DEFAULT NULL COMMENT '对接点id',
  `delivery_area_id` bigint NULL DEFAULT NULL COMMENT '配送域id',
  PRIMARY KEY (`transit_delivery_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for transit_depot
-- ----------------------------
DROP TABLE IF EXISTS `transit_depot`;
CREATE TABLE `transit_depot`  (
  `transit_depot_id` bigint NOT NULL AUTO_INCREMENT COMMENT '中转场id',
  `transit_depot_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中转场名称',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '中转场启用状态(0：禁用；1：启用)',
  `longitude` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中转场经度',
  `latitude` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中转场纬度',
  `area_id` bigint NOT NULL COMMENT '大区id',
  `group_id` bigint NULL DEFAULT NULL COMMENT '班组id',
  `is_delete` int NULL DEFAULT 0 COMMENT '是否进行软删除，1删除',
  PRIMARY KEY (`transit_depot_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for transit_depot_back
-- ----------------------------
DROP TABLE IF EXISTS `transit_depot_back`;
CREATE TABLE `transit_depot_back`  (
  `transit_depot_id` bigint NOT NULL COMMENT '中转场id',
  `transit_depot_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中转场名称',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '中转场启用状态(0：禁用；1：启用)',
  `longitude` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中转场经度',
  `latitude` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '中转场纬度',
  `area_id` bigint NOT NULL COMMENT '大区id',
  PRIMARY KEY (`transit_depot_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for transit_depot_car
-- ----------------------------
DROP TABLE IF EXISTS `transit_depot_car`;
CREATE TABLE `transit_depot_car`  (
  `transit_depot_id` bigint NOT NULL COMMENT '中转场id',
  `car_id` bigint NOT NULL COMMENT '车辆id',
  PRIMARY KEY (`transit_depot_id`, `car_id`) USING BTREE,
  INDEX `car_id`(`car_id` ASC) USING BTREE,
  CONSTRAINT `transit_depot_car_ibfk_1` FOREIGN KEY (`transit_depot_id`) REFERENCES `transit_depot_back` (`transit_depot_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `transit_depot_car_ibfk_2` FOREIGN KEY (`car_id`) REFERENCES `car_back` (`car_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for travel_time
-- ----------------------------
DROP TABLE IF EXISTS `travel_time`;
CREATE TABLE `travel_time`  (
  `longitude_start` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '起始打卡点经度',
  `latitude_start` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '起点打卡点纬度',
  `longitude_end` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '终点打卡点经度',
  `latitude_end` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '终点打卡点纬度',
  `travel_time` double NULL DEFAULT NULL COMMENT '行驶时长'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for unloading_time
-- ----------------------------
DROP TABLE IF EXISTS `unloading_time`;
CREATE TABLE `unloading_time`  (
  `acc_id` bigint NOT NULL COMMENT '打卡点id',
  `acclongitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '打卡点经度',
  `acclatitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '打卡点纬度',
  `unloading_time` double NULL DEFAULT NULL COMMENT '卸货时长',
  PRIMARY KEY (`acc_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `user_id` bigint NOT NULL COMMENT '主键',
  `login_name` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '登录账号',
  `user_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `work_number` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '工号',
  `sex` varchar(4) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '性别',
  `position` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '职位',
  `password` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '密码',
  `department` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '部门',
  `phone` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '状态（0：禁用；1：启用）',
  `avatar_path` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL COMMENT '头像路径',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '最近一次修改者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '最近一次修改时间',
  `sign_time` date NULL DEFAULT NULL COMMENT '入职时间',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `rank` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '职级',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `fk_user_role_role`(`role_id` ASC) USING BTREE,
  CONSTRAINT `fk_user_role_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_group
-- ----------------------------
DROP TABLE IF EXISTS `user_group`;
CREATE TABLE `user_group`  (
  `group_id` bigint NOT NULL,
  `user_id` bigint NULL DEFAULT NULL,
  `is_leader` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for version
-- ----------------------------
DROP TABLE IF EXISTS `version`;
CREATE TABLE `version`  (
  `version_id` int NOT NULL COMMENT '版本id',
  `version_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版本名称',
  `version_db` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实际对应版本',
  `version_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版本备注',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_show` int NULL DEFAULT NULL COMMENT '1显示在列表中，启用，0不显示在列表中，被删除',
  PRIMARY KEY (`version_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Procedure structure for CalculateDeliveryDistance
-- ----------------------------
DROP PROCEDURE IF EXISTS `CalculateDeliveryDistance`;
delimiter ;;
CREATE PROCEDURE `CalculateDeliveryDistance`()
BEGIN
    -- 定义地球半径常量（单位：千米）
    DECLARE EARTH_RADIUS DOUBLE DEFAULT 6371.0;
    DECLARE DEG_TO_RAD DOUBLE DEFAULT PI() / 180.0;

    -- 更新 delivery_distance 字段
    UPDATE pickup_user pu
    JOIN accumulation a ON pu.accumulation_id = a.accumulation_id
    SET pu.delivery_distance = 
        -- 使用 Haversine 公式计算球面距离（更准确的地理距离）
        -- 若坚持欧氏距离，可替换为下一行的计算公式
        -- SQRT(POW(a.longitude - pu.longitude, 2) + POW(a.latitude - pu.latitude, 2)) * 111.195
        ROUND(2 * EARTH_RADIUS * ASIN(
            SQRT(
                POW(SIN((a.latitude - pu.latitude) * DEG_TO_RAD / 2), 2) +
                COS(pu.latitude * DEG_TO_RAD) * COS(a.latitude * DEG_TO_RAD) *
                POW(SIN((a.longitude - pu.longitude) * DEG_TO_RAD / 2), 2)
            )
        ),3)
    WHERE 
        -- 验证坐标合法性（经度 -180~180，纬度 -90~90）
        pu.longitude BETWEEN -180 AND 180
        AND a.longitude BETWEEN -180 AND 180
        AND pu.latitude BETWEEN -90 AND 90
        AND a.latitude BETWEEN -90 AND 90;

    -- 可选：记录更新行数
    SELECT ROW_COUNT() AS '更新记录数';
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for setLocationType
-- ----------------------------
DROP PROCEDURE IF EXISTS `setLocationType`;
delimiter ;;
CREATE PROCEDURE `setLocationType`()
BEGIN-- 定义一个变量来标记游标是否完成
	DECLARE
		done INT DEFAULT FALSE;
	DECLARE
		distance DOUBLE;
	DECLARE
		distance1 DOUBLE;
	DECLARE
		num CHAR;-- 定义一个游标，用于SELECT查询
	 DECLARE s_id BIGINT; -- 声明变量s_id
    DECLARE lng DOUBLE; -- 声明变量lng
    DECLARE lat DOUBLE; -- 声明变量lat
    DECLARE cen_name VARCHAR(255); -- 声明变量cen_name
    DECLARE c_lng DOUBLE; -- 声明变量c_lng
    DECLARE c_lat DOUBLE; -- 声明变量c_lat
    DECLARE rad DOUBLE; -- 声明变量rad
	DECLARE
		cur CURSOR FOR SELECT
		 store_id AS s_id,
    longitude AS lng,
    latitude AS lat,
    centerdistance.cenStore_name AS cen_name,
    centerdistance.lng AS c_lng,
    centerdistance.lat AS c_lat,
    radius AS rad
	FROM
		store
-- 		INNER JOIN centerdistance ON centerdistance.cenStore_name = area_name;-- 定义一个处理游标结束的继续处理句柄
 		INNER JOIN centerdistance ON centerdistance.cenStore_name LIKE CONCAT('%', area_name, '%');-- 定义一个处理游标结束的继续处理句柄
	DECLARE
		CONTINUE HANDLER FOR NOT FOUND 
		SET done = TRUE;-- 打开游标
	OPEN cur;-- 定义一个循环，用于遍历游标中的每一行
	employee_loop :
	LOOP-- 从游标中获取当前行
		FETCH cur INTO 
		s_id, lng, lat, cen_name, c_lng, c_lat, rad;-- 检查是否已获取所有行
		IF
			done THEN-- 如果所有行已获取，则退出循环
				LEAVE employee_loop;
			
		END IF;-- 计算两点之间的距离
		
		SET distance = (ACOS(COS(RADIANS(lat)) * COS(RADIANS(c_lat)) * COS(RADIANS(lng) - RADIANS(c_lng)) + SIN(RADIANS(lat)) * SIN(RADIANS(c_lat))) * 6371000);
	 SELECT	lng,lat,cen_name,c_lng,c_lat,rad,distance;
		CASE
    WHEN distance <= rad THEN
        SET num = '1';
    WHEN cen_name = "始兴县" THEN
        SET distance1 = (ACOS(COS(RADIANS(lat)) * COS(RADIANS(25.025715)) * COS(RADIANS(lng) - RADIANS(114.154253)) + SIN(RADIANS(lat)) * SIN(RADIANS(25.025715))) * 6371000);
        IF distance1 <= 6000 THEN
            SET num = '1';
        ELSE
            SET num = '0';
        END IF;
    ELSE
        SET num = '0';
END CASE;

		UPDATE store 
		SET location_type = num 
		WHERE
			store_id = s_id;
		
	END LOOP;-- 关闭游标
	CLOSE cur;
	
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table operation
-- ----------------------------
DROP TRIGGER IF EXISTS `after_insert_operation`;
delimiter ;;
CREATE TRIGGER `after_insert_operation` AFTER INSERT ON `operation` FOR EACH ROW BEGIN
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (1, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (2, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (3, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (4, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (5, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (6, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (7, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (8, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (9, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (10, NEW.operation_id,0);
  INSERT INTO role_operation (role_id, operation_id,`status`) VALUES (11, NEW.operation_id,0);
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table operation
-- ----------------------------
DROP TRIGGER IF EXISTS `delete_operation`;
delimiter ;;
CREATE TRIGGER `delete_operation` AFTER DELETE ON `operation` FOR EACH ROW BEGIN
	DELETE FROM role_operation WHERE role_operation.operation_id = OLD.operation_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table role
-- ----------------------------
DROP TRIGGER IF EXISTS `after_role_insert`;
delimiter ;;
CREATE TRIGGER `after_role_insert` AFTER INSERT ON `role` FOR EACH ROW BEGIN
  INSERT INTO role_operation (role_id, operation_id, `status`)
  SELECT NEW.role_id, operation_id, 0
  FROM operation;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table role
-- ----------------------------
DROP TRIGGER IF EXISTS `before_role_delete`;
delimiter ;;
CREATE TRIGGER `before_role_delete` BEFORE DELETE ON `role` FOR EACH ROW BEGIN
    DELETE FROM role_operation WHERE role_id = OLD.role_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table version
-- ----------------------------
DROP TRIGGER IF EXISTS `update_updateTime_before_update`;
delimiter ;;
CREATE TRIGGER `update_updateTime_before_update` BEFORE UPDATE ON `version` FOR EACH ROW BEGIN
    SET NEW.update_time = NOW();
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
