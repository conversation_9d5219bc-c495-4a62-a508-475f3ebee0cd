package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation.RouteCountRecommendation;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation.RouteCountAction;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.UnifiedConstraintModel;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver.SolverManager;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPSolution;
import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能路线数量调整器
 * 
 * 基于评估结果智能调整路线数量，包括路线分割和合并算法
 * 集成MILP约束验证确保调整后的路线满足所有约束条件
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class IntelligentRouteCountAdjuster {
    
    @Autowired
    private RouteSplittingAlgorithm routeSplittingAlgorithm;
    
    @Autowired
    private RouteMergingAlgorithm routeMergingAlgorithm;
    
    @Autowired
    private UnifiedConstraintModel unifiedConstraintModel;
    
    @Autowired
    private SolverManager solverManager;
    
    /**
     * 执行路线数量调整
     * 
     * @param originalRoutes 原始路线列表
     * @param recommendation 路线数量调整建议
     * @param depot 中转站信息
     * @param timeMatrix 时间矩阵
     * @return 调整后的路线列表
     */
    public RouteAdjustmentResult adjustRouteCount(
            List<List<Accumulation>> originalRoutes,
            RouteCountRecommendation recommendation,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🚀 开始执行智能路线数量调整：中转站 {}", depot.getTransitDepotName());
        long startTime = System.currentTimeMillis();
        
        // 验证输入参数
        if (originalRoutes == null || originalRoutes.isEmpty()) {
            log.error("❌ 输入路线列表为空");
            return createErrorResult("输入路线列表为空", originalRoutes, startTime);
        }
        
        if (recommendation == null) {
            log.error("❌ 路线调整建议为空");
            return createErrorResult("路线调整建议为空", originalRoutes, startTime);
        }
        
        try {
            // 根据建议执行不同的调整策略
            List<List<Accumulation>> adjustedRoutes;
            
            switch (recommendation.getRecommendedAction()) {
                case INCREASE:
                    adjustedRoutes = increaseRouteCount(originalRoutes, recommendation, depot, timeMatrix);
                    break;
                case DECREASE:
                    adjustedRoutes = decreaseRouteCount(originalRoutes, recommendation, depot, timeMatrix);
                    break;
                case MAINTAIN:
                    log.info("✅ 路线数量评估：当前{}条路线已接近最优，保持不变", originalRoutes.size());
                    adjustedRoutes = originalRoutes;
                    break;
                default:
                    log.warn("⚠️ 未知的调整动作：{}，保持原有路线", recommendation.getRecommendedAction());
                    adjustedRoutes = originalRoutes;
                    break;
            }
            
            // MILP约束验证
            ConstraintValidationResult validationResult = validateConstraints(
                adjustedRoutes, depot, timeMatrix);
            
            // 如果约束验证失败，尝试修复
            if (!validationResult.isValid()) {
                log.warn("⚠️ 调整后路线不满足约束，尝试修复");
                adjustedRoutes = repairConstraintViolations(
                    adjustedRoutes, validationResult, depot, timeMatrix);
                
                // 重新验证
                validationResult = validateConstraints(adjustedRoutes, depot, timeMatrix);
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            return RouteAdjustmentResult.builder()
                .success(true)
                .originalRouteCount(originalRoutes.size())
                .adjustedRouteCount(adjustedRoutes.size())
                .adjustedRoutes(adjustedRoutes)
                .adjustmentAction(recommendation.getRecommendedAction())
                .constraintValidation(validationResult)
                .executionTimeMs(executionTime)
                .message("路线调整成功完成")
                .build();
                
        } catch (Exception e) {
            log.error("❌ 路线调整过程中发生错误", e);
            return createErrorResult("路线调整失败: " + e.getMessage(), originalRoutes, startTime);
        }
    }
    
    /**
     * 智能增加路线数量
     * 使用负载均衡分割算法
     */
    private List<List<Accumulation>> increaseRouteCount(
            List<List<Accumulation>> originalRoutes,
            RouteCountRecommendation recommendation,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        int targetRouteCount = recommendation.getRecommendedRouteCount();
        int currentRouteCount = originalRoutes.size();
        int routesToAdd = targetRouteCount - currentRouteCount;
        
        log.info("🔼 智能增加路线数量：{} → {} 条（+{}条）", 
            currentRouteCount, targetRouteCount, routesToAdd);
        
        if (routesToAdd <= 0) {
            log.warn("⚠️ 建议增加的路线数量不为正数：{}", routesToAdd);
            return originalRoutes;
        }
        
        // 第1步：识别最需要分割的路线
        List<RouteSplitCandidate> splitCandidates = identifyBestSplitCandidates(
            originalRoutes, depot, timeMatrix, routesToAdd);
        
        if (splitCandidates.isEmpty()) {
            log.warn("⚠️ 没有找到合适的分割候选路线");
            return originalRoutes;
        }
        
        List<List<Accumulation>> result = new ArrayList<>(originalRoutes);
        int actualSplits = 0;
        
        // 第2步：执行智能分割
        for (RouteSplitCandidate candidate : splitCandidates) {
            if (actualSplits >= routesToAdd) {
                break; // 已达到目标分割数量
            }
            
            try {
                List<List<Accumulation>> splitResults = routeSplittingAlgorithm.splitRoute(
                    candidate.getRoute(), candidate.getSplitCount(), depot, timeMatrix);
                
                if (splitResults.size() > 1) {
                    // 替换原路线
                    result.remove(candidate.getRoute());
                    result.addAll(splitResults);
                    
                    actualSplits += (splitResults.size() - 1); // 实际增加的路线数
                    
                    log.info("   ✅ 分割路线成功：{:.1f}分钟 → {}个子路线", 
                        candidate.getWorkTime(), splitResults.size());
                } else {
                    log.warn("   ⚠️ 路线分割未成功，保持原路线");
                }
                
            } catch (Exception e) {
                log.error("   ❌ 路线分割失败", e);
            }
        }
        
        log.info("✅ 路线增加完成：最终{}条路线（实际增加{}条）", result.size(), actualSplits);
        return result;
    }
    
    /**
     * 智能减少路线数量
     * 使用最优合并算法
     */
    private List<List<Accumulation>> decreaseRouteCount(
            List<List<Accumulation>> originalRoutes,
            RouteCountRecommendation recommendation,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        int targetRouteCount = recommendation.getRecommendedRouteCount();
        int currentRouteCount = originalRoutes.size();
        int routesToRemove = currentRouteCount - targetRouteCount;
        
        log.info("🔽 智能减少路线数量：{} → {} 条（-{}条）", 
            currentRouteCount, targetRouteCount, routesToRemove);
        
        if (routesToRemove <= 0) {
            log.warn("⚠️ 建议减少的路线数量不为正数：{}", routesToRemove);
            return originalRoutes;
        }
        
        // 第1步：识别最佳合并对
        List<RouteMergePair> mergePairs = identifyBestMergePairs(
            originalRoutes, depot, timeMatrix, routesToRemove);
        
        if (mergePairs.isEmpty()) {
            log.warn("⚠️ 没有找到合适的合并路线对");
            return originalRoutes;
        }
        
        List<List<Accumulation>> result = new ArrayList<>(originalRoutes);
        int actualMerges = 0;
        
        // 第2步：执行智能合并
        for (RouteMergePair pair : mergePairs) {
            if (actualMerges >= routesToRemove) {
                break; // 已达到目标合并数量
            }
            
            // 检查路线是否仍存在于结果集中（可能已被其他合并操作影响）
            if (!result.contains(pair.getRoute1()) || !result.contains(pair.getRoute2())) {
                log.debug("   ⚠️ 合并路线对已不存在，跳过");
                continue;
            }
            
            try {
                List<Accumulation> mergedRoute = routeMergingAlgorithm.mergeRoutes(
                    pair.getRoute1(), pair.getRoute2(), depot, timeMatrix);
                
                if (mergedRoute != null && !mergedRoute.isEmpty()) {
                    // 替换原路线
                    result.remove(pair.getRoute1());
                    result.remove(pair.getRoute2());
                    result.add(mergedRoute);
                    
                    actualMerges++;
                    
                    double mergedWorkTime = calculateRouteWorkTime(mergedRoute, depot, timeMatrix);
                    log.info("   ✅ 合并路线成功：{:.1f}分钟 + {:.1f}分钟 → {:.1f}分钟", 
                        pair.getWorkTime1(), pair.getWorkTime2(), mergedWorkTime);
                } else {
                    log.warn("   ⚠️ 路线合并未成功，保持原路线");
                }
                
            } catch (Exception e) {
                log.error("   ❌ 路线合并失败", e);
            }
        }
        
        log.info("✅ 路线减少完成：最终{}条路线（实际减少{}条）", result.size(), actualMerges);
        return result;
    }
    
    /**
     * 识别最佳分割候选路线
     */
    private List<RouteSplitCandidate> identifyBestSplitCandidates(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix,
            int routesToAdd) {
        
        log.debug("🔍 识别最佳分割候选路线，需要增加{}条路线", routesToAdd);
        
        List<RouteSplitCandidate> candidates = new ArrayList<>();
        
        for (List<Accumulation> route : routes) {
            double workTime = calculateRouteWorkTime(route, depot, timeMatrix);
            
            // 候选条件：工作时间 > 600分钟（能够分割成2条有意义的路线）
            if (workTime > 600.0 && route.size() >= 4) { // 至少4个聚集区才能分割
                
                // 计算分割优先级评分
                double splitPriority = calculateSplitPriority(route, depot, timeMatrix);
                
                // 确定分割数量（通常分割为2条路线）
                int splitCount = Math.min(2, (int) Math.ceil(workTime / 400.0));
                
                candidates.add(RouteSplitCandidate.builder()
                    .route(route)
                    .workTime(workTime)
                    .splitCount(splitCount)
                    .priority(splitPriority)
                    .accumulationCount(route.size())
                    .build());
            }
        }
        
        // 按优先级排序（优先级高的先分割）
        candidates.sort((a, b) -> Double.compare(b.getPriority(), a.getPriority()));
        
        // 限制候选数量，避免过度分割
        int maxCandidates = Math.min(candidates.size(), routesToAdd);
        List<RouteSplitCandidate> result = candidates.subList(0, maxCandidates);
        
        log.debug("   找到{}个分割候选路线", result.size());
        return result;
    }
    
    /**
     * 识别最佳合并路线对
     */
    private List<RouteMergePair> identifyBestMergePairs(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix,
            int routesToRemove) {
        
        log.debug("🔍 识别最佳合并路线对，需要减少{}条路线", routesToRemove);
        
        List<RouteMergePair> candidates = new ArrayList<>();
        
        // 计算所有路线对的合并候选
        for (int i = 0; i < routes.size(); i++) {
            for (int j = i + 1; j < routes.size(); j++) {
                List<Accumulation> route1 = routes.get(i);
                List<Accumulation> route2 = routes.get(j);
                
                double workTime1 = calculateRouteWorkTime(route1, depot, timeMatrix);
                double workTime2 = calculateRouteWorkTime(route2, depot, timeMatrix);
                double combinedWorkTime = workTime1 + workTime2;
                
                // 候选条件：合并后不超过450分钟
                if (combinedWorkTime <= 450.0) {
                    
                    // 计算合并优先级评分
                    double mergePriority = calculateMergePriority(
                        route1, route2, depot, timeMatrix);
                    
                    candidates.add(RouteMergePair.builder()
                        .route1(route1)
                        .route2(route2)
                        .workTime1(workTime1)
                        .workTime2(workTime2)
                        .combinedWorkTime(combinedWorkTime)
                        .priority(mergePriority)
                        .build());
                }
            }
        }
        
        // 按优先级排序（优先级高的先合并）
        candidates.sort((a, b) -> Double.compare(b.getPriority(), a.getPriority()));
        
        // 选择不冲突的合并对
        List<RouteMergePair> result = selectNonConflictingMergePairs(candidates, routesToRemove);
        
        log.debug("   找到{}个合并路线对", result.size());
        return result;
    }
    
    /**
     * 选择不冲突的合并对
     */
    private List<RouteMergePair> selectNonConflictingMergePairs(
            List<RouteMergePair> candidates, int maxPairs) {
        
        List<RouteMergePair> result = new ArrayList<>();
        Set<List<Accumulation>> usedRoutes = new HashSet<>();
        
        for (RouteMergePair candidate : candidates) {
            if (result.size() >= maxPairs) {
                break;
            }
            
            // 检查是否与已选择的合并对冲突
            if (!usedRoutes.contains(candidate.getRoute1()) && 
                !usedRoutes.contains(candidate.getRoute2())) {
                
                result.add(candidate);
                usedRoutes.add(candidate.getRoute1());
                usedRoutes.add(candidate.getRoute2());
            }
        }
        
        return result;
    }
    
    /**
     * 计算分割优先级评分
     */
    private double calculateSplitPriority(List<Accumulation> route, 
                                        TransitDepot depot, 
                                        Map<String, TimeInfo> timeMatrix) {
        
        double workTime = calculateRouteWorkTime(route, depot, timeMatrix);
        int accCount = route.size();
        
        // 优先级因素：
        // 1. 工作时间越长，优先级越高
        // 2. 聚集区数量越多，优先级越高
        // 3. 是否违反450分钟约束
        
        double timeFactor = workTime / 450.0; // 时间因子
        double sizeFactor = accCount / 10.0;  // 规模因子
        double violationFactor = workTime > 450.0 ? 2.0 : 1.0; // 违约因子
        
        return timeFactor * sizeFactor * violationFactor;
    }
    
    /**
     * 计算合并优先级评分
     */
    private double calculateMergePriority(List<Accumulation> route1, 
                                        List<Accumulation> route2,
                                        TransitDepot depot, 
                                        Map<String, TimeInfo> timeMatrix) {
        
        double workTime1 = calculateRouteWorkTime(route1, depot, timeMatrix);
        double workTime2 = calculateRouteWorkTime(route2, depot, timeMatrix);
        double combinedWorkTime = workTime1 + workTime2;
        
        // 优先级因素：
        // 1. 工作时间越短的路线优先合并
        // 2. 合并后越接近理想工作时间(350分钟)越好
        // 3. 地理位置相近性
        
        double timeFactor = 2.0 - (combinedWorkTime / 450.0); // 时间因子（越短越好）
        double idealFactor = 1.0 - Math.abs(combinedWorkTime - 350.0) / 350.0; // 理想度因子
        double geographicFactor = calculateGeographicSimilarity(route1, route2); // 地理因子
        
        return timeFactor * 0.4 + idealFactor * 0.4 + geographicFactor * 0.2;
    }
    
    /**
     * 计算地理相似性
     */
    private double calculateGeographicSimilarity(List<Accumulation> route1, 
                                               List<Accumulation> route2) {
        // 简化实现：基于第一个聚集区的位置距离
        if (route1.isEmpty() || route2.isEmpty()) {
            return 0.0;
        }
        
        // 这里应该实现真实的地理距离计算
        // 暂时返回固定值，实际实现时需要根据坐标计算距离
        return 0.5;
    }
    
    /**
     * 计算路线工作时间
     */
    private double calculateRouteWorkTime(List<Accumulation> route, 
                                        TransitDepot depot, 
                                        Map<String, TimeInfo> timeMatrix) {
        
        if (route == null || route.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        
        // 配送时间
        for (Accumulation acc : route) {
            if (acc.getDeliveryTime() != null) {
                totalTime += acc.getDeliveryTime();
            }
        }
        
        // 往返时间（简化计算，实际实现时需要考虑路线内部顺序）
        for (Accumulation acc : route) {
            String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                totalTime += timeInfo.getTravelTime() * 2; // 往返时间
            }
        }
        
        return totalTime;
    }
    
    /**
     * MILP约束验证
     */
    private ConstraintValidationResult validateConstraints(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.debug("🔍 执行MILP约束验证，路线数量：{}", routes.size());
        
        try {
            // 构建约束模型
            // 创建一个MAINTAIN推荐用于验证
            RouteCountRecommendation maintainRecommendation = RouteCountRecommendation.builder()
                .recommendedAction(RouteCountAction.MAINTAIN)
                .currentRouteCount(routes.size())
                .recommendedRouteCount(routes.size())
                .routeCountAdjustment(0)
                .comprehensiveScore(1.0)
                .confidence(1.0)
                .reasoning("约束验证")
                .build();
            
            MILPProblem problem = unifiedConstraintModel.buildUnifiedConstraintModel(
                routes, depot, timeMatrix, maintainRecommendation);
            
            // 求解验证
            MILPSolution solution = solverManager.solve(problem);
            
            // 分析验证结果
            boolean isValid = solution != null && 
                             solution.getSolutionStatus() == MILPProblem.SolutionStatus.OPTIMAL;
            
            List<String> violations = new ArrayList<>();
            if (!isValid) {
                // 检查具体违约情况
                violations.addAll(identifyConstraintViolations(routes, depot, timeMatrix));
            }
            
            return ConstraintValidationResult.builder()
                .valid(isValid)
                .violations(violations)
                .solutionStatus(solution != null ? solution.getSolutionStatus() : MILPProblem.SolutionStatus.ERROR)
                .build();
                
        } catch (Exception e) {
            log.error("❌ MILP约束验证失败", e);
            return ConstraintValidationResult.builder()
                .valid(false)
                .violations(Arrays.asList("MILP约束验证异常: " + e.getMessage()))
                .solutionStatus(MILPProblem.SolutionStatus.ERROR)
                .build();
        }
    }
    
    /**
     * 识别约束违反情况
     */
    private List<String> identifyConstraintViolations(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<String> violations = new ArrayList<>();
        
        // 检查450分钟约束
        for (int i = 0; i < routes.size(); i++) {
            double workTime = calculateRouteWorkTime(routes.get(i), depot, timeMatrix);
            if (workTime > 450.0) {
                violations.add(String.format("路线%d超时：%.1f分钟 > 450分钟", i + 1, workTime));
            }
        }
        
        // 检查路线数量约束
        if (routes.size() > 130) {
            violations.add(String.format("路线数量超限：%d > 130", routes.size()));
        }
        
        return violations;
    }
    
    /**
     * 修复约束违反
     */
    private List<List<Accumulation>> repairConstraintViolations(
            List<List<Accumulation>> routes,
            ConstraintValidationResult validationResult,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🔧 尝试修复约束违反，违反数量：{}", validationResult.getViolations().size());
        
        List<List<Accumulation>> repairedRoutes = new ArrayList<>(routes);
        
        // 修复超时路线（分割长路线）
        List<List<Accumulation>> routesToSplit = new ArrayList<>();
        for (List<Accumulation> route : repairedRoutes) {
            double workTime = calculateRouteWorkTime(route, depot, timeMatrix);
            if (workTime > 450.0 && route.size() >= 2) {
                routesToSplit.add(route);
            }
        }
        
        for (List<Accumulation> route : routesToSplit) {
            try {
                List<List<Accumulation>> splitResults = routeSplittingAlgorithm.splitRoute(
                    route, 2, depot, timeMatrix);
                
                if (splitResults.size() > 1) {
                    repairedRoutes.remove(route);
                    repairedRoutes.addAll(splitResults);
                    log.info("   ✅ 修复超时路线：分割为{}个子路线", splitResults.size());
                }
            } catch (Exception e) {
                log.warn("   ⚠️ 路线修复失败", e);
            }
        }
        
        log.info("✅ 约束修复完成，最终路线数量：{}", repairedRoutes.size());
        return repairedRoutes;
    }
    
    /**
     * 创建错误结果
     */
    private RouteAdjustmentResult createErrorResult(String message, 
                                                  List<List<Accumulation>> originalRoutes,
                                                  long startTime) {
        return RouteAdjustmentResult.builder()
            .success(false)
            .originalRouteCount(originalRoutes != null ? originalRoutes.size() : 0)
            .adjustedRouteCount(originalRoutes != null ? originalRoutes.size() : 0)
            .adjustedRoutes(originalRoutes != null ? originalRoutes : new ArrayList<>())
            .adjustmentAction(RouteCountAction.MAINTAIN)
            .constraintValidation(ConstraintValidationResult.builder()
                .valid(false)
                .violations(Arrays.asList(message))
                .solutionStatus(MILPProblem.SolutionStatus.ERROR)
                .build())
            .executionTimeMs(System.currentTimeMillis() - startTime)
            .message(message)
            .build();
    }
}