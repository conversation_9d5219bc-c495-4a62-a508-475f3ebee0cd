package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * OR-Tools类加载保护器
 * 通过静态代码块确保在任何OR-Tools类被加载之前就执行JNI修复
 * 解决JVM类初始化缓存导致的测试成功但实际失败问题
 */
@Slf4j
public class ORToolsClassLoadGuard {
    
    private static final AtomicBoolean guardInitialized = new AtomicBoolean(false);
    private static final AtomicBoolean orToolsClassesPolluted = new AtomicBoolean(false);
    
    // 🛡️ 最高优先级静态初始化 - 在任何OR-Tools类加载之前执行
    static {
        try {
            log.info("🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复");
            
            // 1. 首先检查关键OR-Tools类是否已经被加载过
            boolean alreadyPolluted = checkORToolsClassPollution();
            
            if (alreadyPolluted) {
                log.error("⚠️ [类加载保护] 检测到OR-Tools类已被污染！JVM缓存了失败状态，需要重启应用");
                orToolsClassesPolluted.set(true);
            } else {
                log.info("✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复");
                
                // 2. 在任何OR-Tools类被加载之前执行JNI修复
                boolean fixResult = JNIFixService.performJNIFix();
                log.info("🔧 [类加载保护] JNI预修复完成，结果: {}", fixResult);
                
                // 3. 标记保护器已初始化
                guardInitialized.set(true);
                log.info("🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪");
            }
            
        } catch (Exception e) {
            log.error("❌ [类加载保护] OR-Tools类加载保护器初始化失败: {}", e.getMessage(), e);
            orToolsClassesPolluted.set(true);
        }
    }
    
    /**
     * 检查OR-Tools关键类是否已经被污染
     */
    private static boolean checkORToolsClassPollution() {
        String[] criticalClasses = {
            "com.google.ortools.constraintsolver.RoutingModel",
            "com.google.ortools.constraintsolver.RoutingIndexManager",
            "com.google.ortools.Loader"
        };
        
        for (String className : criticalClasses) {
            ClassLoadStatus status = getClassLoadStatus(className);
            log.debug("🔍 [污染检测] {} - 状态: {}", className, status);
            
            if (status == ClassLoadStatus.POLLUTED) {
                log.error("💀 [污染检测] 发现污染类: {} - JVM已缓存失败状态", className);
                return true;
            } else if (status == ClassLoadStatus.LOADED) {
                log.warn("⚠️ [污染检测] 类已加载但未污染: {} - 可能错过了修复时机", className);
            }
        }
        
        return false;
    }
    
    /**
     * 获取类加载状态
     */
    private static ClassLoadStatus getClassLoadStatus(String className) {
        try {
            // 尝试获取类，但不触发初始化
            Class<?> clazz = Class.forName(className, false, 
                    ORToolsClassLoadGuard.class.getClassLoader());
            
            // 检查类是否已经被初始化
            try {
                // 尝试访问类的状态信息（这会触发初始化检查）
                clazz.getDeclaredFields();
                return ClassLoadStatus.LOADED;
            } catch (NoClassDefFoundError e) {
                // 类存在但初始化失败 - 这是污染状态
                return ClassLoadStatus.POLLUTED;
            } catch (ExceptionInInitializerError e) {
                // 类初始化异常 - 这也是污染状态
                return ClassLoadStatus.POLLUTED;
            }
            
        } catch (ClassNotFoundException e) {
            // 类不存在或未加载
            return ClassLoadStatus.NOT_LOADED;
        } catch (Exception e) {
            log.debug("类状态检查异常: {} - {}", className, e.getMessage());
            return ClassLoadStatus.UNKNOWN;
        }
    }
    
    /**
     * 类加载状态枚举
     */
    public enum ClassLoadStatus {
        NOT_LOADED("未加载"),
        LOADED("已加载"),
        POLLUTED("已污染"),
        UNKNOWN("未知状态");
        
        private final String description;
        
        ClassLoadStatus(String description) {
            this.description = description;
        }
        
        @Override
        public String toString() {
            return description;
        }
    }
    
    /**
     * 安全的OR-Tools类加载器
     * 确保在加载前保护器已经初始化
     */
    public static Class<?> safeLoadORToolsClass(String className) throws ClassNotFoundException {
        // 确保保护器已初始化
        ensureGuardInitialized();
        
        // 检查是否已污染
        if (orToolsClassesPolluted.get()) {
            throw new IllegalStateException("OR-Tools类已被污染，无法安全加载。请重启应用后重试。");
        }
        
        try {
            log.debug("🔒 [安全加载] 开始安全加载OR-Tools类: {}", className);
            Class<?> clazz = Class.forName(className);
            log.debug("✅ [安全加载] OR-Tools类加载成功: {}", className);
            return clazz;
            
        } catch (NoClassDefFoundError e) {
            log.error("💀 [安全加载] OR-Tools类加载失败 - 类已污染: {} - {}", className, e.getMessage());
            orToolsClassesPolluted.set(true);
            throw new ClassNotFoundException("OR-Tools类已污染: " + className, e);
        } catch (ExceptionInInitializerError e) {
            log.error("💀 [安全加载] OR-Tools类初始化失败: {} - {}", className, e.getCause().getMessage());
            orToolsClassesPolluted.set(true);
            throw new ClassNotFoundException("OR-Tools类初始化失败: " + className, e);
        }
    }
    
    /**
     * 确保保护器已初始化
     */
    private static void ensureGuardInitialized() {
        if (!guardInitialized.get() && !orToolsClassesPolluted.get()) {
            log.warn("⚠️ [类加载保护] 保护器尚未初始化，执行补偿性修复");
            try {
                JNIFixService.performJNIFix();
                guardInitialized.set(true);
            } catch (Exception e) {
                log.error("❌ [类加载保护] 补偿性修复失败: {}", e.getMessage());
                orToolsClassesPolluted.set(true);
            }
        }
    }
    
    /**
     * 检查OR-Tools类是否已被污染
     */
    public static boolean isORToolsClassesPolluted() {
        return orToolsClassesPolluted.get();
    }
    
    /**
     * 检查保护器是否已初始化
     */
    public static boolean isGuardInitialized() {
        return guardInitialized.get();
    }
    
    /**
     * 获取保护器状态信息
     */
    public static String getGuardStatus() {
        return String.format("ORToolsClassLoadGuard[initialized=%s, polluted=%s]", 
                           guardInitialized.get(), orToolsClassesPolluted.get());
    }
    
    /**
     * 强制重置保护器状态（仅用于测试）
     */
    public static void resetForTesting() {
        guardInitialized.set(false);
        orToolsClassesPolluted.set(false);
        log.info("🧪 [测试重置] OR-Tools类加载保护器状态已重置");
    }
    
    /**
     * 诊断信息输出
     */
    public static void outputDiagnostics() {
        log.info("🔍 [诊断信息] OR-Tools类加载保护器状态:");
        log.info("   保护器已初始化: {}", guardInitialized.get());
        log.info("   OR-Tools类已污染: {}", orToolsClassesPolluted.get());
        log.info("   JNI修复服务状态: {}", JNIFixService.getStatus());
        
        // 检查各个关键类的状态
        String[] criticalClasses = {
            "com.google.ortools.Loader",
            "com.google.ortools.constraintsolver.RoutingIndexManager",
            "com.google.ortools.constraintsolver.RoutingModel"
        };
        
        log.info("   关键类状态检查:");
        for (String className : criticalClasses) {
            ClassLoadStatus status = getClassLoadStatus(className);
            log.info("     {} : {}", className, status);
        }
    }
}