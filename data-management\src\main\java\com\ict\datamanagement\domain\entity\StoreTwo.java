package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

@Data
@TableName("store_two")
public class StoreTwo {
    //商店id
    @TableId
    private Long storeId;
    //负责人
    private String head;
    //备用收货电话
    private String sparePhone;
    //收货电话
    private String receivingPhone;
    //返销周期
    private String resaleCycle;
}
