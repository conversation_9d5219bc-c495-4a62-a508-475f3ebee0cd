package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间均衡优化器
 * 实现多层级时间均衡算法
 */
@Slf4j
@Component
public class TimeBalanceOptimizer {
    
    /**
     * 路线级时间均衡
     * @param context 算法上下文
     * @return 调整的路线数量
     */
    public int balanceRoutes(AlgorithmContext context) {
        log.info("开始路线级时间均衡");
        
        int totalAdjustments = 0;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            
            if (routes.size() <= 1) continue;
            
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            int adjustments = balanceRoutesForDepot(depot, routes, context);
            totalAdjustments += adjustments;
            
            log.debug("中转站 {} 进行了 {} 次路线均衡调整", depot.getTransitDepotName(), adjustments);
        }
        
        log.info("路线级均衡完成，总调整次数: {}", totalAdjustments);
        return totalAdjustments;
    }
    
    /**
     * 中转站级时间均衡
     * @param context 算法上下文
     * @return 调整的中转站数量
     */
    public int balanceDepots(AlgorithmContext context) {
        log.info("开始中转站级时间均衡");
        
        int totalAdjustments = 0;
        
        for (Map.Entry<Long, List<TransitDepot>> entry : context.getTeamGroups().entrySet()) {
            Long teamId = entry.getKey();
            List<TransitDepot> depots = entry.getValue();
            
            if (depots.size() <= 1) continue;
            
            Team team = context.getTeamById(teamId);
            int adjustments = balanceDepotsForTeam(team, depots, context);
            totalAdjustments += adjustments;
            
            log.debug("班组 {} 进行了 {} 次中转站均衡调整", team.getTeamName(), adjustments);
        }
        
        log.info("中转站级均衡完成，总调整次数: {}", totalAdjustments);
        return totalAdjustments;
    }
    
    /**
     * 班组级时间均衡评估
     * @param context 算法上下文
     */
    public void evaluateTeamBalance(AlgorithmContext context) {
        log.info("开始班组级时间均衡评估");
        
        Map<Long, Double> teamWorkloads = new HashMap<>();
        
        for (Map.Entry<Long, List<TransitDepot>> entry : context.getTeamGroups().entrySet()) {
            Long teamId = entry.getKey();
            List<TransitDepot> depots = entry.getValue();
            
            double totalWorkload = 0.0;
            int totalRoutes = 0;
            
            for (TransitDepot depot : depots) {
                List<RouteResult> routes = context.getOptimizedRoutes().get(depot.getTransitDepotId());
                if (routes != null) {
                    for (RouteResult route : routes) {
                        totalWorkload += route.getTotalWorkTime();
                        totalRoutes++;
                    }
                }
            }
            
            double avgWorkload = totalRoutes > 0 ? totalWorkload / totalRoutes : 0.0;
            teamWorkloads.put(teamId, avgWorkload);
            
            Team team = context.getTeamById(teamId);
            log.info("班组 {} 平均工作时间: {}分钟 ({}条路线)", 
                    team.getTeamName(), String.format("%.1f", avgWorkload), totalRoutes);
        }
        
        // 计算班组间差距
        if (teamWorkloads.size() > 1) {
            double maxWorkload = Collections.max(teamWorkloads.values());
            double minWorkload = Collections.min(teamWorkloads.values());
            double gap = maxWorkload - minWorkload;
            
            log.info("班组间最大时间差距: {}分钟", String.format("%.1f", gap));
            
            if (gap > AlgorithmParameters.TEAM_TIME_GAP_THRESHOLD) {
                log.warn("班组间时间差距超过阈值 {}分钟，建议调整人员配置", 
                        String.format("%.1f", AlgorithmParameters.TEAM_TIME_GAP_THRESHOLD));
            }
        }
    }
    
    /**
     * 生成时间均衡统计
     * @param context 算法上下文
     * @return 时间均衡统计信息
     */
    public TimeBalanceStats generateStats(AlgorithmContext context) {
        TimeBalanceStats stats = new TimeBalanceStats();
        
        // 计算路线级时间方差
        List<Double> allRouteTimes = new ArrayList<>();
        Map<Long, Double> routeTimeGaps = new HashMap<>();
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            
            // 收集所有路线时间用于方差计算
            List<Double> routeTimes = routes.stream()
                    .mapToDouble(RouteResult::getTotalWorkTime)
                    .boxed()
                    .collect(Collectors.toList());
            allRouteTimes.addAll(routeTimes);
            
            // 计算该中转站的路线时间差距
            if (routes.size() > 1) {
                double maxTime = Collections.max(routeTimes);
                double minTime = Collections.min(routeTimes);
                routeTimeGaps.put(transitDepotId, maxTime - minTime);
            }
        }
        
        // 计算路线级方差
        if (!allRouteTimes.isEmpty()) {
            stats.setRouteTimeVariance(calculateVariance(allRouteTimes));
        }
        stats.setRouteTimeGapByDepot(routeTimeGaps);
        
        // 计算中转站级时间方差
        List<Double> allDepotAvgTimes = new ArrayList<>();
        Map<Long, Double> depotTimeGaps = new HashMap<>();
        
        for (Map.Entry<Long, List<TransitDepot>> entry : context.getTeamGroups().entrySet()) {
            Long teamId = entry.getKey();
            List<TransitDepot> depots = entry.getValue();
            
            List<Double> depotAvgTimes = new ArrayList<>();
            for (TransitDepot depot : depots) {
                List<RouteResult> routes = context.getOptimizedRoutes().get(depot.getTransitDepotId());
                if (routes != null && !routes.isEmpty()) {
                    double avgTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).average().orElse(0.0);
                    depotAvgTimes.add(avgTime);
                    allDepotAvgTimes.add(avgTime);
                }
            }
            
            // 计算该班组的中转站时间差距
            if (depotAvgTimes.size() > 1) {
                double maxTime = Collections.max(depotAvgTimes);
                double minTime = Collections.min(depotAvgTimes);
                depotTimeGaps.put(teamId, maxTime - minTime);
            }
        }
        
        // 计算中转站级方差
        if (!allDepotAvgTimes.isEmpty()) {
            stats.setDepotTimeVariance(calculateVariance(allDepotAvgTimes));
        }
        stats.setDepotTimeGapByTeam(depotTimeGaps);
        
        // 计算班组级时间方差
        List<Double> teamAvgTimes = new ArrayList<>();
        for (Map.Entry<Long, List<TransitDepot>> entry : context.getTeamGroups().entrySet()) {
            List<TransitDepot> depots = entry.getValue();
            double totalTime = 0.0;
            int totalRoutes = 0;
            
            for (TransitDepot depot : depots) {
                List<RouteResult> routes = context.getOptimizedRoutes().get(depot.getTransitDepotId());
                if (routes != null) {
                    for (RouteResult route : routes) {
                        totalTime += route.getTotalWorkTime();
                        totalRoutes++;
                    }
                }
            }
            
            if (totalRoutes > 0) {
                teamAvgTimes.add(totalTime / totalRoutes);
            }
        }
        
        // 计算班组级方差和时间差距
        if (!teamAvgTimes.isEmpty()) {
            stats.setTeamTimeVariance(calculateVariance(teamAvgTimes));
            
            if (teamAvgTimes.size() > 1) {
                double maxTime = Collections.max(teamAvgTimes);
                double minTime = Collections.min(teamAvgTimes);
                stats.setTeamTimeGap(maxTime - minTime);
            }
        }
        
        // 计算均衡度
        calculateBalanceRatios(stats, allRouteTimes, allDepotAvgTimes, teamAvgTimes);
        
        return stats;
    }
    
    /**
     * 计算方差
     */
    private double calculateVariance(List<Double> values) {
        if (values.isEmpty()) return 0.0;
        
        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = values.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .average()
                .orElse(0.0);
        
        return variance;
    }
    
    /**
     * 计算均衡度比例
     */
    private void calculateBalanceRatios(TimeBalanceStats stats, List<Double> routeTimes, 
                                       List<Double> depotTimes, List<Double> teamTimes) {
        // 路线级均衡度
        if (!routeTimes.isEmpty() && stats.getRouteTimeVariance() != null) {
            double mean = routeTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double coefficientOfVariation = mean > 0 ? Math.sqrt(stats.getRouteTimeVariance()) / mean : 0.0;
            stats.setRouteBalanceRatio(Math.max(0.0, 100.0 * (1.0 - coefficientOfVariation)));
        }
        
        // 中转站级均衡度
        if (!depotTimes.isEmpty() && stats.getDepotTimeVariance() != null) {
            double mean = depotTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double coefficientOfVariation = mean > 0 ? Math.sqrt(stats.getDepotTimeVariance()) / mean : 0.0;
            stats.setDepotBalanceRatio(Math.max(0.0, 100.0 * (1.0 - coefficientOfVariation)));
        }
        
        // 班组级均衡度
        if (!teamTimes.isEmpty() && stats.getTeamTimeVariance() != null) {
            double mean = teamTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double coefficientOfVariation = mean > 0 ? Math.sqrt(stats.getTeamTimeVariance()) / mean : 0.0;
            stats.setTeamBalanceRatio(Math.max(0.0, 100.0 * (1.0 - coefficientOfVariation)));
        }
        
        // 总体均衡度（各级别权重平均）
        Double routeRatio = stats.getRouteBalanceRatio();
        Double depotRatio = stats.getDepotBalanceRatio();
        Double teamRatio = stats.getTeamBalanceRatio();
        
        if (routeRatio != null && depotRatio != null && teamRatio != null) {
            // 路线级权重最高，因为直接影响执行效果
            stats.setOverallBalanceRatio((routeRatio * 0.5) + (depotRatio * 0.3) + (teamRatio * 0.2));
        }
    }
    
    /**
     * 为单个中转站平衡路线
     */
    private int balanceRoutesForDepot(TransitDepot depot, List<RouteResult> routes, AlgorithmContext context) {
        int adjustments = 0;
        
        for (int iteration = 0; iteration < AlgorithmParameters.MAX_BALANCE_ITERATIONS; iteration++) {
            // 计算当前时间差距
            double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
            double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
            double gap = maxTime - minTime;
            
            if (gap <= AlgorithmParameters.ROUTE_TIME_GAP_THRESHOLD) {
                break; // 已达到均衡
            }
            
            // 找到最重载和最轻载的路线
            RouteResult heaviestRoute = routes.stream()
                    .max(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                    .orElse(null);
            RouteResult lightestRoute = routes.stream()
                    .min(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                    .orElse(null);
            
            if (heaviestRoute == null || lightestRoute == null || heaviestRoute == lightestRoute) {
                break;
            }
            
            // 尝试转移聚集区
            if (tryTransferAccumulation(heaviestRoute, lightestRoute, context)) {
                adjustments++;
                
                // 重新计算路线工作时间
                updateRouteWorkTime(heaviestRoute, context);
                updateRouteWorkTime(lightestRoute, context);
            } else {
                // 尝试添加人为延迟
                if (tryAddArtificialDelay(lightestRoute, heaviestRoute.getTotalWorkTime() - lightestRoute.getTotalWorkTime())) {
                    adjustments++;
                } else {
                    break; // 无法进一步优化
                }
            }
        }
        
        return adjustments;
    }
    
    /**
     * 为单个班组平衡中转站
     */
    private int balanceDepotsForTeam(Team team, List<TransitDepot> depots, AlgorithmContext context) {
        // 计算每个中转站的平均工作时间
        Map<Long, Double> depotAvgTimes = new HashMap<>();
        
        for (TransitDepot depot : depots) {
            List<RouteResult> routes = context.getOptimizedRoutes().get(depot.getTransitDepotId());
            if (routes != null && !routes.isEmpty()) {
                double avgTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).average().orElse(0.0);
                depotAvgTimes.put(depot.getTransitDepotId(), avgTime);
            }
        }
        
        if (depotAvgTimes.size() <= 1) return 0;
        
        double maxTime = Collections.max(depotAvgTimes.values());
        double minTime = Collections.min(depotAvgTimes.values());
        double gap = maxTime - minTime;
        
        if (gap <= AlgorithmParameters.DEPOT_TIME_GAP_THRESHOLD) {
            return 0; // 已达到均衡
        }
        
        log.debug("班组 {} 中转站间时间差距 {}分钟，超过阈值，开始均衡", team.getTeamName(), String.format("%.1f", gap));
        
        // 🔧 修复：实现真正的中转站均衡操作
        return performDepotBalance(team, depots, depotAvgTimes, context);
    }
    
    /**
     * 尝试在路线间转移聚集区
     */
    private boolean tryTransferAccumulation(RouteResult fromRoute, RouteResult toRoute, AlgorithmContext context) {
        List<Long> fromSequence = fromRoute.getAccumulationSequence();
        List<Long> toSequence = toRoute.getAccumulationSequence();
        
        if (fromSequence.isEmpty()) return false;
        
        // 尝试转移边界聚集区（优先转移配送时间较长的）
        Long bestCandidate = null;
        double bestDeliveryTime = 0.0;
        
        // 检查首尾位置的聚集区
        int[] candidateIndices = {0, fromSequence.size() - 1};
        
        for (int index : candidateIndices) {
            Long accId = fromSequence.get(index);
            Accumulation acc = context.getAccumulationById(accId);
            
            if (acc != null && acc.getDeliveryTime() > bestDeliveryTime) {
                bestCandidate = accId;
                bestDeliveryTime = acc.getDeliveryTime();
            }
        }
        
        if (bestCandidate != null) {
            // 执行转移 - 创建新的可变列表避免UnsupportedOperationException
            List<Long> newFromSequence = new ArrayList<>(fromSequence);
            List<Long> newToSequence = new ArrayList<>(toSequence);
            
            newFromSequence.remove(bestCandidate);
            newToSequence.add(bestCandidate);
            
            // 更新RouteResult对象
            fromRoute.setAccumulationSequence(newFromSequence);
            toRoute.setAccumulationSequence(newToSequence);
            
            log.debug("转移聚集区 {} 从路线 {} 到路线 {}，配送时间: {}分钟", 
                    bestCandidate, fromRoute.getRouteName(), toRoute.getRouteName(), String.format("%.1f", bestDeliveryTime));
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 添加人为延迟
     */
    private boolean tryAddArtificialDelay(RouteResult route, double targetGap) {
        if (targetGap <= 0) return false;
        
        // 添加最多30%的目标差距作为人为延迟
        double maxDelay = targetGap * 0.3;
        double actualDelay = Math.min(maxDelay, AlgorithmParameters.DEFAULT_REST_TIME);
        
        if (actualDelay > 1.0) { // 至少1分钟的延迟才有意义
            route.setTotalWorkTime(route.getTotalWorkTime() + actualDelay);
            log.debug("为路线 {} 添加人为延迟 {}分钟", route.getRouteName(), String.format("%.1f", actualDelay));
            return true;
        }
        
        return false;
    }
    
    /**
     * 更新路线工作时间 - 修复：转移后重新运行TSP算法优化路径
     */
    private void updateRouteWorkTime(RouteResult route, AlgorithmContext context) {
        List<Long> sequence = route.getAccumulationSequence();
        TransitDepot depot = context.getTransitDepotById(route.getTransitDepotId());
        
        if (sequence.isEmpty()) {
            route.setTotalWorkTime(AlgorithmParameters.LOADING_TIME_MINUTES);
            route.setPolyline(Arrays.asList(depot.getCoordinate()));
            return;
        }
        
        // 获取转移后的聚集区列表
        List<Accumulation> clusterAccumulations = new ArrayList<>();
        for (Long accId : sequence) {
            Accumulation acc = context.getAccumulationById(accId);
            if (acc != null) {
                clusterAccumulations.add(acc);
            }
        }
        
        if (clusterAccumulations.isEmpty()) {
            route.setTotalWorkTime(AlgorithmParameters.LOADING_TIME_MINUTES);
            route.setPolyline(Arrays.asList(depot.getCoordinate()));
            return;
        }
        
        try {
            // 🔧 关键修复：重新运行TSP算法优化路径
            TSPSolverManager tspSolver = new TSPSolverManager();
            RouteResult optimizedRoute = tspSolver.solveRoute(
                depot, 
                clusterAccumulations, 
                context.getTimeMatrix(), 
                route.getRouteId().intValue() % 1000 // 提取路线编号
            );
            
            // 更新优化后的结果
            route.setAccumulationSequence(optimizedRoute.getAccumulationSequence());
            route.setTotalWorkTime(optimizedRoute.getTotalWorkTime());
            route.setPolyline(optimizedRoute.getPolyline());
            
            log.debug("路线 {} 转移后重新TSP优化，时间: {:.1f}分钟 -> {:.1f}分钟", 
                     route.getRouteName(), 
                     calculateSimpleTime(clusterAccumulations, depot, context), 
                     optimizedRoute.getTotalWorkTime());
                     
        } catch (Exception e) {
            log.warn("路线 {} TSP重新优化失败，使用简单计算: {}", route.getRouteName(), e.getMessage());
            
            // 降级处理：简单计算时间（保持原有逻辑）
            double totalTime = calculateSimpleTime(clusterAccumulations, depot, context);
            route.setTotalWorkTime(totalTime);
            
            // 构建简单路径
            List<CoordinatePoint> polyline = new ArrayList<>();
            polyline.add(depot.getCoordinate());
            for (Accumulation acc : clusterAccumulations) {
                polyline.add(acc.getCoordinate());
            }
            polyline.add(depot.getCoordinate());
            route.setPolyline(polyline);
        }
    }
    
    /**
     * 简单时间计算（降级处理）
     */
    private double calculateSimpleTime(List<Accumulation> accumulations, TransitDepot depot, AlgorithmContext context) {
        double totalTime = AlgorithmParameters.LOADING_TIME_MINUTES;
        CoordinatePoint currentPos = depot.getCoordinate();
        
        for (Accumulation acc : accumulations) {
            // 获取行驶时间
            TimeInfo timeInfo = context.getTimeInfo(
                    currentPos.getLongitude(), currentPos.getLatitude(),
                    acc.getLongitude(), acc.getLatitude());
            if (timeInfo != null) {
                totalTime += timeInfo.getTravelTime();
            }
            
            // 添加配送时间（点权）
            totalTime += acc.getDeliveryTime();
            currentPos = acc.getCoordinate();
        }
        
        // 回到中转站的时间
        TimeInfo returnTimeInfo = context.getTimeInfo(
                currentPos.getLongitude(), currentPos.getLatitude(),
                depot.getLongitude(), depot.getLatitude());
        if (returnTimeInfo != null) {
            totalTime += returnTimeInfo.getTravelTime();
        }
        
        return totalTime;
    }
    
    /**
     * 执行中转站均衡操作 - 新增：真正的跨中转站均衡
     */
    private int performDepotBalance(Team team, List<TransitDepot> depots, 
                                   Map<Long, Double> depotAvgTimes, AlgorithmContext context) {
        
        int adjustments = 0;
        
        // 找到最重载和最轻载的中转站
        Long heaviestDepotId = null;
        Long lightestDepotId = null;
        double maxTime = Double.MIN_VALUE;
        double minTime = Double.MAX_VALUE;
        
        for (Map.Entry<Long, Double> entry : depotAvgTimes.entrySet()) {
            if (entry.getValue() > maxTime) {
                maxTime = entry.getValue();
                heaviestDepotId = entry.getKey();
            }
            if (entry.getValue() < minTime) {
                minTime = entry.getValue();
                lightestDepotId = entry.getKey();
            }
        }
        
        if (heaviestDepotId == null || lightestDepotId == null || heaviestDepotId.equals(lightestDepotId)) {
            return 0;
        }
        
        TransitDepot heaviestDepot = context.getTransitDepotById(heaviestDepotId);
        TransitDepot lightestDepot = context.getTransitDepotById(lightestDepotId);
        
        // 获取两个中转站的路线
        List<RouteResult> heaviestRoutes = context.getOptimizedRoutes().get(heaviestDepotId);
        List<RouteResult> lightestRoutes = context.getOptimizedRoutes().get(lightestDepotId);
        
        if (heaviestRoutes == null || lightestRoutes == null) {
            return 0;
        }
        
        // 尝试转移最短的路线从重载站到轻载站
        RouteResult candidateRoute = heaviestRoutes.stream()
                .min(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                .orElse(null);
                
        if (candidateRoute != null) {
            double transferTime = candidateRoute.getTotalWorkTime();
            double newHeaviestTime = maxTime - transferTime;
            double newLightestTime = minTime + transferTime;
            
            // 检查转移后是否改善了均衡度
            double currentGap = maxTime - minTime;
            double newGap = Math.abs(newHeaviestTime - newLightestTime);
            
            if (newGap < currentGap && newLightestTime <= AlgorithmParameters.TARGET_ROUTE_TIME) {
                // 执行路线转移
                if (transferRouteBetweenDepots(candidateRoute, heaviestDepot, lightestDepot, context)) {
                    adjustments++;
                    log.debug("转移路线 {} 从中转站 {} 到中转站 {}，时间: {:.1f}分钟", 
                             candidateRoute.getRouteName(), 
                             heaviestDepot.getTransitDepotName(), 
                             lightestDepot.getTransitDepotName(), 
                             transferTime);
                }
            }
        }
        
        // 如果整条路线转移不合适，尝试转移部分聚集区
        if (adjustments == 0) {
            adjustments += tryTransferAccumulationsBetweenDepots(
                heaviestDepot, lightestDepot, context);
        }
        
        return adjustments;
    }
    
    /**
     * 在中转站间转移路线
     */
    private boolean transferRouteBetweenDepots(RouteResult route, TransitDepot fromDepot, 
                                             TransitDepot toDepot, AlgorithmContext context) {
        try {
            // 从原中转站移除路线
            List<RouteResult> fromRoutes = context.getOptimizedRoutes().get(fromDepot.getTransitDepotId());
            if (fromRoutes != null) {
                fromRoutes.remove(route);
            }
            
            // 更新路线的中转站归属
            route.setTransitDepotId(toDepot.getTransitDepotId());
            
            // 重新计算路线（使用新的中转站）
            List<Accumulation> accumulations = new ArrayList<>();
            for (Long accId : route.getAccumulationSequence()) {
                Accumulation acc = context.getAccumulationById(accId);
                if (acc != null) {
                    accumulations.add(acc);
                }
            }
            
            // 使用新中转站重新计算TSP
            TSPSolverManager tspSolver = new TSPSolverManager();
            RouteResult newRoute = tspSolver.solveRoute(
                toDepot, accumulations, context.getTimeMatrix(), 
                route.getRouteId().intValue() % 1000);
            
            // 更新路线信息
            route.setAccumulationSequence(newRoute.getAccumulationSequence());
            route.setTotalWorkTime(newRoute.getTotalWorkTime());
            route.setPolyline(newRoute.getPolyline());
            route.setRouteName(generateNewRouteName(toDepot, route.getRouteId().intValue() % 1000));
            
            // 添加到新中转站
            List<RouteResult> toRoutes = context.getOptimizedRoutes().computeIfAbsent(
                toDepot.getTransitDepotId(), k -> new ArrayList<>());
            toRoutes.add(route);
            
            return true;
            
        } catch (Exception e) {
            log.warn("中转站间路线转移失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 在中转站间转移部分聚集区
     */
    private int tryTransferAccumulationsBetweenDepots(TransitDepot fromDepot, TransitDepot toDepot, 
                                                     AlgorithmContext context) {
        
        List<RouteResult> fromRoutes = context.getOptimizedRoutes().get(fromDepot.getTransitDepotId());
        List<RouteResult> toRoutes = context.getOptimizedRoutes().get(toDepot.getTransitDepotId());
        
        if (fromRoutes == null || toRoutes == null || fromRoutes.isEmpty() || toRoutes.isEmpty()) {
            return 0;
        }
        
        // 找到最重的from路线和最轻的to路线
        RouteResult heaviestFromRoute = fromRoutes.stream()
                .max(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                .orElse(null);
        RouteResult lightestToRoute = toRoutes.stream()
                .min(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                .orElse(null);
                
        if (heaviestFromRoute != null && lightestToRoute != null) {
            // 尝试转移边界聚集区
            if (tryTransferBoundaryAccumulation(heaviestFromRoute, lightestToRoute, fromDepot, toDepot, context)) {
                return 1;
            }
        }
        
        return 0;
    }
    
    /**
     * 转移边界聚集区（跨中转站）
     */
    private boolean tryTransferBoundaryAccumulation(RouteResult fromRoute, RouteResult toRoute,
                                                   TransitDepot fromDepot, TransitDepot toDepot, 
                                                   AlgorithmContext context) {
        
        List<Long> fromSequence = fromRoute.getAccumulationSequence();
        if (fromSequence.isEmpty()) return false;
        
        // 选择距离目标中转站最近的边界聚集区
        Long bestCandidate = null;
        double minDistanceToTarget = Double.MAX_VALUE;
        
        int[] candidateIndices = {0, fromSequence.size() - 1};
        for (int index : candidateIndices) {
            Long accId = fromSequence.get(index);
            Accumulation acc = context.getAccumulationById(accId);
            
            if (acc != null) {
                double distanceToTarget = calculateEuclideanDistance(
                    acc.getCoordinate(), toDepot.getCoordinate());
                    
                if (distanceToTarget < minDistanceToTarget) {
                    minDistanceToTarget = distanceToTarget;
                    bestCandidate = accId;
                }
            }
        }
        
        if (bestCandidate != null) {
            // 执行跨中转站转移
            List<Long> newFromSequence = new ArrayList<>(fromSequence);
            List<Long> newToSequence = new ArrayList<>(toRoute.getAccumulationSequence());
            
            newFromSequence.remove(bestCandidate);
            newToSequence.add(bestCandidate);
            
            // 更新序列
            fromRoute.setAccumulationSequence(newFromSequence);
            toRoute.setAccumulationSequence(newToSequence);
            
            // 重新计算两条路线的TSP和时间
            updateRouteWorkTime(fromRoute, context);
            updateRouteWorkTime(toRoute, context);
            
            log.debug("跨中转站转移聚集区 {} 从 {} 到 {}，距离: {:.2f}km", 
                     bestCandidate, fromDepot.getTransitDepotName(), 
                     toDepot.getTransitDepotName(), minDistanceToTarget);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 计算欧几里得距离
     */
    private double calculateEuclideanDistance(CoordinatePoint p1, CoordinatePoint p2) {
        double dLat = Math.toRadians(p2.getLatitude() - p1.getLatitude());
        double dLon = Math.toRadians(p2.getLongitude() - p1.getLongitude());
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(p1.getLatitude())) * Math.cos(Math.toRadians(p2.getLatitude())) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return 6371.0 * c; // 地球半径
    }
    
    /**
     * 生成新的路线名称
     */
    private String generateNewRouteName(TransitDepot depot, int routeNumber) {
        return depot.getTransitDepotName() + "-路线" + routeNumber;
    }
} 