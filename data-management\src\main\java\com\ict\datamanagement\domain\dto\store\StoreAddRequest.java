package com.ict.datamanagement.domain.dto.store;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@ApiModel("商铺添加表单")
@Data
public class StoreAddRequest  {

    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty(value = "客户编码", dataType = "String" ,required = true)
    private String customerCode;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称", dataType = "String")
    private String storeName;

    /**
     * 店铺经营地址
     */
    @NotBlank(message = "地址不能为空")
    @ApiModelProperty(value = "地址", dataType = "String",required = true)
    private String storeAddress;

    /**
     * 店铺经度
     */
    @Min(value = 1,message = "经度不能为0")
    @ApiModelProperty(value = "店铺经度", dataType = "Double",required = true)
    private Double longitude;

    /**
     * 店铺纬度
     */
    @Min(value = 1,message = "纬度不能为0")
    @ApiModelProperty(value = "店铺纬度", dataType = "Double",required = true)
    private Double latitude;

    /**
     * 商圈类型
     */
    @ApiModelProperty(value = "商圈类型", dataType = "String")
    private String type;

    /**
     * 订货周期
     @ApiModelProperty(value = "订货周期", dataType = "String")
     private String orderCycle;*/

    /**
     * 店铺所属行政区
     @ApiModelProperty(value = "店铺所属行政区", dataType = "String")
     private String district;*/

    /**
     * 店铺所属大区
     */
    @NotBlank(message = "所属大区不能为空")
    @ApiModelProperty(value = "所属大区", dataType = "String",required = true)
    private String areaName;

    @NotBlank(message = "路线名称不能为空")
    @ApiModelProperty(value = "路线名称", dataType = "String",required = true)
    private String routeName;


    /**
     * 店铺联系人名称
     */
    @ApiModelProperty(value = "客户名称", dataType = "String")
    private String contactName;

    /**
     * 店铺联系人电话号码
     */
    @ApiModelProperty(value = "订货电话", dataType = "String")
    private String contactPhone;

   /* *
     * 客户专员id

    @ApiModelProperty(value = "客户专员id", dataType = "Long")
    private Long customerManagerId;*/


    /**
     * 大区id
     */
    @Min(value = 1,message = "纬度不能为0")
    @ApiModelProperty(value = "大区id", dataType = "Long",required = true)
    private Long areaId;

    /**
     * 客户专员名称
     */
    @NotBlank(message = "客户专员名称不能为空")
    @ApiModelProperty(value = "客户专员名称", dataType = "String",required = true)
    private String customerManagerName;
    /**
     * 客户档位
     */
    @NotBlank(message = "客户档位不能为空")
    @ApiModelProperty(value = "客户档位", dataType = "String",required = true)
    private String gear;
    /**
     * 商铺类型归属
     */
    @NotBlank(message = "商铺类型不能为空")
    @ApiModelProperty(value = "商铺类型", dataType = "String",required = true)
    private String locationType;

    @NotBlank(message = "客户状态不能为空")
    @ApiModelProperty(value = "客户状态", dataType = "String",required = true)
    private String status;
    //负责人
    @ApiModelProperty(value = "负责人", dataType = "String")
    private String head;
    //备用收货电话
    @ApiModelProperty(value = "备用收货电话", dataType = "String")
    private String sparePhone;
    //收货电话
    @ApiModelProperty(value = "收货电话", dataType = "String")
    private String receivingPhone;
    //返销周期
    @ApiModelProperty(value = "返销周期", dataType = "String")
    private String resaleCycle;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}
