package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 聚集区数据结构（算法专用）
 * 专门为路径规划算法设计的聚集区数据结构，只包含算法必需的字段
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Accumulation {
    
    /**
     * 聚集区ID
     */
    private Long accumulationId;
    
    /**
     * 聚集区名称
     */
    private String accumulationName;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 所属中转站ID
     */
    private Long transitDepotId;
    
    /**
     * 聚集区配送时间（分钟）- 点权
     * 这是算法计算路线总时间的重要参数
     */
    private Double deliveryTime;
    
    /**
     * 获取坐标点对象
     * 
     * @return 坐标点对象
     */
    public CoordinatePoint getCoordinate() {
        return new CoordinatePoint(longitude, latitude);
    }
    
    /**
     * 设置坐标信息
     * 
     * @param coordinate 坐标点对象
     */
    public void setCoordinate(CoordinatePoint coordinate) {
        if (coordinate != null && coordinate.isValid()) {
            this.longitude = coordinate.getLongitude();
            this.latitude = coordinate.getLatitude();
        }
    }
    
    /**
     * 检查聚集区数据是否有效
     * 
     * @return 数据是否有效
     */
    public boolean isValid() {
        return accumulationId != null &&
               accumulationName != null && !accumulationName.trim().isEmpty() &&
               longitude != null && latitude != null &&
               longitude >= -180 && longitude <= 180 &&
               latitude >= -90 && latitude <= 90 &&
               transitDepotId != null &&
               deliveryTime != null && deliveryTime > 0;
    }
    
    /**
     * 获取聚集区的唯一标识字符串
     * 
     * @return 唯一标识字符串
     */
    public String getUniqueKey() {
        return String.format("acc_%d", accumulationId);
    }
    
    /**
     * 计算到另一个聚集区的直线距离（米）
     * 
     * @param other 目标聚集区
     * @return 距离（米），如果坐标无效则返回-1
     */
    public double distanceTo(Accumulation other) {
        if (other == null) {
            return -1;
        }
        return this.getCoordinate().distanceTo(other.getCoordinate());
    }
    
    /**
     * 从业务实体转换为算法实体
     * 
     * @param businessAccumulation 业务聚集区实体
     * @return 算法聚集区实体
     */
    public static Accumulation fromBusinessEntity(com.ict.ycwl.pathcalculate.pojo.Accumulation businessAccumulation) {
        if (businessAccumulation == null) {
            return null;
        }
        
        return Accumulation.builder()
                .accumulationId(businessAccumulation.getAccumulationId())
                .accumulationName(businessAccumulation.getAccumulationName())
                .longitude(businessAccumulation.getLongitude())
                .latitude(businessAccumulation.getLatitude())
                .transitDepotId(businessAccumulation.getTransitDepotId())
                .deliveryTime(15.0) // NOTE: 默认配送时间，实际应从配置或计算得出
                .build();
    }
    
    /**
     * 复制聚集区对象
     * 
     * @return 复制的聚集区对象
     */
    public Accumulation copy() {
        return Accumulation.builder()
                .accumulationId(this.accumulationId)
                .accumulationName(this.accumulationName)
                .longitude(this.longitude)
                .latitude(this.latitude)
                .transitDepotId(this.transitDepotId)
                .deliveryTime(this.deliveryTime)
                .build();
    }
} 