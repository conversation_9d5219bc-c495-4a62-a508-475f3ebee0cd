package com.ict.ycwl.clustercalculate.utlis.dbDataSourceUtils;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: jiangjs
 * @description: 设置数据源
 * @date: 2023/7/27 11:34
 **/
@Configuration
public class DateSourceConfig {

    @ConfigurationProperties("spring.datasource.druid.master")
    @Bean
    public DataSource masterDataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    @Bean
    @ConfigurationProperties("spring.datasource.druid.slave1")
    public DataSource slave1DataSource(){
        return DruidDataSourceBuilder.create().build();
    }


    @Bean
    @ConfigurationProperties("spring.datasource.druid.slave2")
    public DataSource slave2DataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    @Bean
    @ConfigurationProperties("spring.datasource.druid.slave3")
    public DataSource slave3DataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    /*@Bean(name = "dynamicDataSource")
    @Primary
    public DynamicDataSource createDynamicDataSource(){
        Map<Object,Object> dataSourceMap = new HashMap<>();
        DataSource defaultDataSource = masterDataSource();
        dataSourceMap.put("master",defaultDataSource);
        dataSourceMap.put("slave1",slave1DataSource());
        dataSourceMap.put("slave2",slave2DataSource());
        dataSourceMap.put("slave3",slave3DataSource());
        return new DynamicDataSource(defaultDataSource,dataSourceMap);
    }*/

    @Bean(name = "dynamicDataSource")
    @Primary
    public DynamicDataSource createDynamicDataSource(
            @Lazy DataSource masterDataSource,  // 关键修改：添加@Lazy
            @Lazy DataSource slave1DataSource,
            @Lazy DataSource slave2DataSource,
            @Lazy DataSource slave3DataSource) {

        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put("master", masterDataSource);
        dataSourceMap.put("slave1", slave1DataSource);
        dataSourceMap.put("slave2", slave2DataSource);
        dataSourceMap.put("slave3", slave3DataSource);

        return new DynamicDataSource(masterDataSource, dataSourceMap);
    }

}
