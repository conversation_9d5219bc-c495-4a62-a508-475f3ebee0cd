package com.ict.datamanagement.domain.dto.store;

import com.alibaba.excel.annotation.ExcelProperty;
import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;

@Data
public class StoreExcelFile {
    //客户编码
    @ExcelProperty("客户编码")
    private String customerCode;
    //客户名称
    @ExcelProperty("客户名称")
    private String contactName;
    //地址
    @ExcelProperty("客户地址")
    private String storeAddress;
    //负责人
    @ExcelProperty("负责人")
    private String head;
    //客户挡位
    @ExcelProperty("客户档位")
    private String gear;
    //GIS经度
    @ExcelProperty("GIS经度")
    private Double longitude;
    //GIS纬度
    @ExcelProperty("GIS维度")
    private Double latitude;
    //线路
    @ExcelProperty("线路")
    private String routeName;
    //访销周期
    @ExcelProperty("访销周期")
    private String orderCycle;
    //客户专员
    @ExcelProperty("客户专员")
    private String customerManagerName;
    //配送域
    @ExcelProperty("配送域")
    private String deliveryArea;
    //结论
    @ExcelProperty("结论")
    private String conclusion;
    //导入详情
    @ExcelProperty("导入详情")
    private String ImportDetails;
}
