package com.ict.datamanagement.filter;


import com.ict.datamanagement.util.dbDataSourceUtils.DataSourceContextHolder;
import com.ict.datamanagement.util.dbDataSourceUtils.FileUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.servlet.*;
import java.io.IOException;

@Configuration
public class CustomDataSourceFilter implements Filter {

    @Value("${jjking.dbPath}")
    private String dbPath;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOEx<PERSON>, ServletException {
       String type = FileUtil.readSingleLine(dbPath);
        System.out.println("type: " + type);
        DataSourceContextHolder.setDataSource(type);
        filterChain.doFilter(servletRequest, servletResponse);
    }

}
