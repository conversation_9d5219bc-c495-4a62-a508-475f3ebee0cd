package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 中转站数据结构（算法专用）
 * 专门为路径规划算法设计的中转站数据结构，只包含算法必需的字段
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransitDepot {
    
    /**
     * 中转站ID
     */
    private Long transitDepotId;
    
    /**
     * 中转站名称
     */
    private String transitDepotName;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 所属班组ID
     */
    private Long groupId;
    
    /**
     * 规划路线数量
     * 表示该中转站需要规划多少条配送路线
     */
    private Integer routeCount;
    
    /**
     * 获取坐标点对象
     * 
     * @return 坐标点对象
     */
    public CoordinatePoint getCoordinate() {
        return new CoordinatePoint(longitude, latitude);
    }
    
    /**
     * 设置坐标信息
     * 
     * @param coordinate 坐标点对象
     */
    public void setCoordinate(CoordinatePoint coordinate) {
        if (coordinate != null && coordinate.isValid()) {
            this.longitude = coordinate.getLongitude();
            this.latitude = coordinate.getLatitude();
        }
    }
    
    /**
     * 检查中转站数据是否有效
     * 
     * @return 数据是否有效
     */
    public boolean isValid() {
        return transitDepotId != null &&
               transitDepotName != null && !transitDepotName.trim().isEmpty() &&
               longitude != null && latitude != null &&
               longitude >= -180 && longitude <= 180 &&
               latitude >= -90 && latitude <= 90 &&
               groupId != null &&
               routeCount != null && routeCount > 0;
    }
    
    /**
     * 获取中转站的唯一标识字符串
     * 
     * @return 唯一标识字符串
     */
    public String getUniqueKey() {
        return String.format("depot_%d", transitDepotId);
    }
    
    /**
     * 计算到聚集区的直线距离（米）
     * 
     * @param accumulation 目标聚集区
     * @return 距离（米），如果坐标无效则返回-1
     */
    public double distanceTo(Accumulation accumulation) {
        if (accumulation == null) {
            return -1;
        }
        return this.getCoordinate().distanceTo(accumulation.getCoordinate());
    }
    
    /**
     * 计算到另一个中转站的直线距离（米）
     * 
     * @param other 目标中转站
     * @return 距离（米），如果坐标无效则返回-1
     */
    public double distanceTo(TransitDepot other) {
        if (other == null) {
            return -1;
        }
        return this.getCoordinate().distanceTo(other.getCoordinate());
    }
    
    /**
     * 从业务实体转换为算法实体
     * 
     * @param businessTransitDepot 业务中转站实体
     * @return 算法中转站实体
     */
    public static TransitDepot fromBusinessEntity(com.ict.ycwl.pathcalculate.pojo.TransitDepot businessTransitDepot) {
        if (businessTransitDepot == null) {
            return null;
        }
        
        // 处理坐标类型转换（业务实体中是String类型）
        Double lng = null;
        Double lat = null;
        try {
            if (businessTransitDepot.getLongitude() != null) {
                lng = Double.parseDouble(businessTransitDepot.getLongitude());
            }
            if (businessTransitDepot.getLatitude() != null) {
                lat = Double.parseDouble(businessTransitDepot.getLatitude());
            }
        } catch (NumberFormatException e) {
            // 坐标解析失败，返回null
            return null;
        }
        
        return TransitDepot.builder()
                .transitDepotId(businessTransitDepot.getTransitDepotId())
                .transitDepotName(businessTransitDepot.getTransitDepotName())
                .longitude(lng)
                .latitude(lat)
                .groupId(businessTransitDepot.getGroupId())
                .routeCount(3) // NOTE: 默认路线数量，实际应从配置或计算得出
                .build();
    }
    
    /**
     * 复制中转站对象
     * 
     * @return 复制的中转站对象
     */
    public TransitDepot copy() {
        return TransitDepot.builder()
                .transitDepotId(this.transitDepotId)
                .transitDepotName(this.transitDepotName)
                .longitude(this.longitude)
                .latitude(this.latitude)
                .groupId(this.groupId)
                .routeCount(this.routeCount)
                .build();
    }
    
    /**
     * 检查是否可以服务指定的聚集区
     * 
     * @param accumulation 聚集区
     * @return 是否可以服务
     */
    public boolean canServe(Accumulation accumulation) {
        if (accumulation == null) {
            return false;
        }
        
        // 1. 检查聚集区是否归属于该中转站
        boolean belongsToDepot = this.transitDepotId.equals(accumulation.getTransitDepotId());
        
        // OPTIMIZE: 可以加入距离限制等其他约束条件
        
        return belongsToDepot;
    }
} 