package com.ict.datamanagement.domain.dto.delivery;

import com.ict.datamanagement.domain.dto.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("配送域列表表单")
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryListRequest extends PageRequest {
    @ApiModelProperty(value = "对接中转站",dataType = "String")
    //对接中转站
    private String transitDepotName;

    //配送域
    @ApiModelProperty(value = "配送域",dataType = "String")
    private String deliveryName;

    //配送类型
    @ApiModelProperty(value = "配送类型",dataType = "String")
    private String deliveryType;

    //所属班组
    @ApiModelProperty(value = "所属班组",dataType = "String")
    private String teamName;

    //行政区
    @ApiModelProperty(value = "行政区",dataType = "String")
    private String areaName;

}
