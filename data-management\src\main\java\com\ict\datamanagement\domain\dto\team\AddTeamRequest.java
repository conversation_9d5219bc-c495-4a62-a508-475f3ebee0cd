package com.ict.datamanagement.domain.dto.team;

import com.ict.datamanagement.domain.vo.teamVO.AddTeamInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("添加班组表单")
@AllArgsConstructor
@NoArgsConstructor
public class AddTeamRequest extends AddTeamInfoVO {
    @ApiModelProperty(value = "车辆总数",dataType = "int")
    //车辆总数
    private int carSum;
    //路径总数
    @ApiModelProperty(value = "路线总数",dataType = "int")
    private int routeSum;
}
