package com.ict.datamanagement.domain.dto.transitDepot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("添加中转站表单")
@AllArgsConstructor
@NoArgsConstructor
public class AddTransitDepot {
    @ApiModelProperty(value = "中转站名称",dataType = "String")
    private String transitDepotName;

    @ApiModelProperty(value = "所属班组",dataType = "String")
    private String teamName;

    @ApiModelProperty(value = "启用状态",dataType = "String")
    private String status;

    @ApiModelProperty(value = "配送类型",dataType = "String")
    private String deliveryType;

    @ApiModelProperty(value = "对接配送域",dataType = "list")
    private List<String> deliveryName;

    @ApiModelProperty(value = "经度",dataType = "String")
    private String longitude;

    @ApiModelProperty(value = "纬度",dataType = "String")
    private String latitude;

}
