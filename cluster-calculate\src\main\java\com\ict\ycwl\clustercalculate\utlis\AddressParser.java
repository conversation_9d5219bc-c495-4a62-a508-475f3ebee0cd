/*
package com.ict.ycwl.clustercalculate.utlis;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.net.URLEncoder;


public class AddressParser {
    private static final String AK = "你的高德AK";
    private static final String URL = "https://restapi.amap.com/v3/geocode/geo";

    public static Address parse(String address) throws Exception {
        // 构造请求参数
        String queryParam = "address=" + URLEncoder.encode(address, "UTF-8")
                        + "&key=" + AK 
                        + "&output=json";

        // 发送HTTP请求
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(URL + "?" + queryParam)
                .build();

        try (Response response = client.newCall(request).execute()) {
            JsonNode root = JacksonUtils.parse(response.body().string());
            if (root.path("status").asInt() != 1) {
                throw new RuntimeException("解析失败：" + root.path("info").asText());
            }

            // 解析返回结果
            JsonNode location = root.path("geocodes").get(0);
            return Address.builder()
                    .province(location.path("province").asText())
                    .city(location.path("city").asText())
                    .district(location.path("district").asText())
                    .town(location.path("town").asText())
                    .village(location.path("addressComponent").path("village").asText())
                    .build();
        }
    }

    // 地址实体类（示例）
    @Data
    public static class Address {
        private String province;
        private String city;
        private String district;
        private String town;
        private String village;
    }
}
*/
