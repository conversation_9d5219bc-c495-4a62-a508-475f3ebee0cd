package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * OR-Tools引导加载器
 * 在应用启动时预先修复JNI问题，避免后续初始化失败
 */
@Slf4j
public class ORToolsBootstrap {
    
    private static final AtomicBoolean initialized = new AtomicBoolean(false);
    private static final AtomicBoolean available = new AtomicBoolean(false);
    
    /**
     * 静态代码块：在类加载时立即尝试初始化
     */
    static {
        initializeORTools();
    }
    
    /**
     * 预初始化OR-Tools
     * 在任何其他组件使用之前修复JNI问题
     */
    public static synchronized boolean initializeORTools() {
        if (initialized.get()) {
            return available.get();
        }
        
        log.info("开始OR-Tools预初始化...");
        
        try {
            // 阶段1：预检查和环境准备
            if (!performPreCheck()) {
                log.warn("OR-Tools预检查失败");
                return attemptAdvancedRecovery();
            }
            
            // 阶段2：深度JNI修复
            performDeepJNIFix();
            
            // 阶段3：强制类重置
            forceClassReset();
            
            // 阶段4：分步加载和验证
            boolean success = stepByStepLoading();
            
            if (!success) {
                log.warn("常规初始化失败，尝试高级恢复...");
                return attemptAdvancedRecovery();
            }
            
            initialized.set(true);
            available.set(true);
            log.info("🎉 OR-Tools预初始化成功！");
            return true;
            
        } catch (Exception e) {
            log.error("OR-Tools预初始化过程中发生异常: {}", e.getMessage());
            return attemptAdvancedRecovery();
        }
    }
    
    /**
     * 尝试高级恢复
     */
    private static boolean attemptAdvancedRecovery() {
        log.info("开始高级JNI恢复尝试...");
        
        try {
            // 检查是否需要恢复
            if (!AdvancedJNIRecovery.needsRecovery()) {
                log.info("✅ 检测到OR-Tools实际可用，无需恢复");
                initialized.set(true);
                available.set(true);
                return true;
            }
            
            // 尝试高级恢复
            boolean recoverySuccess = AdvancedJNIRecovery.attemptClassRecovery();
            
            if (recoverySuccess) {
                // 恢复成功后重新验证
                boolean verifySuccess = stepByStepLoading();
                initialized.set(true);
                available.set(verifySuccess);
                
                if (verifySuccess) {
                    log.info("🎉 高级JNI恢复成功！OR-Tools现已可用");
                    return true;
                } else {
                    log.warn("❌ 高级恢复后验证失败");
                    return false;
                }
            } else {
                log.warn("❌ 高级JNI恢复失败");
                initialized.set(true);
                available.set(false);
                return false;
            }
            
        } catch (Exception e) {
            log.error("高级恢复过程中发生异常: {}", e.getMessage());
            initialized.set(true);
            available.set(false);
            return false;
        }
    }
    
    /**
     * 预检查
     */
    private static boolean performPreCheck() {
        try {
            // 检查关键类是否存在
            Class.forName("com.google.ortools.Loader");
            Class.forName("com.google.ortools.constraintsolver.RoutingModel");
            Class.forName("com.google.ortools.constraintsolver.RoutingIndexManager");
            
            log.debug("✅ OR-Tools类预检查通过");
            return true;
            
        } catch (ClassNotFoundException e) {
            log.error("❌ OR-Tools类预检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 深度JNI修复
     */
    private static void performDeepJNIFix() {
        log.debug("执行深度JNI修复...");
        
        try {
            // 1. 清理可能的缓存状态
            System.gc();
            System.runFinalization();
            Thread.sleep(100);
            
            // 2. 重置系统库路径
            resetLibraryPath();
            
            // 3. 清理ClassLoader缓存
            clearClassLoaderCache();
            
            // 4. 设置JNI相关系统属性
            setJNISystemProperties();
            
            log.debug("✅ 深度JNI修复完成");
            
        } catch (Exception e) {
            log.warn("深度JNI修复过程中出现异常（继续）: {}", e.getMessage());
        }
    }
    
    /**
     * 重置系统库路径
     */
    private static void resetLibraryPath() {
        try {
            String javaLibraryPath = System.getProperty("java.library.path");
            String tempDir = System.getProperty("java.io.tmpdir");
            String userDir = System.getProperty("user.dir");
            
            // 构建增强的库路径
            String enhancedPath = javaLibraryPath + 
                java.io.File.pathSeparator + tempDir + 
                java.io.File.pathSeparator + userDir;
            
            System.setProperty("java.library.path", enhancedPath);
            
            // 强制JVM重新读取library path
            Field fieldSysPath = ClassLoader.class.getDeclaredField("sys_paths");
            fieldSysPath.setAccessible(true);
            fieldSysPath.set(null, null);
            
            log.debug("✅ 系统库路径已重置");
            
        } catch (Exception e) {
            log.debug("重置系统库路径失败（忽略）: {}", e.getMessage());
        }
    }
    
    /**
     * 清理ClassLoader缓存
     */
    private static void clearClassLoaderCache() {
        try {
            // 尝试清理可能的类加载器缓存
            ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
            
            // 如果是URLClassLoader，尝试清理缓存
            if (systemClassLoader instanceof java.net.URLClassLoader) {
                // 这里可以添加特定的缓存清理逻辑
                log.debug("检测到URLClassLoader");
            }
            
            // 强制垃圾回收
            System.gc();
            System.runFinalization();
            
            log.debug("✅ ClassLoader缓存清理完成");
            
        } catch (Exception e) {
            log.debug("ClassLoader缓存清理失败（忽略）: {}", e.getMessage());
        }
    }
    
    /**
     * 设置JNI相关系统属性
     */
    private static void setJNISystemProperties() {
        try {
            // 设置可能有助于JNI加载的系统属性
            System.setProperty("java.awt.headless", "true");
            System.setProperty("file.encoding", "UTF-8");
            
            // 如果是Windows，设置特定属性
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("windows")) {
                System.setProperty("sun.jnu.encoding", "UTF-8");
            }
            
            log.debug("✅ JNI系统属性设置完成");
            
        } catch (Exception e) {
            log.debug("JNI系统属性设置失败（忽略）: {}", e.getMessage());
        }
    }
    
    /**
     * 强制类重置
     */
    private static void forceClassReset() {
        log.debug("尝试强制类重置...");
        
        try {
            // 尝试获取并重置可能已经失败的类
            String[] problematicClasses = {
                "com.google.ortools.constraintsolver.mainJNI",
                "com.google.ortools.constraintsolver.RoutingModel",
                "com.google.ortools.constraintsolver.RoutingIndexManager"
            };
            
            for (String className : problematicClasses) {
                try {
                    Class<?> clazz = Class.forName(className, false, ClassLoader.getSystemClassLoader());
                    
                    // 尝试重置类的初始化状态（如果可能）
                    Field[] fields = clazz.getDeclaredFields();
                    for (Field field : fields) {
                        if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                            field.setAccessible(true);
                            // 这里不实际重置，只是访问确保类状态正确
                        }
                    }
                    
                    log.debug("✅ 类 {} 重置检查完成", className);
                    
                } catch (Exception e) {
                    log.debug("类 {} 重置失败（可能是正常的）: {}", className, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.debug("强制类重置过程中出现异常（继续）: {}", e.getMessage());
        }
    }
    
    /**
     * 分步加载和验证
     */
    private static boolean stepByStepLoading() {
        log.debug("开始分步加载OR-Tools...");
        
        try {
            // 步骤1：加载原生库
            log.debug("步骤1: 加载原生库");
            com.google.ortools.Loader.loadNativeLibraries();
            log.debug("✅ 原生库加载成功");
            
            // 步骤2：创建RoutingIndexManager
            log.debug("步骤2: 创建RoutingIndexManager");
            com.google.ortools.constraintsolver.RoutingIndexManager manager = 
                new com.google.ortools.constraintsolver.RoutingIndexManager(2, 1, 0);
            log.debug("✅ RoutingIndexManager创建成功");
            
            // 步骤3：创建RoutingModel
            log.debug("步骤3: 创建RoutingModel");
            com.google.ortools.constraintsolver.RoutingModel model = 
                new com.google.ortools.constraintsolver.RoutingModel(manager);
            log.debug("✅ RoutingModel创建成功");
            
            // 步骤4：基本求解测试
            log.debug("步骤4: 基本求解测试");
            com.google.ortools.constraintsolver.Assignment solution = model.solve();
            log.debug("✅ 基本求解测试成功，解状态: {}", solution != null ? "有解" : "无解");
            
            return true;
            
        } catch (UnsatisfiedLinkError e) {
            log.error("❌ JNI库加载失败: {}", e.getMessage());
            return false;
        } catch (NoClassDefFoundError e) {
            log.error("❌ 类定义找不到: {}", e.getMessage());
            return false;
        } catch (ExceptionInInitializerError e) {
            log.error("❌ 类初始化失败: {}", e.getCause() != null ? e.getCause().getMessage() : e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("❌ 分步加载失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查OR-Tools是否可用
     */
    public static boolean isAvailable() {
        if (!initialized.get()) {
            return initializeORTools();
        }
        return available.get();
    }
    
    /**
     * 强制重新初始化
     */
    public static synchronized boolean forceReInitialize() {
        log.info("强制重新初始化OR-Tools...");
        initialized.set(false);
        available.set(false);
        return initializeORTools();
    }
    
    /**
     * 获取初始化状态信息
     */
    public static String getStatusInfo() {
        return String.format("OR-Tools状态: initialized=%s, available=%s", 
                           initialized.get(), available.get());
    }
}