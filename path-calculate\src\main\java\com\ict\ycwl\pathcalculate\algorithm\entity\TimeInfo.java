package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 时间信息数据结构
 * 用于表示两点间的时间和距离信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TimeInfo {
    
    /**
     * 行驶时间（分钟）
     */
    private Double travelTime;
    
    /**
     * 距离（千米）
     */
    private Double distance;
    
    /**
     * 起点经度（仅用于兼容性，实际使用中可选）
     */
    private Double fromLongitude;
    
    /**
     * 起点纬度（仅用于兼容性，实际使用中可选）
     */
    private Double fromLatitude;
    
    /**
     * 终点经度（仅用于兼容性，实际使用中可选）
     */
    private Double toLongitude;
    
    /**
     * 终点纬度（仅用于兼容性，实际使用中可选）
     */
    private Double toLatitude;
    
    /**
     * 检查时间信息是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return travelTime != null && travelTime >= 0 &&
               distance != null && distance >= 0;
    }
    
    /**
     * 获取平均速度（千米/小时）
     * 
     * @return 平均速度，如果时间为0则返回0
     */
    public double getAverageSpeed() {
        if (travelTime == null || travelTime <= 0 || distance == null) {
            return 0;
        }
        return distance / (travelTime / 60.0); // 转换为小时
    }
} 