package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * VRP约束验证结果
 * 
 * 包含约束配置的验证结果、错误信息和警告信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPConstraintValidationResult {
    
    /**
     * 验证是否通过
     */
    private Boolean isValid;
    
    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * 警告信息列表
     */
    @Builder.Default
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 验证详情描述
     */
    private String validationDetails;
    
    /**
     * 检查是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 检查是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings != null ? warnings.size() : 0;
    }
    
    /**
     * 获取完整的验证摘要
     * 
     * @return 验证摘要
     */
    public String getValidationSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (isValid) {
            summary.append("✅ 约束配置验证通过");
        } else {
            summary.append("❌ 约束配置验证失败");
        }
        
        if (hasErrors()) {
            summary.append(String.format(" - %d个错误", getErrorCount()));
        }
        
        if (hasWarnings()) {
            summary.append(String.format(" - %d个警告", getWarningCount()));
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return getValidationSummary();
    }
}