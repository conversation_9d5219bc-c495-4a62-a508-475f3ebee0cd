package com.ict.datamanagement.domain.vo.ststemParameter;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("second_transit")
public class SecondTransitVO {
    //记录id
    @ApiModelProperty(value = "该条记录id",dataType = "int")
    private int id;

    //二次中转站id
    @ApiModelProperty(value = "二次中转站名称",dataType = "String")
    private String secondTransitName;

    //二次中转时长
    @ApiModelProperty(value = "二次中转时长",dataType = "double")
    private double transitTime;
}
