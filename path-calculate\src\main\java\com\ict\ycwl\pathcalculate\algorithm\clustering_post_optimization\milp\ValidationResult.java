package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * MILP问题验证结果
 * 
 * 包含MILP问题定义完整性和正确性的验证结果
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ValidationResult {
    
    /**
     * 验证是否通过
     */
    private boolean isValid;
    
    /**
     * 验证错误列表
     */
    private List<String> errors;
    
    /**
     * 验证警告列表
     */
    private List<String> warnings;
    
    /**
     * 验证信息列表
     */
    private List<String> infos;
    
    /**
     * 验证开始时间
     */
    private long validationStartTime;
    
    /**
     * 验证耗时（毫秒）
     */
    private long validationTimeMs;
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    /**
     * 是否有信息
     */
    public boolean hasInfos() {
        return infos != null && !infos.isEmpty();
    }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    /**
     * 获取警告数量
     */
    public int getWarningCount() {
        return warnings != null ? warnings.size() : 0;
    }
    
    /**
     * 获取信息数量
     */
    public int getInfoCount() {
        return infos != null ? infos.size() : 0;
    }
    
    /**
     * 生成验证结果摘要
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== MILP问题验证结果 ===\n");
        summary.append(String.format("验证状态: %s\n", isValid ? "通过" : "失败"));
        
        if (validationTimeMs > 0) {
            summary.append(String.format("验证耗时: %d ms\n", validationTimeMs));
        }
        
        summary.append(String.format("错误数: %d, 警告数: %d, 信息数: %d\n", 
            getErrorCount(), getWarningCount(), getInfoCount()));
        
        if (hasErrors()) {
            summary.append("\n❌ 错误:\n");
            for (int i = 0; i < errors.size(); i++) {
                summary.append(String.format("  %d. %s\n", i + 1, errors.get(i)));
            }
        }
        
        if (hasWarnings()) {
            summary.append("\n⚠️ 警告:\n");
            for (int i = 0; i < warnings.size(); i++) {
                summary.append(String.format("  %d. %s\n", i + 1, warnings.get(i)));
            }
        }
        
        if (hasInfos()) {
            summary.append("\nℹ️ 信息:\n");
            for (int i = 0; i < infos.size(); i++) {
                summary.append(String.format("  %d. %s\n", i + 1, infos.get(i)));
            }
        }
        
        return summary.toString();
    }
}