package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain.*;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.solver.ClusteringSolverConfig;
import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import org.optaplanner.core.api.solver.Solver;
import org.optaplanner.core.api.solver.SolverFactory;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OptaPlanner约束优化器主控制器
 * 
 * 基于业界最佳实践的OptaPlanner集成，提供完整的聚类约束优化功能：
 * - 数据模型转换（原始聚类 ↔ OptaPlanner领域模型）
 * - 求解器配置和执行
 * - 结果分析和统计报告
 * - 异常处理和降级机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class OptaPlannerConstraintOptimizer {
    
    @Autowired
    private ClusteringSolverConfig solverConfig;
    
    // 优化统计
    private OptimizationStatistics lastOptimizationStats;
    
    /**
     * 使用OptaPlanner执行约束优化
     * 
     * @param depot 中转站信息
     * @param originalClusters 原始聚类结果
     * @param timeMatrix 时间矩阵
     * @param parameters 优化参数
     * @return 优化后的聚类结果
     */
    public List<List<Accumulation>> optimizeWithConstraints(
        TransitDepot depot,
        List<List<Accumulation>> originalClusters,
        Map<String, TimeInfo> timeMatrix,
        OptimizationParameters parameters
    ) {
        
        String sessionId = generateSessionId();
        Instant startTime = Instant.now();
        
        try {
            log.info("🚀 开始OptaPlanner约束优化，会话: {}, 中转站: {}, 聚类数: {}", 
                sessionId, depot.getTransitDepotName(), originalClusters.size());
            
            // 第1步：数据验证
            if (!validateInputData(depot, originalClusters, timeMatrix)) {
                log.error("❌ 输入数据验证失败，取消优化");
                return originalClusters;
            }
            
            // 第2步：转换为OptaPlanner数据模型
            ClusteringOptimizationSolution problem = convertToOptaPlannerModel(
                depot, originalClusters, timeMatrix, parameters);
            
            if (problem == null) {
                log.error("❌ 数据模型转换失败，取消优化");
                return originalClusters;
            }
            
            log.info("📊 OptaPlanner问题规模: {}", problem.getProblemSizeInfo());
            
            // 第3步：配置和创建求解器
            Solver<ClusteringOptimizationSolution> solver = createSolver(parameters, sessionId);
            
            if (solver == null) {
                log.error("❌ 求解器创建失败，取消优化");
                return originalClusters;
            }
            
            // 第4步：执行优化
            ClusteringOptimizationSolution solution = executeSolver(solver, problem, sessionId);
            
            if (solution == null) {
                log.error("❌ 求解器执行失败，返回原始聚类");
                return originalClusters;
            }
            
            // 第5步：分析优化结果
            OptimizationResult result = analyzeOptimizationResult(
                originalClusters, solution, startTime, sessionId);
            
            // 第6步：转换回原始数据格式
            List<List<Accumulation>> optimizedClusters = convertFromOptaPlannerModel(solution);
            
            // 第7步：验证结果完整性
            if (!validateResultIntegrity(originalClusters, optimizedClusters)) {
                log.error("❌ 结果完整性验证失败，返回原始聚类");
                return originalClusters;
            }
            
            // 记录成功统计
            recordOptimizationStatistics(result, true);
            
            log.info("✅ OptaPlanner优化完成，会话: {}, 耗时: {}ms, 得分改进: {} → {}", 
                sessionId, 
                Duration.between(startTime, Instant.now()).toMillis(),
                result.getInitialScore(),
                result.getFinalScore());
            
            return optimizedClusters;
            
        } catch (Exception e) {
            log.error("❌ OptaPlanner优化异常，会话: {}", sessionId, e);
            recordOptimizationStatistics(null, false);
            return originalClusters;
        }
    }
    
    /**
     * 快速优化（使用预设的快速参数）
     */
    public List<List<Accumulation>> quickOptimize(
        TransitDepot depot,
        List<List<Accumulation>> originalClusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        OptimizationParameters fastParams = OptimizationParameters.createFast();
        return optimizeWithConstraints(depot, originalClusters, timeMatrix, fastParams);
    }
    
    /**
     * 深度优化（使用预设的深度参数）
     */
    public List<List<Accumulation>> deepOptimize(
        TransitDepot depot,
        List<List<Accumulation>> originalClusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        OptimizationParameters deepParams = OptimizationParameters.createDeep();
        return optimizeWithConstraints(depot, originalClusters, timeMatrix, deepParams);
    }
    
    /**
     * 验证输入数据
     */
    private boolean validateInputData(
        TransitDepot depot,
        List<List<Accumulation>> originalClusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        if (depot == null) {
            log.error("❌ 中转站信息为空");
            return false;
        }
        
        if (originalClusters == null || originalClusters.isEmpty()) {
            log.error("❌ 原始聚类为空");
            return false;
        }
        
        if (timeMatrix == null || timeMatrix.isEmpty()) {
            log.error("❌ 时间矩阵为空");
            return false;
        }
        
        // 验证聚类内容
        int totalAccumulations = 0;
        for (List<Accumulation> cluster : originalClusters) {
            if (cluster != null) {
                totalAccumulations += cluster.size();
            }
        }
        
        if (totalAccumulations == 0) {
            log.error("❌ 聚类中没有聚集区");
            return false;
        }
        
        log.debug("✅ 输入数据验证通过，总聚集区数: {}", totalAccumulations);
        return true;
    }
    
    /**
     * 转换为OptaPlanner数据模型
     */
    private ClusteringOptimizationSolution convertToOptaPlannerModel(
        TransitDepot depot,
        List<List<Accumulation>> originalClusters,
        Map<String, TimeInfo> timeMatrix,
        OptimizationParameters parameters
    ) {
        
        try {
            log.debug("🔄 转换为OptaPlanner数据模型");
            
            // 创建聚类实体
            List<Cluster> clusters = createClusterEntities(originalClusters);
            
            // 创建聚集区分配实体
            List<AccumulationAssignment> assignments = createAccumulationAssignments(originalClusters, clusters);
            
            // 创建优化解决方案
            ClusteringOptimizationSolution solution = new ClusteringOptimizationSolution(
                depot, timeMatrix, clusters, assignments);
            
            // 设置配置
            ConstraintWeights weights = determineConstraintWeights(originalClusters);
            solution.setConstraintWeights(weights);
            solution.setOptimizationParameters(parameters);
            
            log.debug("✅ 数据模型转换完成，聚类数: {}, 分配数: {}", clusters.size(), assignments.size());
            return solution;
            
        } catch (Exception e) {
            log.error("❌ 数据模型转换异常", e);
            return null;
        }
    }
    
    /**
     * 创建聚类实体
     */
    private List<Cluster> createClusterEntities(List<List<Accumulation>> originalClusters) {
        List<Cluster> clusters = new ArrayList<>();
        
        for (int i = 0; i < originalClusters.size(); i++) {
            List<Accumulation> cluster = originalClusters.get(i);
            
            // 计算聚类中心坐标
            double[] center = calculateClusterCenter(cluster);
            
            Cluster clusterEntity = Cluster.createWithCenter(
                "cluster_" + i, center[0], center[1]);
            clusterEntity.setName("聚类_" + (i + 1));
            
            clusters.add(clusterEntity);
        }
        
        return clusters;
    }
    
    /**
     * 计算聚类中心坐标
     */
    private double[] calculateClusterCenter(List<Accumulation> cluster) {
        if (cluster.isEmpty()) {
            return new double[]{0.0, 0.0};
        }
        
        double sumLon = 0.0, sumLat = 0.0;
        int validCount = 0;
        
        for (Accumulation acc : cluster) {
            if (acc.getLongitude() != null && acc.getLatitude() != null) {
                sumLon += acc.getLongitude();
                sumLat += acc.getLatitude();
                validCount++;
            }
        }
        
        if (validCount == 0) {
            return new double[]{0.0, 0.0};
        }
        
        return new double[]{sumLon / validCount, sumLat / validCount};
    }
    
    /**
     * 创建聚集区分配实体
     */
    private List<AccumulationAssignment> createAccumulationAssignments(
        List<List<Accumulation>> originalClusters,
        List<Cluster> clusters
    ) {
        
        List<AccumulationAssignment> assignments = new ArrayList<>();
        
        for (int clusterIndex = 0; clusterIndex < originalClusters.size(); clusterIndex++) {
            List<Accumulation> cluster = originalClusters.get(clusterIndex);
            Cluster clusterEntity = clusters.get(clusterIndex);
            
            for (Accumulation accumulation : cluster) {
                AccumulationAssignment assignment = new AccumulationAssignment(accumulation, clusterEntity);
                assignments.add(assignment);
            }
        }
        
        return assignments;
    }
    
    /**
     * 确定约束权重
     */
    private ConstraintWeights determineConstraintWeights(List<List<Accumulation>> originalClusters) {
        // 根据问题特征调整权重
        int totalAccumulations = originalClusters.stream().mapToInt(List::size).sum();
        
        if (totalAccumulations > 100) {
            // 大规模问题，提高硬约束权重
            return ConstraintWeights.createStrict();
        } else if (totalAccumulations > 50) {
            // 中等规模问题，使用平衡权重
            return ConstraintWeights.createBalanced();
        } else {
            // 小规模问题，使用默认权重
            return ConstraintWeights.createDefault();
        }
    }
    
    /**
     * 创建求解器
     */
    private Solver<ClusteringOptimizationSolution> createSolver(
        OptimizationParameters parameters, 
        String sessionId
    ) {
        
        try {
            log.debug("🔧 创建OptaPlanner求解器，会话: {}", sessionId);
            
            SolverConfig config = solverConfig.createStandardSolverConfig(parameters);
            
            if (!solverConfig.validateSolverConfig(config)) {
                log.error("❌ 求解器配置验证失败");
                return null;
            }
            
            SolverFactory<ClusteringOptimizationSolution> solverFactory = SolverFactory.create(config);
            Solver<ClusteringOptimizationSolution> solver = solverFactory.buildSolver();
            
            log.debug("✅ 求解器创建完成: {}", solverConfig.getConfigSummary(config));
            return solver;
            
        } catch (Exception e) {
            log.error("❌ 求解器创建异常，会话: {}", sessionId, e);
            return null;
        }
    }
    
    /**
     * 执行求解器
     */
    private ClusteringOptimizationSolution executeSolver(
        Solver<ClusteringOptimizationSolution> solver,
        ClusteringOptimizationSolution problem,
        String sessionId
    ) {
        
        try {
            log.info("🎯 开始OptaPlanner求解，会话: {}", sessionId);
            
            // 添加事件监听器
            solver.addEventListener(event -> {
                if (event.isEveryProblemFactChangeProcessed()) {
                    HardSoftScore score = event.getNewBestSolution().getScore();
                    log.debug("   求解进度: {}", score != null ? score.toString() : "计算中");
                }
            });
            
            // 执行求解
            ClusteringOptimizationSolution solution = solver.solve(problem);
            
            if (solution != null && solution.getScore() != null) {
                log.info("🎉 求解完成，最终得分: {}", solution.getScore());
            } else {
                log.warn("⚠️ 求解完成但无有效得分");
            }
            
            return solution;
            
        } catch (Exception e) {
            log.error("❌ 求解器执行异常，会话: {}", sessionId, e);
            return null;
        }
    }
    
    /**
     * 分析优化结果
     */
    private OptimizationResult analyzeOptimizationResult(
        List<List<Accumulation>> originalClusters,
        ClusteringOptimizationSolution solution,
        Instant startTime,
        String sessionId
    ) {
        
        Duration executionTime = Duration.between(startTime, Instant.now());
        
        OptimizationResult result = OptimizationResult.builder()
            .sessionId(sessionId)
            .executionTime(executionTime)
            .originalClusterCount(originalClusters.size())
            .optimizedClusterCount(solution.getClusterList().size())
            .totalAccumulations(solution.getAccumulationAssignments().size())
            .finalScore(solution.getScore())
            .assignedCount(solution.getAssignedCount())
            .unassignedCount(solution.getUnassignedCount())
            .isFullyAssigned(solution.isFullyAssigned())
            .build();
        
        // 计算改进指标
        if (solution.getScore() != null) {
            result.setHardConstraintViolations(Math.abs(solution.getScore().getHardScore()));
            result.setSoftOptimizationScore(solution.getScore().getSoftScore());
        }
        
        log.info("📊 优化结果分析: {}", result.getSummary());
        return result;
    }
    
    /**
     * 转换回原始数据格式
     */
    private List<List<Accumulation>> convertFromOptaPlannerModel(ClusteringOptimizationSolution solution) {
        
        try {
            log.debug("🔄 转换回原始数据格式");
            
            Map<String, List<Accumulation>> clusterMap = new HashMap<>();
            
            // 按聚类分组聚集区
            for (AccumulationAssignment assignment : solution.getAccumulationAssignments()) {
                if (assignment.isAssigned()) {
                    String clusterId = assignment.getAssignedClusterId();
                    clusterMap.computeIfAbsent(clusterId, k -> new ArrayList<>())
                        .add(assignment.getAccumulation());
                }
            }
            
            // 转换为列表格式
            List<List<Accumulation>> result = new ArrayList<>(clusterMap.values());
            
            log.debug("✅ 数据格式转换完成，聚类数: {}", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("❌ 数据格式转换异常", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 验证结果完整性
     */
    private boolean validateResultIntegrity(
        List<List<Accumulation>> original,
        List<List<Accumulation>> optimized
    ) {
        
        // 计算原始聚集区总数
        int originalCount = original.stream().mapToInt(List::size).sum();
        int optimizedCount = optimized.stream().mapToInt(List::size).sum();
        
        if (originalCount != optimizedCount) {
            log.error("❌ 聚集区数量不匹配: 原始={}, 优化={}", originalCount, optimizedCount);
            return false;
        }
        
        // 验证所有聚集区都存在
        Set<String> originalIds = original.stream()
            .flatMap(List::stream)
            .map(acc -> String.valueOf(acc.getAccumulationId()))
            .collect(Collectors.toSet());
        
        Set<String> optimizedIds = optimized.stream()
            .flatMap(List::stream)
            .map(acc -> String.valueOf(acc.getAccumulationId()))
            .collect(Collectors.toSet());
        
        if (!originalIds.equals(optimizedIds)) {
            log.error("❌ 聚集区ID不匹配");
            return false;
        }
        
        log.debug("✅ 结果完整性验证通过");
        return true;
    }
    
    /**
     * 记录优化统计
     */
    private void recordOptimizationStatistics(OptimizationResult result, boolean success) {
        this.lastOptimizationStats = OptimizationStatistics.builder()
            .timestamp(Instant.now())
            .success(success)
            .result(result)
            .build();
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "optaplanner_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }
    
    /**
     * 获取最近的优化统计
     */
    public OptimizationStatistics getLastOptimizationStatistics() {
        return lastOptimizationStats;
    }
    
    /**
     * 优化结果数据类
     */
    @lombok.Data
    @lombok.Builder
    public static class OptimizationResult {
        private String sessionId;
        private Duration executionTime;
        private int originalClusterCount;
        private int optimizedClusterCount;
        private int totalAccumulations;
        private HardSoftScore initialScore;
        private HardSoftScore finalScore;
        private long assignedCount;
        private long unassignedCount;
        private boolean isFullyAssigned;
        private int hardConstraintViolations;
        private int softOptimizationScore;
        
        public String getSummary() {
            return String.format(
                "OptaPlanner优化结果 - 聚类:%d→%d, 分配:%d/%d, 得分:%s, 耗时:%dms",
                originalClusterCount, optimizedClusterCount,
                assignedCount, totalAccumulations,
                finalScore != null ? finalScore.toString() : "未知",
                executionTime.toMillis()
            );
        }
    }
    
    /**
     * 优化统计数据类
     */
    @lombok.Data
    @lombok.Builder
    public static class OptimizationStatistics {
        private Instant timestamp;
        private boolean success;
        private OptimizationResult result;
        
        public String getReport() {
            if (!success) {
                return "OptaPlanner优化失败 - " + timestamp;
            }
            return "OptaPlanner优化统计 - " + (result != null ? result.getSummary() : "无结果");
        }
    }
}