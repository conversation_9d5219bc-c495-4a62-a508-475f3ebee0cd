package com.ict.datamanagement.domain.vo.transitDepotV0;

import lombok.Data;

@Data
public class TransitDepotVO {
    //中转站id
    private int transitDepotId;
    //中转站名称
    private String transitDepotName;
    //经度
    private double longitude;
    //纬度
    private double latitude;
    //所属班组
    private String groupName;
    //启用状态
    private String status;
    //配送类型
    private String deliveryType;
    //对接配送域
    private String deliveryName;
}
