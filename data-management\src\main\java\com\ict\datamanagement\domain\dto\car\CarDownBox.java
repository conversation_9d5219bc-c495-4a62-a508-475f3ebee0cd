package com.ict.datamanagement.domain.dto.car;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("车辆搜索下拉框")
@AllArgsConstructor
@NoArgsConstructor
public class CarDownBox {
    @ApiModelProperty(value = "车牌号",dataType = "List")
    private List<String> licensePlateNumberList;
    @ApiModelProperty(value = "班组",dataType = "List")
    private List<String> teamList;
    @ApiModelProperty(value = "驾驶人",dataType = "List")
    private Map<String,String> carDriverList;
    @ApiModelProperty(value = "状态",dataType = "List")
    private List<String> statusList;
    @ApiModelProperty(value = "最大载重量",dataType = "String")
    private List<String> maxLoad;

}
