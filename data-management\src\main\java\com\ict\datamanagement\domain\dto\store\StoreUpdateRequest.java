package com.ict.datamanagement.domain.dto.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("商铺更新表单")
@Data
public class StoreUpdateRequest implements Serializable {

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id", dataType = "Long", required = true)
    private Long storeId;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码", dataType = "String")
    private String customerCode;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称", dataType = "String")
    private String storeName;

    /**
     * 店铺经营地址
     */
    @ApiModelProperty(value = "店铺经营地址", dataType = "String")
    private String storeAddress;

    /**
     * 店铺经度
     */
    @ApiModelProperty(value = "店铺经度", dataType = "Double")
    private Double longitude;

    /**
     * 店铺纬度
     */
    @ApiModelProperty(value = "店铺纬度", dataType = "Double")
    private Double latitude;

    /**
     * 商圈类型
     */
    @ApiModelProperty(value = "商圈类型", dataType = "String")
    private String type;

    /**
     * 订货周期
     */
    @ApiModelProperty(value = "订货周期", dataType = "String")
    private String orderCycle;

    /**
     * 店铺所属行政区
     */
    @ApiModelProperty(value = "店铺所属行政区", dataType = "String")
    private String district;

    /**
     * 店铺所属大区
     */
    @ApiModelProperty(value = "店铺所属大区", dataType = "String")
    private String areaName;

    /**
     * 店铺联系人名称
     */
    @ApiModelProperty(value = "店铺联系人名称", dataType = "String")
    private String contactName;

    /**
     * 店铺联系人电话号码
     */
    @ApiModelProperty(value = "店铺联系人电话号码", dataType = "String")
    private String contactPhone;

    /**
     * 客户专员id
     */
    @ApiModelProperty(value = "客户专员id", dataType = "Long")
    private Long customerManagerId;

    /**
     * 路线名称
     */
    @ApiModelProperty(value = "路线名称", dataType = "String")
    private String routeName;

    /**
     * 客户专员名称
     */
    @ApiModelProperty(value = "客户专员名称", dataType = "String")
    private String customerManagerName;

    @ApiModelProperty(value = "商铺城区/乡镇", dataType = "String")
    private String locationType;

    @ApiModelProperty(value = "客户挡位", dataType = "String")
    private String gear;
    @ApiModelProperty(value = "客户状态", dataType = "String")
    private String status;

    //负责人
    @ApiModelProperty(value = "负责人", dataType = "String")
    private String head;
    //备用收货电话
    @ApiModelProperty(value = "备用收货电话", dataType = "String")
    private String sparePhone;
    //收货电话
    @ApiModelProperty(value = "收货电话", dataType = "String")
    private String receivingPhone;
    //返销周期
    @ApiModelProperty(value = "返销周期", dataType = "String")
    private String resaleCycle;
    /*
     * 是否是特殊点
     * */
    @ApiModelProperty(value = "1是特殊点0不是特殊点", dataType = "String")
    private String isSpecial;
    /*
     * 特殊点备注
     * */
    @ApiModelProperty(value = "特殊点标签", dataType = "String")
    private String remark;

}
