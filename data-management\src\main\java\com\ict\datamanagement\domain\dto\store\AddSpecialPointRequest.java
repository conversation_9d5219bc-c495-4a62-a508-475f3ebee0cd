package com.ict.datamanagement.domain.dto.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("添加为特殊点表单")
public class AddSpecialPointRequest {
    //特殊点商铺id
    @ApiModelProperty(value = "店铺id", dataType = "Long", required = true)
    private long storeId;
    //是否为特殊点(0不是1是)
    @ApiModelProperty(value = "是否为特殊点(0不是1是)", dataType = "String", required = true)
    private String isSpecialPoint;
    //添加备注信息
    @ApiModelProperty(value = "添加备注信息", dataType = "String", required = false)
    private String remark;
    //特殊点类型
    @ApiModelProperty(value = "特殊点类型", dataType = "String", required = true)
    private String specialType;
}
