package com.ict.datamanagement.domain.dto.car;

import com.google.j2objc.annotations.AutoreleasePool;
import com.ict.datamanagement.domain.dto.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

@Data
@ApiModel("车辆搜索表单")
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class CarListRequest extends PageRequest {
    //车牌号
    @ApiModelProperty(value = "车牌号",dataType = "String")
    private String licensePlateNumber;
    //班组
    @ApiModelProperty(value = "班组",dataType = "String")
    private String teamName;
    //驾驶人
    @ApiModelProperty(value = "驾驶人",dataType = "String")
    private String carDriver;
    //状态
    @ApiModelProperty(value = "状态",dataType = "String")
    private String status;
    //最大载重量
    @ApiModelProperty(value = "最大载重量",dataType = "String")
    private String maxLoad;
}
