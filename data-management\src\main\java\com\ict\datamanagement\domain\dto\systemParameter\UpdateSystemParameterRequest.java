package com.ict.datamanagement.domain.dto.systemParameter;

import com.ict.datamanagement.domain.entity.SecondTransit;
import com.ict.datamanagement.domain.vo.ststemParameter.SecondTransitVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiOperation("修改系统参数表单")
@AllArgsConstructor
@NoArgsConstructor
public class UpdateSystemParameterRequest {
    //系统参数id
    @ApiModelProperty(value = "系统参数id",dataType = "int")
    private int id;
    //聚集区密集度系数
    @ApiModelProperty(value = "聚集区密集度系数",dataType = "double")
    private double accumulationIntensity;
    //商铺平均卸货时长
    //城区
    @ApiModelProperty(value = "商铺平均卸货时长-城区",dataType = "double")
    private double shoreUnloadCityTime;
    //乡镇
    @ApiModelProperty(value = "商铺平均卸货时长-乡镇",dataType = "double")
    private double shoreUnloadTownshipTime;
    //车辆时速
    //高速公路
    @ApiModelProperty(value = "车辆时速-高速公路",dataType = "double")
    private double freeway;
    //城区公路
    @ApiModelProperty(value = "车辆时速-城区公路",dataType = "double")
    private double urbanRoads;
    //乡镇公路
    @ApiModelProperty(value = "车辆时速-乡镇公路",dataType = "double")
    private double townshipRoads;

    //装车时长分钟
    @ApiModelProperty(value = "装车时长分钟",dataType = "double")
    private double loadingTime;

    //二次中转时长
    @ApiModelProperty(value = "二次中转时长",dataType = "List")
    private List<SecondTransitVO> secondTransitList;
}

