package com.ict.ycwl.clustercalculate.service;


/**
 * <AUTHOR>
 */
public interface CalculateService {
    /**
     * 全部聚集区计算
     */
    void calculateAll();

    /**
     * 用于单个聚集区计算
     *
     * @param areaName        当次计算的聚集区
     * @param storeNumber     聚集区包含的最大商铺数量
     * @param minNumOfCluster 最小簇数
     * @param maxNumOfCluster 最大簇数
     */
    void calculate(String areaName, int storeNumber, int minNumOfCluster, int maxNumOfCluster);

}
