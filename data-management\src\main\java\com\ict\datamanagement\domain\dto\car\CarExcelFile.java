package com.ict.datamanagement.domain.dto.car;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class CarExcelFile {
    //车牌号
    @ExcelProperty("车牌号")
    private String licensePlateNumber;
    //驾驶人名称
    @ExcelProperty("驾驶人名称")
    private String carDriverName;
    //实际载货量
    @ExcelProperty("实际载货量(吨)")
    private String actualLoad;
    //实际工作时长
    @ExcelProperty("实际工作时长(小时)")
    private String actualTime;
    //路线
    @ExcelProperty("路线")
    private String routeName;
    //星期
    @ExcelProperty("星期")
    private String week;
    //日期
    @ExcelProperty("日期")
    private String date;
    //配送域
    @ExcelProperty("配送域")
    private String deliveryAreaName;
    //结论
    @ExcelProperty("结论")
    private String conclusion;
    //导入详情
    @ExcelProperty("导入详情")
    private String ImportDetails;
}