package com.ict.datamanagement.domain.dto.team;

import com.ict.datamanagement.domain.dto.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@ApiModel("班组列表表单")
@AllArgsConstructor
@NoArgsConstructor
public class TeamListRequest extends PageRequest implements Serializable {
    //班组名称
    @ApiModelProperty(value = "班组名称",dataType = "String")
    private String teamName;
    //配送域
    @ApiModelProperty(value = "配送域",dataType = "String")
    private String deliveryName;
    //中转站
    @ApiModelProperty(value = "中转站",dataType = "String")
    private String transitDepotName;
}
