package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName route
 */
@TableName(value ="route")
@Data
public class Route implements Serializable {
    /**
     * 路线id
     */
    @TableId
    private Long routeId;

    /**
     * 路线名称
     */
    private String routeName;

    /**
     * 路线距离
     */
    private String distance;

    /**
     * 中转站id
     */
    private Long transitDepotId;

    /**
     * 大区id
     */
    private Long areaId;

    /**
     * 路线坐标点串
     */
    private String polyline;

    /**
     * 是否软删除（0：否；1：是）
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 载货量
     */
    private String cargoWeight;

    /**
     * 版本号
     */
    private Integer versionNumber;

    /**
     * 凸包坐标点串
     */
    private String convex;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}