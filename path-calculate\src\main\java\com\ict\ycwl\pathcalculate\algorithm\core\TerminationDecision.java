package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * 终止决策结果
 * 
 * 基于时间评估的智能终止决策，包含：
 * - 决策类型：立即停止、谨慎停止、谨慎继续、继续合并
 * - 决策原因：详细的决策依据说明
 * - 置信度：决策的可信程度
 * - 相关指标：支持决策的关键数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-08-05
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
public class TerminationDecision {
    
    /**
     * 决策类型枚举
     */
    public enum DecisionType {
        IMMEDIATE_STOP("立即停止", "⛔", 1.0),
        CAUTIOUS_STOP("谨慎停止", "🚫", 0.8),
        CONTINUE_WITH_CAUTION("谨慎继续", "⚠️", 0.6),
        CONTINUE("继续合并", "✅", 0.4);
        
        private final String description;
        private final String emoji;
        private final double urgencyLevel;
        
        DecisionType(String description, String emoji, double urgencyLevel) {
            this.description = description;
            this.emoji = emoji;
            this.urgencyLevel = urgencyLevel;
        }
        
        public String getDescription() { return description; }
        public String getEmoji() { return emoji; }
        public double getUrgencyLevel() { return urgencyLevel; }
        
        public String getDisplayName() {
            return emoji + " " + description;
        }
    }
    
    // ===================== 核心属性 =====================
    
    /**
     * 决策类型
     */
    private final DecisionType decisionType;
    
    /**
     * 决策原因
     * 详细说明做出此决策的依据
     */
    private final String reason;
    
    /**
     * 决策置信度 (0.0 - 1.0)
     * 表示对这个决策的信心程度
     */
    private final double confidence;
    
    /**
     * 关键指标
     * 支持决策的重要数据
     */
    @Builder.Default
    private final Map<String, Object> metrics = new HashMap<>();
    
    /**
     * 决策时间戳
     */
    @Builder.Default
    private final long timestamp = System.currentTimeMillis();
    
    /**
     * 额外的决策上下文信息
     */
    @Builder.Default
    private final Map<String, String> context = new HashMap<>();
    
    // ===================== 静态工厂方法 =====================
    
    /**
     * 立即停止决策
     */
    public static TerminationDecision immediateStop(String reason) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.IMMEDIATE_STOP)
            .reason(reason)
            .confidence(0.95)
            .build();
    }
    
    /**
     * 立即停止决策（带指标）
     */
    public static TerminationDecision immediateStop(String reason, Map<String, Object> metrics) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.IMMEDIATE_STOP)
            .reason(reason)
            .confidence(0.95)
            .metrics(metrics)
            .build();
    }
    
    /**
     * 谨慎停止决策
     */
    public static TerminationDecision cautiousStop(String reason) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.CAUTIOUS_STOP)
            .reason(reason)
            .confidence(0.85)
            .build();
    }
    
    /**
     * 谨慎停止决策（带指标）
     */
    public static TerminationDecision cautiousStop(String reason, Map<String, Object> metrics) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.CAUTIOUS_STOP)
            .reason(reason)
            .confidence(0.85)
            .metrics(metrics)
            .build();
    }
    
    /**
     * 谨慎继续决策
     */
    public static TerminationDecision continueWithCaution(String reason) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.CONTINUE_WITH_CAUTION)
            .reason(reason)
            .confidence(0.70)
            .build();
    }
    
    /**
     * 谨慎继续决策（带指标）
     */
    public static TerminationDecision continueWithCaution(String reason, Map<String, Object> metrics) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.CONTINUE_WITH_CAUTION)
            .reason(reason)
            .confidence(0.70)
            .metrics(metrics)
            .build();
    }
    
    /**
     * 继续合并决策
     */
    public static TerminationDecision continueOptimization(String reason) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.CONTINUE)
            .reason(reason)
            .confidence(0.80)
            .build();
    }
    
    /**
     * 继续合并决策（带指标）
     */
    public static TerminationDecision continueOptimization(String reason, Map<String, Object> metrics) {
        return TerminationDecision.builder()
            .decisionType(DecisionType.CONTINUE)
            .reason(reason)
            .confidence(0.80)
            .metrics(metrics)
            .build();
    }
    
    // ===================== 便捷方法 =====================
    
    /**
     * 是否应该停止合并
     */
    public boolean shouldTerminate() {
        return decisionType == DecisionType.IMMEDIATE_STOP || 
               decisionType == DecisionType.CAUTIOUS_STOP;
    }
    
    /**
     * 是否应该继续合并
     */
    public boolean shouldContinue() {
        return decisionType == DecisionType.CONTINUE || 
               decisionType == DecisionType.CONTINUE_WITH_CAUTION;
    }
    
    /**
     * 是否处于谨慎模式
     */
    public boolean isCautiousMode() {
        return decisionType == DecisionType.CAUTIOUS_STOP || 
               decisionType == DecisionType.CONTINUE_WITH_CAUTION;
    }
    
    /**
     * 获取紧急程度
     */
    public double getUrgencyLevel() {
        return decisionType.getUrgencyLevel();
    }
    
    /**
     * 添加指标
     */
    public void addMetric(String key, Object value) {
        metrics.put(key, value);
    }
    
    /**
     * 添加上下文信息
     */
    public void addContext(String key, String value) {
        context.put(key, value);
    }
    
    /**
     * 获取指标值
     */
    @SuppressWarnings("unchecked")
    public <T> T getMetric(String key, Class<T> type) {
        Object value = metrics.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 获取指标值（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getMetric(String key, Class<T> type, T defaultValue) {
        T value = getMetric(key, type);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 获取格式化的决策报告
     */
    public String getFormattedReport() {
        StringBuilder report = new StringBuilder();
        report.append("🎯 终止决策报告:\n");
        report.append(String.format("   %s 决策: %s\n", 
            decisionType.getEmoji(), decisionType.getDescription()));
        report.append(String.format("   📝 原因: %s\n", reason));
        report.append(String.format("   🎲 置信度: %.1f%%\n", confidence * 100));
        report.append(String.format("   🚨 紧急程度: %.1f/1.0\n", getUrgencyLevel()));
        
        if (!metrics.isEmpty()) {
            report.append("   📊 关键指标:\n");
            for (Map.Entry<String, Object> entry : metrics.entrySet()) {
                report.append(String.format("      - %s: %s\n", entry.getKey(), entry.getValue()));
            }
        }
        
        if (!context.isEmpty()) {
            report.append("   🔍 上下文信息:\n");
            for (Map.Entry<String, String> entry : context.entrySet()) {
                report.append(String.format("      - %s: %s\n", entry.getKey(), entry.getValue()));
            }
        }
        
        return report.toString();
    }
    
    /**
     * 获取简洁的决策描述
     */
    public String getShortDescription() {
        return String.format("%s (%s, 置信度%.0f%%)", 
            decisionType.getDisplayName(), reason, confidence * 100);
    }
    
    /**
     * 决策是否高置信度
     */
    public boolean isHighConfidence() {
        return confidence >= 0.8;
    }
    
    /**
     * 决策是否低置信度
     */
    public boolean isLowConfidence() {
        return confidence < 0.6;
    }
}