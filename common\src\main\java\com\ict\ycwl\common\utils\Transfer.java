package com.ict.ycwl.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Transfer {
    public static Date transferString2Date(String time){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date dateTime = null;
        try{
            dateTime = sdf.parse(time);
        }catch (ParseException e){
            e.printStackTrace();
        }
        return dateTime;
    }
}
