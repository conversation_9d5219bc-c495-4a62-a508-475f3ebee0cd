package com.ict.datamanagement.controller;


import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.common.StatusCode;
import com.ict.datamanagement.domain.dto.systemParameter.UpdateSystemParameterRequest;
import com.ict.datamanagement.domain.vo.ststemParameter.SystemParameterVO;
import com.ict.datamanagement.service.PathFeignService;
import com.ict.datamanagement.service.SystemParameterService;
import com.ict.ycwl.common.web.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "系统参数")
@Slf4j
@RestController("/systemParameter")
public class SystemParameterController {
    @Autowired
    private SystemParameterService systemParameterService;

    @Autowired
    private PathFeignService pathFeignService;
    @ApiOperation("系统参数列表显示")
    @GetMapping("/getSystemParameterInfo")
    public BaseResponse<SystemParameterVO> getSystemParameterInfo(){
        SystemParameterVO res= systemParameterService.getInfo();
        return ResultUtils.success(res);
    }

    @ApiOperation("修改系统参数")
    @PostMapping("/updateSystemParameter")
    public String updateSystemParameter(@RequestBody UpdateSystemParameterRequest request,@RequestParam String apiKey){
        String i=systemParameterService.updateInfo(request,apiKey);
        return i;
    }

    @ApiOperation("删除二次中转站")
    @DeleteMapping("/deleteSecondTransit")
    public AjaxResult deleteSecondTransit(int id){
        boolean b = systemParameterService.deleteById(id);
        if(b){
            pathFeignService.deleteTranWorkTime();
            return AjaxResult.success("删除成功");
        }else{
            return AjaxResult.error("删除失败");
        }
    }
}
