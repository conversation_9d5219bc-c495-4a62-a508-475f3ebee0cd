package com.ict.datamanagement.domain.vo.storeVO;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class StoreExport {
    /**
     * 客户编码
     */
    @ExcelProperty("客户编码")
    private String customerCode;
    /**
     * 店铺联系人名称
     */
    @ExcelProperty("客户名称")
    private String contactName;
    /**
     * 客户专员名称(负责人)
     */
    @ExcelProperty("负责人")
    private String customerManagerName;
    /**
     * 店铺联系人电话号码
     */
    @ExcelProperty("订货电话")
    private String contactPhone;
    /**
     * 店铺经营地址
     */
    @ExcelProperty("地址")
    private String storeAddress;
    //经度
    @ExcelProperty("GIS经度")
    private Double longitude;

    /**
     * 店铺纬度
     */
    @ExcelProperty("GIS纬度")
    private Double latitude;
    /**
     * 订货周期
     */
    @ExcelProperty("访销周期")
    private String orderCycle;
    /**
     * 商铺位置（类型0：城区；1：乡镇）
     */
    @ExcelProperty("商铺类型")
    private String locationType;
    /**
     * 店铺所属行政区
     */
    @ExcelProperty("所属行政区")
    private String areaName;
    /**
     * 路线名称
     */
    @ExcelProperty("所属配送路线")
    private String routeName;
}
