{"name": "@amap/amap-jsapi-loader", "version": "1.0.1", "description": "高德官网提供的地图JSAPI加载器，可以避免多种异步加载API的错误用法", "main": "dist/index.js", "types": "index.d.ts", "directories": {"test": "test"}, "scripts": {"build": "rollup -c rollup.config.js"}, "repository": {"type": "git", "url": "**************************:amap-web/amap-jsapi-loader.git"}, "keywords": ["amap", "jsapi", "sdk", "loader", "地图", "高德"], "author": "<EMAIL>", "license": "MIT", "devDependencies": {"@ampproject/rollup-plugin-closure-compiler": "^0.23.0", "@babel/core": "^7.8.7", "@babel/preset-env": "^7.8.7", "rollup": "^1.32.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-server": "^0.7.0", "rollup-plugin-typescript2": "^0.27.1", "typescript": "^3.9.7"}}