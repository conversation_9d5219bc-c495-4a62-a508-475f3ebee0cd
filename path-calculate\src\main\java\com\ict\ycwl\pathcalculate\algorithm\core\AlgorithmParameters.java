package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.Data;

/**
 * 算法参数配置
 * 集中管理所有算法配置参数
 */
@Data
public class AlgorithmParameters {
    
    // ============ 时间均衡参数 ============
    
    /**
     * 路线间最大时间差（分钟）
     */
    public static final double ROUTE_TIME_GAP_THRESHOLD = 30.0;
    
    /**
     * 中转站间最大时间差（分钟） - 调整为用户要求的30分钟
     */
    public static final double DEPOT_TIME_GAP_THRESHOLD = 30.0;
    
    /**
     * 班组间最大时间差（分钟）
     */
    public static final double TEAM_TIME_GAP_THRESHOLD = 120.0;
    
    // ============ TSP优化参数 ============
    
    /**
     * 动态规划算法最大节点数
     */
    public static final int DP_MAX_NODES = 12;
    
    /**
     * 分支定界算法最大节点数
     */
    public static final int BRANCH_BOUND_MAX_NODES = 20;
    
    /**
     * 遗传算法种群大小
     */
    public static final int GENETIC_POPULATION_SIZE = 50;
    
    /**
     * 遗传算法代数
     */
    public static final int GENETIC_GENERATIONS = 200;
    
    /**
     * 遗传算法最大节点数
     */
    public static final int GENETIC_MAX_NODES = 200;
    
    /**
     * TSP求解时间限制（秒）
     */
    public static final int TSP_TIME_LIMIT_SECONDS = 30;
    
    // ============ 点权和边权权重系数 ============
    
    /**
     * 点权（配送时间）权重系数
     */
    public static final double DELIVERY_TIME_WEIGHT = 1.0;
    
    /**
     * 边权（行驶时间）权重系数
     */
    public static final double TRAVEL_TIME_WEIGHT = 1.0;
    
    /**
     * 聚集区转移时点权优先系数
     */
    public static final double POINT_WEIGHT_PRIORITY = 2.0;
    
    // ============ 凸包处理参数 ============
    
    /**
     * 凸包重叠容忍度（5%）
     */
    public static final double CONVEX_OVERLAP_TOLERANCE = 0.05;
    
    /**
     * 最大路线数量调整比例
     */
    public static final double MAX_ROUTE_ADJUSTMENT = 0.5;
    
    // ============ 迭代控制参数 ============
    
    /**
     * 最大均衡迭代次数
     */
    public static final int MAX_BALANCE_ITERATIONS = 50;
    
    /**
     * 最大冲突解决尝试次数
     */
    public static final int MAX_CONFLICT_RESOLUTION_ATTEMPTS = 20;
    
    /**
     * 改进阈值（1%）
     */
    public static final double IMPROVEMENT_THRESHOLD = 0.01;
    
    /**
     * 工作量均衡收敛阈值（5%）
     */
    public static final double WORKLOAD_BALANCE_THRESHOLD = 0.05;
    
    // ============ 固定时间参数 ============
    
    /**
     * 装车时间（分钟）
     */
    public static final double LOADING_TIME_MINUTES = 30.0;
    
    /**
     * 默认配送时间（当点权缺失时）
     */
    public static final double DEFAULT_DELIVERY_TIME = 15.0;
    
    /**
     * 默认休息时间
     */
    public static final double DEFAULT_REST_TIME = 5.0;
    
    /**
     * 最小配送时间
     */
    public static final double MIN_DELIVERY_TIME = 5.0;
    
    // ============ 聚类平衡参数 ============
    
    /**
     * 目标路线时间（分钟） - 用于聚类时平衡配送时间，调整为450分钟上限
     */
    public static final double TARGET_ROUTE_TIME = 450.0; // 7.5小时（用户要求上限）
    
    /**
     * 目标聚类大小 - 理想的每个聚类包含的点数
     */
    public static final int TARGET_CLUSTER_SIZE = 25;
    
    /**
     * 聚类大小差异阈值 - 允许的聚类大小差异
     */
    public static final int CLUSTER_SIZE_TOLERANCE = 10;
    
    /**
     * 最大配送时间
     */
    public static final double MAX_DELIVERY_TIME = 120.0;
    
    // ============ 聚类参数 ============
    
    /**
     * K-means最大迭代次数
     */
    public static final int KMEANS_MAX_ITERATIONS = 50;
    
    /**
     * 聚类收敛阈值
     */
    public static final double KMEANS_CONVERGENCE_THRESHOLD = 0.01;
    
    /**
     * 工作量不均衡惩罚系数
     * 降低权重以优先考虑地理合理性
     */
    public static final double BALANCE_PENALTY_COEFFICIENT = 0.3;
    
    /**
     * 最大服务半径（公里）
     * 超出此半径的聚集区将被强制惩罚
     */
    public static final double MAX_SERVICE_RADIUS = 50.0;
    
    /**
     * 服务半径违反惩罚系数
     */
    public static final double RADIUS_VIOLATION_PENALTY = 100.0;
    
    /**
     * 路径交叉惩罚系数
     */
    public static final double CROSSING_PENALTY_COEFFICIENT = 10.0;
    
    /**
     * 路径交叉检测采样密度
     */
    public static final int CROSSING_DETECTION_SAMPLES = 5;
    
    // ============ TSP动态调整策略参数 ============
    
    /**
     * 启用TSP后动态调整功能开关
     * true: 启用第三方库聚集区动态调整
     * false: 禁用动态调整，专注TSP本身优化
     */
    public static final boolean ENABLE_TSP_DYNAMIC_ADJUSTMENT = false;  // 用户要求：先关闭专注TSP
    
    /**
     * TSP动态调整策略选择
     * CONSERVATIVE: 保守策略，只在严重违反时介入
     * AGGRESSIVE: 激进策略，积极进行动态调整
     * DISABLED: 完全禁用策略
     */
    public static final String TSP_ADJUSTMENT_STRATEGY = "DISABLED";
    
    /**
     * TSP优化质量等级
     * STANDARD: 标准优化
     * HIGH: 高质量优化，增加求解时间
     * ULTRA: 超高质量优化，最大求解时间
     */
    public static final String TSP_OPTIMIZATION_QUALITY = "HIGH";
    
    /**
     * TSP求解器选择策略
     * AUTO: 自动选择最优求解器
     * OR_TOOLS_PRIORITY: 优先使用OR-Tools
     * GENETIC_PRIORITY: 优先使用遗传算法
     */
    public static final String TSP_SOLVER_STRATEGY = "OR_TOOLS_PRIORITY";
    
    // ============ 聚类二次优化参数 ============
    
    /**
     * 启用聚类二次优化功能开关
     * true: 在聚类和TSP之间插入二次优化阶段
     * false: 禁用二次优化，使用原始聚类结果
     */
    public static final boolean ENABLE_CLUSTERING_POST_OPTIMIZATION = false;
    
    /**
     * 聚类二次优化最大执行时间（毫秒）
     * 2分钟时间限制，避免影响整体算法性能
     */
    public static final long CLUSTERING_OPTIMIZATION_TIME_LIMIT = 120000L;
    
    /**
     * 聚类二次优化最大轮数
     * 多轮渐进式优化，每轮处理不同约束违反
     */
    public static final int CLUSTERING_OPTIMIZATION_MAX_ROUNDS = 3;
    
    /**
     * 450分钟工作时间硬约束（分钟）
     * 任何聚类的工作时间不得超过此限制
     */
    public static final double CLUSTERING_MAX_WORK_TIME_CONSTRAINT = 450.0;
    
    /**
     * 30分钟时间差异硬约束（分钟）
     * 同一中转站内路线间时间差不得超过此限制
     */
    public static final double CLUSTERING_MAX_TIME_GAP_CONSTRAINT = 30.0;
    
    /**
     * 聚类二次优化最小改进阈值
     * 低于此阈值的改进将终止优化
     */
    public static final double CLUSTERING_MIN_IMPROVEMENT_THRESHOLD = 0.05;
    
    /**
     * 聚类二次优化策略选择
     * AUTO: 自动选择最优策略
     * OPTAPLANNER: 优先使用OptaPlanner约束求解
     * JSPRIT: 优先使用JSPRIT负载均衡
     * OR_TOOLS: 优先使用OR-Tools几何优化
     */
    public static final String CLUSTERING_OPTIMIZATION_STRATEGY = "AUTO";
} 