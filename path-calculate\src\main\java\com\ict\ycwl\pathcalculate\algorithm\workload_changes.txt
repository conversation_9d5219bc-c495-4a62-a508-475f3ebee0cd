diff --git a/core/WorkloadBalancedKMeans.java b/core/WorkloadBalancedKMeans.java
index cf6e281..b7e69f4 100644
--- a/core/WorkloadBalancedKMeans.java
+++ b/core/WorkloadBalancedKMeans.java
@@ -22,7 +22,7 @@ public class WorkloadBalancedKMeans {
     private static final double MIN_CLUSTER_WORK_TIME = 300.0;    // 最小工作时间300分钟
     private static final double MAX_CLUSTER_WORK_TIME = 400.0;    // 最大工作时间400分钟
     private static final double IDEAL_CLUSTER_WORK_TIME = 350.0;  // 理想工作时间350分钟
-    private static final double MERGE_MAX_WORK_TIME = 600.0;      // 合并上限600分钟（用户设计）
+    private static final double MERGE_MAX_WORK_TIME = 600.0;      // 合并上限600分钟
     private static final double TIME_BALANCE_THRESHOLD = 30.0;    // 时间平衡阈值30分钟
     
     // 拆分合并阈值参数
@@ -3744,285 +3744,683 @@ public class WorkloadBalancedKMeans {
             TransitDepot depot, 
             Map<String, TimeInfo> timeMatrix) {
         
-        log.info("=== 开始方差优化转移策略 ===");
+        log.info("=== 开始自然扩散时间平衡优化 ===");
         log.info("当前聚类数: {}", clusters.size());
         
         if (clusters.size() < 2) {
-            log.info("聚类数量不足，跳过方差优化转移");
+            log.info("聚类数量不足，跳过时间平衡优化");
             return false;
         }
         
         boolean hasTransfers = false;
         
-        // 计算每个聚类的工作时间并排序
-        List<ClusterTimeAnalysis> timeAnalysis = new ArrayList<>();
-        for (int i = 0; i < clusters.size(); i++) {
-            List<Accumulation> cluster = clusters.get(i);
+        // 计算全局平均工作时间
+        double totalWorkTime = 0.0;
+        int validClusterCount = 0;
+        for (List<Accumulation> cluster : clusters) {
             if (!cluster.isEmpty()) {
-                double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
-                timeAnalysis.add(new ClusterTimeAnalysis(i, cluster, workTime, 0.0, 0.0));
+                totalWorkTime += calculateClusterWorkTime(cluster, depot, timeMatrix);
+                validClusterCount++;
             }
         }
+        double globalAverage = totalWorkTime / validClusterCount;
         
-        // 按工作时间排序：最大的在前，最小的在后
-        timeAnalysis.sort((a, b) -> Double.compare(b.workTime, a.workTime));
+        log.info("全局平均工作时间: {}分钟，有效聚类数: {}", 
+            String.format("%.1f", globalAverage), validClusterCount);
         
-        log.info("方差优化前工作时间分布:");
-        for (ClusterTimeAnalysis analysis : timeAnalysis) {
-            log.info("聚类[{}]: {}分钟 ({} 个聚集区)", 
-                analysis.index, String.format("%.1f", analysis.workTime), analysis.cluster.size());
-        }
+        // 自然扩散迭代优化（参数自适应机制）
+        int maxIterations = 20;
+        double baseBalanceThreshold = 30.0;  // 基础偏差阈值
+        double baseNearbyThreshold = 20.0;   // 基础就近阈值（分钟差）
         
-        // 计算当前方差
-        double currentVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
-        log.info("当前工作时间方差: {}", String.format("%.2f", currentVariance));
+        // 计算初始不均衡比例用于参数自适应
+        double initialImbalanceRatio = calculateImbalanceRatio(clusters, depot, timeMatrix, globalAverage);
+        log.info("初始不均衡比例: {}, 将应用参数自适应机制", String.format("%.3f", initialImbalanceRatio));
         
-        // 执行渐进转移策略：优先考虑中等差距的转移机会
-        int maxTransferAttempts = 30; // 增加转移尝试次数支持渐进转移
-        int transferAttempts = 0;
-        
-        while (transferAttempts < maxTransferAttempts && timeAnalysis.size() >= 2) {
-            transferAttempts++;
-            
-            // 生成渐进转移候选对（优先30-100分钟差距）
-            List<TransferPair> transferPairs = generateProgressiveTransferPairs(timeAnalysis);
-            
-            if (transferPairs.isEmpty()) {
-                log.debug("没有找到合适的渐进转移对，结束方差优化");
-                break;
-            }
+        for (int iteration = 0; iteration < maxIterations; iteration++) {
+            log.debug("自然扩散第{}轮迭代（参数自适应）", iteration + 1);
             
-            // 选择最优的转移对（优先小差距）
-            TransferPair bestPair = transferPairs.get(0);
-            double timeDifference = bestPair.source.workTime - bestPair.target.workTime;
+            // 动态收敛阈值：θ = base_threshold * (1 - iteration/max_iter)
+            double dynamicBalanceThreshold = baseBalanceThreshold * (1.0 - (double)iteration / maxIterations);
             
-            // 如果最优转移对的差距小于30分钟，认为已经足够平衡
-            if (timeDifference < 30.0) {
-                log.info("最优转移差距已缩小至{}分钟，方差优化完成", String.format("%.1f", timeDifference));
-                break;
-            }
-            
-            if (transferAttempts <= 5 || transferAttempts % 10 == 0) {
-                log.debug("第{}次渐进转移尝试: 从聚类[{}]({}分钟) → 聚类[{}]({}分钟), 差距{}分钟",
-                    transferAttempts, bestPair.source.index, String.format("%.1f", bestPair.source.workTime),
-                    bestPair.target.index, String.format("%.1f", bestPair.target.workTime),
-                    String.format("%.1f", timeDifference));
-            }
+            // 动态邻近阈值：基于不均衡程度调整
+            double dynamicNearbyThreshold = baseNearbyThreshold * (1.0 + initialImbalanceRatio);
             
-            // 根据源聚类规模选择不同的转移候选生成策略
-            List<AccumulationTransferCandidate> candidates = new ArrayList<>();
+            log.debug("动态阈值: 收敛阈值={}分钟, 邻近阈值={}分钟", 
+                String.format("%.1f", dynamicBalanceThreshold),
+                String.format("%.1f", dynamicNearbyThreshold));
             
-            // 小聚类（≤10个聚集区）使用特殊转移逻辑
-            if (bestPair.source.cluster.size() <= 10) {
-                log.debug("使用小聚类特殊转移逻辑处理聚类[{}]（规模{}）", 
-                    bestPair.source.index, bestPair.source.cluster.size());
-                candidates = findTransferCandidatesForSmallCluster(
-                    bestPair.source, clusters, depot, timeMatrix);
-            }
+            boolean roundHasTransfer = false;
             
-            // 如果小聚类特殊处理没有找到候选，或者是大聚类，使用传统边缘点检测
-            if (candidates.isEmpty()) {
-                log.debug("使用传统边缘点检测处理聚类[{}]", bestPair.source.index);
-                candidates = findEdgePointsForTransfer(
-                    bestPair.source, clusters, depot, timeMatrix);
+            // 找出所有高负载聚类（使用动态阈值）
+            List<Integer> highLoadClusters = new ArrayList<>();
+            for (int i = 0; i < clusters.size(); i++) {
+                List<Accumulation> cluster = clusters.get(i);
+                if (!cluster.isEmpty()) {
+                    double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
+                    if (workTime > globalAverage + dynamicBalanceThreshold) {
+                        highLoadClusters.add(i);
+                    }
+                }
             }
             
-            if (candidates.isEmpty()) {
-                log.debug("聚类[{}]没有找到合适的转移候选点", bestPair.source.index);
+            if (highLoadClusters.isEmpty()) {
+                log.info("没有高负载聚类，自然扩散优化完成");
                 break;
             }
             
-            boolean transferExecuted = false;
-            
-            // 尝试转移候选点到目标聚类
-            // 优先策略：先尝试匹配预设转移对的目标聚类
-            for (AccumulationTransferCandidate candidate : candidates) {
-                if (candidate.targetCluster == bestPair.target.cluster) {
-                    // 使用自适应方差判断方法（动态调整容忍度）
-                    if (shouldExecuteAdaptiveTransfer(clusters, candidate, depot, timeMatrix, timeDifference, transferAttempts)) {
-                        // 地理聚集检测：检查转移后是否会产生地理聚集冲突
-                        if (canTransferWithoutConvexHullConflict(candidate.point, candidate.targetCluster, depot)) {
-                            // 执行转移
-                            bestPair.source.cluster.remove(candidate.point);
-                            bestPair.target.cluster.add(candidate.point);
-                            
-                            log.info("渐进转移成功: {} 从聚类[{}] → 聚类[{}], 工作时间: {}分钟",
-                                candidate.point.getAccumulationName(), 
-                                bestPair.source.index, bestPair.target.index,
-                                String.format("%.1f", candidate.pointWorkTime));
-                            
-                            hasTransfers = true;
-                            transferExecuted = true;
-                            break;
-                        }
+            // 对每个高负载聚类尝试向邻近的低负载聚类转移一个点
+            for (int sourceIndex : highLoadClusters) {
+                List<Accumulation> sourceCluster = clusters.get(sourceIndex);
+                double sourceWorkTime = calculateClusterWorkTime(sourceCluster, depot, timeMatrix);
+                
+                log.debug("处理高负载聚类[{}]: {}分钟 ({} 个点)", 
+                    sourceIndex, String.format("%.1f", sourceWorkTime), sourceCluster.size());
+                
+                // 查找所有可能的转移目标（工作时间低于源聚类的邻近聚类）
+                List<TransferCandidate> transferTargets = new ArrayList<>();
+                
+                for (int targetIndex = 0; targetIndex < clusters.size(); targetIndex++) {
+                    if (targetIndex == sourceIndex) continue;
+                    
+                    List<Accumulation> targetCluster = clusters.get(targetIndex);
+                    if (targetCluster.isEmpty()) continue;
+                    
+                    double targetWorkTime = calculateClusterWorkTime(targetCluster, depot, timeMatrix);
+                    
+                    // 使用动态邻近阈值判断转移目标
+                    if (targetWorkTime < sourceWorkTime - dynamicNearbyThreshold) {
+                        // 计算两个聚类中心的距离作为邻近度指标
+                        double distance = calculateClusterToClusterDistance(sourceCluster, targetCluster);
+                        
+                        transferTargets.add(new TransferCandidate(targetIndex, targetCluster, targetWorkTime, distance));
                     }
                 }
-            }
-            
-            // 小聚类特殊转移策略：如果预设目标转移失败，允许使用候选自选的最优目标
-            if (!transferExecuted && bestPair.source.cluster.size() <= 10) {
-                log.debug("预设目标转移失败，启用小聚类自选目标转移策略");
                 
-                for (AccumulationTransferCandidate candidate : candidates) {
-                    // 跳过已经尝试过的预设目标
-                    if (candidate.targetCluster == bestPair.target.cluster) {
-                        continue;
-                    }
+                if (transferTargets.isEmpty()) {
+                    log.debug("聚类[{}]没有找到合适的转移目标", sourceIndex);
+                    continue;
+                }
+                
+                // 按距离排序，优先选择最近的目标
+                transferTargets.sort(Comparator.comparingDouble(t -> t.distance));
+                
+                // 尝试向最近的几个目标转移
+                boolean clusterTransferred = false;
+                for (TransferCandidate target : transferTargets.subList(0, Math.min(3, transferTargets.size()))) {
                     
-                    // 计算与候选目标聚类的时间差距
-                    double candidateTimeDiff = Math.abs(
-                        calculateClusterWorkTime(bestPair.source.cluster, depot, timeMatrix) -
-                        calculateClusterWorkTime(candidate.targetCluster, depot, timeMatrix));
+                    // 选择最适合转移的点（支持阶段性策略）
+                    Accumulation bestPoint = selectBestTransferPoint(sourceCluster, target.cluster, iteration, maxIterations);
                     
-                    // 使用自适应转移判断（小聚类获得更宽松的容忍度）
-                    if (shouldExecuteAdaptiveTransfer(clusters, candidate, depot, timeMatrix, candidateTimeDiff, transferAttempts)) {
-                        // 地理聚集检测
-                        if (canTransferWithoutConvexHullConflict(candidate.point, candidate.targetCluster, depot)) {
-                            // 执行小聚类特殊转移
-                            bestPair.source.cluster.remove(candidate.point);
-                            candidate.targetCluster.add(candidate.point);
+                    if (bestPoint != null) {
+                        // 地理约束检查
+                        if (canTransferWithoutConvexHullConflict(bestPoint, target.cluster, depot)) {
+                            // 执行转移
+                            sourceCluster.remove(bestPoint);
+                            target.cluster.add(bestPoint);
                             
-                            // 找到目标聚类的索引用于日志
-                            int targetIndex = -1;
-                            for (int i = 0; i < clusters.size(); i++) {
-                                if (clusters.get(i) == candidate.targetCluster) {
-                                    targetIndex = i;
-                                    break;
-                                }
-                            }
+                            double pointWorkTime = calculateAccumulationWorkTime(bestPoint, depot);
                             
-                            log.info("小聚类特殊转移成功: {} 从聚类[{}] → 聚类[{}], 工作时间: {}分钟",
-                                candidate.point.getAccumulationName(), 
-                                bestPair.source.index, targetIndex,
-                                String.format("%.1f", candidate.pointWorkTime));
+                            log.info("自然扩散转移成功: {} 从聚类[{}] → 聚类[{}], 点工作时间: {}分钟, 距离: {}公里",
+                                bestPoint.getAccumulationName(), 
+                                sourceIndex, target.index,
+                                String.format("%.1f", pointWorkTime),
+                                String.format("%.1f", target.distance));
                             
                             hasTransfers = true;
-                            transferExecuted = true;
+                            roundHasTransfer = true;
+                            clusterTransferred = true;
                             break;
+                        } else {
+                            log.debug("地理约束阻止转移: {} 从聚类[{}] → 聚类[{}]", 
+                                bestPoint.getAccumulationName(), sourceIndex, target.index);
                         }
                     }
                 }
-            }
-            
-            // 大聚类备用转移策略：如果预设目标转移失败，尝试其他候选目标（扩展差距范围）
-            if (!transferExecuted) {
-                log.debug("预设目标转移失败，启用大聚类扩展范围转移策略，源聚类规模: {}", bestPair.source.cluster.size());
                 
-                for (AccumulationTransferCandidate candidate : candidates) {
-                    // 跳过已经尝试过的预设目标
-                    if (candidate.targetCluster == bestPair.target.cluster) {
-                        continue;
-                    }
-                    
-                    // 计算候选转移的时间差距
-                    double candidateTimeDiff = Math.abs(
-                        calculateClusterWorkTime(bestPair.source.cluster, depot, timeMatrix) -
-                        calculateClusterWorkTime(candidate.targetCluster, depot, timeMatrix));
-                    
-                    // 扩展差距范围处理：30-400分钟差距（覆盖极端不均衡情况）
-                    if (candidateTimeDiff >= 30.0 && candidateTimeDiff <= 400.0) {
-                        if (shouldExecuteAdaptiveTransfer(clusters, candidate, depot, timeMatrix, candidateTimeDiff, transferAttempts)) {
-                            if (canTransferWithoutConvexHullConflict(candidate.point, candidate.targetCluster, depot)) {
-                                // 执行转移
-                                bestPair.source.cluster.remove(candidate.point);
-                                candidate.targetCluster.add(candidate.point);
-                                
-                                // 找到目标聚类的索引用于日志
-                                int targetIndex = -1;
-                                for (int i = 0; i < clusters.size(); i++) {
-                                    if (clusters.get(i) == candidate.targetCluster) {
-                                        targetIndex = i;
-                                        break;
-                                    }
-                                }
-                                
-                                log.info("大聚类扩展转移成功: {} 从聚类[{}] → 聚类[{}], 工作时间: {}分钟, 差距: {}分钟",
-                                    candidate.point.getAccumulationName(), 
-                                    bestPair.source.index, targetIndex,
-                                    String.format("%.1f", candidate.pointWorkTime),
-                                    String.format("%.1f", candidateTimeDiff));
-                                
-                                hasTransfers = true;
-                                transferExecuted = true;
-                                break;
-                            } else {
-                                // 被地理冲突检测阻断的转移示例日志
-                                if (Math.random() < 0.1) { // 10%概率打印示例日志，避免信息冗余
-                                    log.debug("转移被地理冲突阻断: {} 从聚类[{}] → 聚类[?], 差距: {}分钟 (示例)",
-                                        candidate.point.getAccumulationName(),
-                                        bestPair.source.index,
-                                        String.format("%.1f", candidateTimeDiff));
-                                }
-                            }
-                        } else {
-                            // 被方差容忍度阻断的转移示例日志
-                            if (Math.random() < 0.1) { // 10%概率打印示例日志，避免信息冗余
-                                log.debug("转移被方差容忍度阻断: {} 从聚类[{}] → 聚类[?], 差距: {}分钟 (示例)",
-                                    candidate.point.getAccumulationName(),
-                                    bestPair.source.index,
-                                    String.format("%.1f", candidateTimeDiff));
-                            }
-                        }
-                    } else {
-                        // 被差距范围阻断的转移示例日志（只在极端情况下打印）
-                        if (candidateTimeDiff > 400.0 && Math.random() < 0.05) { // 5%概率打印极端情况
-                            log.debug("转移被差距范围阻断: {} 从聚类[{}] → 聚类[?], 差距: {}分钟 (超极端)",
-                                candidate.point.getAccumulationName(),
-                                bestPair.source.index,
-                                String.format("%.1f", candidateTimeDiff));
-                        }
-                    }
+                if (clusterTransferred) {
+                    break; // 每轮只处理一个聚类的一次转移
                 }
             }
             
-            if (!transferExecuted) {
-                log.debug("没有找到有利的渐进转移，结束方差优化 (源聚类[{}]规模: {}, 工作时间: {}分钟, 候选数: {})",
-                    bestPair.source.index, 
-                    bestPair.source.cluster.size(),
-                    String.format("%.1f", calculateClusterWorkTime(bestPair.source.cluster, depot, timeMatrix)),
-                    candidates.size());
+            if (!roundHasTransfer) {
+                log.info("本轮没有成功转移，自然扩散优化结束");
                 break;
             }
             
-            // 重新计算工作时间和排序
-            timeAnalysis.clear();
-            for (int i = 0; i < clusters.size(); i++) {
-                List<Accumulation> cluster = clusters.get(i);
+            // 重新计算平均值用于下一轮
+            totalWorkTime = 0.0;
+            validClusterCount = 0;
+            for (List<Accumulation> cluster : clusters) {
                 if (!cluster.isEmpty()) {
-                    double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
-                    timeAnalysis.add(new ClusterTimeAnalysis(i, cluster, workTime, 0.0, 0.0));
+                    totalWorkTime += calculateClusterWorkTime(cluster, depot, timeMatrix);
+                    validClusterCount++;
                 }
             }
-            timeAnalysis.sort((a, b) -> Double.compare(b.workTime, a.workTime));
+            globalAverage = totalWorkTime / validClusterCount;
         }
         
-        // 输出方差优化结果
-        if (hasTransfers) {
-            double finalVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
-            log.info("=== 方差优化转移策略完成 ===");
-            log.info("执行了{}次转移尝试，成功转移若干点", transferAttempts);
-            log.info("方差变化: {} → {}", 
-                String.format("%.2f", currentVariance), 
-                String.format("%.2f", finalVariance));
+        // 离群点检测和处理（异常处理阶段）
+        log.info("=== 开始离群点检测和处理 ===");
+        boolean outlierProcessed = detectAndProcessOutliers(clusters, depot, timeMatrix);
+        if (outlierProcessed) {
+            hasTransfers = true;
+            log.info("离群点处理完成，发现并处理了离群点");
+        } else {
+            log.info("离群点检测完成，未发现需要处理的离群点");
+        }
+        
+        // 全局再平衡机制（极端不均衡处理）
+        log.info("=== 开始全局再平衡检查 ===");
+        boolean globalRebalanced = performGlobalRebalance(clusters, depot, timeMatrix);
+        if (globalRebalanced) {
+            hasTransfers = true;
+            log.info("全局再平衡完成，处理了极端不均衡情况");
+        } else {
+            log.info("全局再平衡检查完成，未发现需要处理的极端不均衡");
+        }
+        
+        log.info("自然扩散优化完成，是否有转移: {}", hasTransfers);
+        return hasTransfers;
+    }
+    
+    /**
+     * 转移候选目标数据结构
+     */
+    private static class TransferCandidate {
+        int index;
+        List<Accumulation> cluster;
+        double workTime;
+        double distance;
+        
+        public TransferCandidate(int index, List<Accumulation> cluster, double workTime, double distance) {
+            this.index = index;
+            this.cluster = cluster;
+            this.workTime = workTime;
+            this.distance = distance;
+        }
+    }
+    
+    /**
+     * 选择最适合转移的点（支持阶段性策略的参数自适应机制）
+     * 
+     * 根据迭代进度实施不同的转移点选择策略：
+     * - 初期（0-33%）：选择边缘点，保持地理聚集
+     * - 中期（33-66%）：选择中等距离点
+     * - 后期（66-100%）：允许任意点，优先均衡
+     */
+    private Accumulation selectBestTransferPoint(List<Accumulation> sourceCluster, List<Accumulation> targetCluster, 
+                                               int currentIteration, int maxIterations) {
+        if (sourceCluster.isEmpty() || targetCluster.isEmpty()) {
+            return null;
+        }
+        
+        // 计算目标聚类的中心点
+        double targetCenterLat = targetCluster.stream()
+            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
+        double targetCenterLon = targetCluster.stream()
+            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
+        
+        // 计算迭代进度比例
+        double iterationProgress = (double) currentIteration / maxIterations;
+        
+        if (iterationProgress <= 0.33) {
+            // 初期策略：选择边缘点（最靠近目标的点）
+            return selectEdgePoint(sourceCluster, targetCenterLat, targetCenterLon);
+        } else if (iterationProgress <= 0.66) {
+            // 中期策略：选择中等距离点
+            return selectMediumDistancePoint(sourceCluster, targetCenterLat, targetCenterLon);
+        } else {
+            // 后期策略：优先均衡，允许任意点转移
+            return selectBalancePriorityPoint(sourceCluster, targetCenterLat, targetCenterLon);
+        }
+    }
+    
+    /**
+     * 初期策略：选择边缘点（保持地理聚集）
+     */
+    private Accumulation selectEdgePoint(List<Accumulation> sourceCluster, double targetCenterLat, double targetCenterLon) {
+        Accumulation bestPoint = null;
+        double minDistance = Double.MAX_VALUE;
+        
+        for (Accumulation point : sourceCluster) {
+            double distance = calculateDistance(
+                point.getLatitude(), point.getLongitude(),
+                targetCenterLat, targetCenterLon);
+            
+            if (distance < minDistance) {
+                minDistance = distance;
+                bestPoint = point;
+            }
+        }
+        
+        return bestPoint;
+    }
+    
+    /**
+     * 中期策略：选择中等距离点
+     */
+    private Accumulation selectMediumDistancePoint(List<Accumulation> sourceCluster, double targetCenterLat, double targetCenterLon) {
+        List<Accumulation> sortedByDistance = new ArrayList<>(sourceCluster);
+        sortedByDistance.sort((a, b) -> {
+            double distA = calculateDistance(a.getLatitude(), a.getLongitude(), targetCenterLat, targetCenterLon);
+            double distB = calculateDistance(b.getLatitude(), b.getLongitude(), targetCenterLat, targetCenterLon);
+            return Double.compare(distA, distB);
+        });
+        
+        // 选择中位数位置的点（中等距离）
+        int medianIndex = sortedByDistance.size() / 2;
+        return sortedByDistance.get(medianIndex);
+    }
+    
+    /**
+     * 后期策略：优先均衡，选择工作量较大的点
+     */
+    private Accumulation selectBalancePriorityPoint(List<Accumulation> sourceCluster, double targetCenterLat, double targetCenterLon) {
+        // 后期优先选择工作量较大的点来实现更好的均衡效果
+        Accumulation bestPoint = null;
+        double maxWorkTime = 0.0;
+        
+        for (Accumulation point : sourceCluster) {
+            // 估算点的工作时间（配送时间）
+            double pointWorkTime = point.getDeliveryTime() != null ? point.getDeliveryTime() : 15.0;
             
-            log.info("方差优化后工作时间分布:");
-            for (ClusterTimeAnalysis analysis : timeAnalysis) {
-                log.info("聚类[{}]: {}分钟 ({} 个聚集区)", 
-                    analysis.index, String.format("%.1f", analysis.workTime), analysis.cluster.size());
+            if (pointWorkTime > maxWorkTime) {
+                maxWorkTime = pointWorkTime;
+                bestPoint = point;
             }
+        }
+        
+        // 如果所有点工作时间相同，则选择最靠近目标的点
+        if (bestPoint == null) {
+            return selectEdgePoint(sourceCluster, targetCenterLat, targetCenterLon);
+        }
+        
+        return bestPoint;
+    }
+    
+    /**
+     * 计算不均衡比例（用于参数自适应）
+     * 
+     * 基于标准差与平均值的比值来量化不均衡程度
+     * 不均衡比例越高，参数调整幅度越大
+     */
+    private double calculateImbalanceRatio(List<List<Accumulation>> clusters, TransitDepot depot, 
+                                         Map<String, TimeInfo> timeMatrix, double globalAverage) {
+        List<Double> workTimes = new ArrayList<>();
+        
+        for (List<Accumulation> cluster : clusters) {
+            if (!cluster.isEmpty()) {
+                double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
+                workTimes.add(workTime);
+            }
+        }
+        
+        if (workTimes.size() < 2) {
+            return 0.0; // 聚类数量不足，无法计算不均衡比例
+        }
+        
+        // 计算标准差
+        double sumSquaredDiff = 0.0;
+        for (double workTime : workTimes) {
+            double diff = workTime - globalAverage;
+            sumSquaredDiff += diff * diff;
+        }
+        
+        double variance = sumSquaredDiff / workTimes.size();
+        double standardDeviation = Math.sqrt(variance);
+        
+        // 不均衡比例 = 标准差 / 平均值
+        // 这个比例反映了数据的相对离散程度
+        double imbalanceRatio = standardDeviation / globalAverage;
+        
+        return Math.min(imbalanceRatio, 1.0); // 限制最大值为1.0
+    }
+    
+    /**
+     * 检测和处理离群点（异常处理机制）
+     * 
+     * 使用统计方法（2σ原则）检测地理离群点并尝试重分配
+     * 
+     * @param clusters 聚类集合
+     * @param depot 中转站
+     * @param timeMatrix 时间矩阵
+     * @return 是否处理了离群点
+     */
+    private boolean detectAndProcessOutliers(List<List<Accumulation>> clusters, TransitDepot depot, 
+                                           Map<String, TimeInfo> timeMatrix) {
+        boolean hasProcessedOutliers = false;
+        
+        for (int clusterIndex = 0; clusterIndex < clusters.size(); clusterIndex++) {
+            List<Accumulation> cluster = clusters.get(clusterIndex);
+            if (cluster.size() <= 2) {
+                continue; // 小聚类跳过离群点检测
+            }
+            
+            // 计算聚类中心
+            double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
+            double centerLon = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
+            
+            // 计算所有点到中心的距离
+            List<Double> distances = new ArrayList<>();
+            for (Accumulation point : cluster) {
+                double distance = calculateDistance(point.getLatitude(), point.getLongitude(), centerLat, centerLon);
+                distances.add(distance);
+            }
+            
+            // 计算距离的均值和标准差
+            double meanDistance = distances.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
+            double variance = distances.stream()
+                .mapToDouble(d -> Math.pow(d - meanDistance, 2))
+                .average().orElse(0.0);
+            double stdDeviation = Math.sqrt(variance);
+            
+            // 2σ原则：超过 μ + 2σ 的点被认为是离群点
+            double outlierThreshold = meanDistance + 2 * stdDeviation;
+            
+            log.debug("聚类[{}]离群点检测: 平均距离={:.1f}km, 标准差={:.1f}km, 阈值={:.1f}km", 
+                clusterIndex, meanDistance, stdDeviation, outlierThreshold);
+            
+            // 查找离群点
+            List<Accumulation> outliers = new ArrayList<>();
+            for (int i = 0; i < cluster.size(); i++) {
+                Accumulation point = cluster.get(i);
+                double distance = distances.get(i);
+                
+                if (distance > outlierThreshold && distance > 10.0) { // 至少10公里才算离群
+                    outliers.add(point);
+                    log.info("发现离群点: {} 距离聚类中心{:.1f}km (阈值{:.1f}km)", 
+                        point.getAccumulationName(), distance, outlierThreshold);
+                }
+            }
+            
+            // 处理离群点
+            for (Accumulation outlier : outliers) {
+                boolean relocated = relocateOutlier(outlier, clusters, clusterIndex, depot, timeMatrix);
+                if (relocated) {
+                    cluster.remove(outlier);
+                    hasProcessedOutliers = true;
+                    log.info("离群点重分配成功: {}", outlier.getAccumulationName());
+                } else {
+                    log.warn("离群点无法重分配，保持现状: {}", outlier.getAccumulationName());
+                }
+            }
+        }
+        
+        return hasProcessedOutliers;
+    }
+    
+    /**
+     * 重分配离群点到最合适的聚类
+     * 
+     * @param outlier 离群点
+     * @param clusters 所有聚类
+     * @param sourceClusterIndex 原聚类索引
+     * @param depot 中转站
+     * @param timeMatrix 时间矩阵
+     * @return 是否成功重分配
+     */
+    private boolean relocateOutlier(Accumulation outlier, List<List<Accumulation>> clusters, 
+                                  int sourceClusterIndex, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
+        
+        List<OutlierRelocationCandidate> candidates = new ArrayList<>();
+        
+        // 查找所有可能的重分配目标
+        for (int i = 0; i < clusters.size(); i++) {
+            if (i == sourceClusterIndex) continue;
+            
+            List<Accumulation> targetCluster = clusters.get(i);
+            if (targetCluster.isEmpty()) continue;
+            
+            // 计算到目标聚类中心的距离
+            double targetCenterLat = targetCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
+            double targetCenterLon = targetCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
+            double distanceToTarget = calculateDistance(outlier.getLatitude(), outlier.getLongitude(), 
+                                                      targetCenterLat, targetCenterLon);
+            
+            // 计算重分配后的工作时间变化
+            double currentTargetTime = calculateClusterWorkTime(targetCluster, depot, timeMatrix);
+            double outlierWorkTime = calculateAccumulationWorkTime(outlier, depot);
+            double newTargetTime = currentTargetTime + outlierWorkTime;
+            
+            // 检查是否在合理范围内（不超过600分钟）
+            if (newTargetTime <= 600.0 && distanceToTarget <= 50.0) { // 50公里距离限制
+                candidates.add(new OutlierRelocationCandidate(i, targetCluster, distanceToTarget, newTargetTime));
+            }
+        }
+        
+        if (candidates.isEmpty()) {
+            return false; // 没有合适的重分配目标
+        }
+        
+        // 选择最近的候选目标
+        candidates.sort(Comparator.comparingDouble(c -> c.distance));
+        OutlierRelocationCandidate bestCandidate = candidates.get(0);
+        
+        // 地理约束检查
+        if (canTransferWithoutConvexHullConflict(outlier, bestCandidate.cluster, depot)) {
+            bestCandidate.cluster.add(outlier);
+            log.info("离群点重分配: {} 转移到聚类[{}], 距离={:.1f}km", 
+                outlier.getAccumulationName(), bestCandidate.clusterIndex, bestCandidate.distance);
+            return true;
+        }
+        
+        return false;
+    }
+    
+    /**
+     * 离群点重分配候选数据结构
+     */
+    private static class OutlierRelocationCandidate {
+        int clusterIndex;
+        List<Accumulation> cluster;
+        double distance;
+        double newWorkTime;
+        
+        public OutlierRelocationCandidate(int clusterIndex, List<Accumulation> cluster, 
+                                        double distance, double newWorkTime) {
+            this.clusterIndex = clusterIndex;
+            this.cluster = cluster;
+            this.distance = distance;
+            this.newWorkTime = newWorkTime;
+        }
+    }
+    
+    /**
+     * 全局再平衡机制（极端不均衡处理）
+     * 
+     * 当自然扩散和离群点处理无法解决极端不均衡时的最后手段
+     * 允许临时违反地理约束来实现负载均衡
+     * 
+     * @param clusters 聚类集合
+     * @param depot 中转站
+     * @param timeMatrix 时间矩阵
+     * @return 是否执行了全局再平衡
+     */
+    private boolean performGlobalRebalance(List<List<Accumulation>> clusters, TransitDepot depot, 
+                                         Map<String, TimeInfo> timeMatrix) {
+        
+        // 计算所有聚类的工作时间
+        List<ClusterWorkTimeInfo> clusterInfos = new ArrayList<>();
+        for (int i = 0; i < clusters.size(); i++) {
+            List<Accumulation> cluster = clusters.get(i);
+            if (!cluster.isEmpty()) {
+                double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
+                clusterInfos.add(new ClusterWorkTimeInfo(i, cluster, workTime));
+            }
+        }
+        
+        if (clusterInfos.size() < 2) {
+            return false; // 聚类数量不足
+        }
+        
+        // 按工作时间排序
+        clusterInfos.sort(Comparator.comparingDouble(c -> c.workTime));
+        
+        ClusterWorkTimeInfo minCluster = clusterInfos.get(0);
+        ClusterWorkTimeInfo maxCluster = clusterInfos.get(clusterInfos.size() - 1);
+        
+        // 检查是否存在极端不均衡（最大最小差异 > 2倍且绝对差异 > 100分钟）
+        double ratio = maxCluster.workTime / minCluster.workTime;
+        double absoluteDiff = maxCluster.workTime - minCluster.workTime;
+        
+        log.debug("全局不均衡检查: 最大={}分钟, 最小={}分钟, 比例={:.2f}, 差异={}分钟", 
+            String.format("%.1f", maxCluster.workTime), 
+            String.format("%.1f", minCluster.workTime), 
+            ratio, String.format("%.1f", absoluteDiff));
+        
+        if (ratio <= 2.0 || absoluteDiff <= 100.0) {
+            return false; // 不需要全局再平衡
+        }
+        
+        log.info("检测到极端不均衡: 聚类[{}]({:.1f}分钟) vs 聚类[{}]({:.1f}分钟), 比例{:.2f}", 
+            maxCluster.clusterIndex, maxCluster.workTime,
+            minCluster.clusterIndex, minCluster.workTime, ratio);
+        
+        // 尝试多跳转移路径
+        boolean transferred = attemptMultiHopTransfer(maxCluster, minCluster, clusterInfos, depot, timeMatrix);
+        
+        if (!transferred) {
+            // 如果多跳转移失败，尝试违约转移（允许违反地理约束）
+            log.warn("多跳转移失败，尝试违约转移（临时违反地理约束）");
+            transferred = attemptConstraintViolationTransfer(maxCluster, minCluster, depot, timeMatrix);
+        }
+        
+        return transferred;
+    }
+    
+    /**
+     * 尝试多跳转移路径
+     * 
+     * 通过中间聚类建立转移链路，避免直接的远距离转移
+     */
+    private boolean attemptMultiHopTransfer(ClusterWorkTimeInfo maxCluster, ClusterWorkTimeInfo minCluster,
+                                          List<ClusterWorkTimeInfo> allClusters, TransitDepot depot, 
+                                          Map<String, TimeInfo> timeMatrix) {
+        
+        // 寻找中间聚类作为跳板
+        List<ClusterWorkTimeInfo> intermediates = new ArrayList<>();
+        for (ClusterWorkTimeInfo cluster : allClusters) {
+            if (cluster != maxCluster && cluster != minCluster) {
+                // 工作时间在最大和最小之间，且有容量接受转移
+                if (cluster.workTime > minCluster.workTime && cluster.workTime < maxCluster.workTime 
+                    && cluster.workTime < 500.0) { // 500分钟以下有转移空间
+                    intermediates.add(cluster);
+                }
+            }
+        }
+        
+        if (intermediates.isEmpty()) {
+            log.debug("没有找到合适的中间聚类进行多跳转移");
+            return false;
+        }
+        
+        // 按距离最大聚类的远近排序中间聚类
+        intermediates.sort((a, b) -> {
+            double distA = calculateClusterToClusterDistance(maxCluster.cluster, a.cluster);
+            double distB = calculateClusterToClusterDistance(maxCluster.cluster, b.cluster);
+            return Double.compare(distA, distB);
+        });
+        
+        // 尝试通过最近的中间聚类进行两跳转移
+        ClusterWorkTimeInfo intermediate = intermediates.get(0);
+        
+        log.info("尝试多跳转移: 聚类[{}] → 聚类[{}] → 聚类[{}]", 
+            maxCluster.clusterIndex, intermediate.clusterIndex, minCluster.clusterIndex);
+        
+        // 第一跳：从最大聚类转移到中间聚类
+        Accumulation transferPoint1 = selectBestTransferPoint(maxCluster.cluster, intermediate.cluster, 0, 1);
+        if (transferPoint1 != null && 
+            canTransferWithoutConvexHullConflict(transferPoint1, intermediate.cluster, depot)) {
+            
+            maxCluster.cluster.remove(transferPoint1);
+            intermediate.cluster.add(transferPoint1);
             
-            if (finalVariance < currentVariance) {
-                log.info("方差优化成功：方差降低了{}", 
-                    String.format("%.2f", currentVariance - finalVariance));
+            log.info("多跳转移第一跳成功: {} 从聚类[{}] → 聚类[{}]", 
+                transferPoint1.getAccumulationName(), maxCluster.clusterIndex, intermediate.clusterIndex);
+            
+            // 第二跳：从中间聚类转移到最小聚类
+            Accumulation transferPoint2 = selectBestTransferPoint(intermediate.cluster, minCluster.cluster, 0, 1);
+            if (transferPoint2 != null && 
+                canTransferWithoutConvexHullConflict(transferPoint2, minCluster.cluster, depot)) {
+                
+                intermediate.cluster.remove(transferPoint2);
+                minCluster.cluster.add(transferPoint2);
+                
+                log.info("多跳转移第二跳成功: {} 从聚类[{}] → 聚类[{}]", 
+                    transferPoint2.getAccumulationName(), intermediate.clusterIndex, minCluster.clusterIndex);
+                
+                return true;
             } else {
-                log.warn("方差优化异常：方差未降低");
+                // 第二跳失败，回滚第一跳
+                intermediate.cluster.remove(transferPoint1);
+                maxCluster.cluster.add(transferPoint1);
+                log.debug("多跳转移第二跳失败，已回滚");
             }
-        } else {
-            log.info("=== 方差优化转移策略完成 ===");
-            log.info("未找到有利的转移操作，聚类时间分布保持不变");
         }
         
-        return hasTransfers;
+        return false;
+    }
+    
+    /**
+     * 违约转移（允许临时违反地理约束）
+     * 
+     * 最后手段：为了实现负载均衡，允许转移地理上不太合理的点
+     */
+    private boolean attemptConstraintViolationTransfer(ClusterWorkTimeInfo maxCluster, ClusterWorkTimeInfo minCluster,
+                                                     TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
+        
+        // 选择工作量最大的点进行转移（优先均衡效果）
+        Accumulation bestPoint = null;
+        double maxWorkTime = 0.0;
+        
+        for (Accumulation point : maxCluster.cluster) {
+            double pointWorkTime = point.getDeliveryTime() != null ? point.getDeliveryTime() : 15.0;
+            if (pointWorkTime > maxWorkTime) {
+                maxWorkTime = pointWorkTime;
+                bestPoint = point;
+            }
+        }
+        
+        if (bestPoint != null) {
+            // 计算转移后的工作时间变化
+            double maxClusterNewTime = maxCluster.workTime - maxWorkTime;
+            double minClusterNewTime = minCluster.workTime + maxWorkTime;
+            
+            // 检查转移是否真的改善了不均衡
+            double currentDiff = maxCluster.workTime - minCluster.workTime;
+            double newDiff = Math.abs(maxClusterNewTime - minClusterNewTime);
+            
+            if (newDiff < currentDiff * 0.8) { // 差异至少减少20%
+                maxCluster.cluster.remove(bestPoint);
+                minCluster.cluster.add(bestPoint);
+                
+                log.warn("违约转移执行: {} 从聚类[{}] → 聚类[{}] (违反地理约束)", 
+                    bestPoint.getAccumulationName(), maxCluster.clusterIndex, minCluster.clusterIndex);
+                log.warn("工作时间变化: {:.1f}→{:.1f}分钟 vs {:.1f}→{:.1f}分钟", 
+                    maxCluster.workTime, maxClusterNewTime, 
+                    minCluster.workTime, minClusterNewTime);
+                
+                return true;
+            }
+        }
+        
+        return false;
+    }
+    
+    /**
+     * 聚类工作时间信息数据结构
+     */
+    private static class ClusterWorkTimeInfo {
+        int clusterIndex;
+        List<Accumulation> cluster;
+        double workTime;
+        
+        public ClusterWorkTimeInfo(int clusterIndex, List<Accumulation> cluster, double workTime) {
+            this.clusterIndex = clusterIndex;
+            this.cluster = cluster;
+            this.workTime = workTime;
+        }
     }
     
     /**
