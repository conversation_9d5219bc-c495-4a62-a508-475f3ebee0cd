package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("transit_delivery")
public class TransitDelivery {
    //记录id
    @TableId(type = IdType.AUTO)
    private Long transitDeLiveryId;
    //中转站id
    private Long transitDepotId;
    //对接点id
    private Long deliveryAreaId;
}
