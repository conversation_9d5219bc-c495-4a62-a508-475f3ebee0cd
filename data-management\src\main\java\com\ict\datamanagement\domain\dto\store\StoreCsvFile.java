package com.ict.datamanagement.domain.dto.store;

import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;

@Data
public class StoreCsvFile {
    //客户编码
    @CsvBindByPosition(position = 0)
    private String customerCode;
    //客户名称
    @CsvBindByPosition(position = 1)
    private String contactName;
    //客户地址
    @CsvBindByPosition(position = 2)
    private String storeAddress;
    //负责人
    @CsvBindByPosition(position = 3)
    private String head;
    //客户挡位
    @CsvBindByPosition(position = 4)
    private String gear;
    //GIS经度
    @CsvBindByPosition(position = 5)
    private Double longitude;
    //GIS纬度
    @CsvBindByPosition(position = 6)
    private Double latitude;
    //线路
    @CsvBindByPosition(position = 7)
    private String routeName;
    //访销周期
    @CsvBindByPosition(position = 8)
    private String orderCycle;
    //客户专员
    @CsvBindByPosition(position = 9)
    private String customerManagerName;
    //配送域
    @CsvBindByPosition(position = 10)
    private String deliveryArea;
    @CsvBindByPosition(position = 11)
    private String conclusion;
    //导入详情
    @CsvBindByPosition(position = 12)
    private String ImportDetails;
}
