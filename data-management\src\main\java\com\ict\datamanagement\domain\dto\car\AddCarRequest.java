package com.ict.datamanagement.domain.dto.car;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("添加车辆表单")
@AllArgsConstructor
@NoArgsConstructor
public class AddCarRequest {
    //车牌号
    @ApiModelProperty(value = "车牌号",dataType = "String")
    private String licensePlateNumber;

    //配送域
    @ApiModelProperty(value = "配送域",dataType = "String")
    private String deliveryAreaName;

    //驾驶人
    @ApiModelProperty(value = "驾驶人",dataType = "String")
    private String carDriverName;

    //电话
    @ApiModelProperty(value = "电话",dataType = "String")
    private String carDriverPhone;

    //最大载重量
    @ApiModelProperty(value = "最大载重量",dataType = "String")
    private String maxLoad;

    //状态
    @ApiModelProperty(value = "状态",dataType = "String")
    private String status;

}
