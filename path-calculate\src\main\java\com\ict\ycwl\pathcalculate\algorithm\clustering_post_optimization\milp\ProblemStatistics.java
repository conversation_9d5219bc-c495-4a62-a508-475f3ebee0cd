package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * MILP问题统计信息
 * 
 * 包含MILP问题的规模和结构统计信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ProblemStatistics {
    
    /**
     * 总变量数
     */
    private int totalVariables;
    
    /**
     * 各类型变量数量统计
     */
    private Map<MILPProblem.VariableType, Integer> variableTypeCount;
    
    /**
     * 总约束数
     */
    private int totalConstraints;
    
    /**
     * 各类型约束数量统计
     */
    private Map<ConstraintType, Integer> constraintTypeCount;
    
    /**
     * 是否有目标函数
     */
    private boolean hasObjectiveFunction;
    
    /**
     * 问题规模等级
     */
    private String problemSize;
    
    /**
     * 非零系数数量（稠密度）
     */
    private Integer nonZeroCoefficients;
    
    /**
     * 矩阵稠密度
     */
    private Double matrixDensity;
    
    /**
     * 获取连续变量数量
     */
    public int getContinuousVariableCount() {
        return variableTypeCount.getOrDefault(MILPProblem.VariableType.CONTINUOUS, 0);
    }
    
    /**
     * 获取整数变量数量
     */
    public int getIntegerVariableCount() {
        return variableTypeCount.getOrDefault(MILPProblem.VariableType.INTEGER, 0);
    }
    
    /**
     * 获取二进制变量数量
     */
    public int getBinaryVariableCount() {
        return variableTypeCount.getOrDefault(MILPProblem.VariableType.BINARY, 0);
    }
    
    /**
     * 检查是否为纯线性规划（无整数变量）
     */
    public boolean isPureLP() {
        return getIntegerVariableCount() == 0 && getBinaryVariableCount() == 0;
    }
    
    /**
     * 检查是否为混合整数规划
     */
    public boolean isMILP() {
        return getContinuousVariableCount() > 0 && 
               (getIntegerVariableCount() > 0 || getBinaryVariableCount() > 0);
    }
    
    /**
     * 检查是否为纯整数规划
     */
    public boolean isPureIP() {
        return getContinuousVariableCount() == 0 && 
               (getIntegerVariableCount() > 0 || getBinaryVariableCount() > 0);
    }
    
    /**
     * 获取问题类型描述
     */
    public String getProblemType() {
        if (isPureLP()) {
            return "Linear Programming (LP)";
        } else if (isMILP()) {
            return "Mixed Integer Linear Programming (MILP)";
        } else if (isPureIP()) {
            return "Integer Programming (IP)";
        } else {
            return "Unknown";
        }
    }
    
    /**
     * 生成统计信息摘要
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== MILP问题统计信息 ===\n");
        summary.append(String.format("问题类型: %s\n", getProblemType()));
        summary.append(String.format("问题规模: %s\n", problemSize));
        summary.append(String.format("总变量数: %d\n", totalVariables));
        
        if (variableTypeCount != null && !variableTypeCount.isEmpty()) {
            summary.append("变量类型分布:\n");
            for (Map.Entry<MILPProblem.VariableType, Integer> entry : variableTypeCount.entrySet()) {
                summary.append(String.format("  - %s: %d\n", entry.getKey().getDescription(), entry.getValue()));
            }
        }
        
        summary.append(String.format("总约束数: %d\n", totalConstraints));
        
        if (constraintTypeCount != null && !constraintTypeCount.isEmpty()) {
            summary.append("约束类型分布:\n");
            for (Map.Entry<ConstraintType, Integer> entry : constraintTypeCount.entrySet()) {
                summary.append(String.format("  - %s: %d\n", entry.getKey().getDescription(), entry.getValue()));
            }
        }
        
        summary.append(String.format("有目标函数: %s\n", hasObjectiveFunction ? "是" : "否"));
        
        if (matrixDensity != null) {
            summary.append(String.format("矩阵稠密度: %.2f%%\n", matrixDensity * 100));
        }
        
        if (nonZeroCoefficients != null) {
            summary.append(String.format("非零系数数: %d\n", nonZeroCoefficients));
        }
        
        return summary.toString();
    }
}