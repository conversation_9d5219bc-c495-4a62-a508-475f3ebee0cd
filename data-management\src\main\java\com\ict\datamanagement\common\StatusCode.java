package com.ict.datamanagement.common;

import lombok.Data;

public enum StatusCode {

    SUCCESS(20000,"ok"),
    Parameter_ERROR(40001,"请求参数错误"),

    SYSTEM_ERROR(50001,"系统异常");




    private int code;

    private String message;

    StatusCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
