package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.optim.PointValuePair;
import org.apache.commons.math3.optim.linear.*;
import org.apache.commons.math3.optim.nonlinear.scalar.GoalType;

import java.util.*;

/**
 * Apache Commons Math 求解器实现
 * 
 * 基于Apache Commons Math库的线性规划求解器
 * 注意：仅支持纯线性规划（不支持整数变量）
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
public class ApacheCommonsMathSolver implements MILPSolver {
    
    private SolverParameters parameters;
    private static final String SOLVER_NAME = "Apache Commons Math";
    private static final String SOLVER_VERSION = "3.6.1";
    
    public ApacheCommonsMathSolver() {
        this.parameters = SolverParameters.createDefault();
    }
    
    @Override
    public MILPSolution solve(MILPProblem problem) {
        log.info("🔧 开始求解MILP问题: {} (使用Apache Commons Math)", problem.getProblemId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证问题是否支持
            if (!supports(problem)) {
                return createErrorSolution("Apache Commons Math不支持该问题类型", startTime);
            }
            
            // 验证问题完整性
            ValidationResult validation = problem.validate();
            if (!validation.isValid()) {
                return createErrorSolution("问题定义不完整: " + validation.getErrors(), startTime);
            }
            
            // 构建Apache Commons Math问题
            LinearObjectiveFunction objectiveFunction = buildObjectiveFunction(problem);
            Collection<org.apache.commons.math3.optim.linear.LinearConstraint> constraints = buildConstraints(problem);
            
            // 创建求解器
            SimplexSolver solver = new SimplexSolver();
            
            // 确定目标类型
            GoalType goalType = problem.getObjectiveFunction().getType() == MILPProblem.ObjectiveType.MINIMIZE ?
                GoalType.MINIMIZE : GoalType.MAXIMIZE;
            
            log.debug("   构建完成: 变量数={}, 约束数={}, 目标={}", 
                problem.getVariables().size(), constraints.size(), goalType);
            
            // 求解
            PointValuePair solution = solver.optimize(
                objectiveFunction,
                new LinearConstraintSet(constraints),
                goalType,
                new NonNegativeConstraint(true)
            );
            
            long solvingTime = System.currentTimeMillis() - startTime;
            
            // 构建解结果
            return buildSolution(problem, solution, solvingTime);
            
        } catch (Exception e) {
            log.error("❌ Apache Commons Math求解失败", e);
            return createErrorSolution("求解过程异常: " + e.getMessage(), startTime);
        }
    }
    
    /**
     * 构建目标函数
     */
    private LinearObjectiveFunction buildObjectiveFunction(MILPProblem problem) {
        ObjectiveFunction objFunc = problem.getObjectiveFunction();
        List<String> variableOrder = new ArrayList<>(problem.getVariables().keySet());
        
        double[] coefficients = new double[variableOrder.size()];
        for (int i = 0; i < variableOrder.size(); i++) {
            String varName = variableOrder.get(i);
            coefficients[i] = objFunc.getCoefficients().getOrDefault(varName, 0.0);
        }
        
        return new LinearObjectiveFunction(coefficients, objFunc.getConstant());
    }
    
    /**
     * 构建约束条件
     */
    private Collection<org.apache.commons.math3.optim.linear.LinearConstraint> buildConstraints(MILPProblem problem) {
        List<org.apache.commons.math3.optim.linear.LinearConstraint> constraints = new ArrayList<>();
        List<String> variableOrder = new ArrayList<>(problem.getVariables().keySet());
        
        for (com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.LinearConstraint milpConstraint : 
             problem.getConstraints().values()) {
            
            // 构建约束系数数组
            double[] coefficients = new double[variableOrder.size()];
            for (int i = 0; i < variableOrder.size(); i++) {
                String varName = variableOrder.get(i);
                coefficients[i] = milpConstraint.getCoefficients().getOrDefault(varName, 0.0);
            }
            
            // 转换约束类型
            Relationship relationship;
            double rhs = milpConstraint.getRightHandSide();
            
            switch (milpConstraint.getType()) {
                case LESS_EQUAL:
                    relationship = Relationship.LEQ;
                    break;
                case GREATER_EQUAL:
                    relationship = Relationship.GEQ;
                    break;
                case EQUAL:
                    relationship = Relationship.EQ;
                    break;
                case RANGE:
                    // Apache Commons Math不直接支持范围约束，需要分解为两个约束
                    if (milpConstraint.getLowerBound() != null) {
                        constraints.add(new org.apache.commons.math3.optim.linear.LinearConstraint(coefficients, Relationship.GEQ, milpConstraint.getLowerBound()));
                    }
                    if (milpConstraint.getUpperBound() != null) {
                        constraints.add(new org.apache.commons.math3.optim.linear.LinearConstraint(coefficients, Relationship.LEQ, milpConstraint.getUpperBound()));
                    }
                    continue;
                default:
                    throw new IllegalArgumentException("不支持的约束类型: " + milpConstraint.getType());
            }
            
            constraints.add(new org.apache.commons.math3.optim.linear.LinearConstraint(coefficients, relationship, rhs));
        }
        
        return constraints;
    }
    
    /**
     * 构建求解结果
     */
    private MILPSolution buildSolution(MILPProblem problem, PointValuePair solution, long solvingTime) {
        List<String> variableOrder = new ArrayList<>(problem.getVariables().keySet());
        Map<String, Double> variableValues = new HashMap<>();
        
        double[] solutionPoint = solution.getPoint();
        for (int i = 0; i < variableOrder.size(); i++) {
            variableValues.put(variableOrder.get(i), solutionPoint[i]);
        }
        
        return MILPSolution.builder()
            .solutionStatus(MILPProblem.SolutionStatus.OPTIMAL)
            .objectiveValue(solution.getValue())
            .variableValues(variableValues)
            .solvingTimeMs(solvingTime)
            .solverName(SOLVER_NAME)
            .statusMessage("Apache Commons Math求解成功")
            .build();
    }
    
    /**
     * 创建错误解
     */
    private MILPSolution createErrorSolution(String errorMessage, long startTime) {
        return MILPSolution.builder()
            .solutionStatus(MILPProblem.SolutionStatus.ERROR)
            .solvingTimeMs(System.currentTimeMillis() - startTime)
            .solverName(SOLVER_NAME)
            .statusMessage(errorMessage)
            .build();
    }
    
    @Override
    public void setParameters(SolverParameters parameters) {
        this.parameters = parameters;
    }
    
    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }
    
    @Override
    public String getSolverVersion() {
        return SOLVER_VERSION;
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // 检查Apache Commons Math类是否可用
            Class.forName("org.apache.commons.math3.optim.linear.SimplexSolver");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    @Override
    public boolean supports(MILPProblem problem) {
        // Apache Commons Math 只支持纯线性规划
        for (MILPVariable variable : problem.getVariables().values()) {
            if (variable.getType() != MILPProblem.VariableType.CONTINUOUS) {
                log.warn("Apache Commons Math不支持整数变量: {}", variable.getName());
                return false;
            }
        }
        
        // 检查约束类型支持
        for (com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.LinearConstraint constraint : 
             problem.getConstraints().values()) {
            // 所有基本约束类型都支持
        }
        
        return true;
    }
    
    @Override
    public SolverCapabilities getCapabilities() {
        Set<MILPProblem.VariableType> variableTypes = new HashSet<>();
        variableTypes.add(MILPProblem.VariableType.CONTINUOUS);
        
        Set<String> constraintTypes = new HashSet<>();
        constraintTypes.add("LESS_EQUAL");
        constraintTypes.add("GREATER_EQUAL");
        constraintTypes.add("EQUAL");
        constraintTypes.add("RANGE");
        
        Set<MILPProblem.ObjectiveType> objectiveTypes = new HashSet<>();
        objectiveTypes.add(MILPProblem.ObjectiveType.MINIMIZE);
        objectiveTypes.add(MILPProblem.ObjectiveType.MAXIMIZE);
        
        return SolverCapabilities.builder()
            .supportedVariableTypes(variableTypes)
            .supportedConstraintTypes(constraintTypes)
            .supportedObjectiveTypes(objectiveTypes)
            .maxVariables(10000)  // 理论上限，实际受内存限制
            .maxConstraints(10000)
            .supportsIntegerProgramming(false)  // 不支持整数规划
            .supportsQuadraticProgramming(false)
            .supportsMultiObjective(false)
            .supportsParallel(false)
            .supportsSensitivityAnalysis(false)
            .supportsDualSolution(false)
            .tier(SolverCapabilities.SolverTier.OPEN_SOURCE_BASIC)
            .licenseType(SolverCapabilities.LicenseType.APACHE)
            .description("Apache Commons Math 单纯形法求解器")
            .build();
    }
}