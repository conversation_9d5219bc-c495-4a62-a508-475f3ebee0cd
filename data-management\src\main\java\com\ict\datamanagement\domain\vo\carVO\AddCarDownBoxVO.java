package com.ict.datamanagement.domain.vo.carVO;

import com.ict.datamanagement.domain.dto.car.LicensePlateNumbersDTO;
import com.ict.datamanagement.domain.entity.CarDriver;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AddCarDownBoxVO {
    //驾驶人名称
    private List<CarDriver> carDriver;
    //状态
    private List<String> status;
    //配送域
    private List<String> deliveryList;
}

