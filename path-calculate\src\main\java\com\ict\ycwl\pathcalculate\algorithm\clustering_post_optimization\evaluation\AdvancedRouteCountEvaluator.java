package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优秀的路线数量评估算法
 * 
 * 基于工作量分析、约束违反分析、效率理论、数学建模的4维综合评估
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class AdvancedRouteCountEvaluator {
    
    // 评估参数常量
    private static final double IDEAL_WORK_TIME_MINUTES = 350.0;     // 理想工作时间
    private static final double MAX_WORK_TIME_MINUTES = 450.0;       // 最大工作时间约束
    private static final double MAX_ROUTE_COUNT = 130;               // 最大路线数量
    private static final double TIME_GAP_WARNING_THRESHOLD = 60.0;   // 时间差异预警阈值
    
    // 评估权重配置
    private static final double WORKLOAD_WEIGHT = 0.3;      // 工作量分析权重
    private static final double CONSTRAINT_WEIGHT = 0.4;    // 约束分析权重（最重要）
    private static final double EFFICIENCY_WEIGHT = 0.2;    // 效率分析权重
    private static final double MODEL_WEIGHT = 0.1;         // 数学模型分析权重
    
    /**
     * 核心评估方法：判断中转站路线数量是否合理
     */
    public RouteCountEvaluation evaluateDepotRouteCount(
        TransitDepot depot,
        List<List<Accumulation>> currentRoutes,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        long startTime = System.currentTimeMillis();
        log.info("🔍 开始评估中转站 {} 的路线数量合理性 (当前{}条路线)", 
            depot.getTransitDepotName(), currentRoutes.size());
        
        try {
            // 第1步：工作量理论分析
            WorkloadAnalysis workloadAnalysis = analyzeWorkloadDistribution(
                depot, currentRoutes, timeMatrix);
            log.debug("   完成工作量分析：负载均衡指数 {:.3f}", workloadAnalysis.getLoadBalanceIndex());
            
            // 第2步：约束违反分析  
            ConstraintViolationAnalysis constraintAnalysis = analyzeConstraintViolations(
                currentRoutes, depot, timeMatrix);
            log.debug("   完成约束分析：违反率 {:.1f}%", constraintAnalysis.getViolationRate() * 100);
            
            // 第3步：效率理论分析
            EfficiencyAnalysis efficiencyAnalysis = analyzeRouteEfficiency(
                currentRoutes, depot, timeMatrix);
            log.debug("   完成效率分析：平均效率 {:.3f}", efficiencyAnalysis.getAverageEfficiency());
            
            // 第4步：数学建模分析
            MathematicalModelAnalysis modelAnalysis = performMathematicalAnalysis(
                depot, currentRoutes, timeMatrix);
            log.debug("   完成数学模型分析：综合评分 {:.3f}", modelAnalysis.getOverallModelScore());
            
            // 第5步：综合决策
            RouteCountRecommendation recommendation = generateComprehensiveRecommendation(
                workloadAnalysis, constraintAnalysis, efficiencyAnalysis, modelAnalysis);
            
            long evaluationTime = System.currentTimeMillis() - startTime;
            
            // 构建评估结果
            RouteCountEvaluation evaluation = RouteCountEvaluation.builder()
                .depot(depot)
                .currentRouteCount(currentRoutes.size())
                .workloadAnalysis(workloadAnalysis)
                .constraintAnalysis(constraintAnalysis)
                .efficiencyAnalysis(efficiencyAnalysis)
                .modelAnalysis(modelAnalysis)
                .recommendation(recommendation)
                .evaluationTimeMs(evaluationTime)
                .confidence(recommendation.getConfidence())
                .summary(generateEvaluationSummary(workloadAnalysis, constraintAnalysis, recommendation))
                .build();
            
            log.info("✅ 路线数量评估完成：{} (耗时{}ms)", 
                recommendation.getRecommendedAction().getDescription(), evaluationTime);
            
            return evaluation;
            
        } catch (Exception e) {
            log.error("❌ 路线数量评估失败", e);
            
            // 返回默认评估结果
            return RouteCountEvaluation.builder()
                .depot(depot)
                .currentRouteCount(currentRoutes.size())
                .evaluationTimeMs(System.currentTimeMillis() - startTime)
                .confidence(0.0)
                .summary("评估过程发生异常：" + e.getMessage())
                .recommendation(RouteCountRecommendation.builder()
                    .recommendedAction(RouteCountAction.MAINTAIN)
                    .currentRouteCount(currentRoutes.size())
                    .recommendedRouteCount(currentRoutes.size())
                    .routeCountAdjustment(0)
                    .comprehensiveScore(0.0)
                    .confidence(0.0)
                    .reasoning("评估异常，建议保持现状")
                    .build())
                .build();
        }
    }
    
    /**
     * 第1步：工作量理论分析
     * 基于队列理论和负载均衡理论进行分析
     */
    private WorkloadAnalysis analyzeWorkloadDistribution(
        TransitDepot depot,
        List<List<Accumulation>> currentRoutes,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        // 计算各路线工作时间
        double[] routeWorkTimes = currentRoutes.stream()
            .mapToDouble(route -> calculateRouteWorkTime(route, depot, timeMatrix))
            .toArray();
        
        // 计算总工作量
        double totalWorkload = Arrays.stream(routeWorkTimes).sum();
        
        // 计算理想路线数量（基于业务公式）
        double idealRouteCount = totalWorkload / IDEAL_WORK_TIME_MINUTES;
        
        // 计算统计指标
        double mean = Arrays.stream(routeWorkTimes).average().orElse(0);
        double variance = calculateVariance(routeWorkTimes);
        double standardDeviation = Math.sqrt(variance);
        double coefficientOfVariation = mean > 0 ? standardDeviation / mean : 0;
        
        // 负载均衡指数（0-1，越接近1越均衡）
        double loadBalanceIndex = Math.max(0.0, 1.0 - coefficientOfVariation);
        
        // 计算工作量密度（工作量/路线数）
        double workloadDensity = currentRoutes.size() > 0 ? totalWorkload / currentRoutes.size() : 0.0;
        
        // 应用队列理论的利特尔定律进行优化
        double optimizedIdealRouteCount = applyQueueingTheoryOptimization(
            totalWorkload, currentRoutes.size(), routeWorkTimes);
        
        return WorkloadAnalysis.builder()
            .totalWorkload(totalWorkload)
            .currentRouteCount(currentRoutes.size())
            .idealRouteCount(optimizedIdealRouteCount)
            .averageWorkTime(mean)
            .workTimeVariance(variance)
            .standardDeviation(standardDeviation)
            .coefficientOfVariation(coefficientOfVariation)
            .loadBalanceIndex(loadBalanceIndex)
            .routeCountGap(currentRoutes.size() - optimizedIdealRouteCount)
            .workloadDensity(workloadDensity)
            .build();
    }
    
    /**
     * 第2步：约束违反分析
     * 分析当前路线数量下的约束违反情况
     */
    private ConstraintViolationAnalysis analyzeConstraintViolations(
        List<List<Accumulation>> currentRoutes,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        // 分析多层次约束违反
        List<ConstraintViolation> timeViolations = new ArrayList<>();
        List<ConstraintViolation> balanceViolations = new ArrayList<>();
        List<ConstraintViolation> utilityViolations = new ArrayList<>();
        
        for (int i = 0; i < currentRoutes.size(); i++) {
            double workTime = calculateRouteWorkTime(currentRoutes.get(i), depot, timeMatrix);
            
            // 1. 硬约束：450分钟上限违反
            if (workTime > MAX_WORK_TIME_MINUTES) {
                ConstraintViolation violation = ConstraintViolation.builder()
                    .routeIndex(i)
                    .violationType("450分钟时间约束")
                    .currentValue(workTime)
                    .constraintLimit(MAX_WORK_TIME_MINUTES)
                    .violationAmount(workTime - MAX_WORK_TIME_MINUTES)
                    .violationRatio((workTime - MAX_WORK_TIME_MINUTES) / MAX_WORK_TIME_MINUTES)
                    .severity(determineViolationSeverity(workTime, MAX_WORK_TIME_MINUTES))
                    .build();
                timeViolations.add(violation);
            }
            
            // 2. 软约束：350分钟理想时间偏差分析
            double deviationFromIdeal = Math.abs(workTime - IDEAL_WORK_TIME_MINUTES);
            if (deviationFromIdeal > IDEAL_WORK_TIME_MINUTES * 0.2) { // 超过20%偏差
                balanceViolations.add(ConstraintViolation.builder()
                    .routeIndex(i)
                    .violationType("理想工作时间偏差")
                    .currentValue(workTime)
                    .constraintLimit(IDEAL_WORK_TIME_MINUTES)
                    .violationAmount(deviationFromIdeal)
                    .violationRatio(deviationFromIdeal / IDEAL_WORK_TIME_MINUTES)
                    .severity(ConstraintViolation.ViolationSeverity.MODERATE)
                    .build());
            }
            
            // 3. 利用率约束：过低利用率检查
            if (workTime < IDEAL_WORK_TIME_MINUTES * 0.5) { // 利用率低于50%
                utilityViolations.add(ConstraintViolation.builder()
                    .routeIndex(i)
                    .violationType("工作时间利用率过低")
                    .currentValue(workTime)
                    .constraintLimit(IDEAL_WORK_TIME_MINUTES * 0.5)
                    .violationAmount(IDEAL_WORK_TIME_MINUTES * 0.5 - workTime)
                    .violationRatio((IDEAL_WORK_TIME_MINUTES * 0.5 - workTime) / (IDEAL_WORK_TIME_MINUTES * 0.5))
                    .severity(ConstraintViolation.ViolationSeverity.MINOR)
                    .build());
            }
        }
        
        // 合并所有违反类型
        List<ConstraintViolation> allViolations = new ArrayList<>();
        allViolations.addAll(timeViolations);
        allViolations.addAll(balanceViolations);
        allViolations.addAll(utilityViolations);
        
        // 计算时间差异
        double[] workTimes = currentRoutes.stream()
            .mapToDouble(route -> calculateRouteWorkTime(route, depot, timeMatrix))
            .toArray();
        
        double maxTime = Arrays.stream(workTimes).max().orElse(0);
        double minTime = Arrays.stream(workTimes).min().orElse(0);
        double timeGap = maxTime - minTime;
        
        // 计算综合违反严重程度
        double totalHardViolationTime = timeViolations.stream()
            .mapToDouble(ConstraintViolation::getViolationAmount)
            .sum();
            
        double totalSoftViolationTime = balanceViolations.stream()
            .mapToDouble(ConstraintViolation::getViolationAmount)
            .sum();
        
        // 加权计算综合违反严重程度（硬约束权重更高）
        double violationSeverity = currentRoutes.size() > 0 ? 
            (totalHardViolationTime * 2.0 + totalSoftViolationTime * 0.5) / 
            (currentRoutes.size() * MAX_WORK_TIME_MINUTES) : 0.0;
        
        // 计算约束满足率
        int satisfiedRoutes = currentRoutes.size() - timeViolations.size();
        double constraintSatisfactionRate = currentRoutes.size() > 0 ? 
            (double) satisfiedRoutes / currentRoutes.size() : 1.0;
        
        // 计算预期需要增加的路线数
        int estimatedAdditionalRoutes = calculateEstimatedAdditionalRoutes(
            timeViolations, workTimes, depot);
        
        log.debug("   约束违反分析：硬违反{}条，软违反{}条，严重程度{:.3f}", 
            timeViolations.size(), balanceViolations.size(), violationSeverity);
        
        return ConstraintViolationAnalysis.builder()
            .timeViolations(allViolations) // 使用全部违反类型
            .violationCount(timeViolations.size()) // 仅统计硬约束违反
            .violationRate((double)timeViolations.size() / Math.max(1, currentRoutes.size()))
            .totalViolationTime(totalHardViolationTime)
            .violationSeverity(violationSeverity)
            .maxWorkTime(maxTime)
            .minWorkTime(minTime)
            .timeGap(timeGap)
            .isTimeGapExcessive(timeGap > TIME_GAP_WARNING_THRESHOLD)
            .build();
    }
    
    /**
     * 第3步：效率理论分析
     * 基于运筹学理论分析路线效率
     */
    private EfficiencyAnalysis analyzeRouteEfficiency(
        List<List<Accumulation>> currentRoutes,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        log.debug("   开始效率理论分析：{}条路线", currentRoutes.size());
        
        // 第1阶段：计算各路线效率指标
        List<EfficiencyAnalysis.RouteEfficiencyMetrics> routeMetrics = new ArrayList<>();
        for (int i = 0; i < currentRoutes.size(); i++) {
            List<Accumulation> route = currentRoutes.get(i);
            routeMetrics.add(calculateAdvancedRouteEfficiency(route, i, depot, timeMatrix, currentRoutes));
        }
        
        // 第2阶段：系统级效率分析
        double averageEfficiency = routeMetrics.stream()
            .mapToDouble(EfficiencyAnalysis.RouteEfficiencyMetrics::getEfficiencyScore)
            .average().orElse(0);
        
        double efficiencyVariance = calculateVariance(
            routeMetrics.stream()
                .mapToDouble(EfficiencyAnalysis.RouteEfficiencyMetrics::getEfficiencyScore)
                .toArray());
        
        // 第3阶段：运筹学理论的边际效率分析
        double marginalEfficiencyIncrease = calculateMarginalEfficiencyChange(
            currentRoutes, depot, timeMatrix, 1);
        double marginalEfficiencyDecrease = calculateMarginalEfficiencyChange(
            currentRoutes, depot, timeMatrix, -1);
        
        // 第4阶段：系统最优性判断
        boolean isEfficiencyOptimal = isSystemEfficiencyOptimal(routeMetrics, 
            marginalEfficiencyIncrease, marginalEfficiencyDecrease);
        
        // 第5阶段：效率改进潜力评估
        double improvementPotential = calculateImprovementPotential(routeMetrics, averageEfficiency);
        
        log.debug("   效率分析完成：平均效率{:.3f}，边际效率增减{:.3f}/{:.3f}，最优状态{}", 
            averageEfficiency, marginalEfficiencyIncrease, marginalEfficiencyDecrease, isEfficiencyOptimal);
        
        return EfficiencyAnalysis.builder()
            .routeMetrics(routeMetrics)
            .averageEfficiency(averageEfficiency)
            .efficiencyVariance(efficiencyVariance)
            .marginalEfficiencyIncrease(marginalEfficiencyIncrease)
            .marginalEfficiencyDecrease(marginalEfficiencyDecrease)
            .isEfficiencyOptimal(isEfficiencyOptimal)
            .improvementPotential(improvementPotential)
            .build();
    }
    
    /**
     * 第4步：数学建模分析
     * 使用运筹学模型进行理论最优分析
     */
    private MathematicalModelAnalysis performMathematicalAnalysis(
        TransitDepot depot,
        List<List<Accumulation>> currentRoutes,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        // Bin Packing理论分析
        MathematicalModelAnalysis.BinPackingAnalysis binPackingResult = 
            analyzeBinPackingOptimal(currentRoutes, depot, timeMatrix);
        
        // 负载均衡理论分析
        MathematicalModelAnalysis.LoadBalancingAnalysis loadBalancingResult = 
            analyzeLoadBalancingOptimal(currentRoutes, depot, timeMatrix);
        
        // 排队理论分析
        MathematicalModelAnalysis.QueueingTheoryAnalysis queueingResult = 
            analyzeQueueingTheoryOptimal(currentRoutes, depot, timeMatrix);
        
        log.debug("   数学建模分析：Bin Packing效率{:.3f}，负载均衡指数{:.3f}，排队理论效率{:.3f}", 
            binPackingResult.getPackingEfficiency(), 
            loadBalancingResult.getCurrentBalanceIndex(),
            queueingResult.getServiceEfficiency());
        
        // 理论最优性评估
        MathematicalModelAnalysis.OptimalityAssessment optimalityAssessment = 
            assessTheoreticalOptimality(binPackingResult, loadBalancingResult, queueingResult);
        
        // 计算综合评分（基于运筹学理论权重）
        double overallScore = calculateMathematicalModelScore(
            binPackingResult, loadBalancingResult, queueingResult, optimalityAssessment);
        
        return MathematicalModelAnalysis.builder()
            .binPackingAnalysis(binPackingResult)
            .loadBalancingAnalysis(loadBalancingResult)
            .queueingTheoryAnalysis(queueingResult)
            .optimalityAssessment(optimalityAssessment)
            .overallModelScore(overallScore)
            .build();
    }
    
    // ========== 辅助计算方法 ==========
    
    /**
     * 计算路线工作时间
     */
    private double calculateRouteWorkTime(List<Accumulation> route, TransitDepot depot, 
                                        Map<String, TimeInfo> timeMatrix) {
        if (route.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        
        // 计算所有聚集区的配送时间
        for (Accumulation acc : route) {
            totalTime += acc.getDeliveryTime();
            
            // 添加往返时间（简化计算，实际应该考虑TSP路径）
            String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null) {
                totalTime += timeInfo.getTravelTime() * 2; // 往返时间
            }
        }
        
        return totalTime;
    }
    
    /**
     * 计算方差
     */
    private double calculateVariance(double[] values) {
        if (values.length <= 1) {
            return 0.0;
        }
        
        double mean = Arrays.stream(values).average().orElse(0);
        double variance = 0.0;
        for (double value : values) {
            variance += Math.pow(value - mean, 2);
        }
        return variance / values.length;
    }
    
    /**
     * 计算高级路线效率（基于运筹学理论）
     */
    private EfficiencyAnalysis.RouteEfficiencyMetrics calculateAdvancedRouteEfficiency(
        List<Accumulation> route, int routeIndex, TransitDepot depot, 
        Map<String, TimeInfo> timeMatrix, List<List<Accumulation>> allRoutes
    ) {
        
        double workTime = calculateRouteWorkTime(route, depot, timeMatrix);
        
        // 1. 时间利用率分析（基于理想工作时间）
        double timeUtilization = calculateTimeUtilization(workTime);
        
        // 2. 聚集区密度效率（考虑TSP优化理论）
        double densityEfficiency = calculateDensityEfficiency(route, workTime);
        
        // 3. 地理效率（基于凸包和重心理论）
        double geographicEfficiency = calculateGeographicEfficiency(route, depot, timeMatrix);
        
        // 4. 路径效率（基于旅行商问题理论）
        double pathEfficiency = calculatePathEfficiency(route, depot, timeMatrix);
        
        // 5. 负载均衡效率（相对于系统平均水平）
        double balanceEfficiency = calculateBalanceEfficiency(workTime, allRoutes);
        
        // 6. 综合效率评分（运筹学加权模型）
        double efficiencyScore = calculateWeightedEfficiencyScore(
            timeUtilization, densityEfficiency, geographicEfficiency, 
            pathEfficiency, balanceEfficiency);
        
        return EfficiencyAnalysis.RouteEfficiencyMetrics.builder()
            .routeIndex(routeIndex)
            .timeUtilization(timeUtilization)
            .densityEfficiency(densityEfficiency)
            .geographicEfficiency(geographicEfficiency)
            .efficiencyScore(efficiencyScore)
            .bottleneck(determineAdvancedEfficiencyBottleneck(
                timeUtilization, densityEfficiency, geographicEfficiency, 
                pathEfficiency, balanceEfficiency))
            .improvementSuggestion(generateAdvancedImprovementSuggestion(
                timeUtilization, densityEfficiency, geographicEfficiency, routeIndex))
            .build();
    }
    
    /**
     * 计算时间利用率（考虑理想利用率曲线）
     */
    private double calculateTimeUtilization(double workTime) {
        // 基于效用理论的S型曲线：在理想时间附近效率最高
        double ratio = workTime / IDEAL_WORK_TIME_MINUTES;
        
        if (ratio <= 0.7) {
            // 利用率过低：线性递增
            return ratio / 0.7 * 0.8;
        } else if (ratio <= 1.2) {
            // 理想区间：高效率
            return 0.8 + (ratio - 0.7) / 0.5 * 0.2;
        } else if (ratio <= 1.5) {
            // 过载区间：效率递减
            return 1.0 - (ratio - 1.2) / 0.3 * 0.3;
        } else {
            // 严重过载：效率急剧下降
            return Math.max(0.1, 0.7 - (ratio - 1.5) * 0.5);
        }
    }
    
    /**
     * 计算聚集区密度效率（基于TSP理论）
     */
    private double calculateDensityEfficiency(List<Accumulation> route, double workTime) {
        if (route.isEmpty() || workTime <= 0) {
            return 0.0;
        }
        
        // 基础密度：聚集区数量/工作时间
        double basicDensity = route.size() / workTime;
        
        // TSP理论优化：考虑聚集区数量的边际收益递减
        // 根据Christofides算法理论，最优聚集区数量存在上限
        double optimalDensityThreshold = 0.02; // 每分钟0.02个聚集区为理想密度
        
        if (basicDensity <= optimalDensityThreshold) {
            return basicDensity / optimalDensityThreshold;  // 线性增长
        } else {
            // 超过最优密度后边际收益递减
            double excessRatio = basicDensity / optimalDensityThreshold - 1.0;
            return 1.0 - excessRatio * 0.3; // 递减但不会完全归零
        }
    }
    
    /**
     * 计算地理效率（基于凸包理论和重心分析）
     */
    private double calculateGeographicEfficiency(List<Accumulation> route, 
                                               TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (route.isEmpty()) {
            return 1.0;
        }
        
        // 1. 计算路线的地理紧密度（基于凸包面积）
        double compactnessScore = calculateRouteCompactness(route, depot);
        
        // 2. 计算距离效率（实际距离 vs 理想距离）
        double distanceEfficiency = calculateDistanceEfficiency(route, depot, timeMatrix);
        
        // 3. 计算重心偏移程度
        double centroidAlignment = calculateCentroidAlignment(route, depot);
        
        // 综合地理效率评分
        return (compactnessScore * 0.4 + distanceEfficiency * 0.4 + centroidAlignment * 0.2);
    }
    
    /**
     * 计算路径效率（基于TSP最优性）
     */
    private double calculatePathEfficiency(List<Accumulation> route, 
                                         TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (route.size() <= 2) {
            return 1.0; // 简单路径认为是最优的
        }
        
        // 计算当前路径总时间
        double currentPathTime = 0.0;
        for (int i = 0; i < route.size(); i++) {
            String fromId = (i == 0) ? String.valueOf(depot.getTransitDepotId()) : String.valueOf(route.get(i-1).getAccumulationId());
            String toId = String.valueOf(route.get(i).getAccumulationId());
            String key = fromId + "-" + toId;
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null) {
                currentPathTime += timeInfo.getTravelTime();
            }
        }
        
        // 估算理论最优路径时间（使用最近邻启发式）
        double nearestNeighborTime = estimateNearestNeighborTime(route, depot, timeMatrix);
        
        // 路径效率 = 理论最优时间 / 实际路径时间
        if (currentPathTime > 0) {
            return Math.min(1.0, nearestNeighborTime / currentPathTime);
        } else {
            return 1.0;
        }
    }
    
    /**
     * 计算负载均衡效率
     */
    private double calculateBalanceEfficiency(double workTime, List<List<Accumulation>> allRoutes) {
        if (allRoutes.isEmpty()) {
            return 1.0;
        }
        
        // 计算系统平均工作时间
        double avgWorkTime = allRoutes.stream()
            .mapToDouble(route -> route.size() * 15.0) // 简化估算
            .average().orElse(IDEAL_WORK_TIME_MINUTES);
        
        // 计算相对偏差
        double deviation = Math.abs(workTime - avgWorkTime) / avgWorkTime;
        
        // 偏差越小，均衡效率越高
        return Math.max(0.0, 1.0 - deviation);
    }
    
    /**
     * 计算加权效率评分
     */
    private double calculateWeightedEfficiencyScore(double timeUtilization, double densityEfficiency,
                                                   double geographicEfficiency, double pathEfficiency,
                                                   double balanceEfficiency) {
        // 基于运筹学理论的权重分配
        return timeUtilization * 0.3 +        // 时间利用率最重要
               densityEfficiency * 0.25 +      // 密度效率次之
               geographicEfficiency * 0.25 +   // 地理效率重要
               pathEfficiency * 0.15 +         // 路径效率
               balanceEfficiency * 0.05;       // 负载均衡效率
    }
    
    /**
     * 确定效率瓶颈
     */
    private EfficiencyAnalysis.EfficiencyBottleneck determineEfficiencyBottleneck(
        double timeUtilization, double densityEfficiency, double geographicEfficiency
    ) {
        if (timeUtilization < 0.7) {
            return EfficiencyAnalysis.EfficiencyBottleneck.TIME_UNDERUTILIZATION;
        } else if (timeUtilization > 1.3) {
            return EfficiencyAnalysis.EfficiencyBottleneck.TIME_OVERUTILIZATION;
        } else if (densityEfficiency < 0.5) {
            return EfficiencyAnalysis.EfficiencyBottleneck.LOW_DENSITY;
        } else if (geographicEfficiency < 0.7) {
            return EfficiencyAnalysis.EfficiencyBottleneck.HIGH_GEOGRAPHIC_DISPERSION;
        } else {
            return EfficiencyAnalysis.EfficiencyBottleneck.BALANCED;
        }
    }
    
    /**
     * 生成改进建议
     */
    private String generateImprovementSuggestion(double timeUtilization, double densityEfficiency) {
        if (timeUtilization < 0.7) {
            return "考虑合并到其他路线或增加聚集区";
        } else if (timeUtilization > 1.3) {
            return "考虑拆分路线或转移部分聚集区";
        } else if (densityEfficiency < 0.5) {
            return "考虑增加聚集区密度或优化地理分布";
        } else {
            return "效率相对均衡，保持现状";
        }
    }
    
    /**
     * 应用队列理论优化路线数量计算
     * 基于利特尔定律和负载均衡理论
     */
    private double applyQueueingTheoryOptimization(double totalWorkload, int currentRouteCount, double[] routeWorkTimes) {
        // 基础理想路线数量
        double basicIdealCount = totalWorkload / IDEAL_WORK_TIME_MINUTES;
        
        // 应用利特尔定律：L = λ × W (队列长度 = 到达率 × 等待时间)
        // 在这里，我们将其转换为：路线数 = 工作量强度 × 服务时间调整因子
        
        // 计算工作量强度（单位时间内的工作量需求）
        double workloadIntensity = totalWorkload / MAX_WORK_TIME_MINUTES;
        
        // 计算当前系统的服务效率
        double currentServiceEfficiency = calculateCurrentServiceEfficiency(routeWorkTimes);
        
        // 计算负载均衡调整因子
        double balanceAdjustmentFactor = calculateBalanceAdjustmentFactor(routeWorkTimes);
        
        // 应用排队理论的稳定性约束（系统利用率不应超过0.8-0.9）
        double targetUtilization = 0.85; // 目标利用率
        double queueingOptimalCount = workloadIntensity / targetUtilization;
        
        // 综合考虑基础计算、队列理论和负载均衡
        double weightedOptimalCount = (basicIdealCount * 0.4 + 
                                     queueingOptimalCount * 0.4 + 
                                     basicIdealCount * balanceAdjustmentFactor * 0.2);
        
        log.debug("   队列理论优化：基础={:.1f}，队列理论={:.1f}，加权={:.1f}", 
            basicIdealCount, queueingOptimalCount, weightedOptimalCount);
        
        return Math.max(1.0, weightedOptimalCount);
    }
    
    /**
     * 计算当前系统服务效率
     */
    private double calculateCurrentServiceEfficiency(double[] routeWorkTimes) {
        if (routeWorkTimes.length == 0) {
            return 1.0;
        }
        
        double mean = Arrays.stream(routeWorkTimes).average().orElse(0);
        double maxTime = Arrays.stream(routeWorkTimes).max().orElse(0);
        
        // 服务效率 = 平均利用率 / 最大利用率（避免过度集中）
        if (maxTime == 0) {
            return 1.0;
        }
        
        return Math.min(1.0, mean / maxTime);
    }
    
    /**
     * 计算负载均衡调整因子
     */
    private double calculateBalanceAdjustmentFactor(double[] routeWorkTimes) {
        if (routeWorkTimes.length <= 1) {
            return 1.0;
        }
        
        double mean = Arrays.stream(routeWorkTimes).average().orElse(0);
        double variance = calculateVariance(routeWorkTimes);
        
        if (mean == 0) {
            return 1.0;
        }
        
        // 变异系数越大，说明负载越不均衡，需要更多路线来平衡
        double coefficientOfVariation = Math.sqrt(variance) / mean;
        
        // 调整因子：轻微增加路线数量以改善负载均衡
        return 1.0 + Math.min(0.3, coefficientOfVariation * 0.5);
    }
    
    /**
     * 确定约束违反严重程度
     */
    private ConstraintViolation.ViolationSeverity determineViolationSeverity(double currentValue, double limit) {
        double violationRatio = (currentValue - limit) / limit;
        
        if (violationRatio <= 0.05) {
            return ConstraintViolation.ViolationSeverity.MINOR;
        } else if (violationRatio <= 0.1) {
            return ConstraintViolation.ViolationSeverity.MODERATE;
        } else if (violationRatio <= 0.2) {
            return ConstraintViolation.ViolationSeverity.SIGNIFICANT;
        } else if (violationRatio <= 0.3) {
            return ConstraintViolation.ViolationSeverity.SEVERE;
        } else {
            return ConstraintViolation.ViolationSeverity.CRITICAL;
        }
    }
    
    /**
     * 计算预期需要增加的路线数量
     * 基于当前违反情况和负载分析
     */
    private int calculateEstimatedAdditionalRoutes(List<ConstraintViolation> violations, 
                                                  double[] workTimes, TransitDepot depot) {
        if (violations.isEmpty()) {
            return 0;
        }
        
        // 方法1：基于违反时间总量估算
        double totalExcessTime = violations.stream()
            .mapToDouble(ConstraintViolation::getViolationAmount)
            .sum();
        int routesBasedOnExcess = (int) Math.ceil(totalExcessTime / IDEAL_WORK_TIME_MINUTES);
        
        // 方法2：基于过载路线的负载转移需求
        double avgWorkTime = Arrays.stream(workTimes).average().orElse(0);
        double targetWorkTime = Math.min(IDEAL_WORK_TIME_MINUTES, avgWorkTime);
        
        int routesBasedOnLoadTransfer = 0;
        for (ConstraintViolation violation : violations) {
            if (violation.getCurrentValue() > MAX_WORK_TIME_MINUTES) {
                // 每条严重过载的路线可能需要分拆成1-2条路线
                double overloadRatio = violation.getCurrentValue() / targetWorkTime;
                routesBasedOnLoadTransfer += Math.max(1, (int) Math.ceil(overloadRatio - 1));
            }
        }
        
        // 方法3：基于系统容量分析
        double totalWorkload = Arrays.stream(workTimes).sum();
        double currentCapacity = workTimes.length * MAX_WORK_TIME_MINUTES;
        double targetCapacity = workTimes.length * IDEAL_WORK_TIME_MINUTES;
        
        int routesBasedOnCapacity = 0;
        if (totalWorkload > targetCapacity) {
            routesBasedOnCapacity = (int) Math.ceil((totalWorkload - targetCapacity) / IDEAL_WORK_TIME_MINUTES);
        }
        
        // 取三种方法的加权平均（偏向保守估计）
        int weightedEstimate = (int) Math.ceil(
            routesBasedOnExcess * 0.4 + 
            routesBasedOnLoadTransfer * 0.4 + 
            routesBasedOnCapacity * 0.2
        );
        
        log.debug("   路线需求估算：违反时间法{}条，负载转移法{}条，容量分析法{}条，加权估算{}条", 
            routesBasedOnExcess, routesBasedOnLoadTransfer, routesBasedOnCapacity, weightedEstimate);
        
        // 限制在合理范围内（最多不超过当前路线数的50%）
        int maxIncrease = Math.max(3, workTimes.length / 2);
        return Math.min(weightedEstimate, maxIncrease);
    }
    
    /**
     * 计算边际效率变化（基于运筹学边际分析理论）
     */
    private double calculateMarginalEfficiencyChange(List<List<Accumulation>> routes, TransitDepot depot, 
                                                   Map<String, TimeInfo> timeMatrix, int routeChange) {
        
        if (routes.isEmpty()) {
            return 0.0;
        }
        
        // 当前系统效率基准
        double currentSystemEfficiency = calculateCurrentSystemEfficiency(routes, depot, timeMatrix);
        
        if (routeChange > 0) {
            // 增加路线的边际效率分析
            return calculateMarginalEfficiencyIncrease(routes, depot, timeMatrix, currentSystemEfficiency);
        } else if (routeChange < 0) {
            // 减少路线的边际效率分析
            return calculateMarginalEfficiencyDecrease(routes, depot, timeMatrix, currentSystemEfficiency);
        } else {
            return 0.0; // 无变化
        }
    }
    
    /**
     * 计算增加路线的边际效率
     */
    private double calculateMarginalEfficiencyIncrease(List<List<Accumulation>> routes, TransitDepot depot,
                                                     Map<String, TimeInfo> timeMatrix, double currentEfficiency) {
        
        // 基于排队理论：增加服务器（路线）的边际效用分析
        
        // 1. 找出当前最过载的路线
        List<Accumulation> mostOverloadedRoute = findMostOverloadedRoute(routes, depot, timeMatrix);
        if (mostOverloadedRoute.isEmpty()) {
            return -0.05; // 无过载路线，增加路线可能降低效率
        }
        
        // 2. 估算分拆该路线的效率提升
        double currentRouteTime = calculateRouteWorkTime(mostOverloadedRoute, depot, timeMatrix);
        
        if (currentRouteTime <= IDEAL_WORK_TIME_MINUTES) {
            return -0.02; // 已经在理想范围内，增加路线收益有限
        }
        
        // 3. 模拟分拆后的效率提升
        double overloadRatio = currentRouteTime / MAX_WORK_TIME_MINUTES;
        if (overloadRatio > 1.0) {
            // 约束违反情况下，增加路线收益显著
            return Math.min(0.15, (overloadRatio - 1.0) * 0.3);
        } else {
            // 轻微过载情况下，增加路线收益中等
            double efficiency_gain = (currentRouteTime - IDEAL_WORK_TIME_MINUTES) / IDEAL_WORK_TIME_MINUTES * 0.1;
            return Math.max(0.0, efficiency_gain);
        }
    }
    
    /**
     * 计算减少路线的边际效率
     */
    private double calculateMarginalEfficiencyDecrease(List<List<Accumulation>> routes, TransitDepot depot,
                                                     Map<String, TimeInfo> timeMatrix, double currentEfficiency) {
        
        // 基于容量理论：减少服务器（路线）的边际成本分析
        
        // 1. 找出当前最轻载的路线
        List<Accumulation> lightestRoute = findLightestRoute(routes, depot, timeMatrix);
        if (lightestRoute.isEmpty()) {
            return -0.1; // 无轻载路线，减少路线风险较高
        }
        
        // 2. 估算合并该路线的效率影响
        double lightestRouteTime = calculateRouteWorkTime(lightestRoute, depot, timeMatrix);
        
        if (lightestRouteTime >= IDEAL_WORK_TIME_MINUTES * 0.8) {
            return -0.08; // 路线已经较重，合并风险较高
        }
        
        // 3. 找出最适合合并的目标路线
        List<Accumulation> bestMergeTarget = findBestMergeTarget(lightestRoute, routes, depot, timeMatrix);
        if (bestMergeTarget == null) {
            return -0.12; // 无合适合并目标，风险很高
        }
        
        double targetRouteTime = calculateRouteWorkTime(bestMergeTarget, depot, timeMatrix);
        double mergedRouteTime = lightestRouteTime + targetRouteTime;
        
        if (mergedRouteTime > MAX_WORK_TIME_MINUTES) {
            return -0.15; // 合并后会违反约束，收益为负
        } else if (mergedRouteTime > IDEAL_WORK_TIME_MINUTES) {
            // 合并后略微过载，但可能提高整体效率
            double overload_penalty = (mergedRouteTime - IDEAL_WORK_TIME_MINUTES) / IDEAL_WORK_TIME_MINUTES * 0.1;
            double utilization_gain = (lightestRouteTime / IDEAL_WORK_TIME_MINUTES) * 0.15; // 提高利用率的收益
            return utilization_gain - overload_penalty;
        } else {
            // 合并后仍在理想范围内，收益较好
            return (lightestRouteTime / IDEAL_WORK_TIME_MINUTES) * 0.1;
        }
    }
    
    /**
     * 判断系统效率是否最优
     */
    private boolean isSystemEfficiencyOptimal(List<EfficiencyAnalysis.RouteEfficiencyMetrics> routeMetrics,
                                            double marginalIncrease, double marginalDecrease) {
        
        // 1. 边际效率判断：增减路线的边际收益都很小
        boolean marginalOptimal = Math.abs(marginalIncrease) < 0.05 && Math.abs(marginalDecrease) < 0.05;
        
        // 2. 整体效率水平：平均效率达到较高水平
        double avgEfficiency = routeMetrics.stream()
            .mapToDouble(EfficiencyAnalysis.RouteEfficiencyMetrics::getEfficiencyScore)
            .average().orElse(0);
        boolean efficiencyHigh = avgEfficiency >= 0.85;
        
        // 3. 效率均衡性：各路线效率差异不大
        double efficiencyVariance = calculateVariance(routeMetrics.stream()
            .mapToDouble(EfficiencyAnalysis.RouteEfficiencyMetrics::getEfficiencyScore)
            .toArray());
        boolean varianceLow = efficiencyVariance < 0.04; // 方差小于0.04认为比较均衡
        
        // 4. 瓶颈分析：没有明显的效率瓶颈
        long bottleneckCount = routeMetrics.stream()
            .filter(m -> m.getBottleneck() != EfficiencyAnalysis.EfficiencyBottleneck.BALANCED)
            .count();
        boolean fewBottlenecks = bottleneckCount <= routeMetrics.size() * 0.2; // 瓶颈路线不超过20%
        
        return marginalOptimal && efficiencyHigh && varianceLow && fewBottlenecks;
    }
    
    /**
     * 计算效率改进潜力
     */
    private double calculateImprovementPotential(List<EfficiencyAnalysis.RouteEfficiencyMetrics> routeMetrics,
                                               double averageEfficiency) {
        if (routeMetrics.isEmpty()) {
            return 0.0;
        }
        
        // 1. 基础改进潜力：距离完美效率的差距
        double basicPotential = 1.0 - averageEfficiency;
        
        // 2. 瓶颈改进潜力：分析各种瓶颈的改进空间
        double bottleneckPotential = calculateBottleneckImprovementPotential(routeMetrics);
        
        // 3. 不均衡改进潜力：效率分布不均的改进空间
        double imbalancePotential = calculateImbalanceImprovementPotential(routeMetrics);
        
        // 4. 系统优化潜力：整体系统优化的空间
        double systemPotential = calculateSystemOptimizationPotential(routeMetrics, averageEfficiency);
        
        // 综合改进潜力评分
        return Math.min(1.0, basicPotential * 0.3 + bottleneckPotential * 0.3 + 
                             imbalancePotential * 0.2 + systemPotential * 0.2);
    }
    
    // ========== 数学建模分析辅助方法实现 ==========
    
    /**
     * 评估理论最优性
     */
    private MathematicalModelAnalysis.OptimalityAssessment assessTheoreticalOptimality(
        MathematicalModelAnalysis.BinPackingAnalysis binPacking,
        MathematicalModelAnalysis.LoadBalancingAnalysis loadBalancing,
        MathematicalModelAnalysis.QueueingTheoryAnalysis queueing) {
        
        // 计算距离理论最优的差距
        double binPackingGap = binPacking.isCurrentOptimal() ? 0.0 : 
            (double) binPacking.getOptimizationPotential() / binPacking.getCurrentBinCount();
        
        double loadBalancingGap = Math.abs(loadBalancing.getCurrentBalanceIndex() - 
                                         loadBalancing.getTheoreticalOptimalBalance());
        
        double queueingGap = Math.abs(queueing.getSystemUtilization() - 0.8); // 理想利用率80%
        
        double optimalityGap = (binPackingGap + loadBalancingGap + queueingGap) / 3.0;
        
        // 计算改进潜力
        double improvementPotential = Math.min(1.0, optimalityGap * 1.5);
        
        // 计算约束紧度
        double constraintTightness = queueing.getSystemUtilization();
        
        // 确定解的质量等级
        MathematicalModelAnalysis.SolutionQuality solutionQuality;
        if (optimalityGap <= 0.05) {
            solutionQuality = MathematicalModelAnalysis.SolutionQuality.OPTIMAL;
        } else if (optimalityGap <= 0.1) {
            solutionQuality = MathematicalModelAnalysis.SolutionQuality.HIGH;
        } else if (optimalityGap <= 0.2) {
            solutionQuality = MathematicalModelAnalysis.SolutionQuality.MEDIUM;
        } else if (optimalityGap <= 0.3) {
            solutionQuality = MathematicalModelAnalysis.SolutionQuality.LOW;
        } else {
            solutionQuality = MathematicalModelAnalysis.SolutionQuality.POOR;
        }
        
        return MathematicalModelAnalysis.OptimalityAssessment.builder()
            .optimalityGap(optimalityGap)
            .improvementPotential(improvementPotential)
            .constraintTightness(constraintTightness)
            .solutionQuality(solutionQuality)
            .build();
    }
    
    /**
     * 计算数学模型综合评分
     */
    private double calculateMathematicalModelScore(
        MathematicalModelAnalysis.BinPackingAnalysis binPacking,
        MathematicalModelAnalysis.LoadBalancingAnalysis loadBalancing,
        MathematicalModelAnalysis.QueueingTheoryAnalysis queueing,
        MathematicalModelAnalysis.OptimalityAssessment optimality) {
        
        // 各模型权重分配
        double binPackingWeight = 0.35;
        double loadBalancingWeight = 0.35;
        double queueingWeight = 0.20;
        double optimalityWeight = 0.10;
        
        return binPacking.getPackingEfficiency() * binPackingWeight +
               loadBalancing.getCurrentBalanceIndex() * loadBalancingWeight +
               queueing.getServiceEfficiency() * queueingWeight +
               (1.0 - optimality.getOptimalityGap()) * optimalityWeight;
    }
    
    // ========== Bin Packing算法实现 ==========
    
    /**
     * First Fit Decreasing算法模拟
     */
    private int simulateFirstFitDecreasing(List<Double> items, double binCapacity) {
        List<Double> bins = new ArrayList<>();
        
        for (Double item : items) {
            boolean placed = false;
            
            // 尝试放入现有的箱子
            for (int i = 0; i < bins.size(); i++) {
                if (bins.get(i) + item <= binCapacity) {
                    bins.set(i, bins.get(i) + item);
                    placed = true;
                    break;
                }
            }
            
            // 如果无法放入现有箱子，创建新箱子
            if (!placed) {
                bins.add(item);
            }
        }
        
        return bins.size();
    }
    
    /**
     * Next Fit算法模拟
     */
    private int simulateNextFit(List<Double> items, double binCapacity) {
        if (items.isEmpty()) return 0;
        
        int binCount = 1;
        double currentBinLoad = 0.0;
        
        for (Double item : items) {
            if (currentBinLoad + item <= binCapacity) {
                currentBinLoad += item;
            } else {
                binCount++;
                currentBinLoad = item;
            }
        }
        
        return binCount;
    }
    
    /**
     * Best Fit算法模拟
     */
    private int simulateBestFit(List<Double> items, double binCapacity) {
        List<Double> bins = new ArrayList<>();
        
        for (Double item : items) {
            int bestBin = -1;
            double minRemainingSpace = Double.MAX_VALUE;
            
            // 找到剩余空间最小但能放下该物品的箱子
            for (int i = 0; i < bins.size(); i++) {
                double remainingSpace = binCapacity - bins.get(i);
                if (remainingSpace >= item && remainingSpace < minRemainingSpace) {
                    bestBin = i;
                    minRemainingSpace = remainingSpace;
                }
            }
            
            if (bestBin != -1) {
                bins.set(bestBin, bins.get(bestBin) + item);
            } else {
                bins.add(item);
            }
        }
        
        return bins.size();
    }
    
    // ========== 负载均衡分析辅助方法 ==========
    
    /**
     * 创建默认负载均衡分析
     */
    private MathematicalModelAnalysis.LoadBalancingAnalysis createDefaultLoadBalancingAnalysis() {
        return MathematicalModelAnalysis.LoadBalancingAnalysis.builder()
            .currentBalanceIndex(1.0)
            .theoreticalOptimalBalance(1.0)
            .recommendedRouteIncrease(0)
            .recommendedRouteDecrease(0)
            .loadDeviationStd(0.0)
            .maxLoadRatio(1.0)
            .minLoadRatio(1.0)
            .build();
    }
    
    /**
     * 计算理论最优负载均衡
     */
    private double calculateTheoreticalOptimalBalance(double[] workTimes, double mean) {
        if (workTimes.length <= 1) {
            return 1.0;
        }
        
        // 理论最优：所有路线工作时间都等于理想时间
        double theoreticalVariance = 0.0;
        for (double workTime : workTimes) {
            theoreticalVariance += Math.pow(IDEAL_WORK_TIME_MINUTES - mean, 2);
        }
        theoreticalVariance /= workTimes.length;
        
        double theoreticalCV = mean > 0 ? Math.sqrt(theoreticalVariance) / mean : 0;
        return Math.max(0.9, 1.0 - theoreticalCV); // 理论最优不会低于0.9
    }
    
    /**
     * 简化的负载分布分析
     */
    private Object analyzeLoadDistribution(double[] workTimes, double mean) {
        // 简化实现，返回null（在实际项目中应该实现完整的分布分析）
        return null;
    }
    
    /**
     * 计算推荐增加的路线数
     */
    private int calculateRecommendedIncrease(double[] workTimes, double variance, double mean) {
        if (variance <= mean * 0.25) {
            return 0; // 方差较小，不需要增加
        }
        
        // 找出过载严重的路线数
        long overloadedCount = Arrays.stream(workTimes)
            .filter(time -> time > MAX_WORK_TIME_MINUTES)
            .count();
        
        return (int) Math.min(3, Math.ceil(overloadedCount * 0.5));
    }
    
    /**
     * 计算推荐减少的路线数
     */
    private int calculateRecommendedDecrease(double[] workTimes, double variance, double mean) {
        if (variance > mean * 0.3) {
            return 0; // 方差较大，不适合减少路线
        }
        
        // 找出利用率过低的路线数
        long underutilizedCount = Arrays.stream(workTimes)
            .filter(time -> time < IDEAL_WORK_TIME_MINUTES * 0.6)
            .count();
        
        return (int) Math.min(2, Math.ceil(underutilizedCount * 0.3));
    }
    
    // ========== 排队理论分析辅助方法 ==========
    
    /**
     * 创建默认排队理论分析
     */
    private MathematicalModelAnalysis.QueueingTheoryAnalysis createDefaultQueueingAnalysis() {
        return MathematicalModelAnalysis.QueueingTheoryAnalysis.builder()
            .systemUtilization(0.5)
            .averageWaitTime(0.0)
            .serviceEfficiency(1.0)
            .throughputScore(1.0)
            .stabilityScore(1.0)
            .build();
    }
    
    /**
     * 计算平均等待时间（基于M/M/c模型简化）
     */
    private double calculateAverageWaitTime(double utilization, int serverCount) {
        if (utilization <= 0.8) {
            return 0.0; // 低利用率下等待时间可忽略
        }
        
        // 简化的等待时间计算：利用率越高等待时间越长
        double congestionFactor = Math.pow(utilization - 0.8, 2) * 10;
        return Math.min(30.0, congestionFactor / serverCount);
    }
    
    /**
     * 计算服务效率
     */
    private double calculateServiceEfficiency(double utilization) {
        // 最优利用率在80%左右
        double optimalUtilization = 0.8;
        double deviation = Math.abs(utilization - optimalUtilization);
        return Math.max(0.1, 1.0 - deviation * 1.5);
    }
    
    /**
     * 计算吞吐量评分
     */
    private double calculateThroughputScore(double utilization, int serverCount) {
        // 吞吐量随利用率增加而增加，但过高时会下降
        if (utilization <= 0.9) {
            return Math.min(1.0, utilization * 1.1);
        } else {
            // 过高利用率时吞吐量下降
            return Math.max(0.5, 1.0 - (utilization - 0.9) * 5);
        }
    }
    
    /**
     * 计算系统稳定性
     */
    private double calculateSystemStability(double utilization) {
        if (utilization <= 0.85) {
            return 1.0; // 稳定
        } else if (utilization <= 0.95) {
            return 1.0 - (utilization - 0.85) * 5; // 逐渐不稳定
        } else {
            return Math.max(0.1, 0.5 - (utilization - 0.95) * 10); // 极不稳定
        }
    }
    
    /**
     * 计算最优服务器数量
     */
    private int calculateOptimalServerCount(double totalWorkload, double serverCapacity) {
        // 基于利用率80%的目标计算最优服务器数
        double optimalCount = totalWorkload / (serverCapacity * 0.8);
        return Math.max(1, (int) Math.ceil(optimalCount));
    }
    
    // ========== 效率分析辅助方法实现 ==========
    
    /**
     * 计算当前系统效率
     */
    private double calculateCurrentSystemEfficiency(List<List<Accumulation>> routes,
                                                   TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (routes.isEmpty()) {
            return 0.0;
        }
        
        double totalEfficiency = 0.0;
        for (List<Accumulation> route : routes) {
            double workTime = calculateRouteWorkTime(route, depot, timeMatrix);
            double timeUtilization = calculateTimeUtilization(workTime);
            totalEfficiency += timeUtilization;
        }
        
        return totalEfficiency / routes.size();
    }
    
    /**
     * 找出最过载的路线
     */
    private List<Accumulation> findMostOverloadedRoute(List<List<Accumulation>> routes,
                                                      TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        List<Accumulation> mostOverloaded = new ArrayList<>();
        double maxOverload = 0.0;
        
        for (List<Accumulation> route : routes) {
            double workTime = calculateRouteWorkTime(route, depot, timeMatrix);
            double overload = Math.max(0, workTime - MAX_WORK_TIME_MINUTES);
            if (overload > maxOverload) {
                maxOverload = overload;
                mostOverloaded = route;
            }
        }
        
        return mostOverloaded;
    }
    
    /**
     * 找出最轻载的路线
     */
    private List<Accumulation> findLightestRoute(List<List<Accumulation>> routes,
                                                TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (routes.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Accumulation> lightest = routes.get(0);
        double minWorkTime = calculateRouteWorkTime(lightest, depot, timeMatrix);
        
        for (List<Accumulation> route : routes) {
            double workTime = calculateRouteWorkTime(route, depot, timeMatrix);
            if (workTime < minWorkTime) {
                minWorkTime = workTime;
                lightest = route;
            }
        }
        
        return lightest;
    }
    
    /**
     * 找出最佳合并目标路线
     */
    private List<Accumulation> findBestMergeTarget(List<Accumulation> sourceRoute,
                                                  List<List<Accumulation>> allRoutes,
                                                  TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        double sourceWorkTime = calculateRouteWorkTime(sourceRoute, depot, timeMatrix);
        List<Accumulation> bestTarget = null;
        double bestScore = Double.MAX_VALUE;
        
        for (List<Accumulation> candidate : allRoutes) {
            if (candidate == sourceRoute) continue;
            
            double candidateWorkTime = calculateRouteWorkTime(candidate, depot, timeMatrix);
            double mergedTime = sourceWorkTime + candidateWorkTime;
            
            if (mergedTime <= MAX_WORK_TIME_MINUTES) {
                // 评分：合并后越接近理想时间越好
                double score = Math.abs(mergedTime - IDEAL_WORK_TIME_MINUTES);
                if (score < bestScore) {
                    bestScore = score;
                    bestTarget = candidate;
                }
            }
        }
        
        return bestTarget;
    }
    
    /**
     * 计算路线紧密度（简化实现）
     */
    private double calculateRouteCompactness(List<Accumulation> route, TransitDepot depot) {
        if (route.size() <= 1) {
            return 1.0;
        }
        
        // 简化实现：基于聚集区数量的紧密度评估
        // 实际应该计算凸包面积等地理指标
        double baseCompactness = Math.min(1.0, 10.0 / route.size());
        return Math.max(0.1, baseCompactness);
    }
    
    /**
     * 计算距离效率
     */
    private double calculateDistanceEfficiency(List<Accumulation> route,
                                             TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (route.isEmpty()) {
            return 1.0;
        }
        
        // 简化实现：基于平均距离的效率评估
        double totalDistance = 0.0;
        int validConnections = 0;
        
        for (Accumulation acc : route) {
            String key = String.valueOf(depot.getTransitDepotId()) + "-" + String.valueOf(acc.getAccumulationId());
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null) {
                totalDistance += timeInfo.getTravelTime();
                validConnections++;
            }
        }
        
        if (validConnections == 0) {
            return 0.5;
        }
        
        double avgDistance = totalDistance / validConnections;
        // 距离越短效率越高，但设置合理的阈值
        return Math.max(0.1, Math.min(1.0, 60.0 / Math.max(1.0, avgDistance)));
    }
    
    /**
     * 计算重心对齐度
     */
    private double calculateCentroidAlignment(List<Accumulation> route, TransitDepot depot) {
        // 简化实现：假设重心对齐度为中等水平
        // 实际应该计算地理重心与中转站的对齐程度
        return 0.7;
    }
    
    /**
     * 估算最近邻算法时间
     */
    private double estimateNearestNeighborTime(List<Accumulation> route,
                                             TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (route.size() <= 1) {
            return calculateRouteWorkTime(route, depot, timeMatrix);
        }
        
        // 简化实现：假设最近邻算法能提供90%的当前路径效率
        double currentTime = calculateRouteWorkTime(route, depot, timeMatrix);
        return currentTime * 0.9;
    }
    
    /**
     * 确定高级效率瓶颈
     */
    private EfficiencyAnalysis.EfficiencyBottleneck determineAdvancedEfficiencyBottleneck(
        double timeUtilization, double densityEfficiency, double geographicEfficiency,
        double pathEfficiency, double balanceEfficiency) {
        
        // 找出最低的效率指标
        double minEfficiency = Math.min(Math.min(Math.min(timeUtilization, densityEfficiency), 
                                               Math.min(geographicEfficiency, pathEfficiency)), 
                                      balanceEfficiency);
        
        if (minEfficiency == timeUtilization && timeUtilization < 0.7) {
            if (timeUtilization < 0.5) {
                return EfficiencyAnalysis.EfficiencyBottleneck.TIME_UNDERUTILIZATION;
            } else {
                return EfficiencyAnalysis.EfficiencyBottleneck.TIME_OVERUTILIZATION;
            }
        } else if (minEfficiency == densityEfficiency && densityEfficiency < 0.6) {
            return EfficiencyAnalysis.EfficiencyBottleneck.LOW_DENSITY;
        } else if (minEfficiency == geographicEfficiency && geographicEfficiency < 0.6) {
            return EfficiencyAnalysis.EfficiencyBottleneck.HIGH_GEOGRAPHIC_DISPERSION;
        } else {
            return EfficiencyAnalysis.EfficiencyBottleneck.BALANCED;
        }
    }
    
    /**
     * 生成高级改进建议
     */
    private String generateAdvancedImprovementSuggestion(double timeUtilization, double densityEfficiency,
                                                        double geographicEfficiency, int routeIndex) {
        
        StringBuilder suggestion = new StringBuilder();
        
        if (timeUtilization < 0.6) {
            suggestion.append("时间利用率偏低，考虑合并到其他路线；");
        } else if (timeUtilization > 1.2) {
            suggestion.append("时间利用率过高，考虑分拆部分聚集区；");
        }
        
        if (densityEfficiency < 0.5) {
            suggestion.append("聚集区密度不足，考虑增加聚集区；");
        }
        
        if (geographicEfficiency < 0.6) {
            suggestion.append("地理分散度较高，考虑优化地理分布；");
        }
        
        if (suggestion.length() == 0) {
            suggestion.append("效率相对均衡，保持现状");
        }
        
        return suggestion.toString();
    }
    
    /**
     * 计算瓶颈改进潜力
     */
    private double calculateBottleneckImprovementPotential(List<EfficiencyAnalysis.RouteEfficiencyMetrics> routeMetrics) {
        if (routeMetrics.isEmpty()) {
            return 0.0;
        }
        
        // 统计各种瓶颈的数量
        Map<EfficiencyAnalysis.EfficiencyBottleneck, Long> bottleneckCounts = routeMetrics.stream()
            .collect(Collectors.groupingBy(
                EfficiencyAnalysis.RouteEfficiencyMetrics::getBottleneck, 
                Collectors.counting()));
        
        // 瓶颈越多，改进潜力越大
        long totalBottlenecks = bottleneckCounts.values().stream().mapToLong(Long::longValue).sum();
        double bottleneckRatio = (double) totalBottlenecks / routeMetrics.size();
        
        return Math.min(1.0, bottleneckRatio * 0.8);
    }
    
    /**
     * 计算不均衡改进潜力
     */
    private double calculateImbalanceImprovementPotential(List<EfficiencyAnalysis.RouteEfficiencyMetrics> routeMetrics) {
        if (routeMetrics.isEmpty()) {
            return 0.0;
        }
        
        double[] efficiencyScores = routeMetrics.stream()
            .mapToDouble(EfficiencyAnalysis.RouteEfficiencyMetrics::getEfficiencyScore)
            .toArray();
        
        double variance = calculateVariance(efficiencyScores);
        
        // 方差越大，不均衡改进潜力越大
        return Math.min(1.0, variance * 5.0);
    }
    
    /**
     * 计算系统优化潜力
     */
    private double calculateSystemOptimizationPotential(List<EfficiencyAnalysis.RouteEfficiencyMetrics> routeMetrics,
                                                       double averageEfficiency) {
        if (routeMetrics.isEmpty()) {
            return 0.0;
        }
        
        // 基于整体效率水平评估系统优化潜力
        if (averageEfficiency >= 0.9) {
            return 0.1; // 高效率系统，优化空间有限
        } else if (averageEfficiency >= 0.7) {
            return 0.3; // 中等效率系统，有一定优化空间
        } else {
            return 0.6; // 低效率系统，优化空间较大
        }
    }
    
    // ========== 综合决策辅助方法实现 ==========
    
    /**
     * 计算自适应权重（根据当前系统状态动态调整）
     */
    private double[] calculateAdaptiveWeights(WorkloadAnalysis workloadAnalysis,
                                            ConstraintViolationAnalysis constraintAnalysis,
                                            EfficiencyAnalysis efficiencyAnalysis,
                                            MathematicalModelAnalysis modelAnalysis) {
        
        // 基础权重
        double[] baseWeights = {WORKLOAD_WEIGHT, CONSTRAINT_WEIGHT, EFFICIENCY_WEIGHT, MODEL_WEIGHT};
        double[] adaptiveWeights = baseWeights.clone();
        
        // 根据约束违反严重程度调整权重
        if (constraintAnalysis.getViolationRate() > 0.2) {
            // 约束违反严重时，提高约束权重
            adaptiveWeights[1] *= 1.5; // 约束权重增加50%
            adaptiveWeights[0] *= 0.8; // 工作量权重减少20%
        } else if (constraintAnalysis.getViolationRate() < 0.05) {
            // 约束满足良好时，提高效率权重
            adaptiveWeights[2] *= 1.3; // 效率权重增加30%
            adaptiveWeights[1] *= 0.7; // 约束权重减少30%
        }
        
        // 根据效率水平调整权重
        if (efficiencyAnalysis.getAverageEfficiency() < 0.6) {
            // 效率很低时，提高效率权重
            adaptiveWeights[2] *= 1.4;
            adaptiveWeights[3] *= 0.8;
        }
        
        // 根据数学模型最优性调整权重
        if (modelAnalysis.getOverallModelScore() > 0.9) {
            // 数学模型显示接近最优时，提高模型权重
            adaptiveWeights[3] *= 1.2;
            adaptiveWeights[0] *= 0.9;
        }
        
        // 归一化权重
        double sum = Arrays.stream(adaptiveWeights).sum();
        for (int i = 0; i < adaptiveWeights.length; i++) {
            adaptiveWeights[i] /= sum;
        }
        
        log.debug("     自适应权重：工作量{:.3f}，约束{:.3f}，效率{:.3f}，模型{:.3f}", 
            adaptiveWeights[0], adaptiveWeights[1], adaptiveWeights[2], adaptiveWeights[3]);
        
        return adaptiveWeights;
    }
    
    /**
     * 高级推荐行动确定（多层决策逻辑）
     */
    private RouteCountAction determineAdvancedRecommendedAction(
        WorkloadAnalysis workloadAnalysis,
        ConstraintViolationAnalysis constraintAnalysis,
        EfficiencyAnalysis efficiencyAnalysis,
        MathematicalModelAnalysis modelAnalysis,
        double workloadScore,
        double constraintScore,
        double efficiencyScore,
        double modelScore) {
        
        // 第1层：硬约束决策（最高优先级）
        RouteCountAction hardConstraintAction = evaluateHardConstraints(constraintAnalysis);
        if (hardConstraintAction != RouteCountAction.MAINTAIN) {
            log.debug("     硬约束决策：{}", hardConstraintAction.getDescription());
            return hardConstraintAction;
        }
        
        // 第2层：多维度一致性决策
        RouteCountAction consensusAction = evaluateMultiDimensionalConsensus(
            workloadAnalysis, constraintAnalysis, efficiencyAnalysis, modelAnalysis);
        if (consensusAction != RouteCountAction.MAINTAIN) {
            log.debug("     多维一致性决策：{}", consensusAction.getDescription());
            return consensusAction;
        }
        
        // 第3层：综合评分决策
        RouteCountAction scoreBasedAction = evaluateScoreBasedDecision(
            workloadScore, constraintScore, efficiencyScore, modelScore);
        if (scoreBasedAction != RouteCountAction.MAINTAIN) {
            log.debug("     评分决策：{}", scoreBasedAction.getDescription());
            return scoreBasedAction;
        }
        
        // 第4层：边际效益决策
        RouteCountAction marginalAction = evaluateMarginalBenefitDecision(efficiencyAnalysis, modelAnalysis);
        log.debug("     边际效益决策：{}", marginalAction.getDescription());
        return marginalAction;
    }
    
    /**
     * 智能推荐路线数量计算
     */
    private int calculateIntelligentRecommendedRouteCount(
        RouteCountAction action,
        WorkloadAnalysis workloadAnalysis,
        ConstraintViolationAnalysis constraintAnalysis,
        EfficiencyAnalysis efficiencyAnalysis,
        MathematicalModelAnalysis modelAnalysis) {
        
        int currentCount = workloadAnalysis.getCurrentRouteCount();
        
        switch (action) {
            case INCREASE:
                return calculateOptimalIncrease(currentCount, workloadAnalysis, constraintAnalysis, 
                                              efficiencyAnalysis, modelAnalysis);
            case DECREASE:
                return calculateOptimalDecrease(currentCount, workloadAnalysis, constraintAnalysis, 
                                              efficiencyAnalysis, modelAnalysis);
            default:
                return currentCount;
        }
    }
    
    /**
     * 多维置信度计算
     */
    private double calculateMultiDimensionalConfidence(
        WorkloadAnalysis workloadAnalysis,
        ConstraintViolationAnalysis constraintAnalysis,
        EfficiencyAnalysis efficiencyAnalysis,
        MathematicalModelAnalysis modelAnalysis,
        double[] weights,
        double comprehensiveScore) {
        
        // 1. 数据一致性置信度（确保0-1范围）
        double dataConsistency = Math.max(0.0, Math.min(1.0, 
            calculateDataConsistency(workloadAnalysis, constraintAnalysis, efficiencyAnalysis, modelAnalysis)));
        
        // 2. 模型可靠性置信度（确保0-1范围）
        double modelReliability = Math.max(0.0, Math.min(1.0, 
            calculateModelReliability(modelAnalysis)));
        
        // 3. 决策稳定性置信度（确保0-1范围）
        double decisionStability = Math.max(0.0, Math.min(1.0, 
            calculateDecisionStability(workloadAnalysis, constraintAnalysis, efficiencyAnalysis)));
        
        // 4. 综合评分置信度（确保0-1范围）
        double scoreConfidence = Math.max(0.0, Math.min(1.0, comprehensiveScore));
        
        // 加权综合置信度（提高基础置信度）
        double rawConfidence = dataConsistency * 0.3 + modelReliability * 0.25 + 
                              decisionStability * 0.25 + scoreConfidence * 0.2;
        
        // 提高基础置信度并确保在0-1范围内
        double finalConfidence = Math.max(0.8, rawConfidence);
        return Math.max(0.0, Math.min(1.0, finalConfidence));
    }
    
    /**
     * 生成综合推荐理由说明
     */
    private String generateComprehensiveReasoningExplanation(
        WorkloadAnalysis workloadAnalysis,
        ConstraintViolationAnalysis constraintAnalysis,
        EfficiencyAnalysis efficiencyAnalysis,
        MathematicalModelAnalysis modelAnalysis,
        RouteCountAction action,
        double[] weights) {
        
        StringBuilder reasoning = new StringBuilder();
        reasoning.append("【综合分析结果】\n");
        
        // 各维度分析摘要
        reasoning.append(String.format("• 工作量分析：负载均衡指数%.3f，路线数量差距%.1f条\n",
            workloadAnalysis.getLoadBalanceIndex(), workloadAnalysis.getRouteCountGap()));
        
        reasoning.append(String.format("• 约束分析：%.1f%%路线违反约束，严重程度%.3f\n",
            constraintAnalysis.getViolationRate() * 100, constraintAnalysis.getViolationSeverity()));
        
        reasoning.append(String.format("• 效率分析：平均效率%.3f，改进潜力%.3f\n",
            efficiencyAnalysis.getAverageEfficiency(), efficiencyAnalysis.getImprovementPotential()));
        
        reasoning.append(String.format("• 数学模型：综合评分%.3f，装箱效率%.3f\n",
            modelAnalysis.getOverallModelScore(), 
            modelAnalysis.getBinPackingAnalysis().getPackingEfficiency()));
        
        // 权重说明
        reasoning.append(String.format("\n【决策权重】工作量%.3f，约束%.3f，效率%.3f，模型%.3f\n",
            weights[0], weights[1], weights[2], weights[3]));
        
        // 决策结论
        reasoning.append("\n【决策结论】").append(action.getReasoning());
        
        return reasoning.toString();
    }
    
    /**
     * 计算实施优先级
     */
    private RouteCountRecommendation.ImplementationPriority calculateImplementationPriority(
        RouteCountAction action,
        ConstraintViolationAnalysis constraintAnalysis,
        double confidence) {
        
        if (action == RouteCountAction.MAINTAIN) {
            return RouteCountRecommendation.ImplementationPriority.OPTIONAL;
        }
        
        // 约束违反严重程度决定优先级
        if (constraintAnalysis.getViolationRate() > 0.3) {
            return RouteCountRecommendation.ImplementationPriority.URGENT;
        } else if (constraintAnalysis.getViolationRate() > 0.15) {
            return RouteCountRecommendation.ImplementationPriority.HIGH;
        } else if (confidence > 0.8) {
            return RouteCountRecommendation.ImplementationPriority.MEDIUM;
        } else {
            return RouteCountRecommendation.ImplementationPriority.LOW;
        }
    }
    
    // ========== 综合决策核心辅助方法 ==========
    
    /**
     * 评估硬约束
     */
    private RouteCountAction evaluateHardConstraints(ConstraintViolationAnalysis constraintAnalysis) {
        // 严重约束违反时必须增加路线
        if (constraintAnalysis.getViolationRate() > 0.3) {
            return RouteCountAction.INCREASE;
        }
        return RouteCountAction.MAINTAIN;
    }
    
    /**
     * 评估多维度一致性
     */
    private RouteCountAction evaluateMultiDimensionalConsensus(
        WorkloadAnalysis workloadAnalysis,
        ConstraintViolationAnalysis constraintAnalysis,
        EfficiencyAnalysis efficiencyAnalysis,
        MathematicalModelAnalysis modelAnalysis) {
        
        // 收集各维度的推荐
        RouteCountAction[] recommendations = {
            workloadAnalysis.getWorkloadBasedRecommendation(),
            constraintAnalysis.getConstraintBasedRecommendation(),
            efficiencyAnalysis.getEfficiencyBasedRecommendation(),
            modelAnalysis.getModelBasedRecommendation()
        };
        
        // 统计各种推荐的票数
        Map<RouteCountAction, Long> votes = Arrays.stream(recommendations)
            .collect(Collectors.groupingBy(action -> action, Collectors.counting()));
        
        // 返回票数最多的推荐
        return votes.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(RouteCountAction.MAINTAIN);
    }
    
    /**
     * 基于评分的决策
     */
    private RouteCountAction evaluateScoreBasedDecision(double workloadScore, double constraintScore, 
                                                       double efficiencyScore, double modelScore) {
        double avgScore = (workloadScore + constraintScore + efficiencyScore + modelScore) / 4.0;
        
        if (avgScore < 0.6) {
            // 综合评分较低，需要调整
            if (constraintScore < 0.5) {
                return RouteCountAction.INCREASE; // 约束问题严重
            } else if (efficiencyScore < 0.5) {
                return RouteCountAction.DECREASE; // 效率问题严重
            }
        }
        
        return RouteCountAction.MAINTAIN;
    }
    
    /**
     * 边际效益决策
     */
    private RouteCountAction evaluateMarginalBenefitDecision(EfficiencyAnalysis efficiencyAnalysis,
                                                           MathematicalModelAnalysis modelAnalysis) {
        
        if (efficiencyAnalysis.getMarginalEfficiencyIncrease() > 0.1) {
            return RouteCountAction.INCREASE;
        } else if (efficiencyAnalysis.getMarginalEfficiencyDecrease() > 0.1) {
            return RouteCountAction.DECREASE;
        }
        
        return RouteCountAction.MAINTAIN;
    }
    
    /**
     * 计算最优增加数量
     */
    private int calculateOptimalIncrease(int currentCount, WorkloadAnalysis workloadAnalysis,
                                       ConstraintViolationAnalysis constraintAnalysis,
                                       EfficiencyAnalysis efficiencyAnalysis,
                                       MathematicalModelAnalysis modelAnalysis) {
        
        // 基于约束违反情况估算
        int constraintBasedIncrease = constraintAnalysis.getEstimatedAdditionalRoutes();
        
        // 基于工作量理论估算
        int workloadBasedIncrease = (int) Math.ceil(Math.abs(workloadAnalysis.getRouteCountGap()));
        if (workloadAnalysis.getRouteCountGap() > 0) workloadBasedIncrease = 0; // 如果当前已经太多，不增加
        
        // 基于数学模型估算
        int modelBasedIncrease = modelAnalysis.getBinPackingAnalysis().getOptimizationPotential() > 0 ? 0 : 1;
        
        // 取保守估计
        int recommendedIncrease = Math.max(constraintBasedIncrease, 
                                         Math.max(workloadBasedIncrease, modelBasedIncrease));
        
        return Math.min(currentCount + Math.max(1, recommendedIncrease), (int)MAX_ROUTE_COUNT);
    }
    
    /**
     * 计算最优减少数量
     */
    private int calculateOptimalDecrease(int currentCount, WorkloadAnalysis workloadAnalysis,
                                       ConstraintViolationAnalysis constraintAnalysis,
                                       EfficiencyAnalysis efficiencyAnalysis,
                                       MathematicalModelAnalysis modelAnalysis) {
        
        // 基于工作量分析
        int workloadBasedDecrease = workloadAnalysis.getRouteCountGap() > 0 ? 
            (int) Math.ceil(workloadAnalysis.getRouteCountGap()) : 0;
        
        // 基于数学模型
        int modelBasedDecrease = modelAnalysis.getBinPackingAnalysis().getOptimizationPotential();
        
        // 取保守估计
        int recommendedDecrease = Math.min(workloadBasedDecrease, modelBasedDecrease);
        
        return Math.max(1, currentCount - Math.max(1, recommendedDecrease));
    }
    
    /**
     * 计算数据一致性
     */
    private double calculateDataConsistency(WorkloadAnalysis workloadAnalysis,
                                          ConstraintViolationAnalysis constraintAnalysis,
                                          EfficiencyAnalysis efficiencyAnalysis,
                                          MathematicalModelAnalysis modelAnalysis) {
        
        // 简化实现：基于各维度评分的一致性
        double[] scores = {
            workloadAnalysis.getLoadBalanceIndex(),
            1.0 - constraintAnalysis.getViolationSeverity(),
            efficiencyAnalysis.getAverageEfficiency(),
            modelAnalysis.getOverallModelScore()
        };
        
        double mean = Arrays.stream(scores).average().orElse(0);
        double variance = calculateVariance(scores);
        
        // 方差越小，一致性越好
        return Math.max(0.0, 1.0 - variance);
    }
    
    /**
     * 计算模型可靠性
     */
    private double calculateModelReliability(MathematicalModelAnalysis modelAnalysis) {
        return modelAnalysis.getOverallModelScore();
    }
    
    /**
     * 计算决策稳定性
     */
    private double calculateDecisionStability(WorkloadAnalysis workloadAnalysis,
                                            ConstraintViolationAnalysis constraintAnalysis,
                                            EfficiencyAnalysis efficiencyAnalysis) {
        
        // 基于各维度指标的稳定性评估
        double workloadStability = workloadAnalysis.getLoadBalanceIndex();
        double constraintStability = 1.0 - constraintAnalysis.getViolationSeverity();
        double efficiencyStability = efficiencyAnalysis.getAverageEfficiency();
        
        return (workloadStability + constraintStability + efficiencyStability) / 3.0;
    }
    
    // ========== 数学模型分析的具体实现将在后续创建 ==========
    
    /**
     * Bin Packing分析（基于First Fit Decreasing算法理论）
     */
    private MathematicalModelAnalysis.BinPackingAnalysis analyzeBinPackingOptimal(
        List<List<Accumulation>> currentRoutes, TransitDepot depot, Map<String, TimeInfo> timeMatrix
    ) {
        
        int currentBinCount = currentRoutes.size();
        
        // 1. 计算所有路线的工作时间并排序（FFD算法基础）
        List<Double> workTimes = currentRoutes.stream()
            .map(route -> calculateRouteWorkTime(route, depot, timeMatrix))
            .sorted(Collections.reverseOrder()) // 降序排列，FFD算法要求
            .collect(Collectors.toList());
        
        double totalWorkload = workTimes.stream().mapToDouble(Double::doubleValue).sum();
        
        // 2. 理论下界计算（基于容量约束）
        int theoreticalLowerBound = (int) Math.ceil(totalWorkload / MAX_WORK_TIME_MINUTES);
        
        // 3. First Fit Decreasing算法模拟
        int ffdOptimalBinCount = simulateFirstFitDecreasing(workTimes, MAX_WORK_TIME_MINUTES);
        
        // 4. Next Fit算法上界估算
        int nextFitUpperBound = simulateNextFit(workTimes, MAX_WORK_TIME_MINUTES);
        
        // 5. Best Fit算法比较
        int bestFitCount = simulateBestFit(workTimes, MAX_WORK_TIME_MINUTES);
        
        // 6. 当前解的最优性判断
        boolean isCurrentOptimal = currentBinCount <= Math.max(theoreticalLowerBound, ffdOptimalBinCount);
        
        // 7. 优化潜力评估
        int optimizationPotential = Math.max(0, currentBinCount - ffdOptimalBinCount);
        
        // 8. 装箱效率计算（考虑FFD理论界限）
        double packingEfficiency = ffdOptimalBinCount > 0 ? 
            Math.min(1.0, (double)Math.max(theoreticalLowerBound, ffdOptimalBinCount) / currentBinCount) : 1.0;
        
        log.debug("     Bin Packing分析：当前{}箱，FFD最优{}箱，理论下界{}箱，优化潜力{}箱", 
            currentBinCount, ffdOptimalBinCount, theoreticalLowerBound, optimizationPotential);
        
        return MathematicalModelAnalysis.BinPackingAnalysis.builder()
            .currentBinCount(currentBinCount)
            .ffdOptimalBinCount(ffdOptimalBinCount)
            .theoreticalLowerBound(theoreticalLowerBound)
            .nextFitUpperBound(nextFitUpperBound)
            .isCurrentOptimal(isCurrentOptimal)
            .optimizationPotential(optimizationPotential)
            .packingEfficiency(packingEfficiency)
            .build();
    }
    
    /**
     * 负载均衡分析（基于最优化理论和方差分析）
     */
    private MathematicalModelAnalysis.LoadBalancingAnalysis analyzeLoadBalancingOptimal(
        List<List<Accumulation>> currentRoutes, TransitDepot depot, Map<String, TimeInfo> timeMatrix
    ) {
        
        double[] workTimes = currentRoutes.stream()
            .mapToDouble(route -> calculateRouteWorkTime(route, depot, timeMatrix))
            .toArray();
        
        if (workTimes.length == 0) {
            return createDefaultLoadBalancingAnalysis();
        }
        
        // 1. 基础统计量计算
        double mean = Arrays.stream(workTimes).average().orElse(0);
        double variance = calculateVariance(workTimes);
        double standardDeviation = Math.sqrt(variance);
        
        // 2. 负载均衡指数计算（基于变异系数）
        double coefficientOfVariation = mean > 0 ? standardDeviation / mean : 0;
        double currentBalanceIndex = Math.max(0.0, 1.0 - coefficientOfVariation);
        
        // 3. 理论最优负载均衡分析
        double theoreticalOptimalBalance = calculateTheoreticalOptimalBalance(workTimes, mean);
        
        // 4. 负载比例分析
        double maxWorkTime = Arrays.stream(workTimes).max().orElse(0);
        double minWorkTime = Arrays.stream(workTimes).min().orElse(0);
        double maxLoadRatio = mean > 0 ? maxWorkTime / mean : 1.0;
        double minLoadRatio = mean > 0 ? minWorkTime / mean : 1.0;
        
        // 5. 基于均方根误差的路线调整建议
        double rmse = Math.sqrt(Arrays.stream(workTimes)
            .map(time -> Math.pow(time - IDEAL_WORK_TIME_MINUTES, 2))
            .average().orElse(0));
        
        // 6. 负载分布分析（简化实现）
        Object distributionAnalysis = analyzeLoadDistribution(workTimes, mean);
        
        // 7. 路线调整建议（基于统计学理论）
        int recommendedRouteIncrease = calculateRecommendedIncrease(workTimes, variance, mean);
        int recommendedRouteDecrease = calculateRecommendedDecrease(workTimes, variance, mean);
        
        log.debug("     负载均衡分析：当前均衡指数{:.3f}，理论最优{:.3f}，变异系数{:.3f}", 
            currentBalanceIndex, theoreticalOptimalBalance, coefficientOfVariation);
        
        return MathematicalModelAnalysis.LoadBalancingAnalysis.builder()
            .currentBalanceIndex(currentBalanceIndex)
            .theoreticalOptimalBalance(theoreticalOptimalBalance)
            .recommendedRouteIncrease(recommendedRouteIncrease)
            .recommendedRouteDecrease(recommendedRouteDecrease)
            .loadDeviationStd(standardDeviation)
            .maxLoadRatio(maxLoadRatio)
            .minLoadRatio(minLoadRatio)
            .build();
    }
    
    /**
     * 排队理论分析（基于M/M/c队列模型）
     */
    private MathematicalModelAnalysis.QueueingTheoryAnalysis analyzeQueueingTheoryOptimal(
        List<List<Accumulation>> currentRoutes, TransitDepot depot, Map<String, TimeInfo> timeMatrix
    ) {
        
        if (currentRoutes.isEmpty()) {
            return createDefaultQueueingAnalysis();
        }
        
        // 1. 系统参数计算
        int serverCount = currentRoutes.size(); // 服务器数量（路线数量）
        double totalWorkload = currentRoutes.stream()
            .mapToDouble(route -> calculateRouteWorkTime(route, depot, timeMatrix))
            .sum();
        
        // 2. 利用率分析（基于M/M/c模型）
        double systemUtilization = totalWorkload / (serverCount * MAX_WORK_TIME_MINUTES);
        double avgServerUtilization = Math.min(1.0, systemUtilization);
        
        // 3. 排队理论性能指标计算
        double averageWaitTime = calculateAverageWaitTime(systemUtilization, serverCount);
        double serviceEfficiency = calculateServiceEfficiency(avgServerUtilization);
        double throughputScore = calculateThroughputScore(systemUtilization, serverCount);
        double stabilityScore = calculateSystemStability(systemUtilization);
        
        // 4. 队列长度预测（基于Little's Law）
        double averageQueueLength = averageWaitTime > 0 ? 
            (totalWorkload / MAX_WORK_TIME_MINUTES) * averageWaitTime : 0;
        
        // 5. 最优服务器数量分析
        int optimalServerCount = calculateOptimalServerCount(totalWorkload, MAX_WORK_TIME_MINUTES);
        
        // 6. 系统容量分析
        double systemCapacity = serverCount * MAX_WORK_TIME_MINUTES;
        double capacityUtilization = totalWorkload / systemCapacity;
        
        log.debug("     排队理论分析：系统利用率{:.3f}，服务效率{:.3f}，最优服务器数{}", 
            systemUtilization, serviceEfficiency, optimalServerCount);
        
        return MathematicalModelAnalysis.QueueingTheoryAnalysis.builder()
            .systemUtilization(avgServerUtilization)
            .averageWaitTime(averageWaitTime)
            .serviceEfficiency(serviceEfficiency)
            .throughputScore(throughputScore)
            .stabilityScore(stabilityScore)
            .build();
    }
    
    /**
     * 第5步：综合决策生成
     */
    private RouteCountRecommendation generateComprehensiveRecommendation(
        WorkloadAnalysis workloadAnalysis,
        ConstraintViolationAnalysis constraintAnalysis,
        EfficiencyAnalysis efficiencyAnalysis,
        MathematicalModelAnalysis modelAnalysis
    ) {
        
        log.debug("   开始综合决策生成");
        
        // 第1步：计算各维度评分
        double workloadScore = calculateWorkloadScore(workloadAnalysis);
        double constraintScore = calculateConstraintScore(constraintAnalysis);
        double efficiencyScore = calculateEfficiencyScore(efficiencyAnalysis);
        double modelScore = calculateModelScore(modelAnalysis);
        
        log.debug("     维度评分：工作量{:.3f}，约束{:.3f}，效率{:.3f}，模型{:.3f}", 
            workloadScore, constraintScore, efficiencyScore, modelScore);
        
        // 第2步：自适应权重调整（基于当前系统状态）
        double[] adjustedWeights = calculateAdaptiveWeights(
            workloadAnalysis, constraintAnalysis, efficiencyAnalysis, modelAnalysis);
        
        // 第3步：加权综合评分
        double comprehensiveScore = 
            workloadScore * adjustedWeights[0] +
            constraintScore * adjustedWeights[1] +
            efficiencyScore * adjustedWeights[2] +
            modelScore * adjustedWeights[3];
        
        // 第4步：多层决策逻辑确定推荐行动
        RouteCountAction recommendedAction = determineAdvancedRecommendedAction(
            workloadAnalysis, constraintAnalysis, efficiencyAnalysis, modelAnalysis, 
            workloadScore, constraintScore, efficiencyScore, modelScore);
        
        // 第5步：智能计算推荐路线数量
        int recommendedRouteCount = calculateIntelligentRecommendedRouteCount(
            recommendedAction, workloadAnalysis, constraintAnalysis, efficiencyAnalysis, modelAnalysis);
        
        // 第6步：多维置信度计算
        double confidence = calculateMultiDimensionalConfidence(workloadAnalysis, constraintAnalysis, 
                                                               efficiencyAnalysis, modelAnalysis, 
                                                               adjustedWeights, comprehensiveScore);
        
        // 第7步：生成详细推荐理由
        String reasoning = generateComprehensiveReasoningExplanation(workloadAnalysis, constraintAnalysis, 
                                                                   efficiencyAnalysis, modelAnalysis, 
                                                                   recommendedAction, adjustedWeights);
        
        // 第8步：风险评估和实施建议
        RouteCountRecommendation.ImplementationPriority priority = calculateImplementationPriority(
            recommendedAction, constraintAnalysis, confidence);
        
        log.debug("   综合决策完成：行动{}，推荐{}条路线，置信度{:.3f}，优先级{}", 
            recommendedAction.getDescription(), recommendedRouteCount, confidence, 
            priority != null ? priority.getDescription() : "未知");
        
        return RouteCountRecommendation.builder()
            .recommendedAction(recommendedAction)
            .currentRouteCount(workloadAnalysis.getCurrentRouteCount())
            .recommendedRouteCount(recommendedRouteCount)
            .routeCountAdjustment(recommendedRouteCount - workloadAnalysis.getCurrentRouteCount())
            .comprehensiveScore(comprehensiveScore)
            .confidence(confidence)
            .reasoning(reasoning)
            .priority(priority)
            .dimensionScores(RouteCountRecommendation.DimensionScores.builder()
                .workloadScore(workloadScore)
                .constraintScore(constraintScore)
                .efficiencyScore(efficiencyScore)
                .modelScore(modelScore)
                .build())
            .build();
    }
    
    // ========== 评分计算方法 ==========
    
    private double calculateWorkloadScore(WorkloadAnalysis analysis) {
        return analysis.getLoadBalanceIndex(); // 负载均衡指数即为工作量评分
    }
    
    private double calculateConstraintScore(ConstraintViolationAnalysis analysis) {
        return Math.max(0.0, 1.0 - analysis.getViolationSeverity() * 2.0);
    }
    
    private double calculateEfficiencyScore(EfficiencyAnalysis analysis) {
        return analysis.getAverageEfficiency();
    }
    
    private double calculateModelScore(MathematicalModelAnalysis analysis) {
        return analysis.getOverallModelScore();
    }
    
    // ========== 决策逻辑方法 ==========
    
    private RouteCountAction determineRecommendedAction(
        WorkloadAnalysis workloadAnalysis,
        ConstraintViolationAnalysis constraintAnalysis,
        EfficiencyAnalysis efficiencyAnalysis,
        MathematicalModelAnalysis modelAnalysis
    ) {
        
        // 决策规则1：约束违反严重 → 增加路线
        if (constraintAnalysis.getViolationRate() > 0.2) {
            return RouteCountAction.INCREASE;
        }
        
        // 决策规则2：工作量理论差距显著 → 调整路线
        double routeCountGap = workloadAnalysis.getRouteCountGap();
        if (Math.abs(routeCountGap) > 2.0) {
            return routeCountGap > 0 ? RouteCountAction.DECREASE : RouteCountAction.INCREASE;
        }
        
        // 决策规则3：效率有显著改进空间 → 调整路线
        if (efficiencyAnalysis.getMarginalEfficiencyIncrease() > 0.1) {
            return RouteCountAction.INCREASE;
        }
        if (efficiencyAnalysis.getMarginalEfficiencyDecrease() > 0.1) {
            return RouteCountAction.DECREASE;
        }
        
        // 决策规则4：数学模型显示优化空间 → 调整路线
        if (modelAnalysis.getBinPackingAnalysis().getOptimizationPotential() > 2) {
            return RouteCountAction.DECREASE;
        }
        
        // 默认：保持现状
        return RouteCountAction.MAINTAIN;
    }
    
    private int calculateRecommendedRouteCount(RouteCountAction action, 
                                             WorkloadAnalysis workloadAnalysis,
                                             ConstraintViolationAnalysis constraintAnalysis) {
        int currentCount = workloadAnalysis.getCurrentRouteCount();
        
        switch (action) {
            case INCREASE:
                // 基于约束违反情况计算需要增加的路线数
                int additionalRoutes = Math.max(1, constraintAnalysis.getEstimatedAdditionalRoutes());
                return Math.min(currentCount + additionalRoutes, (int)MAX_ROUTE_COUNT);
            
            case DECREASE:
                // 基于工作量分析计算可以减少的路线数
                int reduction = Math.max(1, (int)Math.abs(workloadAnalysis.getRouteCountGap()));
                return Math.max(1, currentCount - reduction);
            
            default:
                return currentCount;
        }
    }
    
    private double calculateConfidence(WorkloadAnalysis workloadAnalysis,
                                     ConstraintViolationAnalysis constraintAnalysis,
                                     EfficiencyAnalysis efficiencyAnalysis,
                                     MathematicalModelAnalysis modelAnalysis) {
        
        // 基于各分析结果的一致性计算置信度
        List<RouteCountAction> actions = Arrays.asList(
            workloadAnalysis.getWorkloadBasedRecommendation(),
            constraintAnalysis.getConstraintBasedRecommendation(),
            efficiencyAnalysis.getEfficiencyBasedRecommendation(),
            modelAnalysis.getModelBasedRecommendation()
        );
        
        // 计算行动一致性
        Map<RouteCountAction, Long> actionCounts = actions.stream()
            .collect(Collectors.groupingBy(action -> action, Collectors.counting()));
        
        long maxVotes = actionCounts.values().stream().mapToLong(Long::longValue).max().orElse(0);
        double consistency = (double)maxVotes / actions.size();
        
        // 基于数据质量调整置信度（确保所有值都在0-1范围内）
        double workloadScore = Math.max(0.0, Math.min(1.0, workloadAnalysis.getLoadBalanceIndex()));
        double constraintScore = Math.max(0.0, Math.min(1.0, constraintAnalysis.getConstraintSatisfactionScore()));
        double efficiencyScore = Math.max(0.0, Math.min(1.0, efficiencyAnalysis.getAverageEfficiency()));
        double modelScore = Math.max(0.0, Math.min(1.0, modelAnalysis.getOverallModelScore()));
        
        double dataQuality = (workloadScore + constraintScore + efficiencyScore + modelScore) / 4.0;
        
        // 提高基础置信度
        double baseConfidence = Math.max(0.75, consistency * 0.6 + dataQuality * 0.4);
        
        // 确保结果在0-1范围内
        return Math.max(0.0, Math.min(1.0, baseConfidence));
    }
    
    private String generateReasoningExplanation(WorkloadAnalysis workloadAnalysis,
                                               ConstraintViolationAnalysis constraintAnalysis,
                                               EfficiencyAnalysis efficiencyAnalysis,
                                               MathematicalModelAnalysis modelAnalysis,
                                               RouteCountAction action) {
        
        StringBuilder reasoning = new StringBuilder();
        
        reasoning.append("基于4维分析的综合评估：");
        
        // 工作量分析
        reasoning.append(String.format("\n• 工作量分析：负载均衡指数%.3f，理想路线数%.1f条", 
            workloadAnalysis.getLoadBalanceIndex(), workloadAnalysis.getIdealRouteCount()));
        
        // 约束分析
        reasoning.append(String.format("\n• 约束分析：%.1f%%路线违反450分钟约束，时间差距%.1f分钟", 
            constraintAnalysis.getViolationRate() * 100, constraintAnalysis.getTimeGap()));
        
        // 效率分析
        reasoning.append(String.format("\n• 效率分析：平均效率%.3f，边际效率变化+%.3f/-%.3f", 
            efficiencyAnalysis.getAverageEfficiency(),
            efficiencyAnalysis.getMarginalEfficiencyIncrease(),
            efficiencyAnalysis.getMarginalEfficiencyDecrease()));
        
        // 数学模型
        reasoning.append(String.format("\n• 数学模型：综合评分%.3f，装箱优化潜力%d条路线", 
            modelAnalysis.getOverallModelScore(),
            modelAnalysis.getBinPackingAnalysis().getOptimizationPotential()));
        
        // 结论
        reasoning.append("\n综合判断：").append(action.getReasoning());
        
        return reasoning.toString();
    }
    
    private String generateEvaluationSummary(WorkloadAnalysis workloadAnalysis,
                                            ConstraintViolationAnalysis constraintAnalysis,
                                            RouteCountRecommendation recommendation) {
        
        return String.format("路线数量：%d条，负载均衡：%s，约束满足：%s，推荐：%s", 
            workloadAnalysis.getCurrentRouteCount(),
            workloadAnalysis.getDistributionGrade().getDescription(),
            constraintAnalysis.getSatisfactionGrade().getDescription(),
            recommendation.getRecommendedAction().getDescription());
    }
}