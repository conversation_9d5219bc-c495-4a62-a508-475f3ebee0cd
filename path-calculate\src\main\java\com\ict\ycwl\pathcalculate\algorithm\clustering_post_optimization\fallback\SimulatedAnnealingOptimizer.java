package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 模拟退火算法优化器
 * 
 * 基于物理退火过程的全局优化算法，能够接受部分劣解以跳出局部最优
 * 特别适合约束违反严重的复杂路线优化问题
 * 
 * 算法特点：
 * - 全局搜索能力强，能跳出局部最优
 * - 通过温度控制接受劣解的概率
 * - 适合处理约束违反严重的问题
 * - 收敛速度适中，解质量较高
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class SimulatedAnnealingOptimizer {
    
    // 算法参数
    private static final double INITIAL_TEMPERATURE = 1000.0;    // 初始温度
    private static final double FINAL_TEMPERATURE = 1.0;        // 终止温度
    private static final double COOLING_RATE = 0.95;            // 冷却率
    private static final int ITERATIONS_PER_TEMPERATURE = 100;   // 每个温度的迭代次数
    private static final int MAX_TOTAL_ITERATIONS = 2000;       // 最大总迭代次数
    private static final double CONSTRAINT_PENALTY = 1000.0;    // 约束违反惩罚
    
    // 优化参数
    private static final double MAX_ROUTE_TIME = 450.0;         // 路线时间上限
    private static final double TIME_BALANCE_WEIGHT = 0.7;      // 时间平衡权重
    private static final double CONSTRAINT_WEIGHT = 1.0;       // 约束满足权重
    private static final double GEOGRAPHIC_WEIGHT = 0.3;       // 地理合理性权重
    
    /**
     * 执行模拟退火优化
     * 
     * @param originalRoutes 原始路线
     * @param depot 中转站信息
     * @param timeMatrix 时间矩阵
     * @return 优化结果
     */
    public FallbackOptimizationResult optimize(
            List<List<Accumulation>> originalRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🔥 启动模拟退火算法优化 - 中转站: {}, 路线数: {}", 
            depot.getTransitDepotName(), originalRoutes.size());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 参数验证
            if (originalRoutes == null || originalRoutes.isEmpty()) {
                return createErrorResult("输入路线为空", startTime);
            }
            
            // 初始化
            List<List<Accumulation>> currentSolution = deepCopyRoutes(originalRoutes);
            List<List<Accumulation>> bestSolution = deepCopyRoutes(currentSolution);
            
            double currentScore = evaluateSolution(currentSolution, depot, timeMatrix);
            double bestScore = currentScore;
            
            // 算法执行状态
            double temperature = INITIAL_TEMPERATURE;
            int totalIterations = 0;
            int improvementCount = 0;
            int acceptedMoves = 0;
            
            // 收敛跟踪
            List<Double> scoreHistory = new ArrayList<>();
            scoreHistory.add(currentScore);
            
            log.info("   📊 初始解评分: {:.3f}", currentScore);
            
            // 主优化循环
            while (temperature > FINAL_TEMPERATURE && totalIterations < MAX_TOTAL_ITERATIONS) {
                
                // 当前温度下的迭代
                for (int i = 0; i < ITERATIONS_PER_TEMPERATURE && totalIterations < MAX_TOTAL_ITERATIONS; i++) {
                    totalIterations++;
                    
                    // 生成邻域解
                    List<List<Accumulation>> neighborSolution = generateNeighborSolution(
                        currentSolution, depot, timeMatrix);
                    
                    if (neighborSolution == null) {
                        continue; // 生成邻域解失败，跳过此次迭代
                    }
                    
                    double neighborScore = evaluateSolution(neighborSolution, depot, timeMatrix);
                    double scoreDelta = neighborScore - currentScore;
                    
                    // 接受准则
                    boolean accept = false;
                    if (scoreDelta > 0) {
                        // 更好的解，直接接受
                        accept = true;
                        improvementCount++;
                    } else {
                        // 较差的解，按概率接受
                        double acceptanceProbability = Math.exp(scoreDelta / temperature);
                        accept = Math.random() < acceptanceProbability;
                    }
                    
                    if (accept) {
                        currentSolution = neighborSolution;
                        currentScore = neighborScore;
                        acceptedMoves++;
                        
                        // 更新最优解
                        if (neighborScore > bestScore) {
                            bestSolution = deepCopyRoutes(neighborSolution);
                            bestScore = neighborScore;
                            log.debug("   ✨ 发现更优解: {:.3f} (温度: {:.1f}, 迭代: {})", 
                                bestScore, temperature, totalIterations);
                        }
                    }
                    
                    // 记录评分历史
                    if (totalIterations % 50 == 0) {
                        scoreHistory.add(currentScore);
                    }
                }
                
                // 降温
                temperature *= COOLING_RATE;
                
                // 进度日志
                if (totalIterations % 200 == 0) {
                    double acceptanceRate = (double) acceptedMoves / totalIterations * 100.0;
                    log.debug("   🌡️ 温度: {:.1f}, 迭代: {}, 当前评分: {:.3f}, 最优: {:.3f}, 接受率: {:.1f}%",
                        temperature, totalIterations, currentScore, bestScore, acceptanceRate);
                }
            }
            
            // 计算优化指标
            OptimizationMetrics metrics = calculateOptimizationMetrics(
                originalRoutes, bestSolution, depot, timeMatrix);
            
            // 收敛信息
            FallbackOptimizationResult.ConvergenceInfo convergenceInfo = 
                FallbackOptimizationResult.ConvergenceInfo.builder()
                    .totalIterations(totalIterations)
                    .effectiveImprovements(improvementCount)
                    .convergenceGeneration(totalIterations)
                    .initialScore(scoreHistory.get(0))
                    .finalScore(bestScore)
                    .converged(temperature <= FINAL_TEMPERATURE)
                    .convergenceReason(temperature <= FINAL_TEMPERATURE ? "达到终止温度" : "达到最大迭代次数")
                    .build();
            
            long executionTime = System.currentTimeMillis() - startTime;
            double acceptanceRate = (double) acceptedMoves / totalIterations * 100.0;
            
            String algorithmDetails = String.format(
                "初温:%.0f → 终温:%.1f | 冷却率:%.3f | 总迭代:%d | 改进:%d次 | 接受率:%.1f%%",
                INITIAL_TEMPERATURE, temperature, COOLING_RATE, totalIterations, 
                improvementCount, acceptanceRate);
            
            String parameterInfo = String.format(
                "约束权重:%.1f | 平衡权重:%.1f | 地理权重:%.1f | 每温度迭代:%d",
                CONSTRAINT_WEIGHT, TIME_BALANCE_WEIGHT, GEOGRAPHIC_WEIGHT, ITERATIONS_PER_TEMPERATURE);
            
            boolean success = metrics.getViolationReduction() >= 0 || metrics.getTimeImprovement() > 0;
            
            log.info("✅ 模拟退火优化完成 - 耗时: {}ms, 总迭代: {}, 改进: {}次, 成功: {}", 
                executionTime, totalIterations, improvementCount, success);
            
            return FallbackOptimizationResult.builder()
                .success(success)
                .optimizedRoutes(bestSolution)
                .originalRouteCount(originalRoutes.size())
                .optimizedRouteCount(bestSolution.size())
                .strategy(FallbackStrategy.SIMULATED_ANNEALING)
                .optimizationMetrics(metrics)
                .executionTimeMs(executionTime)
                .algorithmDetails(algorithmDetails)
                .parameterInfo(parameterInfo)
                .convergenceInfo(convergenceInfo)
                .message("模拟退火算法执行完成")
                .build();
                
        } catch (Exception e) {
            log.error("❌ 模拟退火算法执行异常", e);
            return createErrorResult("算法执行异常: " + e.getMessage(), startTime);
        }
    }
    
    /**
     * 生成邻域解
     * 使用多种变换操作：重分配、交换、分割、合并
     */
    private List<List<Accumulation>> generateNeighborSolution(
            List<List<Accumulation>> currentSolution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (currentSolution.isEmpty()) {
            return null;
        }
        
        List<List<Accumulation>> neighbor = deepCopyRoutes(currentSolution);
        Random random = new Random();
        
        // 随机选择变换操作
        int operationType = random.nextInt(4);
        
        try {
            switch (operationType) {
                case 0:
                    // 重分配操作：将聚集区从一条路线移动到另一条路线
                    return performReassignmentMove(neighbor, depot, timeMatrix);
                    
                case 1:
                    // 交换操作：交换两条路线中的聚集区
                    return performSwapMove(neighbor, depot, timeMatrix);
                    
                case 2:
                    // 分割操作：将长路线分割为两条短路线
                    return performSplitMove(neighbor, depot, timeMatrix);
                    
                case 3:
                    // 合并操作：将两条短路线合并为一条长路线
                    return performMergeMove(neighbor, depot, timeMatrix);
                    
                default:
                    return performReassignmentMove(neighbor, depot, timeMatrix);
            }
        } catch (Exception e) {
            log.debug("   ⚠️ 生成邻域解失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 执行重分配操作
     */
    private List<List<Accumulation>> performReassignmentMove(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        // 找到非空路线
        List<Integer> nonEmptyRoutes = new ArrayList<>();
        for (int i = 0; i < solution.size(); i++) {
            if (!solution.get(i).isEmpty()) {
                nonEmptyRoutes.add(i);
            }
        }
        
        if (nonEmptyRoutes.size() < 2) {
            return null; // 需要至少2条非空路线
        }
        
        // 随机选择源路线和目标路线
        int sourceIndex = nonEmptyRoutes.get(random.nextInt(nonEmptyRoutes.size()));
        int targetIndex;
        do {
            targetIndex = random.nextInt(solution.size());
        } while (targetIndex == sourceIndex);
        
        List<Accumulation> sourceRoute = solution.get(sourceIndex);
        List<Accumulation> targetRoute = solution.get(targetIndex);
        
        // 随机选择要移动的聚集区
        int accIndex = random.nextInt(sourceRoute.size());
        Accumulation accToMove = sourceRoute.get(accIndex);
        
        // 检查移动后是否违反约束
        double targetRouteTime = calculateRouteTime(targetRoute, depot, timeMatrix);
        double accTime = calculateAccumulationTime(accToMove, depot, timeMatrix);
        
        if (targetRouteTime + accTime > MAX_ROUTE_TIME * 1.1) { // 允许10%的临时违反
            return null; // 移动会严重违反约束
        }
        
        // 执行移动
        sourceRoute.remove(accIndex);
        targetRoute.add(accToMove);
        
        return solution;
    }
    
    /**
     * 执行交换操作
     */
    private List<List<Accumulation>> performSwapMove(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        // 找到非空路线
        List<Integer> nonEmptyRoutes = new ArrayList<>();
        for (int i = 0; i < solution.size(); i++) {
            if (!solution.get(i).isEmpty()) {
                nonEmptyRoutes.add(i);
            }
        }
        
        if (nonEmptyRoutes.size() < 2) {
            return null;
        }
        
        // 随机选择两条路线
        int route1Index = nonEmptyRoutes.get(random.nextInt(nonEmptyRoutes.size()));
        int route2Index;
        do {
            route2Index = nonEmptyRoutes.get(random.nextInt(nonEmptyRoutes.size()));
        } while (route2Index == route1Index);
        
        List<Accumulation> route1 = solution.get(route1Index);
        List<Accumulation> route2 = solution.get(route2Index);
        
        // 随机选择要交换的聚集区
        int acc1Index = random.nextInt(route1.size());
        int acc2Index = random.nextInt(route2.size());
        
        Accumulation acc1 = route1.get(acc1Index);
        Accumulation acc2 = route2.get(acc2Index);
        
        // 执行交换
        route1.set(acc1Index, acc2);
        route2.set(acc2Index, acc1);
        
        return solution;
    }
    
    /**
     * 执行分割操作
     */
    private List<List<Accumulation>> performSplitMove(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        // 找到可分割的长路线（时间超过350分钟且聚集区数量>=4）
        List<Integer> splittableRoutes = new ArrayList<>();
        for (int i = 0; i < solution.size(); i++) {
            List<Accumulation> route = solution.get(i);
            if (route.size() >= 4) {
                double routeTime = calculateRouteTime(route, depot, timeMatrix);
                if (routeTime > 350.0) {
                    splittableRoutes.add(i);
                }
            }
        }
        
        if (splittableRoutes.isEmpty()) {
            return null;
        }
        
        // 随机选择一条路线进行分割
        int routeIndex = splittableRoutes.get(random.nextInt(splittableRoutes.size()));
        List<Accumulation> routeToSplit = solution.get(routeIndex);
        
        // 随机选择分割点
        int splitPoint = 1 + random.nextInt(routeToSplit.size() - 2);
        
        // 创建两条新路线
        List<Accumulation> newRoute1 = new ArrayList<>(routeToSplit.subList(0, splitPoint));
        List<Accumulation> newRoute2 = new ArrayList<>(routeToSplit.subList(splitPoint, routeToSplit.size()));
        
        // 替换原路线
        solution.set(routeIndex, newRoute1);
        solution.add(newRoute2);
        
        return solution;
    }
    
    /**
     * 执行合并操作
     */
    private List<List<Accumulation>> performMergeMove(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (solution.size() < 2) {
            return null;
        }
        
        Random random = new Random();
        
        // 找到可合并的路线对（合并后不超过450分钟）
        List<int[]> mergeablePairs = new ArrayList<>();
        for (int i = 0; i < solution.size(); i++) {
            for (int j = i + 1; j < solution.size(); j++) {
                double time1 = calculateRouteTime(solution.get(i), depot, timeMatrix);
                double time2 = calculateRouteTime(solution.get(j), depot, timeMatrix);
                
                if (time1 + time2 <= MAX_ROUTE_TIME * 1.05) { // 允许5%的临时违反
                    mergeablePairs.add(new int[]{i, j});
                }
            }
        }
        
        if (mergeablePairs.isEmpty()) {
            return null;
        }
        
        // 随机选择一对路线进行合并
        int[] pair = mergeablePairs.get(random.nextInt(mergeablePairs.size()));
        int route1Index = pair[0];
        int route2Index = pair[1];
        
        List<Accumulation> route1 = solution.get(route1Index);
        List<Accumulation> route2 = solution.get(route2Index);
        
        // 合并路线
        List<Accumulation> mergedRoute = new ArrayList<>(route1);
        mergedRoute.addAll(route2);
        
        // 更新解（保持route1，删除route2）
        solution.set(route1Index, mergedRoute);
        solution.remove(route2Index);
        
        return solution;
    }
    
    /**
     * 评估解的质量
     * 综合考虑约束满足、时间平衡和地理合理性
     */
    private double evaluateSolution(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        double score = 0.0;
        
        // 1. 约束满足评分
        double constraintScore = evaluateConstraintSatisfaction(solution, depot, timeMatrix);
        score += constraintScore * CONSTRAINT_WEIGHT;
        
        // 2. 时间平衡评分
        double balanceScore = evaluateTimeBalance(solution, depot, timeMatrix);
        score += balanceScore * TIME_BALANCE_WEIGHT;
        
        // 3. 地理合理性评分
        double geographicScore = evaluateGeographicRationality(solution, depot, timeMatrix);
        score += geographicScore * GEOGRAPHIC_WEIGHT;
        
        return score;
    }
    
    /**
     * 评估约束满足程度
     */
    private double evaluateConstraintSatisfaction(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        double score = 100.0;
        int violationCount = 0;
        double totalViolationTime = 0.0;
        
        for (List<Accumulation> route : solution) {
            double routeTime = calculateRouteTime(route, depot, timeMatrix);
            if (routeTime > MAX_ROUTE_TIME) {
                violationCount++;
                totalViolationTime += (routeTime - MAX_ROUTE_TIME);
            }
        }
        
        // 扣分：每个违反减10分，每超时1分钟减1分
        score -= violationCount * 10.0;
        score -= totalViolationTime;
        
        return Math.max(0.0, score);
    }
    
    /**
     * 评估时间平衡程度
     */
    private double evaluateTimeBalance(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (solution.isEmpty()) {
            return 0.0;
        }
        
        List<Double> routeTimes = solution.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        double avgTime = routeTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = routeTimes.stream()
            .mapToDouble(time -> Math.pow(time - avgTime, 2))
            .average().orElse(0.0);
        
        // 方差越小，平衡性越好
        double stdDev = Math.sqrt(variance);
        return Math.max(0.0, 100.0 - stdDev); // 标准差越小评分越高
    }
    
    /**
     * 评估地理合理性
     */
    private double evaluateGeographicRationality(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 简化实现：基于路线内聚集区的时间连贯性
        double totalScore = 0.0;
        int routeCount = 0;
        
        for (List<Accumulation> route : solution) {
            if (route.size() > 1) {
                double routeCompactness = evaluateRouteCompactness(route, depot, timeMatrix);
                totalScore += routeCompactness;
                routeCount++;
            }
        }
        
        return routeCount > 0 ? totalScore / routeCount : 50.0;
    }
    
    /**
     * 评估路线紧凑性
     */
    private double evaluateRouteCompactness(
            List<Accumulation> route,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 简化实现：基于时间变异系数
        List<Double> travelTimes = route.stream()
            .mapToDouble(acc -> {
                String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
                TimeInfo timeInfo = timeMatrix.get(key);
                return timeInfo != null && timeInfo.getTravelTime() != null ? 
                    timeInfo.getTravelTime() : 30.0; // 默认30分钟
            })
            .boxed()
            .collect(Collectors.toList());
        
        double avgTravelTime = travelTimes.stream().mapToDouble(Double::doubleValue).average().orElse(30.0);
        double stdDev = Math.sqrt(travelTimes.stream()
            .mapToDouble(time -> Math.pow(time - avgTravelTime, 2))
            .average().orElse(0.0));
        
        double coefficientOfVariation = avgTravelTime > 0 ? stdDev / avgTravelTime : 0.0;
        
        // 变异系数越小，紧凑性越好
        return Math.max(0.0, 100.0 - coefficientOfVariation * 100.0);
    }
    
    /**
     * 计算路线总时间
     */
    private double calculateRouteTime(
            List<Accumulation> route,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (route == null || route.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        
        // 配送时间
        for (Accumulation acc : route) {
            if (acc.getDeliveryTime() != null) {
                totalTime += acc.getDeliveryTime();
            }
        }
        
        // 往返时间
        for (Accumulation acc : route) {
            totalTime += calculateAccumulationTime(acc, depot, timeMatrix);
        }
        
        return totalTime;
    }
    
    /**
     * 计算单个聚集区的往返时间
     */
    private double calculateAccumulationTime(
            Accumulation acc,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
        TimeInfo timeInfo = timeMatrix.get(key);
        if (timeInfo != null && timeInfo.getTravelTime() != null) {
            return timeInfo.getTravelTime() * 2; // 往返
        }
        return 60.0; // 默认往返60分钟
    }
    
    /**
     * 计算优化指标
     */
    private OptimizationMetrics calculateOptimizationMetrics(
            List<List<Accumulation>> originalRoutes,
            List<List<Accumulation>> optimizedRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 计算原始指标
        double originalTotalTime = originalRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        List<Double> originalTimes = originalRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        long originalViolations = originalTimes.stream()
            .mapToLong(time -> time > MAX_ROUTE_TIME ? 1 : 0)
            .sum();
        
        double originalAvg = originalTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double originalStdDev = Math.sqrt(originalTimes.stream()
            .mapToDouble(time -> Math.pow(time - originalAvg, 2))
            .average().orElse(0.0));
        
        // 计算优化后指标
        double optimizedTotalTime = optimizedRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        List<Double> optimizedTimes = optimizedRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        long optimizedViolations = optimizedTimes.stream()
            .mapToLong(time -> time > MAX_ROUTE_TIME ? 1 : 0)
            .sum();
        
        double optimizedAvg = optimizedTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double optimizedStdDev = Math.sqrt(optimizedTimes.stream()
            .mapToDouble(time -> Math.pow(time - optimizedAvg, 2))
            .average().orElse(0.0));
        
        // 计算改进指标
        double timeImprovement = originalTotalTime > 0 ? 
            (originalTotalTime - optimizedTotalTime) / originalTotalTime * 100.0 : 0.0;
        
        double timeBalanceImprovement = originalStdDev > 0 ? 
            (originalStdDev - optimizedStdDev) / originalStdDev * 100.0 : 0.0;
        
        int violationReduction = (int) (originalViolations - optimizedViolations);
        
        double constraintSatisfactionRate = optimizedRoutes.size() > 0 ? 
            1.0 - (double) optimizedViolations / optimizedRoutes.size() : 1.0;
        
        return OptimizationMetrics.builder()
            .originalTotalTime(originalTotalTime)
            .optimizedTotalTime(optimizedTotalTime)
            .timeImprovement(timeImprovement)
            .originalViolations((int) originalViolations)
            .optimizedViolations((int) optimizedViolations)
            .violationReduction(violationReduction)
            .constraintSatisfactionRate(constraintSatisfactionRate)
            .originalTimeStdDev(originalStdDev)
            .optimizedTimeStdDev(optimizedStdDev)
            .timeBalanceImprovement(timeBalanceImprovement)
            .geographicRationalityScore(0.8) // 默认地理合理性评分
            .convergenceScore(0.9) // 默认收敛性评分
            .build();
    }
    
    /**
     * 深度复制路线列表
     */
    private List<List<Accumulation>> deepCopyRoutes(List<List<Accumulation>> originalRoutes) {
        return originalRoutes.stream()
            .map(ArrayList::new)
            .collect(Collectors.toList());
    }
    
    /**
     * 创建错误结果
     */
    private FallbackOptimizationResult createErrorResult(String message, long startTime) {
        return FallbackOptimizationResult.builder()
            .success(false)
            .optimizedRoutes(new ArrayList<>())
            .originalRouteCount(0)
            .optimizedRouteCount(0)
            .strategy(FallbackStrategy.SIMULATED_ANNEALING)
            .message(message)
            .executionTimeMs(System.currentTimeMillis() - startTime)
            .build();
    }
}