package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.pathcalculate.mapper.AccumulationMapper;
import com.ict.ycwl.pathcalculate.mapper.TransitDepotMapper;
import com.ict.ycwl.pathcalculate.mapper.TravelTimeMapper;
import com.ict.ycwl.pathcalculate.pojo.Accumulation;
import com.ict.ycwl.pathcalculate.pojo.TransitDepot;
import com.ict.ycwl.pathcalculate.pojo.TravelTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 时间矩阵生成服务
 * 按中转站分组，为每个中转站内部的所有点对生成完整的时间矩阵
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@Service
public class TravelTimeGeneratorService {
    
    @Autowired
    private AccumulationMapper accumulationMapper;
    
    @Autowired
    private TransitDepotMapper transitDepotMapper;
    
    @Autowired
    private TravelTimeMapper travelTimeMapper;
    
    @Autowired
    private AmapApiService amapApiService;
    
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);
    private static final int BATCH_SIZE = 100; // 批量插入大小
    
    /**
     * 生成所有中转站的完整时间矩阵
     * 
     * @param forceRegenerate 是否强制重新生成（删除现有数据）
     * @return 生成结果统计
     */
    @Transactional
    public GenerationResult generateAllTravelTimeMatrix(boolean forceRegenerate) {
        log.info("开始生成时间矩阵数据，强制重新生成: {}", forceRegenerate);
        
        GenerationResult result = new GenerationResult();
        
        try {
            // 1. 获取所有活跃的中转站
            List<com.ict.ycwl.pathcalculate.pojo.TransitDepot> transitDepots = transitDepotMapper.selectList(null)
                    .stream()
                    .filter(depot -> depot.getIsDelete() == 0)
                    .collect(Collectors.toList());
            
            log.info("找到{}个活跃中转站", transitDepots.size());
            result.setTotalDepots(transitDepots.size());
            
            // 2. 按中转站分组处理
            for (com.ict.ycwl.pathcalculate.pojo.TransitDepot depot : transitDepots) {
                try {
                    DepotGenerationResult depotResult = generateDepotTravelTimeMatrix(depot, forceRegenerate);
                    result.addDepotResult(depotResult);
                    
                    log.info("中转站{}处理完成: 生成{}条记录, API调用{}次, 估算{}次", 
                            depot.getTransitDepotId(), 
                            depotResult.getGeneratedCount(),
                            depotResult.getApiCallCount(),
                            depotResult.getEstimatedCount());
                    
                } catch (Exception e) {
                    log.error("处理中转站{}时发生错误: {}", depot.getTransitDepotId(), e.getMessage(), e);
                    result.addError("中转站" + depot.getTransitDepotId() + ": " + e.getMessage());
                }
            }
            
            log.info("时间矩阵生成完成: 总共生成{}条记录, API调用{}次, 估算{}次, 错误{}个", 
                    result.getTotalGenerated(), 
                    result.getTotalApiCalls(), 
                    result.getTotalEstimated(),
                    result.getErrors().size());
            
        } catch (Exception e) {
            log.error("生成时间矩阵时发生严重错误", e);
            result.addError("系统错误: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 为单个中转站生成时间矩阵
     */
    private DepotGenerationResult generateDepotTravelTimeMatrix(com.ict.ycwl.pathcalculate.pojo.TransitDepot depot, boolean forceRegenerate) {
        log.info("开始处理中转站{}: {}", depot.getTransitDepotId(), depot.getTransitDepotName());
        
        DepotGenerationResult result = new DepotGenerationResult(depot.getTransitDepotId());
        
        // 1. 获取该中转站下的所有聚集区
        List<com.ict.ycwl.pathcalculate.pojo.Accumulation> accumulations = accumulationMapper.selectList(null)
                .stream()
                .filter(acc -> !acc.getIsDelete() &&
                              depot.getTransitDepotId().equals(acc.getTransitDepotId()) &&
                              acc.getLongitude() != null && acc.getLatitude() != null &&
                              acc.getLongitude() != 0 && acc.getLatitude() != 0)
                .collect(Collectors.toList());
        
        log.info("中转站{}包含{}个有效聚集区", depot.getTransitDepotId(), accumulations.size());
        
        // 2. 构建所有坐标点（中转站 + 聚集区）
        List<CoordinatePoint> allPoints = new ArrayList<>();
        
        // 添加中转站坐标（需要转换String到Double）
        Double depotLng = parseCoordinate(depot.getLongitude());
        Double depotLat = parseCoordinate(depot.getLatitude());
        if (depotLng != null && depotLat != null) {
            allPoints.add(new CoordinatePoint(depotLng, depotLat,
                    "depot_" + depot.getTransitDepotId()));
        }

        // 添加聚集区坐标
        for (com.ict.ycwl.pathcalculate.pojo.Accumulation acc : accumulations) {
            allPoints.add(new CoordinatePoint(acc.getLongitude(), acc.getLatitude(),
                    "acc_" + acc.getAccumulationId()));
        }
        
        result.setTotalPoints(allPoints.size());
        int expectedPairs = allPoints.size() * (allPoints.size() - 1);
        result.setExpectedPairs(expectedPairs);
        
        log.info("中转站{}总计{}个坐标点，期望生成{}条时间记录", 
                depot.getTransitDepotId(), allPoints.size(), expectedPairs);
        
        // 3. 如果强制重新生成，先删除现有数据
        if (forceRegenerate) {
            List<String> coordinates = allPoints.stream()
                    .map(p -> String.format("%.6f,%.6f", p.getLongitude(), p.getLatitude()))
                    .collect(Collectors.toList());
            
            int deletedCount = travelTimeMapper.deleteByCoordinates(coordinates);
            log.info("删除中转站{}的现有时间矩阵数据{}条", depot.getTransitDepotId(), deletedCount);
        }
        
        // 4. 生成所有点对的时间矩阵
        List<TravelTime> travelTimes = new ArrayList<>();
        AtomicInteger processedPairs = new AtomicInteger(0);
        
        for (int i = 0; i < allPoints.size(); i++) {
            for (int j = 0; j < allPoints.size(); j++) {
                if (i != j) {
                    CoordinatePoint from = allPoints.get(i);
                    CoordinatePoint to = allPoints.get(j);
                    
                    // 检查是否已存在（如果不是强制重新生成）
                    if (!forceRegenerate) {
                        String fromCoord = String.format("%.6f", from.getLongitude());
                        String fromLat = String.format("%.6f", from.getLatitude());
                        String toCoord = String.format("%.6f", to.getLongitude());
                        String toLat = String.format("%.6f", to.getLatitude());
                        
                        if (travelTimeMapper.existsTravelTime(fromCoord, fromLat, toCoord, toLat)) {
                            result.incrementSkippedCount();
                            continue;
                        }
                    }
                    
                    // 调用高德API获取真实时间
                    AmapApiService.RouteInfo routeInfo = amapApiService.getRouteInfo(
                            from.getLongitude(), from.getLatitude(),
                            to.getLongitude(), to.getLatitude());
                    
                    TravelTime travelTime = new TravelTime(
                            from.getLongitude(), from.getLatitude(),
                            to.getLongitude(), to.getLatitude(),
                            routeInfo.getTravelTime());
                    
                    travelTimes.add(travelTime);
                    
                    if (routeInfo.isFromApi()) {
                        result.incrementApiCallCount();
                    } else {
                        result.incrementEstimatedCount();
                    }
                    
                    // 批量插入
                    if (travelTimes.size() >= BATCH_SIZE) {
                        batchInsertTravelTimes(travelTimes);
                        result.addGeneratedCount(travelTimes.size());
                        travelTimes.clear();
                    }
                    
                    int processed = processedPairs.incrementAndGet();
                    if (processed % 100 == 0) {
                        log.info("中转站{}进度: {}/{} ({:.1f}%)", 
                                depot.getTransitDepotId(), processed, expectedPairs, 
                                (double) processed / expectedPairs * 100);
                    }
                }
            }
        }
        
        // 插入剩余的数据
        if (!travelTimes.isEmpty()) {
            batchInsertTravelTimes(travelTimes);
            result.addGeneratedCount(travelTimes.size());
        }
        
        return result;
    }
    
    /**
     * 批量插入时间矩阵数据
     */
    private void batchInsertTravelTimes(List<TravelTime> travelTimes) {
        try {
            travelTimeMapper.batchInsert(travelTimes);
        } catch (Exception e) {
            log.error("批量插入时间矩阵数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量插入失败", e);
        }
    }

    /**
     * 解析坐标字符串为Double
     */
    private Double parseCoordinate(String coordinate) {
        if (coordinate == null || coordinate.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(coordinate.trim());
        } catch (NumberFormatException e) {
            log.warn("坐标解析失败: {}", coordinate);
            return null;
        }
    }
    
    /**
     * 坐标点数据结构
     */
    private static class CoordinatePoint {
        private final Double longitude;
        private final Double latitude;
        private final String identifier;

        public CoordinatePoint(Double longitude, Double latitude, String identifier) {
            this.longitude = longitude;
            this.latitude = latitude;
            this.identifier = identifier;
        }

        public Double getLongitude() { return longitude; }
        public Double getLatitude() { return latitude; }
        public String getIdentifier() { return identifier; }
    }

    /**
     * 生成结果统计
     */
    public static class GenerationResult {
        private int totalDepots = 0;
        private int totalGenerated = 0;
        private int totalApiCalls = 0;
        private int totalEstimated = 0;
        private List<String> errors = new ArrayList<>();
        private List<DepotGenerationResult> depotResults = new ArrayList<>();

        public void addDepotResult(DepotGenerationResult depotResult) {
            depotResults.add(depotResult);
            totalGenerated += depotResult.getGeneratedCount();
            totalApiCalls += depotResult.getApiCallCount();
            totalEstimated += depotResult.getEstimatedCount();
        }

        public void addError(String error) {
            errors.add(error);
        }

        // Getters and Setters
        public int getTotalDepots() { return totalDepots; }
        public void setTotalDepots(int totalDepots) { this.totalDepots = totalDepots; }

        public int getTotalGenerated() { return totalGenerated; }
        public int getTotalApiCalls() { return totalApiCalls; }
        public int getTotalEstimated() { return totalEstimated; }
        public List<String> getErrors() { return errors; }
        public List<DepotGenerationResult> getDepotResults() { return depotResults; }
    }

    /**
     * 单个中转站的生成结果
     */
    public static class DepotGenerationResult {
        private final Long depotId;
        private int totalPoints = 0;
        private int expectedPairs = 0;
        private int generatedCount = 0;
        private int skippedCount = 0;
        private int apiCallCount = 0;
        private int estimatedCount = 0;

        public DepotGenerationResult(Long depotId) {
            this.depotId = depotId;
        }

        public void incrementApiCallCount() { apiCallCount++; }
        public void incrementEstimatedCount() { estimatedCount++; }
        public void incrementSkippedCount() { skippedCount++; }
        public void addGeneratedCount(int count) { generatedCount += count; }

        // Getters and Setters
        public Long getDepotId() { return depotId; }
        public int getTotalPoints() { return totalPoints; }
        public void setTotalPoints(int totalPoints) { this.totalPoints = totalPoints; }

        public int getExpectedPairs() { return expectedPairs; }
        public void setExpectedPairs(int expectedPairs) { this.expectedPairs = expectedPairs; }

        public int getGeneratedCount() { return generatedCount; }
        public int getSkippedCount() { return skippedCount; }
        public int getApiCallCount() { return apiCallCount; }
        public int getEstimatedCount() { return estimatedCount; }
    }
}
