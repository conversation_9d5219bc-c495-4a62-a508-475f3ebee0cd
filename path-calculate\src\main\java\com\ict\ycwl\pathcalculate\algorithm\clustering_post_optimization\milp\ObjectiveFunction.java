package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 目标函数
 * 
 * 表示MILP问题的目标函数：minimize/maximize Σ(coefficient * variable)
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ObjectiveFunction {
    
    /**
     * 目标函数类型
     */
    private MILPProblem.ObjectiveType type;
    
    /**
     * 目标函数系数（变量名 -> 系数）
     */
    private Map<String, Double> coefficients;
    
    /**
     * 常数项
     */
    @Builder.Default
    private double constant = 0.0;
    
    /**
     * 计算目标函数值
     */
    public double evaluate(Map<String, Double> variableValues) {
        double value = constant;
        for (Map.Entry<String, Double> entry : coefficients.entrySet()) {
            String varName = entry.getKey();
            double coefficient = entry.getValue();
            Double varValue = variableValues.get(varName);
            if (varValue != null) {
                value += coefficient * varValue;
            }
        }
        return value;
    }
    
    /**
     * 生成目标函数表达式字符串
     */
    public String toExpressionString() {
        StringBuilder sb = new StringBuilder();
        
        sb.append(type == MILPProblem.ObjectiveType.MINIMIZE ? "minimize " : "maximize ");
        
        boolean first = true;
        for (Map.Entry<String, Double> entry : coefficients.entrySet()) {
            double coeff = entry.getValue();
            String varName = entry.getKey();
            
            if (!first) {
                sb.append(coeff >= 0 ? " + " : " - ");
                coeff = Math.abs(coeff);
            } else {
                if (coeff < 0) {
                    sb.append("-");
                    coeff = Math.abs(coeff);
                }
                first = false;
            }
            
            if (Math.abs(coeff - 1.0) > 1e-9) {
                sb.append(String.format("%.3f", coeff));
            }
            sb.append(varName);
        }
        
        if (Math.abs(constant) > 1e-9) {
            if (constant > 0 && !first) {
                sb.append(" + ");
            } else if (constant < 0) {
                sb.append(" - ");
                constant = Math.abs(constant);
            }
            sb.append(String.format("%.3f", constant));
        }
        
        if (sb.toString().equals(type == MILPProblem.ObjectiveType.MINIMIZE ? "minimize " : "maximize ")) {
            sb.append("0");
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return toExpressionString();
    }
}