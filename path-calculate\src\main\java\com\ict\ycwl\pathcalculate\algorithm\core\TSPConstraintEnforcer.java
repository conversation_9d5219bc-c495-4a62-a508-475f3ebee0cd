package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TSP约束执行器
 * 在TSP求解后强制执行450分钟硬约束，通过跨路线动态调整实现时间平衡
 */
@Slf4j
@Component
public class TSPConstraintEnforcer {
    
    // 硬约束参数（与用户要求一致）
    private static final double MAX_ROUTE_TIME_HARD_LIMIT = 450.0;  // 450分钟硬约束
    private static final double TIME_GAP_HARD_LIMIT = 30.0;         // 30分钟时间差距硬约束
    private static final double MIN_ROUTE_TIME_LIMIT = 300.0;       // 最小路线时间（5小时）
    
    // 算法参数
    private static final int MAX_REBALANCE_ITERATIONS = 20;         // 最大重平衡迭代次数
    private static final double IMPROVEMENT_THRESHOLD = 5.0;        // 改进阈值（5分钟）
    
    private final TSPSolverManager tspSolverManager;
    
    public TSPConstraintEnforcer() {
        this.tspSolverManager = new TSPSolverManager();
    }
    
    /**
     * 强制执行TSP后的时间约束
     * @param context 算法上下文
     * @return 调整后是否满足约束
     */
    public boolean enforceTimeConstraints(AlgorithmContext context) {
        log.info("🔧 [TSP约束执行] 开始强制执行450分钟硬约束和30分钟时间差约束");
        
        boolean globalConstraintsMet = true;
        int totalAdjustments = 0;
        
        // 对每个中转站执行约束检查和调整
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            
            if (routes.size() <= 1) continue;
            
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            // 检查并修复该中转站的约束违反
            boolean depotConstraintsMet = enforceDepotConstraints(depot, routes, context);
            if (!depotConstraintsMet) {
                globalConstraintsMet = false;
            }
            
            totalAdjustments += rebalanceDepotRoutes(depot, routes, context);
        }
        
        log.info("🎯 [TSP约束执行完成] 总调整次数: {}, 全局约束满足: {}", totalAdjustments, globalConstraintsMet);
        return globalConstraintsMet;
    }
    
    /**
     * 为单个中转站执行约束检查
     */
    private boolean enforceDepotConstraints(TransitDepot depot, List<RouteResult> routes, AlgorithmContext context) {
        boolean constraintsMet = true;
        
        // 检查450分钟硬约束
        List<RouteResult> violatingRoutes = routes.stream()
            .filter(route -> route.getTotalWorkTime() > MAX_ROUTE_TIME_HARD_LIMIT)
            .collect(Collectors.toList());
            
        if (!violatingRoutes.isEmpty()) {
            constraintsMet = false;
            log.warn("🚨 [硬约束违反] 中转站{}有{}条路线超过450分钟硬约束", 
                depot.getTransitDepotName(), violatingRoutes.size());
                
            for (RouteResult route : violatingRoutes) {
                log.warn("   路线{}: {:.1f}分钟 (超过{:.1f}分钟)", 
                    route.getRouteName(), route.getTotalWorkTime(), MAX_ROUTE_TIME_HARD_LIMIT);
            }
        }
        
        // 检查时间差距约束
        if (routes.size() > 1) {
            double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
            double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
            double timeGap = maxTime - minTime;
            
            if (timeGap > TIME_GAP_HARD_LIMIT) {
                constraintsMet = false;
                log.warn("🚨 [时间差距违反] 中转站{}时间差距{:.1f}分钟 (超过{:.1f}分钟)", 
                    depot.getTransitDepotName(), timeGap, TIME_GAP_HARD_LIMIT);
                log.warn("   最长路线: {:.1f}分钟, 最短路线: {:.1f}分钟", maxTime, minTime);
            }
        }
        
        return constraintsMet;
    }
    
    /**
     * 重平衡中转站的路线
     */
    private int rebalanceDepotRoutes(TransitDepot depot, List<RouteResult> routes, AlgorithmContext context) {
        int adjustments = 0;
        
        for (int iteration = 0; iteration < MAX_REBALANCE_ITERATIONS; iteration++) {
            // 当前时间状态
            double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
            double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
            double timeGap = maxTime - minTime;
            
            // 检查是否需要继续调整
            boolean hasViolatingRoutes = routes.stream()
                .anyMatch(route -> route.getTotalWorkTime() > MAX_ROUTE_TIME_HARD_LIMIT);
            boolean hasExcessiveGap = timeGap > TIME_GAP_HARD_LIMIT;
            
            if (!hasViolatingRoutes && !hasExcessiveGap) {
                log.debug("✅ [重平衡完成] 中转站{}在第{}次迭代后满足所有约束", depot.getTransitDepotName(), iteration);
                break;
            }
            
            // 找到最重载和最轻载的路线
            RouteResult heaviestRoute = routes.stream()
                .max(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                .orElse(null);
            RouteResult lightestRoute = routes.stream()
                .min(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                .orElse(null);
                
            if (heaviestRoute == null || lightestRoute == null || heaviestRoute == lightestRoute) {
                break;
            }
            
            // 尝试转移聚集区
            if (trySmartTransfer(heaviestRoute, lightestRoute, depot, context)) {
                adjustments++;
                log.debug("🔄 [路线调整] 中转站{}第{}次迭代：从路线{}转移聚集区到路线{}", 
                    depot.getTransitDepotName(), iteration + 1, 
                    heaviestRoute.getRouteName(), lightestRoute.getRouteName());
            } else {
                // 无法进一步转移，尝试拆分超大路线
                if (heaviestRoute.getTotalWorkTime() > MAX_ROUTE_TIME_HARD_LIMIT && 
                    heaviestRoute.getAccumulationSequence().size() > 2) {
                    
                    if (trySplitOversizedRoute(heaviestRoute, depot, routes, context)) {
                        adjustments++;
                        log.info("✂️ [路线拆分] 拆分超大路线{}以满足450分钟硬约束", heaviestRoute.getRouteName());
                    } else {
                        log.warn("⚠️ [无法拆分] 路线{}无法进一步拆分，将违反450分钟约束", heaviestRoute.getRouteName());
                        break;
                    }
                } else {
                    break; // 无法进一步优化
                }
            }
        }
        
        return adjustments;
    }
    
    /**
     * 智能转移聚集区
     */
    private boolean trySmartTransfer(RouteResult fromRoute, RouteResult toRoute, 
                                   TransitDepot depot, AlgorithmContext context) {
        List<Long> fromSequence = new ArrayList<>(fromRoute.getAccumulationSequence());
        
        if (fromSequence.isEmpty()) return false;
        
        // 选择最佳转移候选：优先转移配送时间长、距离目标路线近的聚集区
        Long bestCandidate = null;
        double bestScore = Double.MIN_VALUE;
        
        for (Long accId : fromSequence) {
            Accumulation acc = context.getAccumulationById(accId);
            if (acc == null) continue;
            
            // 计算转移评分：配送时间权重 + 地理位置权重
            double deliveryTimeScore = acc.getDeliveryTime() / 60.0; // 转换为小时
            double distanceScore = calculateAverageDistanceToRoute(acc, toRoute, context);
            double transferScore = deliveryTimeScore - distanceScore * 0.1; // 配送时间优先，距离次要
            
            if (transferScore > bestScore) {
                bestScore = transferScore;
                bestCandidate = accId;
            }
        }
        
        if (bestCandidate != null) {
            // 预测转移后的时间变化
            Accumulation candidateAcc = context.getAccumulationById(bestCandidate);
            double estimatedTimeReduction = candidateAcc.getDeliveryTime() + 
                estimateRemovalTravelTimeReduction(bestCandidate, fromRoute, depot, context);
            double estimatedTimeIncrease = candidateAcc.getDeliveryTime() + 
                estimateAdditionTravelTimeIncrease(bestCandidate, toRoute, depot, context);
            
            // 检查转移是否改善总体平衡
            double currentGap = fromRoute.getTotalWorkTime() - toRoute.getTotalWorkTime();
            double predictedNewFromTime = fromRoute.getTotalWorkTime() - estimatedTimeReduction;
            double predictedNewToTime = toRoute.getTotalWorkTime() + estimatedTimeIncrease;
            double predictedNewGap = Math.abs(predictedNewFromTime - predictedNewToTime);
            
            // 只有在改善平衡且不违反约束时才转移
            if (predictedNewGap < currentGap && 
                predictedNewFromTime <= MAX_ROUTE_TIME_HARD_LIMIT && 
                predictedNewToTime <= MAX_ROUTE_TIME_HARD_LIMIT &&
                predictedNewToTime >= MIN_ROUTE_TIME_LIMIT) {
                
                // 执行转移
                return executeTransfer(bestCandidate, fromRoute, toRoute, depot, context);
            }
        }
        
        return false;
    }
    
    /**
     * 执行聚集区转移
     */
    private boolean executeTransfer(Long accId, RouteResult fromRoute, RouteResult toRoute, 
                                  TransitDepot depot, AlgorithmContext context) {
        try {
            // 更新路线序列
            List<Long> newFromSequence = new ArrayList<>(fromRoute.getAccumulationSequence());
            List<Long> newToSequence = new ArrayList<>(toRoute.getAccumulationSequence());
            
            newFromSequence.remove(accId);
            newToSequence.add(accId);
            
            // 重新运行TSP优化两条路线
            if (!newFromSequence.isEmpty()) {
                RouteResult optimizedFromRoute = reoptimizeRoute(fromRoute, newFromSequence, depot, context);
                fromRoute.setAccumulationSequence(optimizedFromRoute.getAccumulationSequence());
                fromRoute.setTotalWorkTime(optimizedFromRoute.getTotalWorkTime());
                fromRoute.setPolyline(optimizedFromRoute.getPolyline());
            } else {
                fromRoute.setAccumulationSequence(newFromSequence);
                fromRoute.setTotalWorkTime(AlgorithmParameters.LOADING_TIME_MINUTES);
                fromRoute.setPolyline(Arrays.asList(depot.getCoordinate()));
            }
            
            RouteResult optimizedToRoute = reoptimizeRoute(toRoute, newToSequence, depot, context);
            toRoute.setAccumulationSequence(optimizedToRoute.getAccumulationSequence());
            toRoute.setTotalWorkTime(optimizedToRoute.getTotalWorkTime());
            toRoute.setPolyline(optimizedToRoute.getPolyline());
            
            log.debug("🔄 [转移执行] 聚集区{}从路线{}转移到路线{}，优化后时间: {:.1f}, {:.1f}", 
                accId, fromRoute.getRouteName(), toRoute.getRouteName(),
                fromRoute.getTotalWorkTime(),
                toRoute.getTotalWorkTime());
            
            return true;
            
        } catch (Exception e) {
            log.error("❌ [转移失败] 聚集区{}转移失败: {}", accId, e.getMessage());
            return false;
        }
    }
    
    /**
     * 尝试拆分超大路线
     */
    private boolean trySplitOversizedRoute(RouteResult oversizedRoute, TransitDepot depot, 
                                         List<RouteResult> allRoutes, AlgorithmContext context) {
        List<Long> sequence = oversizedRoute.getAccumulationSequence();
        
        if (sequence.size() <= 2) return false; // 无法再拆分
        
        try {
            // 简单拆分：按序列中点拆分
            int midPoint = sequence.size() / 2;
            List<Long> firstHalf = new ArrayList<>(sequence.subList(0, midPoint));
            List<Long> secondHalf = new ArrayList<>(sequence.subList(midPoint, sequence.size()));
            
            // 重新优化第一部分（保留原路线）
            RouteResult optimizedFirst = reoptimizeRoute(oversizedRoute, firstHalf, depot, context);
            oversizedRoute.setAccumulationSequence(optimizedFirst.getAccumulationSequence());
            oversizedRoute.setTotalWorkTime(optimizedFirst.getTotalWorkTime());
            oversizedRoute.setPolyline(optimizedFirst.getPolyline());
            
            // 创建新路线处理第二部分
            int newRouteNumber = allRoutes.size() + 1;
            RouteResult newRoute = new RouteResult();
            newRoute.setRouteId((long) newRouteNumber);
            newRoute.setRouteName(depot.getTransitDepotName() + "-路线" + newRouteNumber);
            newRoute.setTransitDepotId(depot.getTransitDepotId());
            
            RouteResult optimizedSecond = reoptimizeRoute(newRoute, secondHalf, depot, context);
            newRoute.setAccumulationSequence(optimizedSecond.getAccumulationSequence());
            newRoute.setTotalWorkTime(optimizedSecond.getTotalWorkTime());
            newRoute.setPolyline(optimizedSecond.getPolyline());
            
            // 添加新路线到列表
            allRoutes.add(newRoute);
            
            log.info("✂️ [路线拆分成功] 拆分路线{}为两条路线：{:.1f}分钟 + {:.1f}分钟", 
                oversizedRoute.getRouteName(), 
                optimizedFirst.getTotalWorkTime(), 
                optimizedSecond.getTotalWorkTime());
            
            return true;
            
        } catch (Exception e) {
            log.error("❌ [拆分失败] 路线{}拆分失败: {}", oversizedRoute.getRouteName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 重新优化路线
     */
    private RouteResult reoptimizeRoute(RouteResult originalRoute, List<Long> newSequence, 
                                      TransitDepot depot, AlgorithmContext context) throws Exception {
        
        List<Accumulation> accumulations = newSequence.stream()
            .map(context::getAccumulationById)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
            
        if (accumulations.isEmpty()) {
            RouteResult emptyRoute = new RouteResult();
            emptyRoute.setAccumulationSequence(new ArrayList<>());
            emptyRoute.setTotalWorkTime(AlgorithmParameters.LOADING_TIME_MINUTES);
            emptyRoute.setPolyline(Arrays.asList(depot.getCoordinate()));
            return emptyRoute;
        }
        
        // 使用TSPSolverManager重新优化
        return tspSolverManager.solveRoute(
            depot, 
            accumulations, 
            context.getTimeMatrix(), 
            originalRoute.getRouteId().intValue() % 1000,
            TSPSolverManager.SolverStrategy.AUTO,
            MultiObjectiveTSP.OptimizationGoal.BALANCED,
            30000L // 30秒时间限制
        );
    }
    
    // 辅助方法
    
    private double calculateAverageDistanceToRoute(Accumulation acc, RouteResult route, AlgorithmContext context) {
        List<Long> sequence = route.getAccumulationSequence();
        if (sequence.isEmpty()) return 0.0;
        
        double totalDistance = 0.0;
        int count = 0;
        
        for (Long targetAccId : sequence) {
            Accumulation targetAcc = context.getAccumulationById(targetAccId);
            if (targetAcc != null) {
                totalDistance += calculateEuclideanDistance(acc.getCoordinate(), targetAcc.getCoordinate());
                count++;
            }
        }
        
        return count > 0 ? totalDistance / count : 0.0;
    }
    
    private double estimateRemovalTravelTimeReduction(Long accId, RouteResult route, 
                                                    TransitDepot depot, AlgorithmContext context) {
        // 简化估算：移除一个点大约减少该点配送时间的10-20%行驶时间
        Accumulation acc = context.getAccumulationById(accId);
        return acc != null ? acc.getDeliveryTime() * 0.15 : 0.0;
    }
    
    private double estimateAdditionTravelTimeIncrease(Long accId, RouteResult route, 
                                                    TransitDepot depot, AlgorithmContext context) {
        // 简化估算：添加一个点大约增加该点配送时间的20-30%行驶时间
        Accumulation acc = context.getAccumulationById(accId);
        return acc != null ? acc.getDeliveryTime() * 0.25 : 0.0;
    }
    
    private double calculateEuclideanDistance(CoordinatePoint p1, CoordinatePoint p2) {
        double dLat = Math.toRadians(p2.getLatitude() - p1.getLatitude());
        double dLon = Math.toRadians(p2.getLongitude() - p1.getLongitude());
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(p1.getLatitude())) * Math.cos(Math.toRadians(p2.getLatitude())) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return 6371.0 * c; // 地球半径
    }
}