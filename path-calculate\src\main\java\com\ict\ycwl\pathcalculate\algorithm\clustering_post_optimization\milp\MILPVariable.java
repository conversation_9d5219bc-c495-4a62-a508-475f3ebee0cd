package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import lombok.Builder;
import lombok.Data;

/**
 * MILP决策变量
 * 
 * 表示混合整数线性规划中的决策变量
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class MILPVariable {
    
    /**
     * 变量名称
     */
    private String name;
    
    /**
     * 变量类型
     */
    private MILPProblem.VariableType type;
    
    /**
     * 下界
     */
    private double lowerBound;
    
    /**
     * 上界
     */
    private double upperBound;
    
    /**
     * 变量值（求解后）
     */
    private Double value;
    
    /**
     * 变量描述
     */
    private String description;
    
    /**
     * 是否为二进制变量
     */
    public boolean isBinary() {
        return type == MILPProblem.VariableType.BINARY;
    }
    
    /**
     * 是否为整数变量
     */
    public boolean isInteger() {
        return type == MILPProblem.VariableType.INTEGER || isBinary();
    }
    
    /**
     * 是否为连续变量
     */
    public boolean isContinuous() {
        return type == MILPProblem.VariableType.CONTINUOUS;
    }
    
    /**
     * 检查值是否在边界内
     */
    public boolean isValueInBounds(double value) {
        return value >= lowerBound && value <= upperBound;
    }
    
    /**
     * 检查值是否满足变量类型约束
     */
    public boolean isValueValid(double value) {
        if (!isValueInBounds(value)) {
            return false;
        }
        
        if (isInteger()) {
            return Math.abs(value - Math.round(value)) < 1e-9;
        }
        
        return true;
    }
    
    /**
     * 获取变量范围描述
     */
    public String getBoundDescription() {
        if (lowerBound == upperBound) {
            return String.format("= %.2f", lowerBound);
        } else if (Double.isInfinite(lowerBound) && Double.isInfinite(upperBound)) {
            return "(-∞, +∞)";
        } else if (Double.isInfinite(lowerBound)) {
            return String.format("(-∞, %.2f]", upperBound);
        } else if (Double.isInfinite(upperBound)) {
            return String.format("[%.2f, +∞)", lowerBound);
        } else {
            return String.format("[%.2f, %.2f]", lowerBound, upperBound);
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(name).append(" (").append(type.getDescription()).append(", ");
        sb.append(getBoundDescription()).append(")");
        if (value != null) {
            sb.append(" = ").append(String.format("%.6f", value));
        }
        return sb.toString();
    }
}