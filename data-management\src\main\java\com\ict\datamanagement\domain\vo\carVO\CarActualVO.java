package com.ict.datamanagement.domain.vo.carVO;

import lombok.Data;

@Data
public class CarActualVO {
    //车辆id
    private Long carId;
    //车牌号
    private String licensePlateNumber;
    //驾驶人名称
    private String carDriverName;
    //实际载货量
    private String actualLoad;
    //实际工作时长
    private String actualTime;
    //路线
    private String routeName;
    //星期
    private String week;
    //日期
    private String date;
    //所属班组
    private String teamName;
    //配送域
    private String deliveryAreaName;
}
