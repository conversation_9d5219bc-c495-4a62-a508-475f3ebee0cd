package com.ict.datamanagement.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.common.StatusCode;
import com.ict.datamanagement.domain.dto.store.*;
import com.ict.datamanagement.domain.entity.Store;
import com.ict.datamanagement.domain.entity.StoreTwo;
import com.ict.datamanagement.domain.vo.StoreVO;
import com.ict.datamanagement.domain.vo.storeVO.CheckInPointVo;
import com.ict.datamanagement.domain.vo.storeVO.StoreDownBox;
import com.ict.datamanagement.domain.vo.storeVO.StoreListVo;
import com.ict.datamanagement.exception.BusinessException;
import com.ict.datamanagement.mapper.StoreMapper;
import com.ict.datamanagement.mapper.StoreTwoMapper;
import com.ict.datamanagement.service.AreaService;
import com.ict.datamanagement.service.CarService;
import com.ict.datamanagement.service.StoreService;
import com.ict.datamanagement.util.BeijingTimeUtil;
import com.ict.datamanagement.util.CsvImportUtil;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.annotation.UserLoginToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Api(tags = "商铺管理")
@Slf4j
@RestController("/store")
public class StoreController {

    @Resource
    private StoreService storeService;

    @Resource
    private AreaService areaService;

    @Autowired
    private CarService carService;

    @Autowired
    private CsvImportUtil csvImportUtil;

    @Autowired
    private StoreTwoMapper storeTwoMapper;

    @Autowired
    private StoreMapper storeMapper;

    @ApiOperation("商铺分页列表")
    @GetMapping("/list")
    public BaseResponse<Page<StoreListVo>> list(StoreListRequest storeListRequest) {
        List<Store> stores= storeService.getStoreList(storeListRequest);
        int pageNum = storeListRequest.getPageNum();
        int pageSize = storeListRequest.getPageSize();
        Page<Store> page = storeService.getPage(pageNum, pageSize, stores);

        Page<StoreListVo> storeListVoPage = new Page<>();
        storeListVoPage.setTotal(page.getTotal());
        storeListVoPage.setSize(page.getSize());
        storeListVoPage.setPages(page.getPages());
        storeListVoPage.setCurrent(page.getCurrent());

        List<StoreListVo> list = page.getRecords().stream().map(store -> {
            StoreListVo vo = new StoreListVo();
            if("1".equals(store.getStatus())){
                store.setStatus("正常");
            }else{
                store.setStatus("异常");
            }
            //处理商铺时间
            String createTime = store.getCreateTime();
            // 解析原始字符串为LocalDateTime对象
            LocalDateTime dateTime = LocalDateTime.parse(createTime,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"));

            // 重新格式化为目标格式
            String formatted = dateTime.format(
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

            store.setCreateTime(formatted);
            BeanUtils.copyProperties(store, vo);
            vo.setAccumulationName(store.getAccumulationAddress());
            vo.setAreaName(store.getDistrict());
            StoreTwo storeTwo = storeTwoMapper.selectById(store.getStoreId());
            if (storeTwo != null) {
                vo.setHead(storeTwo.getHead());
            }
            return vo;
        }).collect(Collectors.toList());
        storeListVoPage.setRecords(list);

        return ResultUtils.success(storeListVoPage);
    }

    @ApiOperation("添加商铺")
    @PostMapping("/add")
    public BaseResponse<String> add(@RequestBody StoreAddRequest storeAddRequest) {
        //todo 参数校验
        Store store = new Store();
        //截取经纬度为小数点后6位
        storeAddRequest.setLongitude(Double.parseDouble(String.format("%.6f",storeAddRequest.getLongitude())));
        storeAddRequest.setLatitude(Double.parseDouble(String.format("%.6f",storeAddRequest.getLatitude())));
        BeanUtils.copyProperties(storeAddRequest, store);
        if (storeAddRequest.getLocationType().equals("城区")) {
            store.setLocationType("0");
        } else if (storeAddRequest.getLocationType().equals("乡镇")) {
            store.setLocationType("1");
        } else {
            throw new BusinessException(StatusCode.Parameter_ERROR);
        }
        //获取当前日期时间
        LocalDateTime now = LocalDateTime.now();

        // 格式化日期时间为指定的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        String formattedDateTime = now.format(formatter);
        store.setCreateTime(formattedDateTime);
        boolean save = storeService.save(store);

        StoreTwo storeTwo = new StoreTwo();
        BeanUtils.copyProperties(storeAddRequest,storeTwo);
        storeTwo.setStoreId(store.getStoreId());
        storeTwoMapper.insert(storeTwo);

        if (!save) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }
        return ResultUtils.success();
    }

    @ApiOperation("添加商铺下拉框")
    @PostMapping("/getAddStoreDownBox")
    public BaseResponse<StoreDownBox> getAddStoreDownBox(){
        StoreDownBox res=storeService.getAddStoreDownBox();
        if(res==null){
            return ResultUtils.error(500,"获取失败");
        }
        return ResultUtils.success(res);
    }


    @ApiOperation("获取商铺详细信息")
    @GetMapping("/get/{storeId}")
    public BaseResponse<StoreVO> get(@PathVariable("storeId") Long storeId) {
        //Store store = storeService.getById(storeId);
        Store store = storeMapper.selectById(storeId);
        if (store == null) {
            throw new BusinessException(StatusCode.Parameter_ERROR);
        }
        StoreVO storeVO = storeService.getStoreVO(store);
        return ResultUtils.success(storeVO);
    }

    @ApiOperation("更新商铺信息")
    @PostMapping("/update")
    public BaseResponse<String> add(@RequestBody StoreUpdateRequest storeUpdateRequest) {
        //todo 参数校验
        Store store = new Store();
        //截取经纬度到小数点后六位
        storeUpdateRequest.setLongitude(Double.parseDouble(String.format("%.6f",storeUpdateRequest.getLongitude())));
        storeUpdateRequest.setLatitude(Double.parseDouble(String.format("%.6f",storeUpdateRequest.getLatitude())));
        //处理商铺类型
        storeUpdateRequest.setLocationType(storeUpdateRequest.getLocationType().equals("城区")?"0":"1");
        BeanUtils.copyProperties(storeUpdateRequest, store);
        boolean update = storeService.updateById(store);
        if (!update) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }
        StoreTwo storeTwo = new StoreTwo();
        BeanUtils.copyProperties(storeUpdateRequest,storeTwo);
        StoreTwo storeTwo1 = storeTwoMapper.selectOne(new LambdaQueryWrapper<StoreTwo>().eq(StoreTwo::getStoreId,storeTwo.getStoreId()));
        if(storeTwo1!=null) {
            storeTwoMapper.update(storeTwo, new LambdaQueryWrapper<StoreTwo>().eq(StoreTwo::getStoreId, storeTwo.getStoreId()));
        }else{
            storeTwoMapper.insert(storeTwo);
        }
        return ResultUtils.success();
    }

    @ApiOperation("批量删除商铺")
    @DeleteMapping("/delete/{storeIdList}")
    public BaseResponse<String> delete(@PathVariable("storeIdList") List<Long> storeIdList) {
        boolean remove = storeService.removeBatchByIds(storeIdList);
        if (!remove) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }

        return ResultUtils.success();
    }

    @ApiOperation("修改商铺所属行政区")
    @PostMapping("/updateArea")
    public BaseResponse<String> updateArea(@RequestBody UpdateAreaRequest updateAreaRequest) {
        List<Long> storeIdList = updateAreaRequest.getStoreIdList();
        Long areaId = updateAreaRequest.getAreaId();
        String areaName = areaService.getById(areaId).getAreaName();

        // 参数校验
        Long areaTotalId = 0L;
        for (int i = 0; i < storeIdList.size(); i++) {
            Store store = storeService.getById(storeIdList.get(i));
            if (store == null) {
                throw new BusinessException(StatusCode.Parameter_ERROR);
            }
            Long areaOldId = store.getAreaId();
            if (areaOldId == null) {
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }
            if (i == 0) {
                areaTotalId = areaOldId;
            } else {
                if (!areaTotalId.equals(areaOldId)) {
                    throw new BusinessException(StatusCode.Parameter_ERROR);
                }
            }
        }

        boolean update = false;
        for (Long id : storeIdList) {
            Store store = Store.builder().storeId(id).areaName(areaName).areaId(areaId).build();
            update = storeService.updateById(store);
        }

        if (!update) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }

        return ResultUtils.success();
    }

    @ApiOperation("商铺导入")
    @PostMapping(value = "/storeImport")
    public String csvImport(@RequestParam("File") MultipartFile file, @RequestParam String mode, @RequestHeader("Authorization") String authorization) {
        System.out.println(authorization);
        System.out.println(mode);
        String contentType = file.getContentType();
        File File = csvImportUtil.uploadFile(file);
        //xlsx:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
        //xls:application/vnd.ms-excel
        String beijingTime = "";
        String info = "文件格式不正确";
        try {
            beijingTime = BeijingTimeUtil.getBeijingTime();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            String regex = "^(\\d{4})/(0[1-9]|[1-9]|1[0-2])/([1-9]|0[1-9]|[12]\\d|3[01])$";
            // 编译正则表达式
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(beijingTime);
            //校验日期是否合法
            if (!StringUtil.isNotBlank(beijingTime) || !matcher.matches()) {
                // 设置时区为东八区
                ZoneId zoneId = ZoneId.of("Asia/Shanghai");

                // 获取东八区的当前时间
                ZonedDateTime now = ZonedDateTime.now(zoneId);

                // 设置时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

                // 格式化时间
                beijingTime = now.format(formatter);
            }
            if ("text/csv".equals(contentType)) {
                //处理csv格式文件
                System.out.println("date1: " + beijingTime);
                // 使用CSV工具类，生成file文件
                //上传文件
                boolean flag = carService.checkExportFrom(File, contentType);
                if (!flag) {
                    info = "导入失败,csv文件中的格式与模板不符";
                } else {
                    info = storeService.readCsv(File, beijingTime, mode, authorization);
                }
            } else if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType) || "application/vnd.ms-excel".equals(contentType)) {
                //处理xlsx，xls文件
                boolean flag = carService.checkExportFrom(File, contentType);
                if (!flag) {
                    info = "导入失败,Excel文件中的格式与模板不符";

                } else {
                    info = storeService.exportExcel(beijingTime, File, mode, authorization);
                }
            }
        }
        return info;
    }

    @ApiOperation("商铺导出")
    @GetMapping("/storeExport")
    public AjaxResult Export(HttpServletResponse response) throws IOException {
        String info=storeService.export(response);
        return AjaxResult.success(info);
    }

    @ApiOperation("获取所属大区")
    @GetMapping("/getArea")
    public AjaxResult getArea(){
        return storeService.getArea();
    }


    @ApiOperation("获取所有的打卡点")
    @GetMapping("/getCheckInPoint")
    public AjaxResult getCheckInPoint(){
        List<Map<String, List<CheckInPointVo>>> map = storeService.getCheckInPoint();
        return AjaxResult.success(map);
    }

    @ApiOperation(value = "特殊点标记和添加信息",notes = "storeId:商铺id，isSpecialPoint:0表示不是特殊点，1表示是特殊点；remark：特殊点备注；specialType：特殊点类型，如跨马路特殊点，结冰期特殊点")
    @PostMapping("/addSpecialPoint")
    @UserLoginToken
    public AjaxResult addSpecialPoint(@RequestBody List<AddSpecialPointRequest> addSpecialPointRequest){
        AjaxResult info= storeService.addSpecialPoint(addSpecialPointRequest);
        return info;
    }

}
