package com.ict.datamanagement.domain.dto.car;

import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;

@Data
public class CarFile {
    //车牌号
    @CsvBindByPosition(position = 0)
    private String licensePlateNumber;
    //驾驶人名称
    @CsvBindByPosition(position = 1)
    private String carDriverName;
    //实际载货量
    @CsvBindByPosition(position = 2)
    private String actualLoad;
    //实际工作时长
    @CsvBindByPosition(position = 3)
    private String actualTime;
    //路线
    @CsvBindByPosition(position = 4)
    private String routeName;
    //星期
    @CsvBindByPosition(position = 5)
    private String week;
    //日期
    @CsvBindByPosition(position = 6)
    private String date;
    //配送域
    @CsvBindByPosition(position = 7)
    private String deliveryAreaName;
    //结论
    @CsvBindByPosition(position = 8)
    private String conclusion;
    //导入详情
    @CsvBindByPosition(position = 9)
    private String ImportDetails;
}
