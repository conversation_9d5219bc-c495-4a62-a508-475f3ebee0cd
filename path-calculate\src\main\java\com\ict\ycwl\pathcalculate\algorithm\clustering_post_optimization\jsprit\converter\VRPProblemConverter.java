package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.converter;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain.*;

import com.graphhopper.jsprit.core.problem.VehicleRoutingProblem;
import com.graphhopper.jsprit.core.problem.job.Service;
import com.graphhopper.jsprit.core.problem.vehicle.Vehicle;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleImpl;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleType;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleTypeImpl;
import com.graphhopper.jsprit.core.problem.Location;
import com.graphhopper.jsprit.core.problem.solution.VehicleRoutingProblemSolution;
import com.graphhopper.jsprit.core.problem.solution.route.VehicleRoute;
import com.graphhopper.jsprit.core.problem.solution.route.activity.TourActivity;
import com.graphhopper.jsprit.core.util.Coordinate;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * VRP问题转换器
 * 
 * 负责在我们的域模型和JSPRIT库之间进行数据转换
 * 包括问题转换、解决方案转换等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Slf4j
@Component
public class VRPProblemConverter {
    
    /**
     * 将聚类结果转换为JSPRIT VehicleRoutingProblem
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @param constraints 约束配置
     * @return JSPRIT VRP问题
     */
    public VehicleRoutingProblem convertToJspritProblem(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix,
        VRPConstraints constraints
    ) {
        log.debug("🔄 开始转换聚类结果为JSPRIT VRP问题");
        
        if (depot == null || clusters == null || clusters.isEmpty()) {
            throw new IllegalArgumentException("中转站和聚类结果不能为空");
        }
        
        VehicleRoutingProblem.Builder vrpBuilder = VehicleRoutingProblem.Builder.newInstance();
        
        // 创建中转站位置
        Location depotLocation = createDepotLocation(depot);
        
        // 转换车辆
        List<Vehicle> jspritVehicles = convertVehicles(depot, clusters, constraints, depotLocation);
        for (Vehicle vehicle : jspritVehicles) {
            vrpBuilder.addVehicle(vehicle);
        }
        
        // 转换服务
        List<Service> jspritServices = convertServices(depot, clusters, timeMatrix, constraints);
        for (Service service : jspritServices) {
            vrpBuilder.addJob(service);
        }
        
        // 设置距离矩阵（如果有时间矩阵）
        if (timeMatrix != null && !timeMatrix.isEmpty()) {
            vrpBuilder.setRoutingCost(createDistanceMatrix(depot, clusters, timeMatrix));
        }
        
        VehicleRoutingProblem vrpProblem = vrpBuilder.build();
        
        log.debug("✅ VRP问题转换完成 - 车辆数: {}, 服务数: {}", 
            jspritVehicles.size(), jspritServices.size());
        
        return vrpProblem;
    }
    
    /**
     * 将JSPRIT解决方案转换回聚类结果
     * 
     * @param solution JSPRIT解决方案
     * @param originalClusters 原始聚类结果
     * @param depot 中转站
     * @return 优化后的聚类结果
     */
    public List<List<Accumulation>> convertFromJspritSolution(
        VehicleRoutingProblemSolution solution,
        List<List<Accumulation>> originalClusters,
        TransitDepot depot
    ) {
        log.debug("🔄 开始转换JSPRIT解决方案为聚类结果");
        
        if (solution == null || originalClusters == null) {
            log.warn("⚠️ 解决方案或原始聚类为空，返回原始聚类");
            return deepCopyOfClusters(originalClusters);
        }
        
        // 创建聚集区ID到对象的映射
        Map<String, Accumulation> accumulationMap = createAccumulationMap(originalClusters);
        
        // 初始化结果聚类
        List<List<Accumulation>> resultClusters = new ArrayList<>();
        
        // 处理每个车辆路线
        Collection<VehicleRoute> routes = solution.getRoutes();
        for (VehicleRoute route : routes) {
            List<Accumulation> routeAccumulations = new ArrayList<>();
            
            // 提取路线中的所有服务
            for (TourActivity activity : route.getActivities()) {
                if (activity instanceof com.graphhopper.jsprit.core.problem.solution.route.activity.ServiceActivity) {
                    com.graphhopper.jsprit.core.problem.solution.route.activity.ServiceActivity serviceActivity = 
                        (com.graphhopper.jsprit.core.problem.solution.route.activity.ServiceActivity) activity;
                    Service service = (Service) serviceActivity.getJob();
                    String serviceId = service.getId();
                    
                    // 从服务ID提取聚集区ID
                    String accumulationId = extractAccumulationIdFromServiceId(serviceId);
                    Accumulation accumulation = accumulationMap.get(accumulationId);
                    
                    if (accumulation != null) {
                        routeAccumulations.add(accumulation);
                    } else {
                        log.warn("⚠️ 找不到聚集区: {}", accumulationId);
                    }
                }
            }
            
            if (!routeAccumulations.isEmpty()) {
                resultClusters.add(routeAccumulations);
            }
        }
        
        // 检查是否有未分配的聚集区
        Set<String> assignedIds = resultClusters.stream()
            .flatMap(List::stream)
            .map(acc -> String.valueOf(acc.getAccumulationId()))
            .collect(Collectors.toSet());
        
        Set<String> allIds = accumulationMap.keySet();
        Set<String> unassignedIds = new HashSet<>(allIds);
        unassignedIds.removeAll(assignedIds);
        
        if (!unassignedIds.isEmpty()) {
            log.warn("⚠️ 发现{}个未分配的聚集区，将创建单独路线", unassignedIds.size());
            for (String unassignedId : unassignedIds) {
                Accumulation unassigned = accumulationMap.get(unassignedId);
                if (unassigned != null) {
                    resultClusters.add(Arrays.asList(unassigned));
                }
            }
        }
        
        log.debug("✅ JSPRIT解决方案转换完成 - 路线数: {} → {}", 
            originalClusters.size(), resultClusters.size());
        
        return resultClusters;
    }
    
    /**
     * 创建中转站位置
     * 
     * @param depot 中转站
     * @return JSPRIT位置对象
     */
    private Location createDepotLocation(TransitDepot depot) {
        Coordinate coordinate = Coordinate.newInstance(depot.getLongitude(), depot.getLatitude());
        return Location.Builder.newInstance()
            .setId("depot_" + depot.getTransitDepotId())
            .setName(depot.getTransitDepotName())
            .setCoordinate(coordinate)
            .build();
    }
    
    /**
     * 转换车辆列表
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param constraints 约束配置
     * @param depotLocation 中转站位置
     * @return JSPRIT车辆列表
     */
    private List<Vehicle> convertVehicles(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        VRPConstraints constraints,
        Location depotLocation
    ) {
        List<Vehicle> vehicles = new ArrayList<>();
        
        for (int i = 0; i < clusters.size(); i++) {
            String vehicleId = "vehicle_" + depot.getTransitDepotId() + "_" + i;
            
            // 创建车辆类型
            VehicleType vehicleType = createVehicleType(vehicleId, constraints);
            
            // 创建车辆
            VehicleImpl.Builder vehicleBuilder = VehicleImpl.Builder.newInstance(vehicleId);
            vehicleBuilder.setStartLocation(depotLocation);
            
            if (constraints.getRequireReturnToDepot()) {
                vehicleBuilder.setEndLocation(depotLocation);
            }
            
            vehicleBuilder.setType(vehicleType);
            
            // 设置时间窗口
            if (constraints.getEnableTimeWindowConstraints()) {
                vehicleBuilder.setEarliestStart(0.0); // 工作日开始
                vehicleBuilder.setLatestArrival(constraints.getMaxVehicleWorkTimeMinutes() * 60); // 转换为秒
            }
            
            vehicles.add(vehicleBuilder.build());
        }
        
        return vehicles;
    }
    
    /**
     * 创建车辆类型
     * 
     * @param vehicleTypeId 车辆类型ID
     * @param constraints 约束配置
     * @return JSPRIT车辆类型
     */
    private VehicleType createVehicleType(String vehicleTypeId, VRPConstraints constraints) {
        VehicleTypeImpl.Builder typeBuilder = VehicleTypeImpl.Builder.newInstance(vehicleTypeId + "_type");
        
        // 设置容量（使用时间维度）
        int timeCapacitySeconds = (int) (constraints.getMaxVehicleWorkTimeMinutes() * 60);
        typeBuilder.addCapacityDimension(0, timeCapacitySeconds);
        
        // 设置成本
        typeBuilder.setCostPerDistance(constraints.getVehicleCostWeight());
        typeBuilder.setCostPerTime(constraints.getTimeBalanceWeight());
        typeBuilder.setFixedCost(100.0);
        
        return typeBuilder.build();
    }
    
    /**
     * 转换服务列表
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @param constraints 约束配置
     * @return JSPRIT服务列表
     */
    private List<Service> convertServices(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix,
        VRPConstraints constraints
    ) {
        List<Service> services = new ArrayList<>();
        
        for (List<Accumulation> cluster : clusters) {
            for (Accumulation accumulation : cluster) {
                Service service = convertSingleService(accumulation, depot, timeMatrix, constraints);
                services.add(service);
            }
        }
        
        return services;
    }
    
    /**
     * 转换单个服务
     * 
     * @param accumulation 聚集区
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @param constraints 约束配置
     * @return JSPRIT服务
     */
    private Service convertSingleService(
        Accumulation accumulation,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        VRPConstraints constraints
    ) {
        String serviceId = "service_" + accumulation.getAccumulationId();
        
        // 创建服务位置
        Coordinate coordinate = Coordinate.newInstance(accumulation.getLongitude(), accumulation.getLatitude());
        Location serviceLocation = Location.Builder.newInstance()
            .setId("acc_" + accumulation.getAccumulationId())
            .setName(accumulation.getAccumulationName())
            .setCoordinate(coordinate)
            .build();
        
        Service.Builder serviceBuilder = Service.Builder.newInstance(serviceId);
        serviceBuilder.setLocation(serviceLocation);
        
        // 设置服务时间
        Double serviceTime = accumulation.getDeliveryTime() != null ? accumulation.getDeliveryTime() : 30.0;
        serviceBuilder.setServiceTime((long) (serviceTime * 60)); // 转换为秒
        
        // 设置需求（时间维度）
        Double demandTime = calculateServiceDemandTime(accumulation, depot, timeMatrix);
        serviceBuilder.addSizeDimension(0, (int) (demandTime * 60)); // 转换为秒
        
        // 设置时间窗口
        if (constraints.getEnableTimeWindowConstraints()) {
            serviceBuilder.setTimeWindow(com.graphhopper.jsprit.core.problem.solution.route.activity.TimeWindow.newInstance(0.0, 7 * 60 * 60)); // 7小时工作窗口，转换为秒
        }
        
        // 设置优先级
        int priority = determineServicePriority(accumulation);
        serviceBuilder.setPriority(priority);
        
        return serviceBuilder.build();
    }
    
    /**
     * 计算服务需求时间
     * 
     * @param accumulation 聚集区
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 需求时间（分钟）
     */
    private Double calculateServiceDemandTime(
        Accumulation accumulation,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        // 配送时间
        Double deliveryTime = accumulation.getDeliveryTime() != null ? accumulation.getDeliveryTime() : 30.0;
        
        // 往返交通时间
        Double travelTime = 0.0;
        if (timeMatrix != null) {
            String key = depot.getTransitDepotId() + "-" + accumulation.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                travelTime = timeInfo.getTravelTime() * 2; // 往返
            }
        }
        
        // 如果没有时间矩阵数据，使用距离估算
        if (travelTime <= 0.0) {
            double distance = calculateDistance(
                depot.getLatitude(), depot.getLongitude(),
                accumulation.getLatitude(), accumulation.getLongitude()
            );
            travelTime = distance * 2.0; // 假设1公里/分钟，往返
        }
        
        return deliveryTime + travelTime;
    }
    
    /**
     * 确定服务优先级
     * 
     * @param accumulation 聚集区
     * @return 优先级（1-10）
     */
    private int determineServicePriority(Accumulation accumulation) {
        Double deliveryTime = accumulation.getDeliveryTime();
        if (deliveryTime == null) {
            return 5; // 默认中等优先级
        }
        
        if (deliveryTime > 60.0) {
            return 8; // 大型聚集区，高优先级
        } else if (deliveryTime > 30.0) {
            return 6; // 中型聚集区，中高优先级
        } else {
            return 4; // 小型聚集区，中低优先级
        }
    }
    
    /**
     * 创建距离矩阵
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return 距离矩阵
     */
    private com.graphhopper.jsprit.core.util.VehicleRoutingTransportCostsMatrix createDistanceMatrix(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        com.graphhopper.jsprit.core.util.VehicleRoutingTransportCostsMatrix.Builder matrixBuilder = 
            com.graphhopper.jsprit.core.util.VehicleRoutingTransportCostsMatrix.Builder.newInstance(true);
        
        // 收集所有位置
        List<String> allLocationIds = new ArrayList<>();
        allLocationIds.add("depot_" + depot.getTransitDepotId());
        
        for (List<Accumulation> cluster : clusters) {
            for (Accumulation accumulation : cluster) {
                allLocationIds.add("acc_" + accumulation.getAccumulationId());
            }
        }
        
        // 构建距离矩阵
        for (String fromId : allLocationIds) {
            for (String toId : allLocationIds) {
                if (!fromId.equals(toId)) {
                    double distance = calculateLocationDistance(fromId, toId, depot, clusters, timeMatrix);
                    double time = distance * 60; // 假设1公里/分钟，转换为秒
                    
                    matrixBuilder.addTransportDistance(fromId, toId, distance);
                    matrixBuilder.addTransportTime(fromId, toId, time);
                }
            }
        }
        
        return matrixBuilder.build();
    }
    
    /**
     * 计算位置间距离
     * 
     * @param fromLocationId 起始位置ID
     * @param toLocationId 目标位置ID
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return 距离（公里）
     */
    private double calculateLocationDistance(
        String fromLocationId,
        String toLocationId,
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        // 尝试从时间矩阵获取
        if (timeMatrix != null) {
            String fromId = extractOriginalId(fromLocationId);
            String toId = extractOriginalId(toLocationId);
            String key = fromId + "-" + toId;
            TimeInfo timeInfo = timeMatrix.get(key);
            
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                return timeInfo.getTravelTime(); // 假设时间矩阵存储的是距离
            }
        }
        
        // 使用地理坐标计算
        Coordinate fromCoord = getLocationCoordinate(fromLocationId, depot, clusters);
        Coordinate toCoord = getLocationCoordinate(toLocationId, depot, clusters);
        
        if (fromCoord != null && toCoord != null) {
            return calculateDistance(fromCoord.getY(), fromCoord.getX(), toCoord.getY(), toCoord.getX());
        }
        
        return 10.0; // 默认距离
    }
    
    /**
     * 获取位置坐标
     * 
     * @param locationId 位置ID
     * @param depot 中转站
     * @param clusters 聚类结果
     * @return 坐标
     */
    private Coordinate getLocationCoordinate(String locationId, TransitDepot depot, List<List<Accumulation>> clusters) {
        if (locationId.startsWith("depot_")) {
            return Coordinate.newInstance(depot.getLongitude(), depot.getLatitude());
        }
        
        if (locationId.startsWith("acc_")) {
            String accId = locationId.substring(4);
            for (List<Accumulation> cluster : clusters) {
                for (Accumulation acc : cluster) {
                    if (acc.getAccumulationId().equals(accId)) {
                        return Coordinate.newInstance(acc.getLongitude(), acc.getLatitude());
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 从位置ID提取原始ID
     * 
     * @param locationId 位置ID
     * @return 原始ID
     */
    private String extractOriginalId(String locationId) {
        if (locationId.startsWith("depot_")) {
            return locationId.substring(6);
        } else if (locationId.startsWith("acc_")) {
            return locationId.substring(4);
        }
        return locationId;
    }
    
    /**
     * 从服务ID提取聚集区ID
     * 
     * @param serviceId 服务ID
     * @return 聚集区ID
     */
    private String extractAccumulationIdFromServiceId(String serviceId) {
        if (serviceId.startsWith("service_")) {
            return serviceId.substring(8);
        }
        return serviceId;
    }
    
    /**
     * 创建聚集区ID到对象的映射
     * 
     * @param clusters 聚类结果
     * @return ID映射
     */
    private Map<String, Accumulation> createAccumulationMap(List<List<Accumulation>> clusters) {
        Map<String, Accumulation> map = new HashMap<>();
        for (List<Accumulation> cluster : clusters) {
            for (Accumulation accumulation : cluster) {
                map.put(String.valueOf(accumulation.getAccumulationId()), accumulation);
            }
        }
        return map;
    }
    
    /**
     * 计算两点间距离（Haversine公式）
     * 
     * @param lat1 纬度1
     * @param lon1 经度1
     * @param lat2 纬度2
     * @param lon2 经度2
     * @return 距离（公里）
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371.0; // 地球半径（公里）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    /**
     * 深拷贝聚类列表
     * 
     * @param clusters 原始聚类
     * @return 深拷贝结果
     */
    private List<List<Accumulation>> deepCopyOfClusters(List<List<Accumulation>> clusters) {
        return clusters.stream()
            .map(cluster -> cluster.stream()
                .map(Accumulation::copy)
                .collect(Collectors.toList()))
            .collect(Collectors.toList());
    }
}