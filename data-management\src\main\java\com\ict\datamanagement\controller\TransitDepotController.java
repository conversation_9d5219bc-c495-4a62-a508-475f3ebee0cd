package com.ict.datamanagement.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.common.StatusCode;
import com.ict.datamanagement.domain.dto.transitDepot.AddTransitDepot;
import com.ict.datamanagement.domain.dto.transitDepot.TransitDepotRequest;
import com.ict.datamanagement.domain.dto.transitDepot.UpdateTransitDepotRequest;
import com.ict.datamanagement.domain.vo.transitDepotV0.TeamInfoVO;
import com.ict.datamanagement.domain.vo.transitDepotV0.TransitDepotVO;
import com.ict.datamanagement.service.TransitDepotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "中转站信息")
@Slf4j
@RestController("/transitDepot")
public class TransitDepotController {

    @Autowired
    private TransitDepotService transitDepotService;

    //-----------中转站展示列表
    @ApiOperation("中转站搜索和列表")
    @GetMapping("/TransitDepotList")
    public BaseResponse<Page<TransitDepotVO>> transitDepotList(TransitDepotRequest transitDepotRequest) {
        List<TransitDepotVO> transitDepotVOS = transitDepotService.transitDepotList(transitDepotRequest);
        Integer pageNum = transitDepotRequest.getPageNum();
        Integer pageSize = transitDepotRequest.getPageSize();
        Page<TransitDepotVO> page = transitDepotService.getPage(pageNum, pageSize, transitDepotVOS);
        return ResultUtils.success(page);
    }

    //------------------删除中转站
    @ApiOperation("删除中转站")
    @DeleteMapping("/deleteTransitDepot")
    public String deleteTransitDepot(int id) {
        int i = transitDepotService.deleteTransitDepot(id);
        if(i==1){
            return "删除成功";
        }
        return "删除失败,所选中转站为启用状态";
    }
    //----------------添加中转站
    @ApiOperation("添加中转站")
    @PostMapping("/addTransitDepot")
    public BaseResponse addTransitDepot(AddTransitDepot addTransitDepot){
        String s= transitDepotService.addTransitDepot(addTransitDepot);
        return ResultUtils.success(s);
    }

    @ApiOperation("获取班组信息")
    @PostMapping("/getTeamInfo")
    public BaseResponse<List<TeamInfoVO>> getTeamInfo(){
       List<TeamInfoVO> infos= transitDepotService.getTeamInfo();
        return ResultUtils.success(infos);
    }
    //------------------修改中转站
    @ApiOperation("修改中转站")
    @PostMapping("/updateTransitDepot")
    public String updateTransitDepot(UpdateTransitDepotRequest updateTransitDepotRequest){
        return transitDepotService.updateTransitDepot(updateTransitDepotRequest);
    }
}
