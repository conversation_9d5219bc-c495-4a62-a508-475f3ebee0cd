package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.Builder;
import lombok.Data;

/**
 * 聚集区权重
 * 
 * 表示聚集区在路线分割/合并算法中的权重信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class AccumulationWeight {
    
    /**
     * 聚集区对象
     */
    private Accumulation accumulation;
    
    /**
     * 工作时间权重（分钟）
     */
    private double workTime;
    
    /**
     * 往返时间权重（分钟）
     */
    private double travelTime;
    
    /**
     * 总权重（分钟）
     */
    private double totalWeight;
    
    /**
     * 获取聚集区ID
     */
    public Long getAccumulationId() {
        return accumulation != null ? accumulation.getAccumulationId() : null;
    }
    
    /**
     * 获取聚集区名称
     */
    public String getAccumulationName() {
        return accumulation != null ? accumulation.getAccumulationName() : "未知";
    }
    
    /**
     * 计算工作时间占比
     */
    public double getWorkTimeRatio() {
        return totalWeight > 0 ? workTime / totalWeight : 0.0;
    }
    
    /**
     * 计算往返时间占比
     */
    public double getTravelTimeRatio() {
        return totalWeight > 0 ? travelTime / totalWeight : 0.0;
    }
    
    /**
     * 是否为高权重聚集区
     */
    public boolean isHighWeight() {
        return totalWeight > 60.0; // 总时间超过60分钟
    }
    
    /**
     * 是否为低权重聚集区
     */
    public boolean isLowWeight() {
        return totalWeight < 30.0; // 总时间少于30分钟
    }
    
    /**
     * 获取权重等级描述
     */
    public String getWeightLevel() {
        if (isHighWeight()) {
            return "高权重";
        } else if (isLowWeight()) {
            return "低权重";
        } else {
            return "中权重";
        }
    }
    
    /**
     * 计算相对权重（相对于给定基准）
     */
    public double getRelativeWeight(double baseWeight) {
        return baseWeight > 0 ? totalWeight / baseWeight : 1.0;
    }
    
    /**
     * 生成权重描述
     */
    public String generateDescription() {
        return String.format("聚集区权重 - %s: 工作%.1f分钟 + 往返%.1f分钟 = 总计%.1f分钟 (%s)",
            getAccumulationName(), workTime, travelTime, totalWeight, getWeightLevel());
    }
    
    /**
     * 比较权重（用于排序）
     */
    public int compareWeight(AccumulationWeight other) {
        return Double.compare(this.totalWeight, other.totalWeight);
    }
}