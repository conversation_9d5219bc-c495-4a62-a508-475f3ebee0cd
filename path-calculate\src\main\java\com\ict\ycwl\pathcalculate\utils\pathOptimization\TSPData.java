package com.ict.ycwl.pathcalculate.utils.pathOptimization;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import com.ict.ycwl.pathcalculate.pojo.Dist;
import com.ict.ycwl.pathcalculate.pojo.DoublePoint;
import com.ict.ycwl.pathcalculate.utils.MySQLConnection;

import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * TSP数据类
 * 包含：
 * disMap 各个城市间距离矩阵
 */


public class TSPData {

    static int CITY_NUM; //城市数
    static final int SPECIES_NUM = 200; //种群数
    static final int DEVELOP_NUM = 1000; //进化代数
    static final float pcl = 0.6f, pch = 0.95f;//交叉概率
    static final float pm = 0.4f;//变异概率
    static float[][] disMap; //地图数据

    //城市坐标集合
    static double[][] cityPosition;//31个城市（最优解:14700）


    public static String[] run(double[][] cityPosition1) {
        cityPosition = cityPosition1;

        //路径集合
        CITY_NUM = cityPosition.length;
        disMap = new float[CITY_NUM][CITY_NUM];
        for (int i = 0; i < CITY_NUM; i++) {
            double[] point1 = {cityPosition[i][0], cityPosition[i][1]};
            for (int j = i; j < CITY_NUM; j++) {
                //float dis=(float)Math.sqrt(Math.pow((cityPosition[i][0] - cityPosition[j][0]),2) + Math.pow((cityPosition[i][1] - cityPosition[j][1]),2));
                double[] point2 = {cityPosition[j][0], cityPosition[j][1]};
                float dis = 0;
                try {
                    dis = (float) saveDistanceInformation(new DoublePoint(point1), new DoublePoint(point2), "3729e38b382749ba3a10bae7539e0d9a");
                    //dis = (float) saveDistanceInformationBaiDu(new DoublePoint(point1), new DoublePoint(point2), "");
                    //dis = (float) saveDistanceInformationTenXun(new DoublePoint(point1), new DoublePoint(point2), "");
                } catch (ApiKeyException e) {
                    throw new RuntimeException(e);
                }
                disMap[i][j] = dis;
                disMap[j][i] = disMap[i][j];
            }
        }

        //创建遗传算法驱动对象
        GeneticAlgorithm GA = new GeneticAlgorithm();

        //创建初始种群
        SpeciesPopulation speciesPopulation = new SpeciesPopulation();

        //开始遗传算法（选择算子、交叉算子、变异算子）
        SpeciesIndividual bestRate = GA.run(speciesPopulation);

        //打印路径与最短距离
        bestRate.printRate();
        return bestRate.genes;
    }

    public static double saveDistanceInformation(DoublePoint point1, DoublePoint point2, String apiKey) throws
            ApiKeyException {
        //查询数据库，查看是否有该条数据，有则直接返回距离

        String url = "https://restapi.amap.com/v3/direction/driving";
//        String apiKey = "3acb45c690a8aed5095eff50887689f6";
        //apiKey = "a123fae9da370c45984c58720bf3ac7c";
        // 拼接经度和纬度字符串
        String origin = point1.getPoint()[0] + "," + point1.getPoint()[1];
        String destination = point2.getPoint()[0] + "," + point2.getPoint()[1];
        if (origin.equals(destination)) {
            return 1;
        }
        //List<Dist> dist1 = distMapper.selectList(new QueryWrapper<Dist>().eq("origin", origin).eq("destination", destination));
        //List<Dist> dist3 = distMapper.selectList(new QueryWrapper<Dist>().eq("origin", destination).eq("destination", origin));
        List<Dist> dist1 = query(origin, destination);
        List<Dist> dist3 = query(destination, origin);
        if (dist1.size() > 0) {
            if (dist1.get(0).getDist() > 0) {
                return dist1.get(0).getDist();
            }
        }
        if (dist3.size() > 0) {
            if (dist3.get(0).getDist() > 0) {
                return dist3.get(0).getDist();
            }
        }

        // 构建请求URL
        String requestUrl = url + "?origin=" + origin + "&destination=" + destination + "&strategy=2" + "&number=FD08088&extensions=all&output=json&key=" + apiKey;

        double dist = 0.0;

        for (int i = 0; i < 5; i++) {
            try {
                // 发送HTTP请求
                URL urlObj = new URL(requestUrl);
                System.out.println(1);
                HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
                System.out.println(2);
                connection.setRequestMethod("GET");
                System.out.println(3);
                // 读取响应数据
                connection.setConnectTimeout(7000);//设置连接超时时间
                connection.setReadTimeout(7000);
                Scanner scanner = new Scanner(connection.getInputStream());
                System.out.println(4);
                StringBuilder response = new StringBuilder();
                System.out.println(5);
                while (scanner.hasNextLine()) {
                    System.out.println(6);
                    response.append(scanner.nextLine());
                    System.out.println(7);
                }
                System.out.println(8);
                scanner.close();
                System.out.println(9);
                // 解析响应JSON数据
                ObjectMapper objectMapper = new ObjectMapper();
                System.out.println(10);
                JsonNode rootNode = objectMapper.readTree(response.toString());
                System.out.println(rootNode);
                System.out.println(11);
                JsonNode pathsNode = rootNode.path("route").path("paths");
                System.out.println(12);
                System.out.println("-----------------------");
                System.out.println(pathsNode);
                dist = pathsNode.get(0).path("distance").asDouble();
                System.out.println(dist);
                System.out.println(13);
                break;
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        //插入数据
        Dist dist2 = new Dist();

        dist2.setDist(dist);
        dist2.setOrigin(origin);
        dist2.setDestination(destination);
        List<Dist> dist4 = query(origin, destination);
        if(dist4.size()>0){
            update(dist2);
        }else {
            //distMapper.insert(dist2);
            save(dist2);
        }
        return dist;//单位m
    }


    public static List<Dist> query(String origin, String destination) {
        ArrayList<Dist> dists = new ArrayList<>();
        String sql = "select * from dist where origin=? and destination=?";
        try (Connection connection = MySQLConnection.getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, origin);
            preparedStatement.setString(2, destination);
            ResultSet resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                double dist = resultSet.getDouble("dist");
                Dist obj = new Dist();
                obj.setDist(dist);
                obj.setOrigin(origin);
                obj.setDestination(destination);
                dists.add(obj);
            }
        } catch (SQLException e) {
            System.out.println("插入数据失败！" + e.getMessage());
        }
        return dists;
    }

    public static void save(Dist dist) {
        String sql = "INSERT INTO dist (id, origin,destination,dist) VALUES (?,?,?,?)";
        try (Connection connection = MySQLConnection.getConnection();
             PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, null);
            preparedStatement.setString(2, dist.getOrigin());
            preparedStatement.setString(3, dist.getDestination());
            preparedStatement.setDouble(4, dist.getDist());
            preparedStatement.executeUpdate();
        } catch (SQLException e) {
            System.out.println("插入数据失败！" + e.getMessage());
        }
    }

    public static void update(Dist dist) {
        String sql = "UPDATE dist SET origin=?, destination=?, dist=? WHERE id=?";
        try (Connection connection = MySQLConnection.getConnection();
             PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, dist.getOrigin());
            preparedStatement.setString(2, dist.getDestination());
            preparedStatement.setDouble(3, dist.getDist());
            preparedStatement.setLong(4, dist.getId());
            int rowsAffected = preparedStatement.executeUpdate();
            System.out.println("修改数据影响行数：" + rowsAffected);
        } catch (SQLException e) {
            System.out.println("修改数据失败！" + e.getMessage());
        }
    }
}