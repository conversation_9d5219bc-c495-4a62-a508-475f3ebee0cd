#!/usr/bin/env python3
"""
快速启动脚本
帮助用户快速开始使用数据提取工具

使用方法:
python quick_start.py
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖是否安装"""
    print("检查Python依赖...")
    
    try:
        import pymysql
        print("✓ PyMySQL 已安装")
        return True
    except ImportError:
        print("✗ PyMySQL 未安装")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("✗ 依赖安装失败")
        return False

def check_config():
    """检查配置文件"""
    print("检查配置文件...")
    
    from config import DATABASE_CONFIG
    
    if DATABASE_CONFIG['password'] == 'your_password':
        print("✗ 请先配置数据库密码")
        print("请编辑 config.py 文件，修改 DATABASE_CONFIG 中的数据库连接信息")
        return False
    
    print("✓ 配置文件检查通过")
    return True

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    try:
        import pymysql
        from config import DATABASE_CONFIG
        
        connection = pymysql.connect(**DATABASE_CONFIG)
        connection.close()
        print("✓ 数据库连接成功")
        return True
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def run_sample_extraction():
    """运行示例数据提取"""
    print("运行示例数据提取...")
    
    try:
        subprocess.check_call([sys.executable, "extract_data.py", "--scale", "small", "--version", "v1.0"])
        print("✓ 示例数据提取完成")
        return True
    except subprocess.CalledProcessError:
        print("✗ 数据提取失败")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("路径规划算法数据提取工具 - 快速启动")
    print("=" * 50)
    
    # 1. 检查依赖
    if not check_dependencies():
        if input("是否安装依赖? (y/n): ").lower() == 'y':
            if not install_dependencies():
                sys.exit(1)
        else:
            sys.exit(1)
    
    # 2. 检查配置
    if not check_config():
        print("\n配置步骤:")
        print("1. 编辑 config.py 文件")
        print("2. 修改 DATABASE_CONFIG 中的数据库连接信息")
        print("3. 重新运行此脚本")
        sys.exit(1)
    
    # 3. 测试数据库连接
    if not test_database_connection():
        print("\n请检查:")
        print("1. 数据库服务是否运行")
        print("2. 连接信息是否正确")
        print("3. 用户权限是否充足")
        sys.exit(1)
    
    # 4. 运行示例提取
    print("\n准备运行示例数据提取...")
    if input("是否继续? (y/n): ").lower() == 'y':
        if run_sample_extraction():
            print("\n✓ 快速启动完成!")
            print("\n生成的文件位置:")
            print("../data/v1.0/")
            print("\n下一步:")
            print("1. 查看生成的JSON文件")
            print("2. 运行Java测试代码验证数据加载")
            print("3. 开始实现算法逻辑")
        else:
            print("\n✗ 快速启动失败")
            sys.exit(1)
    else:
        print("跳过示例提取，快速启动完成")

if __name__ == '__main__':
    main() 