package com.ict.datamanagement.domain.dto.route;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class RouteExport {
    //路径标识
    @ExcelProperty("路径标识")
    private String routeFlag;
    //星期
    @ExcelProperty("星期")
    private String week;
    //车牌号
    @ExcelProperty("车牌号")
    private String licensePlateNumber;
    //对接点
    @ExcelProperty("对接点")
    private String TransitDepot;
    //打卡点
    @ExcelProperty("打卡点")
    private String accumulation;
    //送货顺序
    @ExcelProperty("送货顺序")
    private Long deliveryOrder;
    //商铺
    @ExcelProperty("商铺")
    private String storeName;
    //商铺
    @ExcelProperty("商铺新增时间")
    private String storeCreateTime;
    //商铺
    @ExcelProperty("商铺修改时间")
    private String storeUpdateTime;
    //商铺
    @ExcelProperty("商铺状态")
    private String storeStatus;
    //地址
    @ExcelProperty("地址")
    private String storeAddress;
    //客户编码
    @ExcelProperty("客户编码")
    private String customerCode;
    //负责人
    @ExcelProperty("负责人")
    private String customerManagerName;
    //订货电话
    @ExcelProperty("订货电话")
    private String phone;
}
