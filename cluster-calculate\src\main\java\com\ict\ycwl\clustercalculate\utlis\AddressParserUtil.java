package com.ict.ycwl.clustercalculate.utlis;


import com.ict.ycwl.clustercalculate.pojo.ListPointDto;

import java.util.regex.Matcher;
import java.util.regex.Pattern;



public class AddressParserUtil {

    /**
     * 解析韶关市中的县和镇
     * @param address 地址字符串
     * @return ListPointDto 包含县和镇信息
     */
    public static ListPointDto parseXianAndZhen(String address) {
        ListPointDto result = new ListPointDto();
        
        // 正则表达式匹配县级和镇级
        Pattern pattern = Pattern.compile("(.*?)(县|区|市)(.*?)(镇|街道)");
        Matcher matcher = pattern.matcher(address);
        
        if (matcher.find()) {
            result.setXian(matcher.group(1) + matcher.group(2)); // 县级
            result.setZheng(matcher.group(3) + matcher.group(4)); // 镇级
        } else {
            // 如果没有匹配到，可以设置默认值或抛出异常
            result.setXian("未匹配到县级");
            result.setZheng("未匹配到镇级");
        }
        
        return result;
    }

    // 测试方法
    public static void main(String[] args) {
        String address = "广东省韶关市乐昌市（廊田所）五山镇青岭";
        ListPointDto dto = parseXianAndZhen(address);
        System.out.println("县级: " + dto.getXian());
        System.out.println("镇级: " + dto.getZheng());
    }
}
