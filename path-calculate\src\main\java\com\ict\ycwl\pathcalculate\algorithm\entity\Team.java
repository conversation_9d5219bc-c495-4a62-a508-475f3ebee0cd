package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.ArrayList;

/**
 * 班组数据结构（算法专用）
 * 专门为路径规划算法设计的班组数据结构，用于多层级时间均衡算法
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Team {
    
    /**
     * 班组ID
     */
    private Long teamId;
    
    /**
     * 班组名称
     */
    private String teamName;
    
    /**
     * 班组下所有中转站ID列表
     * 用于建立班组与中转站的层级关系
     */
    private List<Long> transitDepotIds;
    
    /**
     * 检查班组数据是否有效
     * 
     * @return 数据是否有效
     */
    public boolean isValid() {
        return teamId != null &&
               teamName != null && !teamName.trim().isEmpty() &&
               transitDepotIds != null && !transitDepotIds.isEmpty();
    }
    
    /**
     * 获取班组的唯一标识字符串
     * 
     * @return 唯一标识字符串
     */
    public String getUniqueKey() {
        return String.format("team_%d", teamId);
    }
    
    /**
     * 检查是否包含指定的中转站
     * 
     * @param transitDepotId 中转站ID
     * @return 是否包含
     */
    public boolean containsTransitDepot(Long transitDepotId) {
        return transitDepotIds != null && transitDepotIds.contains(transitDepotId);
    }
    
    /**
     * 添加中转站ID
     * 
     * @param transitDepotId 中转站ID
     */
    public void addTransitDepot(Long transitDepotId) {
        if (transitDepotId != null) {
            if (transitDepotIds == null) {
                transitDepotIds = new ArrayList<>();
            }
            if (!transitDepotIds.contains(transitDepotId)) {
                transitDepotIds.add(transitDepotId);
            }
        }
    }
    
    /**
     * 移除中转站ID
     * 
     * @param transitDepotId 中转站ID
     */
    public void removeTransitDepot(Long transitDepotId) {
        if (transitDepotIds != null) {
            transitDepotIds.remove(transitDepotId);
        }
    }
    
    /**
     * 获取中转站数量
     * 
     * @return 中转站数量
     */
    public int getTransitDepotCount() {
        return transitDepotIds != null ? transitDepotIds.size() : 0;
    }
    
    /**
     * 从业务实体转换为算法实体
     * 
     * @param businessTeam 业务班组实体
     * @return 算法班组实体
     */
    public static Team fromBusinessEntity(com.ict.ycwl.pathcalculate.pojo.Group businessTeam) {
        if (businessTeam == null) {
            return null;
        }
        // NOTE: 业务实体中的Group类可能没有直接的中转站列表
        return Team.builder()
                .teamId(businessTeam.getGroupId())
                .teamName(businessTeam.getGroupName())
                .transitDepotIds(new ArrayList<>())
                .build();
    }
    
    /**
     * 复制班组对象
     * 
     * @return 复制的班组对象
     */
    public Team copy() {
        List<Long> copiedDepotIds = null;
        if (transitDepotIds != null) {
            copiedDepotIds = new ArrayList<>(transitDepotIds);
        }
        
        return Team.builder()
                .teamId(this.teamId)
                .teamName(this.teamName)
                .transitDepotIds(copiedDepotIds)
                .build();
    }
    
    /**
     * 获取班组管辖的所有中转站（从请求数据中过滤）
     * 
     * @param allTransitDepots 所有中转站列表
     * @return 该班组管辖的中转站列表
     */
    public List<TransitDepot> getManagedTransitDepots(List<TransitDepot> allTransitDepots) {
        List<TransitDepot> managedDepots = new ArrayList<>();
        if (allTransitDepots != null && transitDepotIds != null) {
            for (TransitDepot depot : allTransitDepots) {
                if (depot != null && transitDepotIds.contains(depot.getTransitDepotId())) {
                    managedDepots.add(depot);
                }
            }
        }
        return managedDepots;
    }
    
    /**
     * 计算班组的工作负载（基于中转站数量）
     * 
     * @return 工作负载值
     */
    public double calculateWorkload() {
        // OPTIMIZE: 可以基于中转站数量、路线数量等因素综合计算
        return getTransitDepotCount();
    }
} 