package com.ict.datamanagement.domain.dto.car;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("更新车辆表单")
@AllArgsConstructor
@NoArgsConstructor
public class UpdateCarRequest extends AddCarRequest {
    //车辆id
    @ApiModelProperty(value = "车辆id",dataType = "Long")
    private Long carId;
}
