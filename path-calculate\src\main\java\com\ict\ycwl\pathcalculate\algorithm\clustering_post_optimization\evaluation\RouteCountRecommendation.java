package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 路线数量推荐决策
 * 
 * 基于4维分析的综合推荐决策结果
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class RouteCountRecommendation {
    
    /**
     * 推荐的调整行动
     */
    private RouteCountAction recommendedAction;
    
    /**
     * 当前路线数量
     */
    private int currentRouteCount;
    
    /**
     * 推荐的路线数量
     */
    private int recommendedRouteCount;
    
    /**
     * 路线数量调整值（推荐数量 - 当前数量）
     */
    private int routeCountAdjustment;
    
    /**
     * 综合评分 (0.0-1.0)
     */
    private double comprehensiveScore;
    
    /**
     * 决策置信度 (0.0-1.0)
     */
    private double confidence;
    
    /**
     * 推荐理由说明
     */
    private String reasoning;
    
    /**
     * 各维度评分详情
     */
    private DimensionScores dimensionScores;
    
    /**
     * 预期改善效果
     */
    private ExpectedImprovement expectedImprovement;
    
    /**
     * 风险评估
     */
    private List<AdjustmentRisk> risks;
    
    /**
     * 实施优先级
     */
    private ImplementationPriority priority;
    
    /**
     * 获取决策强度
     */
    public DecisionStrength getDecisionStrength() {
        if (confidence >= 0.9) {
            return DecisionStrength.VERY_STRONG;
        } else if (confidence >= 0.8) {
            return DecisionStrength.STRONG;
        } else if (confidence >= 0.7) {
            return DecisionStrength.MODERATE;
        } else if (confidence >= 0.6) {
            return DecisionStrength.WEAK;
        } else {
            return DecisionStrength.VERY_WEAK;
        }
    }
    
    /**
     * 是否是高置信度的推荐
     */
    public boolean isHighConfidenceRecommendation() {
        return confidence >= 0.8;
    }
    
    /**
     * 获取调整幅度评级
     */
    public AdjustmentMagnitude getAdjustmentMagnitude() {
        int absAdjustment = Math.abs(routeCountAdjustment);
        if (absAdjustment == 0) {
            return AdjustmentMagnitude.NONE;
        } else if (absAdjustment <= 2) {
            return AdjustmentMagnitude.MINOR;
        } else if (absAdjustment <= 5) {
            return AdjustmentMagnitude.MODERATE;
        } else if (absAdjustment <= 10) {
            return AdjustmentMagnitude.MAJOR;
        } else {
            return AdjustmentMagnitude.DRAMATIC;
        }
    }
    
    /**
     * 生成决策摘要
     */
    public String generateDecisionSummary() {
        StringBuilder summary = new StringBuilder();
        
        summary.append(String.format("【推荐】%s", recommendedAction.getDescription()));
        
        if (recommendedAction.needsAdjustment()) {
            summary.append(String.format("（%d条 → %d条，%+d条）", 
                currentRouteCount, recommendedRouteCount, routeCountAdjustment));
        }
        
        summary.append(String.format("\n【置信度】%.1f%% (%s)", 
            confidence * 100, getDecisionStrength().getDescription()));
        
        summary.append(String.format("\n【调整幅度】%s", 
            getAdjustmentMagnitude().getDescription()));
        
        if (priority != null) {
            summary.append(String.format("\n【实施优先级】%s", priority.getDescription()));
        }
        
        if (reasoning != null) {
            summary.append(String.format("\n【理由】%s", reasoning));
        }
        
        return summary.toString();
    }
    
    /**
     * 各维度评分详情
     */
    @Data
    @Builder
    public static class DimensionScores {
        private double workloadScore;      // 工作量分析评分
        private double constraintScore;    // 约束分析评分
        private double efficiencyScore;    // 效率分析评分
        private double modelScore;         // 数学模型评分
        
        public double getAverageScore() {
            return (workloadScore + constraintScore + efficiencyScore + modelScore) / 4.0;
        }
    }
    
    /**
     * 预期改善效果
     */
    @Data
    @Builder
    public static class ExpectedImprovement {
        private double constraintSatisfactionImprovement;  // 约束满足率改善
        private double timeBalanceImprovement;             // 时间平衡改善
        private double efficiencyImprovement;              // 效率改善
        private double overallScore;                       // 整体改善评分
    }
    
    /**
     * 调整风险
     */
    @Data
    @Builder
    public static class AdjustmentRisk {
        private String riskType;           // 风险类型
        private String description;        // 风险描述
        private double probability;        // 发生概率 (0.0-1.0)
        private RiskLevel severity;        // 严重程度
        private String mitigationStrategy; // 缓解策略
    }
    
    /**
     * 决策强度枚举
     */
    public enum DecisionStrength {
        VERY_STRONG("极强", "非常确定的推荐"),
        STRONG("强", "高度确定的推荐"),
        MODERATE("中等", "较为确定的推荐"),
        WEAK("弱", "不太确定的推荐"),
        VERY_WEAK("极弱", "不确定的推荐");
        
        private final String description;
        private final String detail;
        
        DecisionStrength(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getDetail() {
            return detail;
        }
    }
    
    /**
     * 调整幅度枚举
     */
    public enum AdjustmentMagnitude {
        NONE("无调整", "保持现状"),
        MINOR("微调", "小幅调整"),
        MODERATE("中等", "中等幅度调整"),
        MAJOR("大幅", "较大幅度调整"),
        DRAMATIC("巨大", "大幅度调整");
        
        private final String description;
        private final String detail;
        
        AdjustmentMagnitude(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getDetail() {
            return detail;
        }
    }
    
    /**
     * 实施优先级枚举
     */
    public enum ImplementationPriority {
        URGENT("紧急", "立即实施"),
        HIGH("高", "优先实施"),
        MEDIUM("中", "计划实施"),
        LOW("低", "可选实施"),
        OPTIONAL("可选", "条件允许时实施");
        
        private final String description;
        private final String detail;
        
        ImplementationPriority(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getDetail() {
            return detail;
        }
    }
    
    /**
     * 风险级别枚举
     */
    public enum RiskLevel {
        LOW("低风险"),
        MEDIUM("中风险"),
        HIGH("高风险"),
        CRITICAL("极高风险");
        
        private final String description;
        
        RiskLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}