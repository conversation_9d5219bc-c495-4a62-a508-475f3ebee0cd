package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@TableName("team")
public class Team {
    //班组id
    @TableId(type = IdType.AUTO) // 指定主键策略为自增
    private int teamId;
    //班组名称
    private String teamName;
    //配送域名称
    private String deliveryAreaName;
    //中转站名称
    private String transitDepotName;
    //车辆总数
    private int carSum;
    //路线总数
    private int routeSum;
    //是否进行软删除
    private int isDelete;
}
