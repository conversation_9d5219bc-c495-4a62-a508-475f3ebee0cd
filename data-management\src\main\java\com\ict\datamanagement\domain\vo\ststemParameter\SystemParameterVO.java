package com.ict.datamanagement.domain.vo.ststemParameter;

import com.ict.datamanagement.domain.entity.SecondTransit;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SystemParameterVO {
    //系统参数记录id
    @ApiModelProperty("这是id属性的描述")
    private int id;

    //车辆总数
    private int carSum;

    //路径总数
    private int routeSum;

    //班组总数
    private int teamSum;

    //聚集区密集度系数
    private double accumulationIntensity;

    //商铺平均卸货时长(小时)城区
    private double shoreUnloadCityTime;

    //商铺平均卸货时长(小时)乡村
    private double shoreUnloadTownshipTime;

    //车辆时速(千米每时)-高速公路
    private double freeway;

    //车辆时速(千米每时)-城区公路
    private double urbanRoads;

    //车辆时速(千米每时)-乡镇公路
    private double townshipRoads;

    //装车时长分钟
    private double loadingTime;

    //二次中转时长
    private List<SecondTransitVO> secondTransitList;

}
