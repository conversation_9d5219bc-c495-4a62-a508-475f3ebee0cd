package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

// JSPRIT imports
import com.graphhopper.jsprit.core.problem.VehicleRoutingProblem;
import com.graphhopper.jsprit.core.problem.job.Service;
import com.graphhopper.jsprit.core.problem.vehicle.Vehicle;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleImpl;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleType;
import com.graphhopper.jsprit.core.problem.vehicle.VehicleTypeImpl;
import com.graphhopper.jsprit.core.problem.solution.VehicleRoutingProblemSolution;
import com.graphhopper.jsprit.core.problem.solution.route.VehicleRoute;
import com.graphhopper.jsprit.core.problem.solution.route.activity.TourActivity;
import com.graphhopper.jsprit.core.algorithm.VehicleRoutingAlgorithm;
import com.graphhopper.jsprit.core.algorithm.box.Jsprit;
import com.graphhopper.jsprit.core.util.Coordinate;
import com.graphhopper.jsprit.core.util.Solutions;
import com.graphhopper.jsprit.core.problem.Location;

import java.util.*;
import java.util.stream.Collectors;

/**
 * JSPRIT VRP重优化器
 * 专门在TSP阶段后使用JSPRIT进行快速聚集区重新分配
 * 
 * 核心功能：
 * 1. 将当前路线分配转换为JSPRIT VRP问题
 * 2. 使用JSPRIT的高效VRP算法重新分配聚集区
 * 3. 应用450分钟和30分钟时间差约束
 * 4. 快速求解，适合轻微约束违反的情况
 */
@Slf4j
@Component
public class JSPRITVRPReoptimizer {
    
    // 约束参数
    private static final double MAX_ROUTE_TIME_MINUTES = 450.0;
    private static final double TIME_GAP_LIMIT_MINUTES = 30.0;
    private static final int MAX_ITERATIONS = 1000; // JSPRIT迭代次数
    
    private final TSPSolverManager tspSolver;
    
    public JSPRITVRPReoptimizer() {
        this.tspSolver = new TSPSolverManager();
    }
    
    /**
     * 使用JSPRIT重优化聚集区分配
     */
    public boolean reoptimizeWithJSPRIT(AlgorithmContext context,
                                       TSPPostOptimizationManager.ConstraintViolationAnalysis analysis) {
        log.info("⚡ [JSPRIT重优化] 开始使用JSPRIT进行快速VRP重优化");
        
        boolean globalSuccess = true;
        
        // 收集需要重优化的中转站
        Set<Long> problematicDepots = new HashSet<>();
        
        for (RouteResult violatingRoute : analysis.getViolatingRoutes()) {
            problematicDepots.add(violatingRoute.getTransitDepotId());
        }
        problematicDepots.addAll(analysis.getExcessiveGapDepots());
        
        log.info("📍 [重优化范围] 需要JSPRIT重优化的中转站数量: {}", problematicDepots.size());
        
        for (Long transitDepotId : problematicDepots) {
            List<RouteResult> routes = context.getOptimizedRoutes().get(transitDepotId);
            if (routes == null || routes.size() <= 1) continue;
            
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            log.info("⚡ [JSPRIT] 快速重优化中转站{}: {}条路线", depot.getTransitDepotName(), routes.size());
            
            boolean depotSuccess = reoptimizeDepotWithJSPRIT(depot, routes, context);
            if (!depotSuccess) {
                globalSuccess = false;
                log.warn("⚠️ [JSPRIT失败] 中转站{}重优化失败", depot.getTransitDepotName());
            }
        }
        
        log.info("⚡ [JSPRIT完成] 全局重优化成功: {}", globalSuccess);
        return globalSuccess;
    }
    
    /**
     * 为单个中转站使用JSPRIT重优化
     */
    private boolean reoptimizeDepotWithJSPRIT(TransitDepot depot, List<RouteResult> routes,
                                             AlgorithmContext context) {
        try {
            // 收集当前所有聚集区
            List<Long> allAccumulations = routes.stream()
                .flatMap(route -> route.getAccumulationSequence().stream())
                .collect(Collectors.toList());
                
            if (allAccumulations.isEmpty()) {
                return true;
            }
            
            log.debug("📊 [JSPRIT输入] 中转站{}: {}条路线, {}个聚集区",
                depot.getTransitDepotName(), routes.size(), allAccumulations.size());
            
            // 创建JSPRIT VRP问题
            VehicleRoutingProblem vrpProblem = createJSPRITProblem(depot, routes, allAccumulations, context);
            
            // 配置和运行JSPRIT算法
            VehicleRoutingAlgorithm algorithm = Jsprit.Builder.newInstance(vrpProblem)
                .setProperty(Jsprit.Parameter.FAST_REGRET, "true")
                .setProperty(Jsprit.Parameter.THREADS, "4")
                .buildAlgorithm();
            
            algorithm.setMaxIterations(MAX_ITERATIONS);
            
            log.debug("🔍 [JSPRIT求解] 开始快速求解中转站{}", depot.getTransitDepotName());
            Collection<VehicleRoutingProblemSolution> solutions = algorithm.searchSolutions();
            
            // 获取最佳解
            VehicleRoutingProblemSolution bestSolution = Solutions.bestOf(solutions);
            
            if (bestSolution != null) {
                double solutionCost = bestSolution.getCost();
                int unassignedJobs = bestSolution.getUnassignedJobs().size();
                
                log.info("✅ [JSPRIT成功] 中转站{}找到解，成本: {:.1f}, 未分配作业: {}",
                    depot.getTransitDepotName(), solutionCost, unassignedJobs);
                
                if (unassignedJobs == 0) {
                    // 应用JSPRIT解到实际路线
                    return applyJSPRITSolution(depot, routes, bestSolution, context);
                } else {
                    log.warn("⚠️ [JSPRIT部分解] 中转站{}有{}个未分配作业", depot.getTransitDepotName(), unassignedJobs);
                    return false;
                }
                
            } else {
                log.warn("❌ [JSPRIT失败] 中转站{}无解", depot.getTransitDepotName());
                return false;
            }
            
        } catch (Exception e) {
            log.error("❌ [JSPRIT异常] 中转站{}重优化异常: {}", depot.getTransitDepotName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建JSPRIT VRP重优化问题
     */
    private VehicleRoutingProblem createJSPRITProblem(TransitDepot depot, List<RouteResult> routes,
                                                    List<Long> allAccumulations, AlgorithmContext context) {
        
        VehicleRoutingProblem.Builder vrpBuilder = VehicleRoutingProblem.Builder.newInstance();
        
        // 设置中转站位置
        Location depotLocation = Location.Builder.newInstance()
            .setCoordinate(Coordinate.newInstance(depot.getLongitude(), depot.getLatitude()))
            .setId("depot")
            .build();
        
        // 创建车辆类型
        VehicleType vehicleType = VehicleTypeImpl.Builder.newInstance("standard")
            .addCapacityDimension(0, 1000) // 容量维度（可忽略）
            .setFixedCost(0.0)
            .setCostPerDistance(1.0)
            .setCostPerTransportTime(1.0)
            .build();
        
        // 创建车辆（路线）
        for (int i = 0; i < routes.size(); i++) {
            Vehicle vehicle = VehicleImpl.Builder.newInstance("vehicle_" + i)
                .setStartLocation(depotLocation)
                .setEndLocation(depotLocation)
                .setType(vehicleType)
                .setLatestArrival((long)(MAX_ROUTE_TIME_MINUTES * 60)) // 转换为秒
                .build();
            
            vrpBuilder.addVehicle(vehicle);
        }
        
        // 创建服务（聚集区）
        for (Long accId : allAccumulations) {
            Accumulation acc = context.getAccumulationById(accId);
            if (acc != null) {
                Location accLocation = Location.Builder.newInstance()
                    .setCoordinate(Coordinate.newInstance(acc.getLongitude(), acc.getLatitude()))
                    .setId("acc_" + accId)
                    .build();
                
                Service service = Service.Builder.newInstance("service_" + accId)
                    .setLocation(accLocation)
                    .setServiceTime((long)(acc.getDeliveryTime() * 60)) // 转换为秒
                    .addSizeDimension(0, 1) // 需求维度
                    .build();
                
                vrpBuilder.addJob(service);
            }
        }
        
        // JSPRIT 1.8版本 - 约束通过后处理验证
        return vrpBuilder.build();
    }
    
    /**
     * 应用JSPRIT求解结果到实际路线
     */
    private boolean applyJSPRITSolution(TransitDepot depot, List<RouteResult> routes,
                                      VehicleRoutingProblemSolution solution, AlgorithmContext context) {
        // 🔒 备份原始路线数据，防止数据丢失
        Map<Long, List<Long>> originalAccumulationBackup = new HashMap<>();
        Map<Long, Double> originalWorkTimeBackup = new HashMap<>();
        Map<Long, List<CoordinatePoint>> originalPolylineBackup = new HashMap<>();
        
        try {
            
            for (RouteResult route : routes) {
                originalAccumulationBackup.put(route.getRouteId(), new ArrayList<>(route.getAccumulationSequence()));
                originalWorkTimeBackup.put(route.getRouteId(), route.getTotalWorkTime());
                originalPolylineBackup.put(route.getRouteId(), new ArrayList<>(route.getPolyline()));
            }
            
            log.debug("🔒 [JSPRIT备份] 已备份{}条路线的原始数据", routes.size());
            
            // 清空所有路线的聚集区分配
            for (RouteResult route : routes) {
                route.setAccumulationSequence(new ArrayList<>());
            }
            
            // 解析JSPRIT解
            Map<String, List<String>> vehicleAssignments = new HashMap<>();
            
            for (VehicleRoute route : solution.getRoutes()) {
                String vehicleId = route.getVehicle().getId();
                List<String> assignedServices = new ArrayList<>();
                
                // JSPRIT 1.8 API - 简化获取作业ID  
                for (TourActivity activity : route.getTourActivities().getActivities()) {
                    if (activity.getName() != null && !activity.getName().equals("start") && !activity.getName().equals("end")) {
                        assignedServices.add(activity.getName());
                    }
                }
                
                vehicleAssignments.put(vehicleId, assignedServices);
            }
            
            // 为每条路线重新运行TSP优化
            for (int i = 0; i < routes.size(); i++) {
                RouteResult route = routes.get(i);
                String vehicleId = "vehicle_" + i;
                List<String> assignedServices = vehicleAssignments.getOrDefault(vehicleId, new ArrayList<>());
                
                if (assignedServices.isEmpty()) {
                    // 空路线
                    route.setAccumulationSequence(new ArrayList<>());
                    route.setTotalWorkTime(AlgorithmParameters.LOADING_TIME_MINUTES);
                    route.setPolyline(Arrays.asList(depot.getCoordinate()));
                } else {
                    // 提取聚集区ID
                    List<Long> accIds = assignedServices.stream()
                        .filter(serviceId -> serviceId.startsWith("service_"))
                        .map(serviceId -> Long.parseLong(serviceId.substring("service_".length())))
                        .collect(Collectors.toList());
                    
                    // 获取聚集区对象
                    List<Accumulation> accumulations = accIds.stream()
                        .map(context::getAccumulationById)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                    
                    if (!accumulations.isEmpty()) {
                        // 重新TSP优化
                        RouteResult optimizedRoute = tspSolver.solveRoute(
                            depot, accumulations, context.getTimeMatrix(),
                            i + 1, TSPSolverManager.SolverStrategy.AUTO,
                            MultiObjectiveTSP.OptimizationGoal.BALANCED, 30000L
                        );
                        
                        route.setAccumulationSequence(optimizedRoute.getAccumulationSequence());
                        route.setTotalWorkTime(optimizedRoute.getTotalWorkTime());
                        route.setPolyline(optimizedRoute.getPolyline());
                    }
                }
            }
            
            // 验证结果
            double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
            double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
            double timeGap = maxTime - minTime;
            
            log.info("⚡ [JSPRIT结果] 中转站{}: 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟",
                depot.getTransitDepotName(), maxTime, minTime, timeGap);
            
            boolean constraintsSatisfied = maxTime <= MAX_ROUTE_TIME_MINUTES && timeGap <= TIME_GAP_LIMIT_MINUTES;
            
            if (constraintsSatisfied) {
                log.info("✅ [约束满足] JSPRIT重优化成功满足所有约束");
            } else {
                log.warn("⚠️ [约束违反] JSPRIT重优化后仍有约束违反");
            }
            
            return constraintsSatisfied;
            
        } catch (Exception e) {
            log.error("❌ [JSPRIT应用解失败] 异常: {}, 正在恢复原始数据", e.getMessage());
            
            // 🛡️ 恢复原始数据，防止数据丢失
            try {
                for (RouteResult route : routes) {
                    List<Long> originalAccumulation = originalAccumulationBackup.get(route.getRouteId());
                    Double originalWorkTime = originalWorkTimeBackup.get(route.getRouteId());
                    List<CoordinatePoint> originalPolyline = originalPolylineBackup.get(route.getRouteId());
                    
                    if (originalAccumulation != null) {
                        route.setAccumulationSequence(originalAccumulation);
                    }
                    if (originalWorkTime != null) {
                        route.setTotalWorkTime(originalWorkTime);
                    }
                    if (originalPolyline != null) {
                        route.setPolyline(originalPolyline);
                    }
                }
                
                log.info("🛡️ [数据恢复] JSPRIT失败后成功恢复{}条路线的原始数据", routes.size());
                
            } catch (Exception restoreException) {
                log.error("💥 [致命错误] JSPRIT数据恢复也失败: {}", restoreException.getMessage());
            }
            
            return false;
        }
    }
    
    // JSPRIT 1.8版本简化实现 - 约束通过后处理验证
}