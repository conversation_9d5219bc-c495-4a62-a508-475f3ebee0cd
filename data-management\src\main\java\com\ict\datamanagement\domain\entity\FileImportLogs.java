package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("file_import_logs")
public class FileImportLogs {
    //文件id
    @TableId(type = IdType.AUTO)
    private int fileId;
    //文件名称
    private String fileName;
    //导入时间
    private String importTime;
    //导入用户
    private String userName;
    //文件导入状态
    private String status;
    //表格类型,0表示是商铺表，1表示是车辆实情表
    private String storeOrCar;
    //文件大小，单位kb，如果文件大小小于1mb则显示kb，为单位，大于等于1mb则用mb为单位
    private String fileSize;
}
