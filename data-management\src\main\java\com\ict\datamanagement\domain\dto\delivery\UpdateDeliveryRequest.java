package com.ict.datamanagement.domain.dto.delivery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("修改配送域表单")
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class UpdateDeliveryRequest extends AddDeliveryRequest{
    @ApiModelProperty(value = "配送域id",dataType = "int")
    @NotNull(message = "配送域id不能为空")
    private int deliveryId;
}
