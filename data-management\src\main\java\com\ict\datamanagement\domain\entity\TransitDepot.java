package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@TableName("transit_depot")
public class TransitDepot {
    //中转站id
    @TableId(type = IdType.AUTO)
    private Long transitDepotId;
    //中转站名称
    private String transitDepotName;
    //状态
    private String status;
    //经度
    private String longitude;
    //纬度
    private String latitude;
    //大区id
    private int areaId;
    //分组id
    private int groupId;
    //是否删除
    private int isDelete;
}
