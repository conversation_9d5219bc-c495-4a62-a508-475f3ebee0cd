package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * JSPRIT VRP约束配置
 * 
 * 定义VRP问题的各种约束条件，包括时间约束、容量约束、地理约束等
 * 支持灵活的约束配置以适应不同的优化策略
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPConstraints {
    
    /**
     * 最大车辆工作时间（分钟）
     * 对应450分钟硬约束
     */
    @Builder.Default
    private Double maxVehicleWorkTimeMinutes = 450.0;
    
    /**
     * 最大路线时间差异（分钟）
     * 对应30分钟差异约束（如果技术可行）
     */
    @Builder.Default
    private Double maxRouteTimeDifferenceMinutes = 30.0;
    
    /**
     * 是否启用时间窗口约束
     */
    @Builder.Default
    private Boolean enableTimeWindowConstraints = true;
    
    /**
     * 是否启用车辆容量约束
     */
    @Builder.Default
    private Boolean enableCapacityConstraints = true;
    
    /**
     * 是否要求车辆返回起始位置
     */
    @Builder.Default
    private Boolean requireReturnToDepot = true;
    
    /**
     * 地理约束权重（0.0-1.0）
     * 0.0表示不考虑地理约束，1.0表示强制地理紧凑
     */
    @Builder.Default
    private Double geographicConstraintWeight = 0.3;
    
    /**
     * 时间平衡权重（0.0-1.0）
     * 1.0表示时间平衡优先级最高
     */
    @Builder.Default
    private Double timeBalanceWeight = 1.0;
    
    /**
     * 最大允许的服务违反数量
     * 0表示不允许任何违反
     */
    @Builder.Default
    private Integer maxServiceViolations = 0;
    
    /**
     * 最大允许的车辆利用率（0.0-1.0）
     * 1.0表示车辆可以达到100%容量利用
     */
    @Builder.Default
    private Double maxVehicleUtilization = 0.95;
    
    /**
     * 最小车辆利用率（0.0-1.0）
     * 避免车辆利用率过低导致资源浪费
     */
    @Builder.Default
    private Double minVehicleUtilization = 0.2;
    
    /**
     * 允许的最大地理分散度（公里）
     * 单个路线的最大地理跨度
     */
    @Builder.Default
    private Double maxGeographicSpreadKm = 50.0;
    
    /**
     * 服务优先级权重
     * 是否考虑服务的优先级差异
     */
    @Builder.Default
    private Double servicePriorityWeight = 0.1;
    
    /**
     * 车辆成本权重
     * 影响车辆使用数量的决策
     */
    @Builder.Default
    private Double vehicleCostWeight = 0.2;
    
    /**
     * 约束违反惩罚系数
     * 违反约束时的惩罚权重
     */
    @Builder.Default
    private Double constraintViolationPenalty = 1000.0;
    
    /**
     * 是否启用动态约束调整
     * 根据问题特征自动调整约束参数
     */
    @Builder.Default
    private Boolean enableDynamicConstraintAdjustment = true;
    
    /**
     * 创建默认约束配置
     * 
     * @return 默认约束配置
     */
    public static VRPConstraints createDefaultConstraints() {
        return VRPConstraints.builder()
            .maxVehicleWorkTimeMinutes(450.0) // 450分钟硬约束
            .maxRouteTimeDifferenceMinutes(30.0) // 30分钟差异约束
            .enableTimeWindowConstraints(true)
            .enableCapacityConstraints(true)
            .requireReturnToDepot(true)
            .geographicConstraintWeight(0.3) // 地理约束优先级较低
            .timeBalanceWeight(1.0) // 时间平衡优先级最高
            .maxServiceViolations(0) // 不允许服务违反
            .maxVehicleUtilization(0.95)
            .minVehicleUtilization(0.2)
            .maxGeographicSpreadKm(50.0)
            .servicePriorityWeight(0.1)
            .vehicleCostWeight(0.2)
            .constraintViolationPenalty(1000.0)
            .enableDynamicConstraintAdjustment(true)
            .build();
    }
    
    /**
     * 创建严格约束配置（优先满足硬约束）
     * 
     * @return 严格约束配置
     */
    public static VRPConstraints createStrictConstraints() {
        return VRPConstraints.builder()
            .maxVehicleWorkTimeMinutes(450.0)
            .maxRouteTimeDifferenceMinutes(20.0) // 更严格的时间差异要求
            .enableTimeWindowConstraints(true)
            .enableCapacityConstraints(true)
            .requireReturnToDepot(true)
            .geographicConstraintWeight(0.1) // 进一步降低地理约束权重
            .timeBalanceWeight(1.0)
            .maxServiceViolations(0)
            .maxVehicleUtilization(0.9) // 更保守的利用率
            .minVehicleUtilization(0.3)
            .maxGeographicSpreadKm(40.0)
            .servicePriorityWeight(0.05)
            .vehicleCostWeight(0.1)
            .constraintViolationPenalty(2000.0) // 更高的违反惩罚
            .enableDynamicConstraintAdjustment(false) // 固定约束参数
            .build();
    }
    
    /**
     * 创建地理优化约束配置（平衡时间和地理约束）
     * 
     * @return 地理优化约束配置
     */
    public static VRPConstraints createGeographicBalancedConstraints() {
        return VRPConstraints.builder()
            .maxVehicleWorkTimeMinutes(450.0)
            .maxRouteTimeDifferenceMinutes(45.0) // 放宽时间差异要求
            .enableTimeWindowConstraints(true)
            .enableCapacityConstraints(true)
            .requireReturnToDepot(true)
            .geographicConstraintWeight(0.6) // 提高地理约束权重
            .timeBalanceWeight(0.8) // 稍降低时间平衡权重
            .maxServiceViolations(0)
            .maxVehicleUtilization(0.95)
            .minVehicleUtilization(0.15)
            .maxGeographicSpreadKm(35.0)
            .servicePriorityWeight(0.15)
            .vehicleCostWeight(0.25)
            .constraintViolationPenalty(800.0)
            .enableDynamicConstraintAdjustment(true)
            .build();
    }
    
    /**
     * 创建快速优化约束配置（适合时间限制较紧的场景）
     * 
     * @return 快速优化约束配置
     */
    public static VRPConstraints createQuickOptimizationConstraints() {
        return VRPConstraints.builder()
            .maxVehicleWorkTimeMinutes(450.0)
            .maxRouteTimeDifferenceMinutes(60.0) // 放宽时间差异
            .enableTimeWindowConstraints(false) // 禁用时间窗口约束
            .enableCapacityConstraints(true)
            .requireReturnToDepot(true)
            .geographicConstraintWeight(0.2)
            .timeBalanceWeight(0.9)
            .maxServiceViolations(2) // 允许少量违反
            .maxVehicleUtilization(1.0)
            .minVehicleUtilization(0.1)
            .maxGeographicSpreadKm(60.0)
            .servicePriorityWeight(0.05)
            .vehicleCostWeight(0.1)
            .constraintViolationPenalty(500.0)
            .enableDynamicConstraintAdjustment(false)
            .build();
    }
    
    /**
     * 验证约束配置的合理性
     * 
     * @return 验证结果和建议
     */
    public VRPConstraintValidationResult validate() {
        VRPConstraintValidationResult.VRPConstraintValidationResultBuilder resultBuilder = 
            VRPConstraintValidationResult.builder().isValid(true);
        
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 验证时间约束
        if (maxVehicleWorkTimeMinutes == null || maxVehicleWorkTimeMinutes <= 0) {
            errors.add("最大车辆工作时间必须大于0");
        } else if (maxVehicleWorkTimeMinutes > 600.0) {
            warnings.add("车辆工作时间超过10小时，可能不现实");
        }
        
        if (maxRouteTimeDifferenceMinutes != null && maxRouteTimeDifferenceMinutes < 0) {
            errors.add("最大路线时间差异不能为负数");
        }
        
        // 验证权重配置
        if (geographicConstraintWeight == null || geographicConstraintWeight < 0 || geographicConstraintWeight > 1) {
            errors.add("地理约束权重必须在0.0-1.0之间");
        }
        
        if (timeBalanceWeight == null || timeBalanceWeight < 0 || timeBalanceWeight > 1) {
            errors.add("时间平衡权重必须在0.0-1.0之间");
        }
        
        // 验证利用率配置
        if (maxVehicleUtilization == null || maxVehicleUtilization <= 0 || maxVehicleUtilization > 1) {
            errors.add("最大车辆利用率必须在0.0-1.0之间");
        }
        
        if (minVehicleUtilization == null || minVehicleUtilization < 0 || minVehicleUtilization > 1) {
            errors.add("最小车辆利用率必须在0.0-1.0之间");
        }
        
        if (minVehicleUtilization != null && maxVehicleUtilization != null 
            && minVehicleUtilization >= maxVehicleUtilization) {
            errors.add("最小车辆利用率不能大于或等于最大车辆利用率");
        }
        
        // 检查约束冲突
        if (geographicConstraintWeight != null && timeBalanceWeight != null) {
            if (geographicConstraintWeight > 0.7 && timeBalanceWeight > 0.7) {
                warnings.add("地理约束和时间平衡权重都很高，可能导致约束冲突");
            }
        }
        
        boolean isValid = errors.isEmpty();
        
        return resultBuilder
            .isValid(isValid)
            .errors(errors)
            .warnings(warnings)
            .build();
    }
    
    /**
     * 根据问题特征动态调整约束参数
     * 
     * @param vehicleCount 车辆数量
     * @param serviceCount 服务数量
     * @param avgServiceTime 平均服务时间
     * @return 调整后的约束配置
     */
    public VRPConstraints adjustForProblemCharacteristics(
        int vehicleCount, 
        int serviceCount, 
        double avgServiceTime
    ) {
        if (!enableDynamicConstraintAdjustment) {
            return this; // 不启用动态调整，返回当前配置
        }
        
        VRPConstraints adjusted = this.copy();
        
        // 根据问题规模调整地理约束权重
        if (serviceCount > 50) {
            // 大规模问题，降低地理约束权重以提高求解效率
            adjusted.geographicConstraintWeight = Math.max(0.1, adjusted.geographicConstraintWeight * 0.8);
        }
        
        // 根据平均服务时间调整时间约束
        if (avgServiceTime > 60.0) {
            // 服务时间较长，可能需要更宽松的时间差异约束
            adjusted.maxRouteTimeDifferenceMinutes = Math.min(60.0, 
                adjusted.maxRouteTimeDifferenceMinutes * 1.5);
        }
        
        // 根据车辆服务比例调整利用率约束
        double servicePerVehicle = (double) serviceCount / vehicleCount;
        if (servicePerVehicle < 3) {
            // 车辆相对较多，可以降低最小利用率要求
            adjusted.minVehicleUtilization = Math.max(0.1, adjusted.minVehicleUtilization * 0.7);
        }
        
        return adjusted;
    }
    
    /**
     * 创建约束配置的副本
     * 
     * @return 副本
     */
    public VRPConstraints copy() {
        return VRPConstraints.builder()
            .maxVehicleWorkTimeMinutes(this.maxVehicleWorkTimeMinutes)
            .maxRouteTimeDifferenceMinutes(this.maxRouteTimeDifferenceMinutes)
            .enableTimeWindowConstraints(this.enableTimeWindowConstraints)
            .enableCapacityConstraints(this.enableCapacityConstraints)
            .requireReturnToDepot(this.requireReturnToDepot)
            .geographicConstraintWeight(this.geographicConstraintWeight)
            .timeBalanceWeight(this.timeBalanceWeight)
            .maxServiceViolations(this.maxServiceViolations)
            .maxVehicleUtilization(this.maxVehicleUtilization)
            .minVehicleUtilization(this.minVehicleUtilization)
            .maxGeographicSpreadKm(this.maxGeographicSpreadKm)
            .servicePriorityWeight(this.servicePriorityWeight)
            .vehicleCostWeight(this.vehicleCostWeight)
            .constraintViolationPenalty(this.constraintViolationPenalty)
            .enableDynamicConstraintAdjustment(this.enableDynamicConstraintAdjustment)
            .build();
    }
    
    @Override
    public String toString() {
        return String.format("VRPConstraints{maxWorkTime=%.1fmin, maxTimeDiff=%.1fmin, geoWeight=%.2f, timeWeight=%.2f}", 
            maxVehicleWorkTimeMinutes, maxRouteTimeDifferenceMinutes, 
            geographicConstraintWeight, timeBalanceWeight);
    }
}