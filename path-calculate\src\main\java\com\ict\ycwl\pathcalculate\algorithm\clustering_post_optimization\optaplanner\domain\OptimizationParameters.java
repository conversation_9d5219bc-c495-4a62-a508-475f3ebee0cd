package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.Duration;

/**
 * OptaPlanner优化参数配置
 * 
 * 定义优化过程的各种参数，包括时间限制、算法配置等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OptimizationParameters {
    
    // ========== 时间限制参数 ==========
    
    /**
     * 优化时间限制
     * 默认: 60秒
     */
    @Builder.Default
    private Duration optimizationTimeLimit = Duration.ofSeconds(60);
    
    /**
     * 初始解构造时间限制
     * 默认: 10秒
     */
    @Builder.Default
    private Duration constructionHeuristicTimeLimit = Duration.ofSeconds(10);
    
    /**
     * 局部搜索时间限制
     * 默认: 50秒
     */
    @Builder.Default
    private Duration localSearchTimeLimit = Duration.ofSeconds(50);
    
    // ========== 算法参数 ==========
    
    /**
     * 并行线程数量
     * 默认: 4
     */
    @Builder.Default
    private int threadCount = 4;
    
    /**
     * 早期终止条件：连续无改进的迭代次数
     * 默认: 1000
     */
    @Builder.Default
    private int unimprovedIterationsLimit = 1000;
    
    /**
     * 内存限制（MB）
     * 默认: 256MB
     */
    @Builder.Default
    private int memoryLimitMB = 256;
    
    // ========== Late Acceptance参数 ==========
    
    /**
     * Late Acceptance历史大小
     * 默认: 400
     */
    @Builder.Default
    private int lateAcceptanceSize = 400;
    
    // ========== Tabu Search参数 ==========
    
    /**
     * Tabu列表大小
     * 默认: 7
     */
    @Builder.Default
    private int tabuListSize = 7;
    
    /**
     * Tabu搜索最小实体比例
     * 默认: 0.2 (20%)
     */
    @Builder.Default
    private double tabuSearchMinimumEntityRatio = 0.2;
    
    // ========== 构造启发式参数 ==========
    
    /**
     * 构造启发式类型
     * 默认: FIRST_FIT_DECREASING
     */
    @Builder.Default
    private ConstructionHeuristicType constructionHeuristicType = ConstructionHeuristicType.FIRST_FIT_DECREASING;
    
    /**
     * 是否启用分割算法
     * 默认: true
     */
    @Builder.Default
    private boolean enablePartitioning = true;
    
    // ========== 约束流参数 ==========
    
    /**
     * 约束流并行度
     * 默认: AUTO（自动检测）
     */
    @Builder.Default
    private ConstraintStreamParallelism constraintStreamParallelism = ConstraintStreamParallelism.AUTO;
    
    // ========== 调试参数 ==========
    
    /**
     * 是否启用详细日志
     * 默认: false
     */
    @Builder.Default
    private boolean enableVerboseLogging = false;
    
    /**
     * 是否导出最佳解决方案
     * 默认: true
     */
    @Builder.Default
    private boolean exportBestSolution = true;
    
    /**
     * 统计更新间隔（毫秒）
     * 默认: 5000ms (5秒)
     */
    @Builder.Default
    private long statisticsUpdateIntervalMs = 5000L;
    
    /**
     * 构造启发式类型枚举
     */
    public enum ConstructionHeuristicType {
        FIRST_FIT,
        FIRST_FIT_DECREASING,
        BEST_FIT,
        BEST_FIT_DECREASING,
        WEAKEST_FIT,
        STRONGEST_FIT
    }
    
    /**
     * 约束流并行度枚举
     */
    public enum ConstraintStreamParallelism {
        AUTO,
        NONE,
        PARALLEL
    }
    
    /**
     * 创建默认优化参数
     * 
     * @return 默认参数配置
     */
    public static OptimizationParameters createDefault() {
        return OptimizationParameters.builder().build();
    }
    
    /**
     * 创建快速优化参数（时间限制较短）
     * 
     * @return 快速优化的参数配置
     */
    public static OptimizationParameters createFast() {
        return OptimizationParameters.builder()
            .optimizationTimeLimit(Duration.ofSeconds(30))
            .constructionHeuristicTimeLimit(Duration.ofSeconds(5))
            .localSearchTimeLimit(Duration.ofSeconds(25))
            .unimprovedIterationsLimit(500)
            .lateAcceptanceSize(200)
            .build();
    }
    
    /**
     * 创建深度优化参数（时间限制较长）
     * 
     * @return 深度优化的参数配置
     */
    public static OptimizationParameters createDeep() {
        return OptimizationParameters.builder()
            .optimizationTimeLimit(Duration.ofMinutes(5))
            .constructionHeuristicTimeLimit(Duration.ofSeconds(30))
            .localSearchTimeLimit(Duration.ofMinutes(4).plusSeconds(30))
            .unimprovedIterationsLimit(5000)
            .lateAcceptanceSize(1000)
            .threadCount(8)
            .memoryLimitMB(512)
            .build();
    }
    
    /**
     * 创建调试优化参数
     * 
     * @return 调试模式的参数配置
     */
    public static OptimizationParameters createDebug() {
        return OptimizationParameters.builder()
            .optimizationTimeLimit(Duration.ofMinutes(2))
            .enableVerboseLogging(true)
            .exportBestSolution(true)
            .statisticsUpdateIntervalMs(1000L)
            .threadCount(1) // 单线程便于调试
            .build();
    }
    
    /**
     * 创建高性能优化参数
     * 
     * @return 高性能的参数配置
     */
    public static OptimizationParameters createHighPerformance() {
        return OptimizationParameters.builder()
            .optimizationTimeLimit(Duration.ofMinutes(3))
            .threadCount(Runtime.getRuntime().availableProcessors())
            .memoryLimitMB(1024)
            .constraintStreamParallelism(ConstraintStreamParallelism.PARALLEL)
            .enablePartitioning(true)
            .lateAcceptanceSize(800)
            .tabuListSize(12)
            .build();
    }
    
    /**
     * 验证参数是否合理
     * 
     * @return true如果参数配置合理
     */
    public boolean isValid() {
        return optimizationTimeLimit != null 
            && !optimizationTimeLimit.isNegative()
            && !optimizationTimeLimit.isZero()
            && threadCount > 0
            && memoryLimitMB > 0
            && lateAcceptanceSize > 0
            && tabuListSize > 0
            && unimprovedIterationsLimit > 0
            && constructionHeuristicTimeLimit != null
            && localSearchTimeLimit != null;
    }
    
    /**
     * 获取参数配置摘要
     * 
     * @return 参数配置的可读描述
     */
    public String getSummary() {
        return String.format(
            "优化参数 - 时间限制:%ds, 线程数:%d, 内存:%dMB, Late Acceptance:%d, Tabu:%d",
            optimizationTimeLimit.getSeconds(),
            threadCount,
            memoryLimitMB,
            lateAcceptanceSize,
            tabuListSize
        );
    }
    
    /**
     * 获取总时间限制（毫秒）
     * 
     * @return 总时间限制毫秒数
     */
    public long getTotalTimeLimitMs() {
        return optimizationTimeLimit.toMillis();
    }
    
    /**
     * 检查是否启用并行处理
     * 
     * @return true如果启用并行处理
     */
    public boolean isParallelProcessingEnabled() {
        return threadCount > 1 || constraintStreamParallelism == ConstraintStreamParallelism.PARALLEL;
    }
    
    /**
     * 深拷贝优化参数
     * 
     * @return 深拷贝的优化参数对象
     */
    public OptimizationParameters copy() {
        return OptimizationParameters.builder()
            .optimizationTimeLimit(this.optimizationTimeLimit)
            .constructionHeuristicTimeLimit(this.constructionHeuristicTimeLimit)
            .localSearchTimeLimit(this.localSearchTimeLimit)
            .threadCount(this.threadCount)
            .unimprovedIterationsLimit(this.unimprovedIterationsLimit)
            .memoryLimitMB(this.memoryLimitMB)
            .lateAcceptanceSize(this.lateAcceptanceSize)
            .tabuListSize(this.tabuListSize)
            .tabuSearchMinimumEntityRatio(this.tabuSearchMinimumEntityRatio)
            .constructionHeuristicType(this.constructionHeuristicType)
            .enablePartitioning(this.enablePartitioning)
            .constraintStreamParallelism(this.constraintStreamParallelism)
            .enableVerboseLogging(this.enableVerboseLogging)
            .exportBestSolution(this.exportBestSolution)
            .statisticsUpdateIntervalMs(this.statisticsUpdateIntervalMs)
            .build();
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}