package com.ict.ycwl.pathcalculate.algorithm;

import com.ict.ycwl.pathcalculate.algorithm.core.*;
import com.ict.ycwl.pathcalculate.algorithm.core.TimeEvaluationConfig;
import com.ict.ycwl.pathcalculate.algorithm.core.RouteTimeCalculator;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core.ClusteringPostOptimizer;
import com.ict.ycwl.pathcalculate.algorithm.debug.DebugDataExporter;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult;
import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 路径规划算法工具类 - 主入口
 * 
 * 整合第三方库实现高性能路径规划算法：
 * - TSP求解：Google OR-Tools
 * - 凸包计算：JTS Topology Suite  
 * - 统计计算：Apache Commons Math
 * - 核心业务逻辑：自实现
 * 
 * <AUTHOR> Assistant
 * @date 2024-01-01
 */
@Slf4j
@Component
public class PathPlanningUtils {

    // 注入各个算法组件
    private final DataValidator dataValidator = new DataValidator();
    private final DataPreprocessor dataPreprocessor = new DataPreprocessor();
    private final ClusteringQualityEvaluator qualityEvaluator = new ClusteringQualityEvaluator();
    private final UnifiedClusteringAdapter clusteringAlgorithm = new UnifiedClusteringAdapter(qualityEvaluator);
    private final TSPSolverManager tspSolverManager = new TSPSolverManager();
    private final TSPPostOptimizationManager tspPostOptimizer = new TSPPostOptimizationManager();
    private final ConvexHullManager convexHullManager = new ConvexHullManager();
    private final TimeBalanceOptimizer timeBalanceOptimizer = new TimeBalanceOptimizer();
    
    // H3时间评估组件（用于debug导出器）
    private final TimeEvaluationConfig timeConfig = new TimeEvaluationConfig();
    private final RouteTimeCalculator routeTimeCalculator = new RouteTimeCalculator(timeConfig);
    private final DebugDataExporter debugExporter = new DebugDataExporter(routeTimeCalculator);
    
    // 聚类二次优化器（可选依赖，如果未启用则为null）
    @Autowired(required = false)
    private ClusteringPostOptimizer clusteringPostOptimizer;
    
    /**
     * 全地图路径规划算法（静态入口，无Spring依赖注入）
     * 
     * @param request 路径规划请求数据
     * @return 路径规划结果
     */
    public static PathPlanningResult calculate(PathPlanningRequest request) {
        // 创建实例但不依赖Spring注入，聚类二次优化在此模式下禁用
        PathPlanningUtils instance = new PathPlanningUtils();
        log.warn("⚠️ 使用静态方法调用，聚类二次优化器未注入，将跳过聚类二次优化");
        return instance.executeAlgorithm(request);
    }
    
    /**
     * Spring管理的实例方法入口（支持完整依赖注入）
     * 
     * @param request 路径规划请求数据
     * @return 路径规划结果
     */
    public PathPlanningResult calculateWithSpring(PathPlanningRequest request) {
        return executeAlgorithm(request);
    }

    /**
     * 执行算法的核心流程
     */
    private PathPlanningResult executeAlgorithm(PathPlanningRequest request) {
        long startTime = System.currentTimeMillis();
        String debugSessionId = DebugDataExporter.generateSessionId();
        Map<String, Long> stageExecutionTimes = new HashMap<>();
        
        try {
            log.info("开始路径规划算法，聚集区数量: {}, 中转站数量: {}, 调试会话: {}", 
                    request.getAccumulations().size(), request.getTransitDepots().size(), debugSessionId);
            
            // 1. 数据验证和预处理
            long stage1Start = System.currentTimeMillis();
            AlgorithmContext context = validateAndPreprocess(request, startTime);
            context.setDebugSessionId(debugSessionId);
            stageExecutionTimes.put("preprocessing", System.currentTimeMillis() - stage1Start);
            
            // 2. 初始路线分配（聚类）
            long stage2Start = System.currentTimeMillis();
            log.info("🔍 开始聚类阶段，调试会话ID: {}", debugSessionId);
            clusterRoutes(context);
            stageExecutionTimes.put("clustering", System.currentTimeMillis() - stage2Start);
            
            // 导出聚类结果
            log.info("🔍 准备导出聚类结果，路线聚类数: {}, 会话ID: {}", 
                context.getRouteClusters().size(), debugSessionId);
            debugExporter.exportClusteringResults(context.getRouteClusters(), 
                    context.getTransitDepotMap(), debugSessionId);
            log.info("🔍 聚类结果导出完成");
            
            // 3. 路线内序列优化（TSP）
            long stage3Start = System.currentTimeMillis();
            optimizeRouteSequences(context);
            stageExecutionTimes.put("tsp", System.currentTimeMillis() - stage3Start);
            // 导出TSP结果
            debugExporter.exportTSPResults(context.getOptimizedRoutes(), debugSessionId);
            
            // 3.5. TSP后约束优化（使用第三方高性能库动态调整）
            long stage3_5Start = System.currentTimeMillis();
            performTSPPostOptimization(context);
            stageExecutionTimes.put("tspPostOptimization", System.currentTimeMillis() - stage3_5Start);
            // 导出TSP后优化结果
            debugExporter.exportTSPResults(context.getOptimizedRoutes(), debugSessionId + "_post_optimization");
            
            // 4. 凸包生成与冲突解决
            long stage4Start = System.currentTimeMillis();
            resolveConvexHullConflicts(context);
            stageExecutionTimes.put("convexHull", System.currentTimeMillis() - stage4Start);
            // 导出凸包处理结果
            debugExporter.exportConvexHullResults(context.getOptimizedRoutes(), 
                    context.getConflictResolutions(), debugSessionId);
            
            // 5. 多层级时间均衡
            long stage5Start = System.currentTimeMillis();
            balanceWorkTime(context);
            stageExecutionTimes.put("timeBalance", System.currentTimeMillis() - stage5Start);
            
            // 6. 构建最终结果
            long stage6Start = System.currentTimeMillis();
            PathPlanningResult result = buildResult(context);
            stageExecutionTimes.put("resultBuilding", System.currentTimeMillis() - stage6Start);
            
            // 导出时间均衡结果和最终结果
            debugExporter.exportTimeBalanceResults(context.getOptimizedRoutes(),
                    result.getTimeBalanceStats(), context.getTimeBalanceAdjustments(), debugSessionId);
            debugExporter.exportFinalResults(result, debugSessionId);
            
            // 导出会话摘要
            long totalExecutionTime = System.currentTimeMillis() - startTime;
            debugExporter.exportSessionSummary(debugSessionId, totalExecutionTime, stageExecutionTimes);
            
            return result;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("路径规划算法执行失败，调试会话: {}", debugSessionId, e);
            return PathPlanningResult.failure(e, executionTime);
        }
    }

    /**
     * 第一阶段：数据验证和预处理
     */
    private AlgorithmContext validateAndPreprocess(PathPlanningRequest request, long startTime) {
        log.info("阶段1：数据验证和预处理");
        
        // 数据验证
        dataValidator.validate(request);
        
        // 数据预处理
        AlgorithmContext context = dataPreprocessor.preprocess(request);
        context.setStartTime(startTime);
        
        log.info("数据预处理完成，构建了 {} 个中转站分组", context.getDepotGroups().size());
        return context;
    }

    /**
     * 第二阶段：初始路线分配（基于工作量均衡的聚类）
     */
    private void clusterRoutes(AlgorithmContext context) {
        log.info("阶段2：初始路线分配");
        
        for (Map.Entry<Long, List<Accumulation>> entry : context.getDepotGroups().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<Accumulation> accumulations = entry.getValue();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            log.info("为中转站 {} 分配 {} 个聚集区到 {} 条路线", 
                    depot.getTransitDepotName(), accumulations.size(), depot.getRouteCount());
            
            // 使用工作量均衡的K-means聚类
            List<List<Accumulation>> routeClusters = clusteringAlgorithm.clusterByWorkload(
                    accumulations, depot, context.getTimeMatrix());
            
            // 【聚类二次优化插入点】
            log.info("🔍 检查聚类二次优化条件 - 启用开关: {}, 优化器: {}", 
                AlgorithmParameters.ENABLE_CLUSTERING_POST_OPTIMIZATION, 
                clusteringPostOptimizer != null ? "已注入" : "NULL");
            
            if (AlgorithmParameters.ENABLE_CLUSTERING_POST_OPTIMIZATION && clusteringPostOptimizer != null) {
                log.info("🔄 开始聚类二次优化，中转站: {}", depot.getTransitDepotName());
                
                try {
                    long optimizationStart = System.currentTimeMillis();
                    
                    // 检查是否需要优化
                    if (clusteringPostOptimizer.needsOptimization(depot, routeClusters, context.getTimeMatrix())) {
                        // 执行二次优化
                        List<List<Accumulation>> optimizedClusters = clusteringPostOptimizer.optimize(
                                depot, routeClusters, context.getTimeMatrix());
                        
                        long optimizationTime = System.currentTimeMillis() - optimizationStart;
                        
                        if (optimizedClusters != null && !optimizedClusters.isEmpty()) {
                            routeClusters = optimizedClusters;
                            log.info("✅ 聚类二次优化完成，耗时: {}ms", optimizationTime);
                        } else {
                            log.warn("⚠️ 聚类二次优化返回空结果，使用原始聚类");
                        }
                    } else {
                        log.info("ℹ️ 聚类已满足约束，跳过二次优化");
                    }
                    
                } catch (Exception e) {
                    log.error("❌ 聚类二次优化失败，使用原始聚类结果", e);
                    // 发生异常时使用原始聚类结果，确保算法流程不中断
                }
            }
            
            context.addRouteClusters(transitDepotId, routeClusters);
        }
        
        log.info("路线分配完成，总路线数: {}", context.getTotalRouteCount());
    }

    /**
     * 第三阶段：路线内序列优化（TSP求解）
     */
    private void optimizeRouteSequences(AlgorithmContext context) {
        log.info("阶段3：路线内序列优化");
        
        int totalRoutes = context.getTotalRouteCount();
        int optimizedCount = 0;
        
        for (Map.Entry<Long, List<List<Accumulation>>> entry : context.getRouteClusters().entrySet()) {
            Long transitDepotId = entry.getKey();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            List<List<Accumulation>> routeClusters = entry.getValue();
            
            for (int i = 0; i < routeClusters.size(); i++) {
                List<Accumulation> cluster = routeClusters.get(i);
                
                // 使用TSP求解器优化访问顺序
                RouteResult optimizedRoute = tspSolverManager.solveRoute(
                        depot, cluster, context.getTimeMatrix(), i + 1);
                
                context.addOptimizedRoute(transitDepotId, optimizedRoute);
                optimizedCount++;
                
                if (optimizedCount % 10 == 0) {
                    log.info("TSP优化进度: {}/{}", optimizedCount, totalRoutes);
                }
            }
        }
        
        log.info("路线序列优化完成，优化了 {} 条路线", optimizedCount);
    }

    /**
     * 第四阶段：凸包生成与冲突解决
     */
    private void resolveConvexHullConflicts(AlgorithmContext context) {
        log.info("阶段4：凸包生成与冲突解决");
        
        int conflictCount = 0;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            
            // 生成凸包
            convexHullManager.generateConvexHulls(routes, context);
            
            // 检测和解决冲突
            int resolvedConflicts = convexHullManager.resolveConflicts(transitDepotId, routes, context);
            conflictCount += resolvedConflicts;
        }
        
        log.info("凸包冲突解决完成，解决了 {} 个冲突", conflictCount);
    }

    /**
     * 第五阶段：多层级时间均衡
     */
    private void balanceWorkTime(AlgorithmContext context) {
        log.info("阶段5：多层级时间均衡");
        
        // 路线级均衡
        int routeBalanceCount = timeBalanceOptimizer.balanceRoutes(context);
        
        // 中转站级均衡
        int depotBalanceCount = timeBalanceOptimizer.balanceDepots(context);
        
        // 班组级均衡（观察性）
        timeBalanceOptimizer.evaluateTeamBalance(context);
        
        log.info("时间均衡完成，路线调整: {}, 中转站调整: {}", routeBalanceCount, depotBalanceCount);
    }

    /**
     * 第六阶段：构建最终结果
     */
    private PathPlanningResult buildResult(AlgorithmContext context) {
        log.info("阶段6：构建最终结果");
        
        long executionTime = System.currentTimeMillis() - context.getStartTime();
        
        // 收集所有路线结果
        List<RouteResult> allRoutes = context.getAllOptimizedRoutes();
        
        // 生成时间均衡统计
        TimeBalanceStats balanceStats = timeBalanceOptimizer.generateStats(context);
        
        PathPlanningResult result = PathPlanningResult.success(allRoutes, executionTime, balanceStats);
        
        log.info("路径规划算法执行完成，耗时: {}ms, 生成路线: {}, 总工作时间: {}分钟",
                executionTime, allRoutes.size(), String.format("%.1f", result.getTotalWorkTime()));
        return result;
    }
    
    /**
     * 第3.5阶段：TSP后约束优化（使用第三方高性能库动态调整）
     */
    private void performTSPPostOptimization(AlgorithmContext context) {
        log.info("阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整");
        
        // 使用TSP后优化管理器进行智能约束优化
        boolean optimizationSuccess = tspPostOptimizer.performTSPPostOptimization(context);
        
        if (optimizationSuccess) {
            log.info("✅ [TSP后优化成功] 第三方库优化完成，约束满足情况已改善");
        } else {
            log.warn("⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反");
        }
        
        // 输出约束满足统计
        logConstraintStatistics(context);
    }
    
    /**
     * 输出约束满足统计信息
     */
    private void logConstraintStatistics(AlgorithmContext context) {
        int totalRoutes = 0;
        int violating450Routes = 0;
        int excessiveGapDepots = 0;
        double maxTime = 0.0;
        double maxGap = 0.0;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            totalRoutes += routes.size();
            
            // 检查450分钟约束
            for (RouteResult route : routes) {
                if (route.getTotalWorkTime() > 450.0) {
                    violating450Routes++;
                }
                maxTime = Math.max(maxTime, route.getTotalWorkTime());
            }
            
            // 检查时间差距约束
            if (routes.size() > 1) {
                double routeMaxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
                double routeMinTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
                double timeGap = routeMaxTime - routeMinTime;
                
                if (timeGap > 30.0) {
                    excessiveGapDepots++;
                }
                maxGap = Math.max(maxGap, timeGap);
            }
        }
        
        log.info("📊 [约束统计] 总路线: {}, 超450分钟路线: {}, 超30分钟差距中转站: {}", 
            totalRoutes, violating450Routes, excessiveGapDepots);
        log.info("   最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟", maxTime, maxGap);
        
        if (violating450Routes == 0 && excessiveGapDepots == 0) {
            log.info("🎉 [约束满足] 所有硬约束均已满足！算法效果达标");
        } else {
            log.warn("⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数");
        }
    }
} 