package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.CoordinatePoint;
import com.ict.ycwl.pathcalculate.algorithm.entity.RouteResult;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.domain.entity.PlanningEntity;
import org.optaplanner.core.api.domain.solution.PlanningEntityCollectionProperty;
import org.optaplanner.core.api.domain.solution.PlanningScore;
import org.optaplanner.core.api.domain.solution.PlanningSolution;
import org.optaplanner.core.api.domain.solution.ProblemFactCollectionProperty;
import org.optaplanner.core.api.domain.valuerange.ValueRangeProvider;
import org.optaplanner.core.api.domain.variable.PlanningVariable;
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore;
import org.optaplanner.core.api.solver.Solver;
import org.optaplanner.core.api.solver.SolverFactory;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.config.solver.termination.TerminationConfig;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * OptaPlanner VRP重优化器
 * 专门在TSP阶段后使用OptaPlanner进行聚集区重新分配
 * 
 * 核心功能：
 * 1. 将当前路线分配转换为OptaPlanner VRP问题
 * 2. 定义450分钟和30分钟时间差的硬约束
 * 3. 使用OptaPlanner的元启发式算法重新分配聚集区
 * 4. 确保聚集区只在同一中转站内的不同路线间转移
 */
@Slf4j
@Component
public class OptaPlannerVRPReoptimizer {
    
    // 约束参数
    private static final double MAX_ROUTE_TIME_MINUTES = 450.0;
    private static final double TIME_GAP_LIMIT_MINUTES = 30.0;
    private static final int SOLVER_TIME_LIMIT_SECONDS = 180; // 3分钟求解时间，提高求解质量
    private static final int MAX_OPTIMIZATION_ROUNDS = 3;     // 最大优化轮数
    
    private final TSPSolverManager tspSolver;
    
    public OptaPlannerVRPReoptimizer() {
        this.tspSolver = new TSPSolverManager();
    }
    
    /**
     * 使用OptaPlanner重优化聚集区分配
     */
    public boolean reoptimizeWithOptaPlanner(AlgorithmContext context, 
                                           TSPPostOptimizationManager.ConstraintViolationAnalysis analysis) {
        log.info("🎯 [OptaPlanner重优化] 开始使用OptaPlanner进行VRP重优化");
        
        boolean globalSuccess = true;
        
        // 对每个有问题的中转站独立进行OptaPlanner重优化
        Set<Long> problematicDepots = new HashSet<>();
        
        // 收集有约束违反的中转站
        for (RouteResult violatingRoute : analysis.getViolatingRoutes()) {
            problematicDepots.add(violatingRoute.getTransitDepotId());
        }
        problematicDepots.addAll(analysis.getExcessiveGapDepots());
        
        log.info("📍 [重优化范围] 需要OptaPlanner重优化的中转站数量: {}", problematicDepots.size());
        
        for (Long transitDepotId : problematicDepots) {
            List<RouteResult> routes = context.getOptimizedRoutes().get(transitDepotId);
            if (routes == null || routes.size() <= 1) continue;
            
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            log.info("🔧 [OptaPlanner] 重优化中转站{}: {}条路线", depot.getTransitDepotName(), routes.size());
            
            boolean depotSuccess = reoptimizeDepotWithOptaPlanner(depot, routes, context);
            if (!depotSuccess) {
                globalSuccess = false;
                log.warn("⚠️ [OptaPlanner失败] 中转站{}重优化失败", depot.getTransitDepotName());
            }
        }
        
        log.info("🎉 [OptaPlanner完成] 全局重优化成功: {}", globalSuccess);
        return globalSuccess;
    }
    
    /**
     * 为单个中转站使用OptaPlanner重优化
     */
    private boolean reoptimizeDepotWithOptaPlanner(TransitDepot depot, List<RouteResult> routes, 
                                                 AlgorithmContext context) {
        try {
            // 收集当前所有聚集区
            List<Long> allAccumulations = routes.stream()
                .flatMap(route -> route.getAccumulationSequence().stream())
                .collect(Collectors.toList());
                
            if (allAccumulations.isEmpty()) {
                return true;
            }
            
            log.info("📊 [OptaPlanner输入] 中转站{}: {}条路线, {}个聚集区",
                depot.getTransitDepotName(), routes.size(), allAccumulations.size());
            
            // 🔄 多轮优化机制：直到满足约束或达到最大轮数
            for (int round = 1; round <= MAX_OPTIMIZATION_ROUNDS; round++) {
                log.info("🔄 [OptaPlanner第{}轮] 开始第{}轮重优化", round, round);
                
                // 创建OptaPlanner VRP问题
                VRPReoptimizationSolution unsolvedProblem = createOptaPlannerProblem(depot, routes, allAccumulations, context);
                
                // 🔧 强化求解器配置，根据轮数调整时间
                int roundTimeLimit = SOLVER_TIME_LIMIT_SECONDS + (round - 1) * 60; // 每轮增加1分钟
                SolverConfig solverConfig = new SolverConfig()
                    .withSolutionClass(VRPReoptimizationSolution.class)
                    .withEntityClasses(VRPCustomerReoptimization.class)
                    .withTerminationConfig(new TerminationConfig()
                        .withSecondsSpentLimit((long) roundTimeLimit)
                        .withBestScoreLimit("0hard/*soft")); // 硬约束必须满足
                
                // 创建求解器并求解
                SolverFactory<VRPReoptimizationSolution> solverFactory = SolverFactory.create(solverConfig);
                Solver<VRPReoptimizationSolution> solver = solverFactory.buildSolver();
                
                log.info("🔍 [OptaPlanner求解] 第{}轮求解中转站{}，时间限制{}秒", round, depot.getTransitDepotName(), roundTimeLimit);
                VRPReoptimizationSolution solvedProblem = solver.solve(unsolvedProblem);
                
                // 检查求解结果
                HardSoftScore score = solvedProblem.getScore();
                log.info("📊 [第{}轮结果] 中转站{}得分: {}", round, depot.getTransitDepotName(), score);
                
                if (score.isFeasible()) {
                    // 应用OptaPlanner解到实际路线
                    boolean applied = applyOptaPlannerSolution(depot, routes, solvedProblem, context);
                    
                    if (applied) {
                        // 🔍 验证应用后的约束满足情况
                        boolean constraintsSatisfied = validateConstraintsAfterOptimization(routes, depot, round);
                        
                        if (constraintsSatisfied) {
                            log.info("✅ [OptaPlanner成功] 中转站{}第{}轮优化成功，约束完全满足", depot.getTransitDepotName(), round);
                            return true;
                        } else {
                            log.warn("⚠️ [约束仍违反] 中转站{}第{}轮优化后约束仍有违反，继续下一轮", depot.getTransitDepotName(), round);
                            // 继续下一轮优化
                        }
                    } else {
                        log.warn("❌ [应用失败] 中转站{}第{}轮解应用失败，继续下一轮", depot.getTransitDepotName(), round);
                    }
                } else {
                    log.warn("❌ [无可行解] 中转站{}第{}轮无可行解，得分: {}，继续下一轮", depot.getTransitDepotName(), round, score);
                }
                
                // 如果不是最后一轮，稍微调整求解参数
                if (round < MAX_OPTIMIZATION_ROUNDS) {
                    log.info("🔄 [准备下轮] 第{}轮未达到预期，准备第{}轮优化", round, round + 1);
                }
            }
            
            // 所有轮次都失败
            log.error("❌ [OptaPlanner彻底失败] 中转站{}经过{}轮优化仍无法满足约束", depot.getTransitDepotName(), MAX_OPTIMIZATION_ROUNDS);
            return false;
            
        } catch (Exception e) {
            log.error("❌ [OptaPlanner异常] 中转站{}重优化异常: {}", depot.getTransitDepotName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建OptaPlanner VRP重优化问题
     */
    private VRPReoptimizationSolution createOptaPlannerProblem(TransitDepot depot, List<RouteResult> routes,
                                                             List<Long> allAccumulations, AlgorithmContext context) {
        
        VRPReoptimizationSolution problem = new VRPReoptimizationSolution();
        
        // 创建车辆（路线）
        List<VRPVehicleReoptimization> vehicles = new ArrayList<>();
        for (int i = 0; i < routes.size(); i++) {
            VRPVehicleReoptimization vehicle = new VRPVehicleReoptimization();
            vehicle.setId(i);
            vehicle.setOriginalRouteId(routes.get(i).getRouteId());
            vehicle.setDepot(depot);
            vehicle.setMaxTimeMinutes(MAX_ROUTE_TIME_MINUTES);
            vehicles.add(vehicle);
        }
        problem.setVehicles(vehicles);
        
        // 创建客户（聚集区）- 保留当前分配作为初始解
        List<VRPCustomerReoptimization> customers = new ArrayList<>();
        for (int routeIndex = 0; routeIndex < routes.size(); routeIndex++) {
            RouteResult route = routes.get(routeIndex);
            VRPVehicleReoptimization assignedVehicle = vehicles.get(routeIndex);
            
            for (Long accId : route.getAccumulationSequence()) {
                Accumulation acc = context.getAccumulationById(accId);
                if (acc != null) {
                    VRPCustomerReoptimization customer = new VRPCustomerReoptimization();
                    customer.setId(acc.getAccumulationId().intValue());
                    customer.setAccumulationId(accId);
                    customer.setServiceTime(acc.getDeliveryTime());
                    customer.setLocation(acc.getCoordinate());
                    customer.setVehicle(assignedVehicle); // 设置初始分配
                    customers.add(customer);
                }
            }
        }
        problem.setCustomers(customers);
        problem.setDepot(depot);
        problem.setContext(context);
        
        return problem;
    }
    
    /**
     * 验证OptaPlanner优化后的约束满足情况
     */
    private boolean validateConstraintsAfterOptimization(List<RouteResult> routes, TransitDepot depot, int round) {
        log.info("🔍 [第{}轮约束验证] 验证中转站{}优化后的约束满足情况", round, depot.getTransitDepotName());
        
        boolean allConstraintsSatisfied = true;
        
        // 检查450分钟硬约束
        int violatingRoutes = 0;
        double maxTimeViolation = 0.0;
        
        for (RouteResult route : routes) {
            if (route.getTotalWorkTime() > MAX_ROUTE_TIME_MINUTES) {
                violatingRoutes++;
                double violation = route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES;
                maxTimeViolation = Math.max(maxTimeViolation, violation);
                allConstraintsSatisfied = false;
                
                log.warn("  🚨 [约束违反] 路线{}: {:.1f}分钟 (超出{:.1f}分钟)", 
                    route.getRouteName(), route.getTotalWorkTime(), violation);
            }
        }
        
        // 检查30分钟时间差约束
        if (routes.size() > 1) {
            double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
            double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
            double timeGap = maxTime - minTime;
            
            if (timeGap > TIME_GAP_LIMIT_MINUTES) {
                double gapViolation = timeGap - TIME_GAP_LIMIT_MINUTES;
                allConstraintsSatisfied = false;
                
                log.warn("  ⏰ [时间差违反] 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟 (超出{:.1f}分钟)",
                    maxTime, minTime, timeGap, gapViolation);
            } else {
                log.info("  ✅ [时间差满足] 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟 ≤ {:.1f}分钟",
                    maxTime, minTime, timeGap, TIME_GAP_LIMIT_MINUTES);
            }
        }
        
        if (violatingRoutes == 0) {
            log.info("  ✅ [时间约束满足] 所有路线都在{:.1f}分钟以内", MAX_ROUTE_TIME_MINUTES);
        } else {
            log.warn("  🚨 [时间约束违反] {}条路线超时，最大违反{:.1f}分钟", violatingRoutes, maxTimeViolation);
        }
        
        if (allConstraintsSatisfied) {
            log.info("✅ [第{}轮验证成功] 中转站{}所有约束均已满足", round, depot.getTransitDepotName());
        } else {
            log.warn("❌ [第{}轮验证失败] 中转站{}仍有约束违反", round, depot.getTransitDepotName());
        }
        
        return allConstraintsSatisfied;
    }
    
    /**
     * 应用OptaPlanner求解结果到实际路线
     */
    private boolean applyOptaPlannerSolution(TransitDepot depot, List<RouteResult> routes,
                                           VRPReoptimizationSolution solution, AlgorithmContext context) {
        // 🔒 备份原始路线数据，防止数据丢失
        Map<Long, List<Long>> originalAccumulationBackup = new HashMap<>();
        Map<Long, Double> originalWorkTimeBackup = new HashMap<>();
        Map<Long, List<CoordinatePoint>> originalPolylineBackup = new HashMap<>();
        
        try {
            
            for (RouteResult route : routes) {
                originalAccumulationBackup.put(route.getRouteId(), new ArrayList<>(route.getAccumulationSequence()));
                originalWorkTimeBackup.put(route.getRouteId(), route.getTotalWorkTime());
                originalPolylineBackup.put(route.getRouteId(), new ArrayList<>(route.getPolyline()));
            }
            
            log.debug("🔒 [OptaPlanner备份] 已备份{}条路线的原始数据", routes.size());
            
            // 清空所有路线的聚集区分配
            for (RouteResult route : routes) {
                route.setAccumulationSequence(new ArrayList<>());
            }
            
            // 按OptaPlanner的解重新分配聚集区
            Map<Integer, List<Long>> vehicleAssignments = new HashMap<>();
            for (VRPVehicleReoptimization vehicle : solution.getVehicles()) {
                vehicleAssignments.put(vehicle.getId(), new ArrayList<>());
            }
            
            for (VRPCustomerReoptimization customer : solution.getCustomers()) {
                if (customer.getVehicle() != null) {
                    int vehicleId = customer.getVehicle().getId();
                    vehicleAssignments.get(vehicleId).add(customer.getAccumulationId());
                }
            }
            
            // 为每条路线重新运行TSP优化
            for (int i = 0; i < routes.size(); i++) {
                RouteResult route = routes.get(i);
                List<Long> assignedAccIds = vehicleAssignments.get(i);
                
                if (assignedAccIds.isEmpty()) {
                    // 空路线
                    route.setAccumulationSequence(new ArrayList<>());
                    route.setTotalWorkTime(AlgorithmParameters.LOADING_TIME_MINUTES);
                    route.setPolyline(Arrays.asList(depot.getCoordinate()));
                } else {
                    // 重新TSP优化
                    List<Accumulation> accumulations = assignedAccIds.stream()
                        .map(context::getAccumulationById)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                        
                    if (!accumulations.isEmpty()) {
                        RouteResult optimizedRoute = tspSolver.solveRoute(
                            depot, accumulations, context.getTimeMatrix(),
                            i + 1, TSPSolverManager.SolverStrategy.AUTO,
                            MultiObjectiveTSP.OptimizationGoal.BALANCED, 30000L
                        );
                        
                        route.setAccumulationSequence(optimizedRoute.getAccumulationSequence());
                        route.setTotalWorkTime(optimizedRoute.getTotalWorkTime());
                        route.setPolyline(optimizedRoute.getPolyline());
                    }
                }
            }
            
            // 验证结果
            double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
            double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
            double timeGap = maxTime - minTime;
            
            log.info("🎯 [OptaPlanner结果] 中转站{}: 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟",
                depot.getTransitDepotName(), maxTime, minTime, timeGap);
            
            boolean constraintsSatisfied = maxTime <= MAX_ROUTE_TIME_MINUTES && timeGap <= TIME_GAP_LIMIT_MINUTES;
            
            if (constraintsSatisfied) {
                log.info("✅ [约束满足] OptaPlanner重优化成功满足所有约束");
            } else {
                log.warn("⚠️ [约束违反] OptaPlanner重优化后仍有约束违反");
            }
            
            return constraintsSatisfied;
            
        } catch (Exception e) {
            log.error("❌ [OptaPlanner应用解失败] 异常: {}, 正在恢复原始数据", e.getMessage());
            
            // 🛡️ 恢复原始数据，防止数据丢失
            try {
                for (RouteResult route : routes) {
                    List<Long> originalAccumulation = originalAccumulationBackup.get(route.getRouteId());
                    Double originalWorkTime = originalWorkTimeBackup.get(route.getRouteId());
                    List<CoordinatePoint> originalPolyline = originalPolylineBackup.get(route.getRouteId());
                    
                    if (originalAccumulation != null) {
                        route.setAccumulationSequence(originalAccumulation);
                    }
                    if (originalWorkTime != null) {
                        route.setTotalWorkTime(originalWorkTime);
                    }
                    if (originalPolyline != null) {
                        route.setPolyline(originalPolyline);
                    }
                }
                
                log.info("🛡️ [数据恢复] OptaPlanner失败后成功恢复{}条路线的原始数据", routes.size());
                
            } catch (Exception restoreException) {
                log.error("💥 [致命错误] OptaPlanner数据恢复也失败: {}", restoreException.getMessage());
            }
            
            return false;
        }
    }
    
    // =================== OptaPlanner域模型定义 ===================
    
    /**
     * OptaPlanner VRP重优化解决方案类
     * 暂时注释@PlanningSolution避免与聚类二次优化冲突
     */
    // @PlanningSolution
    public static class VRPReoptimizationSolution {
        
        // @ProblemFactCollectionProperty
        // @ValueRangeProvider(id = "vehicleRange")
        private List<VRPVehicleReoptimization> vehicles;
        
        // @PlanningEntityCollectionProperty
        private List<VRPCustomerReoptimization> customers;
        
        // @PlanningScore
        private HardSoftScore score;
        
        // 问题上下文
        private TransitDepot depot;
        private AlgorithmContext context;
        
        // Getters and Setters
        public List<VRPVehicleReoptimization> getVehicles() { return vehicles; }
        public void setVehicles(List<VRPVehicleReoptimization> vehicles) { this.vehicles = vehicles; }
        
        public List<VRPCustomerReoptimization> getCustomers() { return customers; }
        public void setCustomers(List<VRPCustomerReoptimization> customers) { this.customers = customers; }
        
        public HardSoftScore getScore() { return score; }
        public void setScore(HardSoftScore score) { this.score = score; }
        
        public TransitDepot getDepot() { return depot; }
        public void setDepot(TransitDepot depot) { this.depot = depot; }
        
        public AlgorithmContext getContext() { return context; }
        public void setContext(AlgorithmContext context) { this.context = context; }
    }
    
    /**
     * OptaPlanner客户（聚集区）重优化实体
     * 暂时注释@PlanningEntity避免与主配置冲突
     */
    // @PlanningEntity
    public static class VRPCustomerReoptimization {
        
        private int id;
        private Long accumulationId;
        private double serviceTime;
        private CoordinatePoint location;
        
        // @PlanningVariable(valueRangeProviderRefs = "vehicleRange")
        private VRPVehicleReoptimization vehicle;
        
        // Getters and Setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }
        
        public Long getAccumulationId() { return accumulationId; }
        public void setAccumulationId(Long accumulationId) { this.accumulationId = accumulationId; }
        
        public double getServiceTime() { return serviceTime; }
        public void setServiceTime(double serviceTime) { this.serviceTime = serviceTime; }
        
        public CoordinatePoint getLocation() { return location; }
        public void setLocation(CoordinatePoint location) { this.location = location; }
        
        public VRPVehicleReoptimization getVehicle() { return vehicle; }
        public void setVehicle(VRPVehicleReoptimization vehicle) { this.vehicle = vehicle; }
    }
    
    /**
     * VRP车辆（路线）重优化
     */
    public static class VRPVehicleReoptimization {
        
        private int id;
        private Long originalRouteId;
        private TransitDepot depot;
        private double maxTimeMinutes;
        
        // Getters and Setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }
        
        public Long getOriginalRouteId() { return originalRouteId; }
        public void setOriginalRouteId(Long originalRouteId) { this.originalRouteId = originalRouteId; }
        
        public TransitDepot getDepot() { return depot; }
        public void setDepot(TransitDepot depot) { this.depot = depot; }
        
        public double getMaxTimeMinutes() { return maxTimeMinutes; }
        public void setMaxTimeMinutes(double maxTimeMinutes) { this.maxTimeMinutes = maxTimeMinutes; }
    }
    
    // OptaPlanner 7.x简化版本，不使用约束流API
    // 约束验证将在后处理阶段进行
}