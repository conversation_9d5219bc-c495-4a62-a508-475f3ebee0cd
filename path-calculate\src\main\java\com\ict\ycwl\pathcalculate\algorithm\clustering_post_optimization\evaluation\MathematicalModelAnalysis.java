package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import lombok.Builder;
import lombok.Data;

/**
 * 数学建模分析结果
 * 
 * 基于运筹学数学模型的理论分析
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class MathematicalModelAnalysis {
    
    /**
     * Bin Packing分析结果
     */
    private BinPackingAnalysis binPackingAnalysis;
    
    /**
     * 负载均衡分析结果
     */
    private LoadBalancingAnalysis loadBalancingAnalysis;
    
    /**
     * 排队理论分析结果
     */
    private QueueingTheoryAnalysis queueingTheoryAnalysis;
    
    /**
     * 数学模型综合评分 (0.0-1.0)
     */
    private double overallModelScore;
    
    /**
     * 理论最优性评估
     */
    private OptimalityAssessment optimalityAssessment;
    
    /**
     * 获取数学模型等级
     */
    public ModelOptimalityGrade getModelGrade() {
        if (overallModelScore >= 0.95) {
            return ModelOptimalityGrade.OPTIMAL;
        } else if (overallModelScore >= 0.9) {
            return ModelOptimalityGrade.NEAR_OPTIMAL;
        } else if (overallModelScore >= 0.8) {
            return ModelOptimalityGrade.GOOD;
        } else if (overallModelScore >= 0.7) {
            return ModelOptimalityGrade.FAIR;
        } else {
            return ModelOptimalityGrade.SUBOPTIMAL;
        }
    }
    
    /**
     * 是否需要基于数学模型调整路线数量
     */
    public boolean needsModelBasedAdjustment() {
        return overallModelScore < 0.8 || 
               (binPackingAnalysis != null && binPackingAnalysis.getOptimizationPotential() > 2);
    }
    
    /**
     * 获取基于数学模型的推荐调整方向
     */
    public RouteCountAction getModelBasedRecommendation() {
        if (binPackingAnalysis != null && binPackingAnalysis.getOptimizationPotential() > 2) {
            return RouteCountAction.DECREASE; // 可以减少路线数量
        } else if (loadBalancingAnalysis != null && 
                   loadBalancingAnalysis.getRecommendedRouteIncrease() > 0) {
            return RouteCountAction.INCREASE; // 需要增加路线数量
        } else {
            return RouteCountAction.MAINTAIN; // 保持现状
        }
    }
    
    /**
     * Bin Packing分析
     */
    @Data
    @Builder
    public static class BinPackingAnalysis {
        
        /**
         * 当前箱子（路线）数量
         */
        private int currentBinCount;
        
        /**
         * First Fit Decreasing算法最优箱子数量
         */
        private int ffdOptimalBinCount;
        
        /**
         * 理论下界（最少需要的箱子数）
         */
        private int theoreticalLowerBound;
        
        /**
         * Next Fit算法上界
         */
        private int nextFitUpperBound;
        
        /**
         * 当前解是否最优
         */
        private boolean isCurrentOptimal;
        
        /**
         * 优化潜力（当前数量 - 最优数量）
         */
        private int optimizationPotential;
        
        /**
         * 装箱效率 (0.0-1.0)
         */
        private double packingEfficiency;
        
        /**
         * 获取装箱质量等级
         */
        public PackingQuality getPackingQuality() {
            if (isCurrentOptimal) {
                return PackingQuality.OPTIMAL;
            } else if (optimizationPotential <= 1) {
                return PackingQuality.NEAR_OPTIMAL;
            } else if (optimizationPotential <= 3) {
                return PackingQuality.GOOD;
            } else if (optimizationPotential <= 5) {
                return PackingQuality.FAIR;
            } else {
                return PackingQuality.POOR;
            }
        }
    }
    
    /**
     * 负载均衡分析
     */
    @Data
    @Builder
    public static class LoadBalancingAnalysis {
        
        /**
         * 当前负载均衡指数 (0.0-1.0)
         */
        private double currentBalanceIndex;
        
        /**
         * 理论最优负载均衡指数
         */
        private double theoreticalOptimalBalance;
        
        /**
         * 推荐的路线增加数量
         */
        private int recommendedRouteIncrease;
        
        /**
         * 推荐的路线减少数量
         */
        private int recommendedRouteDecrease;
        
        /**
         * 负载偏差标准差
         */
        private double loadDeviationStd;
        
        /**
         * 最大负载比例（最重路线/平均负载）
         */
        private double maxLoadRatio;
        
        /**
         * 最小负载比例（最轻路线/平均负载）
         */
        private double minLoadRatio;
        
        /**
         * 获取负载均衡等级
         */
        public LoadBalanceGrade getBalanceGrade() {
            if (currentBalanceIndex >= 0.95) {
                return LoadBalanceGrade.EXCELLENT;
            } else if (currentBalanceIndex >= 0.9) {
                return LoadBalanceGrade.GOOD;
            } else if (currentBalanceIndex >= 0.8) {
                return LoadBalanceGrade.FAIR;
            } else if (currentBalanceIndex >= 0.7) {
                return LoadBalanceGrade.POOR;
            } else {
                return LoadBalanceGrade.VERY_POOR;
            }
        }
    }
    
    /**
     * 排队理论分析
     */
    @Data
    @Builder
    public static class QueueingTheoryAnalysis {
        
        /**
         * 系统利用率 (0.0-1.0)
         */
        private double systemUtilization;
        
        /**
         * 平均等待时间
         */
        private double averageWaitTime;
        
        /**
         * 服务效率评分 (0.0-1.0)
         */
        private double serviceEfficiency;
        
        /**
         * 吞吐量评分 (0.0-1.0)
         */
        private double throughputScore;
        
        /**
         * 系统稳定性评分 (0.0-1.0)
         */
        private double stabilityScore;
        
        /**
         * 获取排队系统等级
         */
        public QueueingSystemGrade getSystemGrade() {
            double avgScore = (serviceEfficiency + throughputScore + stabilityScore) / 3.0;
            if (avgScore >= 0.9) {
                return QueueingSystemGrade.EXCELLENT;
            } else if (avgScore >= 0.8) {
                return QueueingSystemGrade.GOOD;
            } else if (avgScore >= 0.7) {
                return QueueingSystemGrade.FAIR;
            } else if (avgScore >= 0.6) {
                return QueueingSystemGrade.POOR;
            } else {
                return QueueingSystemGrade.CRITICAL;
            }
        }
    }
    
    /**
     * 理论最优性评估
     */
    @Data
    @Builder
    public static class OptimalityAssessment {
        
        /**
         * 距离理论最优的差距 (0.0-1.0)
         */
        private double optimalityGap;
        
        /**
         * 改进潜力评分 (0.0-1.0)
         */
        private double improvementPotential;
        
        /**
         * 约束紧度评分 (0.0-1.0)
         */
        private double constraintTightness;
        
        /**
         * 解的质量等级
         */
        private SolutionQuality solutionQuality;
    }
    
    // 各种等级枚举
    
    public enum ModelOptimalityGrade {
        OPTIMAL("最优", "理论最优解"),
        NEAR_OPTIMAL("接近最优", "非常接近理论最优"),
        GOOD("良好", "较好的解"),
        FAIR("一般", "可接受的解"),
        SUBOPTIMAL("次优", "有明显改进空间");
        
        private final String description;
        private final String detail;
        
        ModelOptimalityGrade(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() { return description; }
        public String getDetail() { return detail; }
    }
    
    public enum PackingQuality {
        OPTIMAL, NEAR_OPTIMAL, GOOD, FAIR, POOR
    }
    
    public enum LoadBalanceGrade {
        EXCELLENT, GOOD, FAIR, POOR, VERY_POOR
    }
    
    public enum QueueingSystemGrade {
        EXCELLENT, GOOD, FAIR, POOR, CRITICAL
    }
    
    public enum SolutionQuality {
        OPTIMAL, HIGH, MEDIUM, LOW, POOR
    }
}