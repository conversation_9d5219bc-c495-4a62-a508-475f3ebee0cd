package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@TableName("delivery_area")
@Data
public class Delivery implements Serializable {
    //配送域id
    @TableId(type = IdType.AUTO)
    private int deliveryAreaId;
    //配送域名称
    private String deliveryAreaName;
    //班组id
    private int teamId;
    //所属行政区id
    private int areaId;
    //中转站id
    private int transitDepotId;
    //配送类型
    private int deliveryTypeId;
    //路线数量
    private int routeNumber;
    //车辆数
    private int carNumber;
    //是否软删除
    private int isDelete;
}
