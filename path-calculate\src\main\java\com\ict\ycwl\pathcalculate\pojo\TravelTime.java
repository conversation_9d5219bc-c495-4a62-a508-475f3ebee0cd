package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 行驶时间实体类
 * 对应travel_time表
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("travel_time")
public class TravelTime {
    
    /**
     * 起点经度
     */
    private String longitudeStart;
    
    /**
     * 起点纬度
     */
    private String latitudeStart;
    
    /**
     * 终点经度
     */
    private String longitudeEnd;
    
    /**
     * 终点纬度
     */
    private String latitudeEnd;
    
    /**
     * 行驶时间（分钟）
     */
    private Double travelTime;
    
    /**
     * 构造方法 - 基于坐标点创建
     * 
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng 终点经度
     * @param toLat 终点纬度
     * @param time 行驶时间（分钟）
     */
    public TravelTime(Double fromLng, Double fromLat, Double toLng, Double toLat, Double time) {
        this.longitudeStart = String.format("%.6f", fromLng);
        this.latitudeStart = String.format("%.6f", fromLat);
        this.longitudeEnd = String.format("%.6f", toLng);
        this.latitudeEnd = String.format("%.6f", toLat);
        this.travelTime = time;
    }
    
    /**
     * 获取起点坐标字符串
     * 
     * @return 格式："经度,纬度"
     */
    public String getStartCoordinate() {
        return longitudeStart + "," + latitudeStart;
    }
    
    /**
     * 获取终点坐标字符串
     * 
     * @return 格式："经度,纬度"
     */
    public String getEndCoordinate() {
        return longitudeEnd + "," + latitudeEnd;
    }
    
    /**
     * 获取路径键（用于时间矩阵）
     * 
     * @return 格式："起点经度,起点纬度->终点经度,终点纬度"
     */
    public String getRouteKey() {
        return getStartCoordinate() + "->" + getEndCoordinate();
    }
    
    /**
     * 检查数据是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return longitudeStart != null && latitudeStart != null &&
               longitudeEnd != null && latitudeEnd != null &&
               travelTime != null && travelTime >= 0;
    }
    
    /**
     * 获取起点经度（Double类型）
     */
    public Double getStartLongitude() {
        try {
            return Double.parseDouble(longitudeStart);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 获取起点纬度（Double类型）
     */
    public Double getStartLatitude() {
        try {
            return Double.parseDouble(latitudeStart);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 获取终点经度（Double类型）
     */
    public Double getEndLongitude() {
        try {
            return Double.parseDouble(longitudeEnd);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 获取终点纬度（Double类型）
     */
    public Double getEndLatitude() {
        try {
            return Double.parseDouble(latitudeEnd);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
