package com.ict.ycwl.common.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.UUID;

public class FileUtils {
    public static String upload(MultipartFile file, String path, String fileName) throws Exception {
        // 生成新的文件名
        String filePath = File.separator + UUID.randomUUID().toString().replace("-", "") + fileName.substring(fileName.lastIndexOf("."));
        String realPath = path + filePath;
        File dest = new File(realPath);
        // 判断文件父目录是否存在
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdir();
        }
        // 保存文件
        file.transferTo(dest);
        return filePath;
    }

    public static boolean deleteFiles(List<String> pathList) {
        for (String path : pathList) {
            boolean status = true;
            File file = new File(path);
            if (file.exists()) {
                status = file.delete();
            }
            if (!status) {
                return status;
            }
        }
        return true;
    }
}


