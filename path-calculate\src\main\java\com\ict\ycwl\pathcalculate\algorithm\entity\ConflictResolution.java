package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 冲突解决记录
 * 
 * 记录凸包处理阶段发现和解决的冲突信息
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConflictResolution {
    
    /**
     * 冲突类型
     */
    private String conflictType;
    
    /**
     * 解决策略
     */
    private String resolutionStrategy;
    
    /**
     * 受影响的路线ID列表
     */
    private List<Long> affectedRouteIds;
    
    /**
     * 是否已解决
     */
    private boolean resolved;
    
    /**
     * 冲突描述
     */
    private String description;
    
    /**
     * 解决成本（时间增加等）
     */
    private double resolutionCost;
    
    public static ConflictResolution create(String conflictType, String strategy, 
                                          List<Long> routeIds, boolean resolved) {
        ConflictResolution resolution = new ConflictResolution();
        resolution.setConflictType(conflictType);
        resolution.setResolutionStrategy(strategy);
        resolution.setAffectedRouteIds(routeIds);
        resolution.setResolved(resolved);
        return resolution;
    }
}