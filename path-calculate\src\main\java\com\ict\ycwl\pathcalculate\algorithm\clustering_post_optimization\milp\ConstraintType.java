package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

/**
 * 约束类型枚举
 * 
 * 定义MILP问题中支持的约束类型
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
public enum ConstraintType {
    LESS_EQUAL("≤", "小于等于"),
    GREATER_EQUAL("≥", "大于等于"),
    EQUAL("=", "等于"),
    RANGE("范围", "范围约束");
    
    private final String symbol;
    private final String description;
    
    ConstraintType(String symbol, String description) {
        this.symbol = symbol;
        this.description = description;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public String getDescription() {
        return description;
    }
}