package com.ict.datamanagement.domain.vo.storeVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("商铺列表")
@Data
public class StoreListVo {
    //店铺id
    @ApiModelProperty(value = "商户id", dataType = "Long")
    private Long storeId;
    //客户编码
    @ApiModelProperty(value = "客户编码", dataType = "String")
    private String customerCode;
    //客户专员名称
    @ApiModelProperty(value = "客户专员名称", dataType = "String")
    private String customerManagerName;
    //负责人
    @ApiModelProperty(value = "负责人", dataType = "String")
    private String head;
    // 订货电话
    @ApiModelProperty(value = "订货电话", dataType = "String")
    private String contactPhone;
    //地址
    @ApiModelProperty(value = "地址", dataType = "String")
    private String storeAddress;
    //店铺所属大区
    @ApiModelProperty(value = "店铺所属大区", dataType = "String")
    private String areaName;
    //路线名称（表中是路线id修改成路线名称）
    @ApiModelProperty(value = "路线名称", dataType = "String")
    private String routeName;
    //所属打卡点
    @ApiModelProperty(value = "所属打卡点", dataType = "String")
    private String accumulationName;
    //商铺状态
    @ApiModelProperty(value = "商铺状态", dataType = "String")
    private String status;
    //商铺状态
    @ApiModelProperty(value = "创建时间", dataType = "String")
    private String createTime;

    //商铺状态
    @ApiModelProperty(value = "是否是特殊点1是0不是", dataType = "String")
    private String isSpecial;

    //商铺状态
    @ApiModelProperty(value = "特殊点标签", dataType = "String")
    private String remark;

    //特殊点类型
    @ApiModelProperty(value = "特殊点类型", dataType = "String")
    private String specialType;
}
