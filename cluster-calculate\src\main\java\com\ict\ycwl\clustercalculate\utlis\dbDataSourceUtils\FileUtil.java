package com.ict.ycwl.clustercalculate.utlis.dbDataSourceUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;

public class FileUtil {

    /**
     * 标准单行读取方法（推荐使用）
     * @param filePath 文件绝对路径
     * @return 文件内容字符串，文件不存在/为空返回null
     * @throws IllegalArgumentException 非法路径或编码格式
     */
    public static String readSingleLine(String filePath) {
        return readSingleLine(filePath, "UTF-8");
    }

    /**
     * 扩展方法：支持自定义编码
     */
    public static String readSingleLine(String filePath, String charsetName) {
        if (filePath == null || filePath.isEmpty()) {
            throw new IllegalArgumentException("File path cannot be null or empty");
        }
        
        try {
            return Files.readAllLines(Paths.get(filePath), Charset.forName(charsetName))
                    .stream()
                    .findFirst()
                    .orElse(null);
        } catch (IOException e) {
            handleFileException(filePath, e);
            return null;
        }
    }

    private static void handleFileException(String path, IOException e) {
        System.err.printf("Error reading file %s: %s%n", path, e.getMessage());
        e.printStackTrace();
    }

    // 进阶用法示例
    public static void main(String[] args) {
        String content = FileUtil.readSingleLine("E:\\www\\wwwroot\\ycwl\\masterDatasource.txt");
        if (content != null) {
            System.out.println("Config loaded: " + content);
        } else {
            System.err.println("Failed to load configuration");
        }
    }
}
