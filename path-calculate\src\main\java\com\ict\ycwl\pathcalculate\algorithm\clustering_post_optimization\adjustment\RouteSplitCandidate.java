package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 路线分割候选
 * 
 * 表示一个可以被分割的路线候选及其相关评估信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class RouteSplitCandidate {
    
    /**
     * 待分割的路线
     */
    private List<Accumulation> route;
    
    /**
     * 路线工作时间（分钟）
     */
    private double workTime;
    
    /**
     * 建议分割数量
     */
    private int splitCount;
    
    /**
     * 分割优先级评分（越高越优先）
     */
    private double priority;
    
    /**
     * 聚集区数量
     */
    private int accumulationCount;
    
    /**
     * 是否违反时间约束
     */
    public boolean isTimeConstraintViolated() {
        return workTime > 450.0;
    }
    
    /**
     * 获取时间约束违反程度
     */
    public double getTimeViolationRatio() {
        if (workTime <= 450.0) {
            return 0.0;
        }
        return (workTime - 450.0) / 450.0;
    }
    
    /**
     * 计算分割后平均工作时间
     */
    public double getExpectedAverageWorkTimeAfterSplit() {
        return workTime / splitCount;
    }
    
    /**
     * 是否适合分割
     */
    public boolean isSuitableForSplitting() {
        return accumulationCount >= splitCount * 2 && // 至少每个子路线有2个聚集区
               workTime > 300.0; // 工作时间足够长
    }
    
    /**
     * 获取优先级等级描述
     */
    public String getPriorityLevel() {
        if (priority >= 3.0) {
            return "高优先级";
        } else if (priority >= 2.0) {
            return "中优先级";
        } else {
            return "低优先级";
        }
    }
    
    /**
     * 生成候选描述
     */
    public String generateDescription() {
        return String.format("路线分割候选 - %d个聚集区, %.1f分钟, 分割为%d个子路线 (%s)",
            accumulationCount, workTime, splitCount, getPriorityLevel());
    }
}