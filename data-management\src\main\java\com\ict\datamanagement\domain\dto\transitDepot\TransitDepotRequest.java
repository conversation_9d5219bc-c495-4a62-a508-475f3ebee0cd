package com.ict.datamanagement.domain.dto.transitDepot;

import com.ict.datamanagement.domain.dto.page.PageRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("搜索信息表单")
@AllArgsConstructor
@NoArgsConstructor
public class TransitDepotRequest extends PageRequest {
    @ApiModelProperty(value = "中转站名称",dataType = "String")
    private String transitDepotName;
    @ApiModelProperty(value = "所属班组",dataType = "String")
    private String teamName;
    @ApiModelProperty(value = "启用状态",dataType = "String")
    private String status;
    @ApiModelProperty(value = "配送类型",dataType = "String")
    private String deliveryType;
    @ApiModelProperty(value = "对接配送域",dataType = "String")
    private String deliveryName;
}
