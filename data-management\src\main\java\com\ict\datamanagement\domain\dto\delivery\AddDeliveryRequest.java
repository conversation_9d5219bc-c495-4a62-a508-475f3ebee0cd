package com.ict.datamanagement.domain.dto.delivery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("添加配送域表单")
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class AddDeliveryRequest {
    //配送域名称
    @ApiModelProperty(value = "配送域名称",dataType = "String")
    @NotNull(message = "配送域名称不能为空")
    private String deliveryName;
    //所属班组
    @ApiModelProperty(value = "所属班组",dataType = "String")
    @NotNull(message = "所属班组不能为空")
    private String teamName;
    //所属行政区
    @ApiModelProperty(value = "所属行政区",dataType = "String")
    @NotNull(message = "所属行政区不能为空")
    private String areaName;
    //对接中转站
    @ApiModelProperty(value = "对接中转站",dataType = "String")
    @NotNull(message = "对接中转站不能为空")
    private String transitDepotName;
    //配送类型
    @ApiModelProperty(value = "配送类型",dataType = "String")
    @NotNull(message = "配送类型不能为空")
    private String deliveryType;
    //路径数
    @ApiModelProperty(value = "路径数",dataType = "int")
    @NotNull(message = "路径数不能为空")
    private int routeNumber;
    //车辆数
    @ApiModelProperty(value = "车辆数",dataType = "int")
    @NotNull(message = "车辆数不能为空")
    private int carNumber;
}
