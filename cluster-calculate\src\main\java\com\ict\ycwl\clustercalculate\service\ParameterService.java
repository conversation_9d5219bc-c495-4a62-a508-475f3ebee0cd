package com.ict.ycwl.clustercalculate.service;

import com.ict.ycwl.clustercalculate.pojo.ParameterListManager;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ParameterService {

    /**
     * 更新发生改变的信息
     *
     * @param managerName 所属区域
     * @param data        发生改变的信息
     */
    void updateList(String managerName, String data);

    /**
     * 获取发生改变的信息的列表
     *
     * @return 返回信息数组
     */
    List<ParameterListManager> getInformationList();

    /**
     * 清空列表里的全部信息
     */
    void clearInformation();

}
