package com.ict.ycwl.clustercalculate.utlis;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class TSPUtils {
    
    // 坐标点类
    public static class Point {
        public final double lon;
        public final double lat;
        public final int id;
        
        public Point(double lon, double lat, int id) {
            this.lon = lon;
            this.lat = lat;
            this.id = id;
        }
    }

    /**
     * 求解旅行商问题的主方法
     * @param coordinates 输入坐标字符串，格式："lon,lat;lon,lat;..."
     * @return 优化后的坐标路径字符串
     */
    public static String solveTSP(String coordinates) {
        List<Point> points = parseCoordinates(coordinates);
        if (points.isEmpty()) return "";
        if (points.size() == 1) return formatPoint(points.get(0));
        
        // 根据问题规模选择算法
        if (points.size() <= 2000) {
            // 中小规模：使用距离矩阵+最近邻+2-opt优化
            double[][] distMatrix = computeDistanceMatrix(points);
            List<Integer> path = nearestNeighbor(points, distMatrix);
            path = twoOptOptimization(points, path, distMatrix);
            return formatPath(points, path);
        } else {
            // 大规模：使用按需距离计算+最近邻
            List<Integer> path = nearestNeighborLarge(points);
            return formatPath(points, path);
        }
    }

    // 解析坐标字符串
    private static List<Point> parseCoordinates(String s) {
        List<Point> points = new ArrayList<>();
        if (s == null || s.trim().isEmpty()) return points;
        
        String[] pairs = s.split(";");
        int id = 0;
        for (String pair : pairs) {
            String[] coords = pair.split(",");
            if (coords.length == 2) {
                try {
                    double lon = Double.parseDouble(coords[0].trim());
                    double lat = Double.parseDouble(coords[1].trim());
                    points.add(new Point(lon, lat, id++));
                } catch (NumberFormatException ignored) {}
            }
        }
        return points;
    }

    // 计算距离矩阵（仅用于中小规模问题）
    private static double[][] computeDistanceMatrix(List<Point> points) {
        int n = points.size();
        double[][] matrix = new double[n][n];
        
        for (int i = 0; i < n; i++) {
            for (int j = i + 1; j < n; j++) {
                double dist = haversineDistance(points.get(i), points.get(j));
                matrix[i][j] = dist;
                matrix[j][i] = dist;
            }
        }
        return matrix;
    }

    // 最近邻算法（使用距离矩阵）
    private static List<Integer> nearestNeighbor(List<Point> points, double[][] distMatrix) {
        int n = points.size();
        boolean[] visited = new boolean[n];
        List<Integer> path = new ArrayList<>(n);
        
        int current = 0;
        path.add(current);
        visited[current] = true;
        
        for (int step = 1; step < n; step++) {
            double minDist = Double.MAX_VALUE;
            int next = -1;
            
            for (int candidate = 0; candidate < n; candidate++) {
                if (!visited[candidate]) {
                    double dist = distMatrix[current][candidate];
                    if (dist < minDist) {
                        minDist = dist;
                        next = candidate;
                    }
                }
            }
            
            if (next != -1) {
                path.add(next);
                visited[next] = true;
                current = next;
            }
        }
        return path;
    }

    // 最近邻算法（大规模问题使用）
    private static List<Integer> nearestNeighborLarge(List<Point> points) {
        int n = points.size();
        boolean[] visited = new boolean[n];
        List<Integer> path = new ArrayList<>(n);
        
        int current = 0;
        path.add(current);
        visited[current] = true;
        
        for (int step = 1; step < n; step++) {
            double minDist = Double.MAX_VALUE;
            int next = -1;
            
            for (int candidate = 0; candidate < n; candidate++) {
                if (!visited[candidate]) {
                    double dist = haversineDistance(points.get(current), points.get(candidate));
                    if (dist < minDist) {
                        minDist = dist;
                        next = candidate;
                    }
                }
            }
            
            if (next != -1) {
                path.add(next);
                visited[next] = true;
                current = next;
            }
        }
        return path;
    }

    // 2-opt局部优化
    private static List<Integer> twoOptOptimization(List<Point> points, List<Integer> path, double[][] distMatrix) {
        int n = path.size();
        boolean improved = true;
        int maxIterations = 100;
        int iteration = 0;
        
        while (improved && iteration++ < maxIterations) {
            improved = false;
            
            for (int i = 0; i < n - 1; i++) {
                for (int j = i + 2; j < n; j++) {
                    // 当前边：i->i+1 和 j->j+1 (j+1取模处理环)
                    int nodeA = path.get(i);
                    int nodeB = path.get(i + 1);
                    int nodeC = path.get(j);
                    int nodeD = path.get((j + 1) % n);
                    
                    double currentCost = distMatrix[nodeA][nodeB] + distMatrix[nodeC][nodeD];
                    double newCost = distMatrix[nodeA][nodeC] + distMatrix[nodeB][nodeD];
                    
                    if (newCost < currentCost) {
                        // 反转路径i+1到j之间的节点
                        reverseSublist(path, i + 1, j);
                        improved = true;
                    }
                }
            }
        }
        return path;
    }

    // 反转子列表
    private static void reverseSublist(List<Integer> list, int start, int end) {
        while (start < end) {
            int temp = list.get(start);
            list.set(start, list.get(end));
            list.set(end, temp);
            start++;
            end--;
        }
    }

    // 哈弗辛公式计算球面距离
    private static double haversineDistance(Point p1, Point p2) {
        final double R = 6371; // 地球半径(km)
        double dLat = Math.toRadians(p2.lat - p1.lat);
        double dLon = Math.toRadians(p2.lon - p1.lon);
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(p1.lat)) * Math.cos(Math.toRadians(p2.lat)) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    // 格式化单个点
    private static String formatPoint(Point p) {
        return String.format("%f,%f", p.lon, p.lat);
    }

    // 格式化路径
    private static String formatPath(List<Point> points, List<Integer> path) {
        return path.stream()
                .map(idx -> formatPoint(points.get(idx)))
                .collect(Collectors.joining(";"));
    }

    // 示例用法
    public static void main(String[] args) {
        // 测试数据：北京、上海、广州、深圳
        String cities = "116.4074,39.9042;" +  // 北京
                        "121.4737,31.2304;" +  // 上海
                        "113.2644,23.1291;" +  // 广州
                        "114.1694,22.3193;";   // 深圳
        
        String optimizedRoute = solveTSP(cities);
        System.out.println("优化路线: ");
        Arrays.stream(optimizedRoute.split(";")).forEach(System.out::println);
    }
}