package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.ArrayList;

/**
 * 路线结果数据结构
 * 表示算法输出的单条路线规划结果
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RouteResult {
    
    /**
     * 路线ID
     */
    private Long routeId;
    
    /**
     * 路线名称
     */
    private String routeName;
    
    /**
     * 所属中转站ID
     */
    private Long transitDepotId;
    
    /**
     * 路线包含的聚集区ID列表（按访问顺序）
     */
    private List<Long> accumulationSequence;
    
    /**
     * 路线坐标串（用于地图绘制）
     */
    private List<CoordinatePoint> polyline;
    
    /**
     * 路线总工作时长（分钟）
     */
    private Double totalWorkTime;
    
    /**
     * 路线凸包坐标点串（用于区域显示和重叠检测）
     */
    private List<CoordinatePoint> convexHull;
    
    /**
     * 检查路线结果是否有效
     * 
     * @return 结果是否有效
     */
    public boolean isValid() {
        return routeId != null &&
               routeName != null && !routeName.trim().isEmpty() &&
               transitDepotId != null &&
               accumulationSequence != null && !accumulationSequence.isEmpty() &&
               polyline != null && !polyline.isEmpty() &&
               totalWorkTime != null && totalWorkTime >= 0 &&
               convexHull != null; // 放宽凸包验证，允许小于3个点的情况
    }
    
    /**
     * 获取路线包含的聚集区数量
     * 
     * @return 聚集区数量
     */
    public int getAccumulationCount() {
        return accumulationSequence != null ? accumulationSequence.size() : 0;
    }
    
    /**
     * 获取路线的唯一标识字符串
     * 
     * @return 唯一标识字符串
     */
    public String getUniqueKey() {
        return String.format("route_%d", routeId);
    }
    
    /**
     * 检查是否包含指定的聚集区
     * 
     * @param accumulationId 聚集区ID
     * @return 是否包含
     */
    public boolean containsAccumulation(Long accumulationId) {
        return accumulationSequence != null && accumulationSequence.contains(accumulationId);
    }
    
    /**
     * 获取指定聚集区在路线中的访问顺序
     * 
     * @param accumulationId 聚集区ID
     * @return 访问顺序（从0开始），如果不存在则返回-1
     */
    public int getAccumulationOrder(Long accumulationId) {
        if (accumulationSequence == null) {
            return -1;
        }
        return accumulationSequence.indexOf(accumulationId);
    }
    
    /**
     * 计算路线的平均配送时间
     * 
     * @return 平均配送时间（分钟）
     */
    public double getAverageDeliveryTime() {
        int count = getAccumulationCount();
        if (count == 0) {
            return 0.0;
        }
        return totalWorkTime / count;
    }
    
    /**
     * 获取凸包的面积（平方度）
     * 使用鞋带公式计算多边形面积
     * 
     * @return 凸包面积，如果凸包无效则返回0
     */
    public double getConvexHullArea() {
        if (convexHull == null || convexHull.size() < 3) {
            return 0.0;
        }
        
        // 使用鞋带公式计算多边形面积
        double area = 0.0;
        int n = convexHull.size();
        
        for (int i = 0; i < n; i++) {
            CoordinatePoint current = convexHull.get(i);
            CoordinatePoint next = convexHull.get((i + 1) % n);
            
            if (current.isValid() && next.isValid()) {
                area += (current.getLongitude() * next.getLatitude() - 
                        next.getLongitude() * current.getLatitude());
            }
        }
        
        return Math.abs(area) / 2.0;
    }
    
    /**
     * 检查与另一条路线的凸包是否重叠
     * 
     * @param other 另一条路线
     * @return 是否重叠
     */
    public boolean isConvexHullOverlapping(RouteResult other) {
        if (other == null || 
            this.convexHull == null || this.convexHull.size() < 3 ||
            other.convexHull == null || other.convexHull.size() < 3) {
            return false;
        }
        // HACK: 当前为简化实现，调用ConvexHullGenerator工具类，后续可替换为更精确的几何算法
        return com.ict.ycwl.pathcalculate.algorithm.utils.ConvexHullGenerator.isConvexHullsOverlapping(this.convexHull, other.convexHull);
    }
    
    /**
     * 计算路线的工作效率（聚集区数量/工作时长）
     * 
     * @return 工作效率
     */
    public double getWorkEfficiency() {
        if (totalWorkTime == null || totalWorkTime <= 0) {
            return 0.0;
        }
        return getAccumulationCount() / totalWorkTime;
    }
    
    /**
     * 复制路线结果对象
     * 
     * @return 复制的路线结果对象
     */
    public RouteResult copy() {
        List<Long> copiedSequence = null;
        if (accumulationSequence != null) {
            copiedSequence = new ArrayList<>(accumulationSequence);
        }
        
        List<CoordinatePoint> copiedPolyline = null;
        if (polyline != null) {
            copiedPolyline = new ArrayList<>(polyline);
        }
        
        List<CoordinatePoint> copiedConvexHull = null;
        if (convexHull != null) {
            copiedConvexHull = new ArrayList<>(convexHull);
        }
        
        return RouteResult.builder()
                .routeId(this.routeId)
                .routeName(this.routeName)
                .transitDepotId(this.transitDepotId)
                .accumulationSequence(copiedSequence)
                .polyline(copiedPolyline)
                .totalWorkTime(this.totalWorkTime)
                .convexHull(copiedConvexHull)
                .build();
    }
} 