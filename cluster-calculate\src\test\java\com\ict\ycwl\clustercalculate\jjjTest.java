package com.ict.ycwl.clustercalculate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ict.ycwl.clustercalculate.mapper.AccumulationMapper;
import com.ict.ycwl.clustercalculate.mapper.ErrorPointMapper;
import com.ict.ycwl.clustercalculate.mapper.RouteMapper;
import com.ict.ycwl.clustercalculate.pojo.Accumulation;
import com.ict.ycwl.clustercalculate.pojo.ErrorPoint;
import com.ict.ycwl.clustercalculate.pojo.LngAndLat;
import com.ict.ycwl.clustercalculate.utlis.ConvexHull;
import com.ict.ycwl.clustercalculate.utlis.CoordinateUtils;
import com.ict.ycwl.clustercalculate.utlis.TSPUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.HashSet;
import java.util.List;

@SpringBootTest
public class jjjTest {

    @Autowired
    private ErrorPointMapper errorPointMapper;

    @Autowired
    private AccumulationMapper accumulationMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RouteMapper routeMapper;

    @Test
    void test() {
        //1.查询出所有的特殊点
        List<ErrorPoint> errorPoints = errorPointMapper.selectList(null);
        System.out.println(errorPoints);
        //2.依次处理当前错误点
        for (ErrorPoint errorPoint : errorPoints) {
            //3.获取当前错误点所在的中转站
            //包含错误点的聚集区的坐标
            double currentStoreLongitude = errorPoint.getCurrentStoreLongitude();
            double currentStoreLatitude = errorPoint.getCurrentStoreLatitude();
            String dakadian = currentStoreLongitude + "," + currentStoreLatitude;

            Accumulation accumulation = accumulationMapper.selectList(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getLongitude, currentStoreLongitude).eq(Accumulation::getLatitude, currentStoreLatitude).eq(Accumulation::getIsDelete, 0)).get(0);
            //查找当前特殊点
            Long routeId = accumulation.getRouteId();
            String polyline = jdbcTemplate.queryForObject("select polyline from route where route_id=? and is_delete=0", String.class, routeId);

            //对路线中的打卡点坐标进行去重，并将中转站坐标去除
            HashSet<String> hashSet = new HashSet<>();

            String[] split = polyline.split(";");
            for (String s : split) {
                if (!dakadian.equals(s)) {
                    hashSet.add(s);
                }
            }

            //查询该打卡点所属的中转站
            Long transitDepotId = accumulation.getTransitDepotId();

            //查找出这个中转站下所有的聚集区
            List<Accumulation> accumulations = accumulationMapper.selectList(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getTransitDepotId, transitDepotId).eq(Accumulation::getIsDelete, 0));
            Accumulation accumulationMubiao = null;
            double minDist = Double.MAX_VALUE;

            //找出一个距离最近的打卡点，并且这个打卡点不能是特殊点;
            for (Accumulation accumulation1 : accumulations) {
                Long aLong = errorPointMapper.selectCount(new LambdaQueryWrapper<ErrorPoint>().eq(ErrorPoint::getCurrentStoreLongitude, accumulation1.getLongitude()).eq(ErrorPoint::getCurrentStoreLatitude, accumulation1.getLatitude()));
                Long aLong1 = errorPointMapper.selectCount(new LambdaQueryWrapper<ErrorPoint>().eq(ErrorPoint::getPairingStoreLongitude, accumulation1.getLongitude()).eq(ErrorPoint::getPairingStoreLatitude, accumulation1.getLatitude()));
                if (aLong == 0 && aLong1 == 0) {
                    if (!hashSet.contains(accumulation1.getLongitude() + "," + accumulation1.getLatitude())) {
                        double v = calculateEuclideanDistance(new double[]{currentStoreLongitude, currentStoreLatitude}, new double[]{accumulation1.getLongitude(), accumulation1.getLatitude()});
                        if (v < minDist) {
                            minDist = v;
                            accumulationMubiao = accumulation1;
                        }
                    }
                }
            }
            //找到了其他路径中最近的一个打卡点，并且这个打开点不是特殊点
            //将这个特殊点加入到距离最尽的打卡点的所属路径上
            Long routeId1 = accumulationMubiao.getRouteId();
            String polyline1 = jdbcTemplate.queryForObject("select polyline from route where route_id=? and is_delete=0", String.class, routeId1);
            //去除中路径中的第一个中转站坐标，
            String[] split1 = polyline1.split(";");
            String transitPoint=split1[0];
            StringBuilder sb = new StringBuilder();
            sb.append(dakadian+";");
            for (int i = 1; i < split1.length; i++) {
                sb.append(split1[i]+";");
            }
            //通过tsp计算最佳配送路径
            String theBestPath = TSPUtils.solveTSP(sb.toString());
            System.out.println(theBestPath);
            //重新排序坐标

            //最终添加坐标之后的路径，首位是中转站
            String path = CoordinateUtils.rebuildCoordinateString(theBestPath, transitPoint);

            //去掉中转站坐标，剩下的进行凸包计算
            String[] split2 = path.split(";");
            LngAndLat[] lngAndLats = new LngAndLat[split2.length - 2];
            int index=0;
            for (int i = 1; i < split2.length-1; i++) {
                lngAndLats[index]=new LngAndLat(Double.parseDouble(split2[i].split(",")[0]),Double.parseDouble(split2[i].split(",")[1]));
                index++;
            }
            //计算新的凸包
            List<LngAndLat> lngAndLats1 = ConvexHull.convexHull(lngAndLats);
            //凸包
            StringBuilder convex = new StringBuilder();

            for (int i = 0; i < lngAndLats1.size(); i++) {
                LngAndLat lngAndLat = lngAndLats1.get(i);
                if(i!=lngAndLats1.size()-1){
                    convex.append(lngAndLat.getLongitude()).append(",").append(lngAndLat.getLatitude()).append(";");
                }else{
                    convex.append(lngAndLat.getLongitude()).append(",").append(lngAndLat.getLatitude());
                }
            }
            System.out.println(convex);

            //计算这个

            //6.减去这条路径的工作时长


            //7.重新计算加上这个特殊点的路径的打卡点
        }
    }

    /**
     * 两点之间欧氏距离的计算方法
     */
    public double calculateEuclideanDistance(double[] point1, double[] point2) {
        double sum = 0.0;
        for (int i = 0; i < point1.length; i++) {
            sum += Math.pow(point1[i] - point2[i], 2);
        }
        return Math.sqrt(sum);
    }

}
