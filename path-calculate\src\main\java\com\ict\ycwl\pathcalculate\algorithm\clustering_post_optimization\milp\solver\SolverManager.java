package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPSolution;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MILP求解器管理器
 * 
 * 负责求解器的注册、选择和管理
 * 提供自动求解器选择和降级机制
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Primary
@Slf4j
@Component("milpSolverManager")
public class SolverManager {
    
    private final List<MILPSolver> registeredSolvers;
    private final SolverSelectionStrategy selectionStrategy;
    
    public SolverManager() {
        this.registeredSolvers = new ArrayList<>();
        this.selectionStrategy = new DefaultSolverSelectionStrategy();
        
        // 注册内置求解器
        registerBuiltinSolvers();
    }
    
    public SolverManager(SolverSelectionStrategy selectionStrategy) {
        this.registeredSolvers = new ArrayList<>();
        this.selectionStrategy = selectionStrategy;
        
        // 注册内置求解器
        registerBuiltinSolvers();
    }
    
    /**
     * 注册内置求解器
     */
    private void registerBuiltinSolvers() {
        // 按优先级注册求解器
        registerSolver(new ApacheCommonsMathSolver());
        registerSolver(new BuiltinHeuristicSolver());
        
        log.info("🔧 已注册 {} 个内置求解器", registeredSolvers.size());
    }
    
    /**
     * 注册求解器
     */
    public void registerSolver(MILPSolver solver) {
        if (solver == null) {
            throw new IllegalArgumentException("求解器不能为空");
        }
        
        registeredSolvers.add(solver);
        log.debug("✅ 注册求解器: {} v{}", solver.getSolverName(), solver.getSolverVersion());
    }
    
    /**
     * 移除求解器
     */
    public boolean removeSolver(String solverName) {
        boolean removed = registeredSolvers.removeIf(solver -> 
            solver.getSolverName().equals(solverName));
        
        if (removed) {
            log.debug("❌ 移除求解器: {}", solverName);
        }
        
        return removed;
    }
    
    /**
     * 获取所有可用的求解器
     */
    public List<MILPSolver> getAvailableSolvers() {
        return registeredSolvers.stream()
            .filter(MILPSolver::isAvailable)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取支持指定问题的求解器
     */
    public List<MILPSolver> getSupportedSolvers(MILPProblem problem) {
        return registeredSolvers.stream()
            .filter(MILPSolver::isAvailable)
            .filter(solver -> solver.supports(problem))
            .collect(Collectors.toList());
    }
    
    /**
     * 自动选择最佳求解器
     */
    public MILPSolver selectBestSolver(MILPProblem problem) {
        return selectBestSolver(problem, SolverParameters.createDefault());
    }
    
    /**
     * 自动选择最佳求解器
     */
    public MILPSolver selectBestSolver(MILPProblem problem, SolverParameters parameters) {
        List<MILPSolver> candidateSolvers = getSupportedSolvers(problem);
        
        if (candidateSolvers.isEmpty()) {
            log.warn("⚠️ 没有找到支持该问题的求解器");
            return null;
        }
        
        MILPSolver bestSolver = selectionStrategy.selectSolver(candidateSolvers, problem, parameters);
        
        if (bestSolver != null) {
            log.info("🎯 选择求解器: {} (来自{}个候选)", 
                bestSolver.getSolverName(), candidateSolvers.size());
        }
        
        return bestSolver;
    }
    
    /**
     * 求解MILP问题（自动选择求解器）
     */
    public MILPSolution solve(MILPProblem problem) {
        return solve(problem, SolverParameters.createDefault());
    }
    
    /**
     * 求解MILP问题（自动选择求解器）
     */
    public MILPSolution solve(MILPProblem problem, SolverParameters parameters) {
        log.info("🚀 开始求解MILP问题: {}", problem.getProblemId());
        
        List<MILPSolver> candidateSolvers = getSupportedSolvers(problem);
        
        if (candidateSolvers.isEmpty()) {
            log.error("❌ 没有找到支持该问题的求解器");
            return MILPSolution.builder()
                .solutionStatus(MILPProblem.SolutionStatus.ERROR)
                .statusMessage("没有可用的求解器")
                .solvingTimeMs(0L)
                .build();
        }
        
        // 按优先级排序候选求解器
        List<MILPSolver> sortedSolvers = selectionStrategy.rankSolvers(candidateSolvers, problem, parameters);
        
        // 依次尝试求解器（降级机制）
        for (int i = 0; i < sortedSolvers.size(); i++) {
            MILPSolver solver = sortedSolvers.get(i);
            
            try {
                log.info("🔧 尝试使用求解器: {} ({}/{})", 
                    solver.getSolverName(), i + 1, sortedSolvers.size());
                
                // 设置参数
                solver.setParameters(parameters);
                
                // 求解
                MILPSolution solution = solver.solve(problem);
                
                // 检查求解结果
                if (solution != null && 
                    solution.getSolutionStatus() != MILPProblem.SolutionStatus.ERROR) {
                    
                    log.info("✅ 求解成功: {} (状态: {})", 
                        solver.getSolverName(), solution.getSolutionStatus());
                    return solution;
                }
                
                log.warn("⚠️ 求解器 {} 未能找到解: {}", 
                    solver.getSolverName(), 
                    solution != null ? solution.getStatusMessage() : "空结果");
                
            } catch (Exception e) {
                log.error("❌ 求解器 {} 执行失败", solver.getSolverName(), e);
            }
        }
        
        // 所有求解器都失败
        log.error("❌ 所有求解器都无法求解该问题");
        return MILPSolution.builder()
            .solutionStatus(MILPProblem.SolutionStatus.ERROR)
            .statusMessage("所有求解器都无法求解该问题")
            .solvingTimeMs(0L)
            .build();
    }
    
    /**
     * 获取求解器统计信息
     */
    public SolverStatistics getStatistics() {
        int totalSolvers = registeredSolvers.size();
        int availableSolvers = (int) registeredSolvers.stream()
            .filter(MILPSolver::isAvailable)
            .count();
        
        Map<String, Integer> solversByTier = registeredSolvers.stream()
            .collect(Collectors.groupingBy(
                solver -> solver.getCapabilities().getTier().getName(),
                Collectors.collectingAndThen(Collectors.toList(), List::size)
            ));
        
        Map<String, Integer> solversByCapability = new HashMap<>();
        for (MILPSolver solver : registeredSolvers) {
            SolverCapabilities cap = solver.getCapabilities();
            if (cap.isSupportsIntegerProgramming()) {
                solversByCapability.merge("整数规划", 1, Integer::sum);
            }
            if (cap.isSupportsQuadraticProgramming()) {
                solversByCapability.merge("二次规划", 1, Integer::sum);
            }
            if (cap.isSupportsParallel()) {
                solversByCapability.merge("并行求解", 1, Integer::sum);
            }
        }
        
        return SolverStatistics.builder()
            .totalSolvers(totalSolvers)
            .availableSolvers(availableSolvers)
            .solversByTier(solversByTier)
            .solversByCapability(solversByCapability)
            .build();
    }
    
    /**
     * 生成求解器状态报告
     */
    public String generateStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== MILP求解器状态报告 ===\n");
        
        SolverStatistics stats = getStatistics();
        report.append(String.format("注册求解器总数: %d\n", stats.getTotalSolvers()));
        report.append(String.format("可用求解器数量: %d\n", stats.getAvailableSolvers()));
        report.append("\n");
        
        report.append("求解器详情:\n");
        for (MILPSolver solver : registeredSolvers) {
            report.append(String.format("  • %s v%s - %s\n", 
                solver.getSolverName(), 
                solver.getSolverVersion(),
                solver.isAvailable() ? "可用" : "不可用"));
            
            SolverCapabilities cap = solver.getCapabilities();
            report.append(String.format("    等级: %s, 评分: %d/100\n", 
                cap.getTier().getName(), cap.getCapabilityScore()));
        }
        
        report.append("\n功能统计:\n");
        stats.getSolversByCapability().forEach((capability, count) -> {
            report.append(String.format("  • %s: %d个求解器\n", capability, count));
        });
        
        return report.toString();
    }
    
    /**
     * 求解器统计信息
     */
    @lombok.Data
    @lombok.Builder
    public static class SolverStatistics {
        private int totalSolvers;
        private int availableSolvers;
        private Map<String, Integer> solversByTier;
        private Map<String, Integer> solversByCapability;
    }
}