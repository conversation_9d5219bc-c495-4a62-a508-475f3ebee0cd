package com.ict.datamanagement.constant;

import cn.hutool.core.util.StrUtil;

/**
 * 下拉框数据常量类
 */
public class OptionalDataType {

    public static final String AREA_TYPE = "area";

    public static final String GROUP_TYPE = "group";

    public static final String ROUTE_TYPE = "route";

    private OptionalDataType() {
    }

    public static Boolean equalsType(String dateType){
        return StrUtil.equalsAny(dateType, new String[]{AREA_TYPE, GROUP_TYPE, ROUTE_TYPE});
    }
}

