package com.ict.ycwl.pathcalculate.algorithm.data;

import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult;
import com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtils;

import java.io.IOException;

/**
 * 数据加载器测试类
 * 演示如何使用DataLoader加载测试数据并运行算法
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class DataLoaderTest {
    
    public static void main(String[] args) {
        testDataLoading();
        testAlgorithmWithTestData();
    }
    
    /**
     * 测试数据加载功能
     */
    public static void testDataLoading() {
        System.out.println("=== 测试数据加载功能 ===");
        
        try {
            // 1. 加载默认版本的测试数据
            String version = "v1.0";
            System.out.println("加载测试数据版本: " + version);
            
            PathPlanningRequest request = DataLoader.loadTestData(version);
            
            // 2. 验证数据完整性
            DataLoader.DataValidationResult validation = DataLoader.validateData(request);
            System.out.println("数据验证结果: " + validation);
            
            if (!validation.isValid()) {
                System.err.println("数据验证失败:");
                validation.getErrors().forEach(error -> System.err.println("  - " + error));
                return;
            }
            
            // 3. 显示数据统计
            printDataStatistics(request);
            
            System.out.println("数据加载测试完成!\n");
            
        } catch (IOException e) {
            System.err.println("数据加载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试使用测试数据运行算法
     */
    public static void testAlgorithmWithTestData() {
        System.out.println("=== 测试算法运行 ===");
        
        try {
            // 1. 加载测试数据
            PathPlanningRequest request = DataLoader.loadTestData("v1.0");
            
            // 2. 验证数据
            DataLoader.DataValidationResult validation = DataLoader.validateData(request);
            if (!validation.isValid()) {
                System.err.println("测试数据无效，跳过算法测试");
                return;
            }
            
            // 3. 运行算法
            System.out.println("开始运行路径规划算法...");
            long startTime = System.currentTimeMillis();
            
            try {
                PathPlanningResult result = PathPlanningUtils.calculate(request);
                
                long endTime = System.currentTimeMillis();
                System.out.println("算法执行完成，耗时: " + (endTime - startTime) + "ms");
                
                // 4. 显示结果
                if (result.isSuccess()) {
                    System.out.println("算法执行成功!");
                    System.out.println(result.getPerformanceSummary());
                    System.out.println(result.getBalanceSummary());
                } else {
                    System.err.println("算法执行失败: " + result.getErrorMessage());
                }
                
            } catch (UnsupportedOperationException e) {
                System.out.println("算法尚未实现，这是正常的（显示TODO消息）");
                System.out.println("错误信息: " + e.getMessage());
            }
            
        } catch (IOException e) {
            System.err.println("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示数据统计信息
     * 
     * @param request 路径规划请求
     */
    private static void printDataStatistics(PathPlanningRequest request) {
        System.out.println("\n数据统计:");
        System.out.println("  聚集区数量: " + 
                          (request.getAccumulations() != null ? request.getAccumulations().size() : 0));
        System.out.println("  中转站数量: " + 
                          (request.getTransitDepots() != null ? request.getTransitDepots().size() : 0));
        System.out.println("  班组数量: " + 
                          (request.getTeams() != null ? request.getTeams().size() : 0));
        System.out.println("  时间矩阵条目: " + 
                          (request.getTimeMatrix() != null ? request.getTimeMatrix().size() : 0));
        
        // 显示聚集区分布
        if (request.getAccumulations() != null && request.getTransitDepots() != null) {
            System.out.println("\n聚集区分布:");
            request.getTransitDepots().forEach(depot -> {
                long count = request.getAccumulations().stream()
                        .filter(acc -> acc.getTransitDepotId().equals(depot.getTransitDepotId()))
                        .count();
                System.out.println("  " + depot.getTransitDepotName() + ": " + count + " 个聚集区");
            });
        }
        
        // 显示班组分布
        if (request.getTeams() != null) {
            System.out.println("\n班组分布:");
            request.getTeams().forEach(team -> {
                System.out.println("  " + team.getTeamName() + ": " + 
                                 team.getTransitDepotIds().size() + " 个中转站");
            });
        }
    }
    
    /**
     * 测试单独组件的数据加载
     */
    public static void testComponentLoading() {
        System.out.println("=== 测试单独组件加载 ===");
        
        try {
            String version = "v1.0";
            
            // 测试聚集区加载
            System.out.println("测试聚集区加载...");
            java.util.List<com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation> accumulations = DataLoader.loadAccumulations(version);
            System.out.println("加载聚集区: " + accumulations.size() + " 条");
            
            // 测试中转站加载
            System.out.println("测试中转站加载...");
            java.util.List<com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot> transitDepots = DataLoader.loadTransitDepots(version);
            System.out.println("加载中转站: " + transitDepots.size() + " 条");
            
            // 测试班组加载
            System.out.println("测试班组加载...");
            java.util.List<com.ict.ycwl.pathcalculate.algorithm.entity.Team> teams = DataLoader.loadTeams(version);
            System.out.println("加载班组: " + teams.size() + " 条");
            
            // 测试时间矩阵加载
            System.out.println("测试时间矩阵加载...");
            java.util.Map<String, com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo> timeMatrix = DataLoader.loadTimeMatrix(version);
            System.out.println("加载时间矩阵: " + timeMatrix.size() + " 条");
            
            System.out.println("单独组件加载测试完成!\n");
            
        } catch (IOException e) {
            System.err.println("组件加载测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试版本管理功能
     */
    public static void testVersionManagement() {
        System.out.println("=== 测试版本管理 ===");
        
        // 获取可用版本
        java.util.List<String> versions = DataLoader.getAvailableVersions();
        System.out.println("可用版本: " + versions);
        
        // 测试不同版本的加载
        for (String version : versions) {
            try {
                System.out.println("测试版本: " + version);
                PathPlanningRequest request = DataLoader.loadTestData(version);
                System.out.println("  - 加载成功，聚集区数量: " + request.getAccumulations().size());
            } catch (IOException e) {
                System.err.println("  - 加载失败: " + e.getMessage());
            }
        }
        
        System.out.println("版本管理测试完成!\n");
    }
} 