package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.file.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * 手动OR-Tools库加载器
 * 绕过OR-Tools默认的损坏解压机制，手动正确提取和加载DLL文件
 */
@Slf4j
public class ManualORToolsLoader {
    
    private static boolean libraryLoaded = false;
    private static String loadedLibraryPath = null;
    private static Exception lastError = null;
    
    /**
     * 手动加载OR-Tools原生库
     * @return 是否成功加载
     */
    public static synchronized boolean loadORToolsLibrary() {
        if (libraryLoaded) {
            log.info("✅ [手动加载] OR-Tools库已经加载: {}", loadedLibraryPath);
            return true;
        }
        
        log.info("🚀 [手动加载] 开始手动加载OR-Tools原生库");
        
        try {
            // 1. 检查系统架构
            String osArch = System.getProperty("os.arch");
            String osName = System.getProperty("os.name").toLowerCase();
            
            if (!osName.contains("windows") || (!osArch.equals("amd64") && !osArch.equals("x86_64"))) {
                log.error("❌ [系统不支持] 仅支持Windows x64系统，当前: {} {}", osName, osArch);
                return false;
            }
            
            // 2. 查找并解压DLL文件
            String dllPath = extractJNILibrary();
            if (dllPath == null) {
                log.error("❌ [解压失败] 无法解压jniortools.dll");
                return false;
            }
            
            // 3. 手动加载DLL
            log.info("📥 [加载DLL] 手动加载DLL文件: {}", dllPath);
            System.load(dllPath);
            
            // 4. 验证加载成功
            if (verifyLibraryLoaded()) {
                libraryLoaded = true;
                loadedLibraryPath = dllPath;
                log.info("🎉 [加载成功] OR-Tools原生库手动加载成功！");
                return true;
            } else {
                log.error("❌ [验证失败] DLL加载成功但验证失败");
                return false;
            }
            
        } catch (UnsatisfiedLinkError e) {
            lastError = new Exception(e);
            log.error("❌ [加载错误] UnsatisfiedLinkError: {}", e.getMessage());
            log.error("🔍 [可能原因] 1) 缺少依赖库 2) DLL损坏 3) 架构不匹配");
            return false;
        } catch (Exception e) {
            lastError = e;
            log.error("❌ [加载失败] 手动加载OR-Tools库失败: {} - {}", 
                     e.getClass().getSimpleName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 从JAR中提取JNI库文件
     */
    private static String extractJNILibrary() {
        try {
            log.info("📦 [提取DLL] 从JAR中提取jniortools.dll");
            
            // 查找包含Windows x64原生库的JAR文件
            String jarPath = findORToolsWin64Jar();
            if (jarPath == null) {
                log.error("❌ [JAR未找到] 无法找到ortools-win32-x86-64.jar");
                return null;
            }
            
            log.info("🎯 [找到JAR] {}", jarPath);
            
            // 从JAR中提取DLL
            String dllPath = extractDllFromJar(jarPath);
            if (dllPath != null) {
                log.info("✅ [提取成功] DLL文件提取到: {}", dllPath);
            }
            
            return dllPath;
            
        } catch (Exception e) {
            log.error("❌ [提取失败] 提取DLL文件失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 查找OR-Tools Windows x64 JAR文件
     */
    private static String findORToolsWin64Jar() {
        try {
            // 1. 首先检查classpath
            String classpath = System.getProperty("java.class.path");
            String[] paths = classpath.split(File.pathSeparator);
            
            for (String path : paths) {
                if (path.contains("ortools-win32-x86-64") && path.endsWith(".jar")) {
                    log.info("📍 [Classpath] 在classpath中找到: {}", path);
                    return path;
                }
            }
            
            // 2. 检查Maven仓库
            String userHome = System.getProperty("user.home");
            Path m2Repo = Paths.get(userHome, ".m2", "repository", "com", "google", "ortools", "ortools-win32-x86-64");
            
            if (Files.exists(m2Repo)) {
                try {
                    // 查找最新版本的JAR
                    Path jarPath = Files.walk(m2Repo, 2)
                            .filter(path -> path.toString().endsWith(".jar"))
                            .filter(path -> !path.toString().contains("sources"))
                            .filter(path -> !path.toString().contains("javadoc"))
                            .sorted((p1, p2) -> p2.toString().compareTo(p1.toString())) // 降序，最新版本在前
                            .findFirst()
                            .orElse(null);
                    
                    if (jarPath != null) {
                        log.info("📍 [Maven仓库] 在Maven仓库中找到: {}", jarPath);
                        return jarPath.toString();
                    }
                } catch (IOException e) {
                    log.warn("⚠️ [搜索警告] 搜索Maven仓库失败: {}", e.getMessage());
                }
            }
            
            log.warn("⚠️ [未找到] 无法找到ortools-win32-x86-64.jar文件");
            return null;
            
        } catch (Exception e) {
            log.error("❌ [搜索失败] 搜索JAR文件失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从JAR文件中提取DLL
     */
    private static String extractDllFromJar(String jarPath) {
        try (JarFile jarFile = new JarFile(jarPath)) {
            
            // 查找jniortools.dll文件
            JarEntry dllEntry = jarFile.getJarEntry("ortools-win32-x86-64/jniortools.dll");
            if (dllEntry == null) {
                log.error("❌ [DLL不存在] JAR中不包含jniortools.dll");
                return null;
            }
            
            log.info("📄 [DLL信息] jniortools.dll大小: {} 字节", dllEntry.getSize());
            
            if (dllEntry.getSize() <= 0) {
                log.error("❌ [DLL为空] JAR中的jniortools.dll文件为空");
                return null;
            }
            
            // 创建自定义临时目录
            String tempDir = System.getProperty("java.io.tmpdir");
            Path customTempDir = Paths.get(tempDir, "manual-ortools-" + System.currentTimeMillis());
            Files.createDirectories(customTempDir);
            
            log.info("📁 [临时目录] 创建临时目录: {}", customTempDir);
            
            // 提取DLL文件
            Path dllPath = customTempDir.resolve("jniortools.dll");
            
            try (InputStream is = jarFile.getInputStream(dllEntry);
                 OutputStream os = Files.newOutputStream(dllPath)) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytesRead = 0;
                
                while ((bytesRead = is.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }
                
                os.flush();
                
                log.info("📥 [写入完成] 已写入 {} 字节到 {}", totalBytesRead, dllPath);
                
                // 验证文件大小
                long actualSize = Files.size(dllPath);
                if (actualSize != dllEntry.getSize()) {
                    log.error("❌ [大小不匹配] 期望: {} 字节, 实际: {} 字节", dllEntry.getSize(), actualSize);
                    return null;
                }
                
                // 设置文件为可执行
                dllPath.toFile().setExecutable(true, false);
                
                log.info("✅ [提取成功] DLL文件提取完成，大小正确: {} 字节", actualSize);
                
                return dllPath.toString();
            }
            
        } catch (IOException e) {
            log.error("❌ [IO错误] 提取DLL文件时发生IO错误: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证库是否正确加载
     */
    private static boolean verifyLibraryLoaded() {
        try {
            log.info("🧪 [验证加载] 验证OR-Tools原生库是否正确加载");
            
            // 尝试创建一个简单的RoutingIndexManager来验证
            Class<?> managerClass = Class.forName("com.google.ortools.constraintsolver.RoutingIndexManager");
            java.lang.reflect.Constructor<?> constructor = managerClass.getConstructor(int.class, int.class, int.class);
            
            Object manager = constructor.newInstance(2, 1, 0);
            
            log.info("✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载");
            return true;
            
        } catch (Exception e) {
            log.error("❌ [验证失败] 验证原生库加载失败: {} - {}", 
                     e.getClass().getSimpleName(), e.getMessage());
            
            // 输出详细的错误信息
            Throwable cause = e;
            while (cause.getCause() != null) {
                cause = cause.getCause();
            }
            log.error("🔍 [根本原因] {}: {}", cause.getClass().getSimpleName(), cause.getMessage());
            
            return false;
        }
    }
    
    /**
     * 检查库是否已加载
     */
    public static boolean isLibraryLoaded() {
        return libraryLoaded;
    }
    
    /**
     * 获取加载的库路径
     */
    public static String getLoadedLibraryPath() {
        return loadedLibraryPath;
    }
    
    /**
     * 获取最后的错误
     */
    public static Exception getLastError() {
        return lastError;
    }
    
    /**
     * 重置加载状态（用于测试）
     */
    public static synchronized void resetForTesting() {
        // 注意：一旦JNI库被加载到JVM中，就无法卸载
        // 这个方法只重置我们的状态标记，不能真正卸载库
        log.warn("⚠️ [重置状态] 重置加载状态标记（注意：JNI库无法从JVM中卸载）");
        // libraryLoaded = false;  // 注释掉，因为库实际上仍然被加载
        // loadedLibraryPath = null;
        lastError = null;
    }
}