package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 数据验证器
 * 验证输入数据的完整性和一致性
 */
@Slf4j
@Component
public class DataValidator {
    
    /**
     * 验证路径规划请求数据
     * @param request 请求数据
     * @throws IllegalArgumentException 数据无效时抛出异常
     */
    public void validate(PathPlanningRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("PathPlanningRequest cannot be null");
        }
        
        log.info("开始数据验证");
        
        // 基础数据验证
        validateBasicData(request);
        
        // ID引用完整性验证
        validateIdReferences(request);
        
        // 时间矩阵验证
        validateTimeMatrix(request);
        
        // 业务逻辑验证
        validateBusinessLogic(request);
        
        log.info("数据验证通过");
    }
    
    /**
     * 验证基础数据
     */
    private void validateBasicData(PathPlanningRequest request) {
        // 验证聚集区数据
        if (request.getAccumulations() == null || request.getAccumulations().isEmpty()) {
            throw new IllegalArgumentException("聚集区数据不能为空");
        }
        
        for (Accumulation acc : request.getAccumulations()) {
            if (acc.getAccumulationId() == null) {
                throw new IllegalArgumentException("聚集区ID不能为空");
            }
            if (acc.getLongitude() == null || acc.getLatitude() == null) {
                throw new IllegalArgumentException("聚集区坐标不能为空");
            }
            if (acc.getDeliveryTime() == null || acc.getDeliveryTime() <= 0) {
                throw new IllegalArgumentException("聚集区配送时间必须大于0");
            }
            if (acc.getTransitDepotId() == null) {
                throw new IllegalArgumentException("聚集区必须关联中转站");
            }
        }
        
        // 验证中转站数据
        if (request.getTransitDepots() == null || request.getTransitDepots().isEmpty()) {
            throw new IllegalArgumentException("中转站数据不能为空");
        }
        
        for (TransitDepot depot : request.getTransitDepots()) {
            if (depot.getTransitDepotId() == null) {
                throw new IllegalArgumentException("中转站ID不能为空");
            }
            if (depot.getLongitude() == null || depot.getLatitude() == null) {
                throw new IllegalArgumentException("中转站坐标不能为空");
            }
            if (depot.getRouteCount() == null || depot.getRouteCount() <= 0) {
                throw new IllegalArgumentException("中转站路线数量必须大于0");
            }
            if (depot.getGroupId() == null) {
                throw new IllegalArgumentException("中转站必须关联班组");
            }
        }
        
        // 验证班组数据
        if (request.getTeams() == null || request.getTeams().isEmpty()) {
            throw new IllegalArgumentException("班组数据不能为空");
        }
        
        for (Team team : request.getTeams()) {
            if (team.getTeamId() == null) {
                throw new IllegalArgumentException("班组ID不能为空");
            }
            if (team.getTransitDepotIds() == null || team.getTransitDepotIds().isEmpty()) {
                throw new IllegalArgumentException("班组必须包含中转站");
            }
        }
        
        // 验证时间矩阵
        if (request.getTimeMatrix() == null || request.getTimeMatrix().isEmpty()) {
            throw new IllegalArgumentException("时间矩阵不能为空");
        }
    }
    
    /**
     * 验证ID引用完整性
     */
    private void validateIdReferences(PathPlanningRequest request) {
        // 构建ID索引
        Set<Long> transitDepotIds = new HashSet<>();
        Set<Long> teamIds = new HashSet<>();
        
        for (TransitDepot depot : request.getTransitDepots()) {
            transitDepotIds.add(depot.getTransitDepotId());
        }
        
        for (Team team : request.getTeams()) {
            teamIds.add(team.getTeamId());
        }
        
        // 验证聚集区的中转站引用
        for (Accumulation acc : request.getAccumulations()) {
            if (!transitDepotIds.contains(acc.getTransitDepotId())) {
                throw new IllegalArgumentException(
                    String.format("聚集区 %d 引用的中转站 %d 不存在", 
                                acc.getAccumulationId(), acc.getTransitDepotId()));
            }
        }
        
        // 验证中转站的班组引用
        for (TransitDepot depot : request.getTransitDepots()) {
            if (!teamIds.contains(depot.getGroupId())) {
                throw new IllegalArgumentException(
                    String.format("中转站 %d 引用的班组 %d 不存在", 
                                depot.getTransitDepotId(), depot.getGroupId()));
            }
        }
        
        // 验证班组的中转站引用
        for (Team team : request.getTeams()) {
            for (Long depotId : team.getTransitDepotIds()) {
                if (!transitDepotIds.contains(depotId)) {
                    throw new IllegalArgumentException(
                        String.format("班组 %d 引用的中转站 %d 不存在", 
                                    team.getTeamId(), depotId));
                }
            }
        }
    }
    
    /**
     * 验证时间矩阵 - 仅验证中转站内部的点对，符合算法约束
     */
    private void validateTimeMatrix(PathPlanningRequest request) {
        // 按中转站分组聚集区
        Map<Long, List<Accumulation>> depotAccumulations = new HashMap<>();
        for (Accumulation acc : request.getAccumulations()) {
            depotAccumulations.computeIfAbsent(acc.getTransitDepotId(), k -> new ArrayList<>()).add(acc);
        }
        
        // 构建中转站ID到对象的映射
        Map<Long, TransitDepot> depotMap = new HashMap<>();
        for (TransitDepot depot : request.getTransitDepots()) {
            depotMap.put(depot.getTransitDepotId(), depot);
        }
        
        int totalExpectedPairs = 0;
        int foundPairs = 0;
        
        // 按中转站验证时间矩阵
        for (Map.Entry<Long, List<Accumulation>> entry : depotAccumulations.entrySet()) {
            Long depotId = entry.getKey();
            List<Accumulation> accumulations = entry.getValue();
            TransitDepot depot = depotMap.get(depotId);
            
            if (depot == null) {
                continue;
            }
            
            // 该中转站内的所有坐标点（中转站 + 聚集区）
            List<String> depotCoordinates = new ArrayList<>();
            
            // 添加中转站坐标
            String depotCoord = String.format("%.6f,%.6f", depot.getLongitude(), depot.getLatitude());
            depotCoordinates.add(depotCoord);
            
            // 添加该中转站的聚集区坐标
            for (Accumulation acc : accumulations) {
                String accCoord = String.format("%.6f,%.6f", acc.getLongitude(), acc.getLatitude());
                depotCoordinates.add(accCoord);
            }
            
            // 计算该中转站内的期望点对数（不包括自环）
            int depotExpectedPairs = depotCoordinates.size() * (depotCoordinates.size() - 1);
            totalExpectedPairs += depotExpectedPairs;
            
            // 检查该中转站内的时间矩阵覆盖
            int depotFoundPairs = 0;
            for (String from : depotCoordinates) {
                for (String to : depotCoordinates) {
                    if (!from.equals(to)) {
                        String key = from + "->" + to;
                        if (request.getTimeMatrix().containsKey(key)) {
                            depotFoundPairs++;
                            foundPairs++;
                            
                            // 验证时间值的合理性
                            TimeInfo timeInfo = request.getTimeMatrix().get(key);
                            if (timeInfo.getTravelTime() == null || timeInfo.getTravelTime() < 0) {
                                throw new IllegalArgumentException(
                                    String.format("时间矩阵中 %s 的行驶时间无效", key));
                            }
                        }
                    }
                }
            }
            
            // 记录每个中转站的覆盖情况
            double depotCoverage = (double) depotFoundPairs / depotExpectedPairs;
            log.debug("中转站{}覆盖度: {}% ({}/{})", 
                depotId, String.format("%.1f", depotCoverage * 100), depotFoundPairs, depotExpectedPairs);
        }
        
        double coverage = (double) foundPairs / totalExpectedPairs;
        log.info("时间矩阵覆盖度(仅中转站内部): {}% ({}/{})", String.format("%.1f", coverage * 100), foundPairs, totalExpectedPairs);
        
        if (coverage < 0.8) {
            throw new IllegalArgumentException(
                String.format("时间矩阵覆盖度过低: %.1f%%, 需要至少80%% (仅限中转站内部)", coverage * 100));
        }
    }
    
    /**
     * 验证业务逻辑
     */
    private void validateBusinessLogic(PathPlanningRequest request) {
        // 检查每个中转站的聚集区数量是否合理
        Map<Long, Integer> depotAccCount = new HashMap<>();
        
        for (Accumulation acc : request.getAccumulations()) {
            depotAccCount.merge(acc.getTransitDepotId(), 1, Integer::sum);
        }
        
        for (TransitDepot depot : request.getTransitDepots()) {
            int accCount = depotAccCount.getOrDefault(depot.getTransitDepotId(), 0);
            
            if (accCount == 0) {
                log.warn("中转站 {} 没有分配任何聚集区", depot.getTransitDepotName());
            }
            
            if (accCount < depot.getRouteCount()) {
                log.warn("中转站 {} 的聚集区数量({})少于路线数量({})", 
                        depot.getTransitDepotName(), accCount, depot.getRouteCount());
            }
        }
        
        // 检查坐标的合理性（是否在中国境内）
        for (Accumulation acc : request.getAccumulations()) {
            if (!isValidChineseCoordinate(acc.getLongitude(), acc.getLatitude())) {
                log.warn("聚集区 {} 的坐标可能不在中国境内: ({}, {})", 
                        acc.getAccumulationName(), acc.getLongitude(), acc.getLatitude());
            }
        }
        
        for (TransitDepot depot : request.getTransitDepots()) {
            if (!isValidChineseCoordinate(depot.getLongitude(), depot.getLatitude())) {
                log.warn("中转站 {} 的坐标可能不在中国境内: ({}, {})", 
                        depot.getTransitDepotName(), depot.getLongitude(), depot.getLatitude());
            }
        }
    }
    
    /**
     * 检查坐标是否在中国境内
     */
    private boolean isValidChineseCoordinate(Double longitude, Double latitude) {
        // 中国大陆坐标范围（粗略）
        return longitude >= 73.0 && longitude <= 135.0 && 
               latitude >= 18.0 && latitude <= 54.0;
    }
} 