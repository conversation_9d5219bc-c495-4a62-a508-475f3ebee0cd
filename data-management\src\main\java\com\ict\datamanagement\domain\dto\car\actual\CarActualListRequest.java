package com.ict.datamanagement.domain.dto.car.actual;


import com.ict.datamanagement.domain.dto.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("搜索车辆实情表单")
@AllArgsConstructor
@NoArgsConstructor
public class CarActualListRequest extends PageRequest {
    //车牌号
    @ApiModelProperty(value = "车牌号",dataType = "String")
    private String licensePlateNumber;
    //驾驶人
    @ApiModelProperty(value = "驾驶人",dataType = "String")
    private String carDriverName;
    //班组
    @ApiModelProperty(value = "班组",dataType = "String")
    private String teamName;
    //时间
    @ApiModelProperty(value = "时间",dataType = "String")
    private String date;
}
