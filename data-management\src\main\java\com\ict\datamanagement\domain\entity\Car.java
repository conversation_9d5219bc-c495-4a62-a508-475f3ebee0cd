package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 车辆类
 */
@Data
@TableName("car")
public class Car {

    /**
     * 车辆id
     */
    @TableId(type = IdType.AUTO)
    Long carId;

    /**
     *车牌号(七位)
     */
    String licensePlateNumber;

    /**
     *最大载重（吨）
     */
    String maxLoad;

    /**
     *最大行驶距离（米）
     */
    String maxDistance;

    /**
     *积分
     */
    String integral;

    /**
     *状态（0：异常；1：正常）
     */
    String status;


    /**
     *所属大区id
     */
    Long areaId;

    /**
     *所属中转站id
     */
    Long transitDepotId;

    /**
     * 车辆驾驶人id
    * */
    Long carDriverId;
    /**
     * 是否软删除,1删除0保留
     * */
    int isDelete;
    /*
     * 实际载货量
     * */
    String actualLoad;
    /*
     * 实际工作时间
     * */
    String actualTime;
    /*
     * 星期
     * */
    String week;
    /*
     * 日期
     * */
    String date;
    /*
    * 配送域id
    * */
    private int deliveryAreaId;
    /*
     * 路线
     * */
    private String routeName;

    /*
    * 是否是工作实情，0表示该记录是基本信息，1表示该记录是工作实情
    * */
    private String is_fact;
}
