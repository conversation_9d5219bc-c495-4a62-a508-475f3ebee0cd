package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("accumulation")
public class Accumulation {
    //聚集区id
    @TableId
    private Long accumulationId;
    //聚集区负责人名称
    private String leaderName;
    //聚集区名称
    private String accumulationName;
    //聚集区负责人联系电话
    private String leaderPhone;
    //聚集区经度
    private double longitude;
    //聚集区纬度
    private double latitude;
    //所属大区
    private String areaName;
    //创建时间
    @TableField(fill = FieldFill.INSERT)
    private String createTime;
    //更新时间
    @TableField(fill = FieldFill.UPDATE)
    private String updateTime;
    //是否软删除（0：否；1：是）
    private String isDelete;
    //聚集区地址
    private String accumulationAddress;
    //路线id
    private double routeId;
    //所属中转站id
    private double transitDepotId;
    //大区Id
    private double areaId;

}
