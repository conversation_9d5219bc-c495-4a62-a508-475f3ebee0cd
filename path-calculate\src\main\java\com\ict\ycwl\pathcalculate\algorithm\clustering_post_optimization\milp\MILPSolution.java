package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * MILP求解结果
 * 
 * 封装混合整数线性规划问题的求解结果
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class MILPSolution {
    
    /**
     * 求解状态
     */
    private MILPProblem.SolutionStatus solutionStatus;
    
    /**
     * 目标函数值
     */
    private Double objectiveValue;
    
    /**
     * 变量值映射（变量名 -> 值）
     */
    private Map<String, Double> variableValues;
    
    /**
     * 求解耗时（毫秒）
     */
    private long solvingTimeMs;
    
    /**
     * 求解器名称
     */
    private String solverName;
    
    /**
     * 迭代次数
     */
    private Integer iterations;
    
    /**
     * 求解器返回的原始状态码
     */
    private Integer rawStatusCode;
    
    /**
     * 求解器返回的状态消息
     */
    private String statusMessage;
    
    /**
     * 对偶解（如果可用）
     */
    private Map<String, Double> dualValues;
    
    /**
     * 求解间隙（对于整数规划）
     */
    private Double mipGap;
    
    /**
     * 下界（对于整数规划）
     */
    private Double lowerBound;
    
    /**
     * 上界（对于整数规划）
     */
    private Double upperBound;
    
    /**
     * 检查解是否可行
     */
    public boolean isFeasible() {
        return solutionStatus == MILPProblem.SolutionStatus.OPTIMAL ||
               solutionStatus == MILPProblem.SolutionStatus.FEASIBLE;
    }
    
    /**
     * 检查解是否最优
     */
    public boolean isOptimal() {
        return solutionStatus == MILPProblem.SolutionStatus.OPTIMAL;
    }
    
    /**
     * 获取变量值
     */
    public Double getVariableValue(String variableName) {
        return variableValues != null ? variableValues.get(variableName) : null;
    }
    
    /**
     * 获取对偶值
     */
    public Double getDualValue(String constraintName) {
        return dualValues != null ? dualValues.get(constraintName) : null;
    }
    
    /**
     * 生成求解结果摘要
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== MILP求解结果摘要 ===\n");
        summary.append(String.format("求解状态: %s\n", solutionStatus.getDescription()));
        
        if (objectiveValue != null) {
            summary.append(String.format("目标函数值: %.6f\n", objectiveValue));
        }
        
        if (solvingTimeMs > 0) {
            summary.append(String.format("求解时间: %d ms\n", solvingTimeMs));
        }
        
        if (solverName != null) {
            summary.append(String.format("求解器: %s\n", solverName));
        }
        
        if (iterations != null) {
            summary.append(String.format("迭代次数: %d\n", iterations));
        }
        
        if (mipGap != null) {
            summary.append(String.format("MIP间隙: %.2f%%\n", mipGap * 100));
        }
        
        if (variableValues != null) {
            summary.append(String.format("变量数量: %d\n", variableValues.size()));
        }
        
        if (statusMessage != null) {
            summary.append(String.format("状态消息: %s\n", statusMessage));
        }
        
        return summary.toString();
    }
}