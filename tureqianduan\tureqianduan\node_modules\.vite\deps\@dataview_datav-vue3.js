import {
  debounce_default,
  merge_default,
  random_default,
  sum_default,
  uniqueId_default
} from "./chunk-SQYHY2BC.js";
import {
  createVNode,
  defineComponent,
  mergeProps,
  onMounted,
  onUnmounted,
  reactive,
  readonly,
  ref
} from "./chunk-PDCNSZTT.js";
import {
  __commonJS,
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/classnames/index.js
var require_classnames = __commonJS({
  "node_modules/classnames/index.js"(exports, module) {
    (function() {
      "use strict";
      var hasOwn = {}.hasOwnProperty;
      function classNames() {
        var classes = "";
        for (var i3 = 0; i3 < arguments.length; i3++) {
          var arg = arguments[i3];
          if (arg) {
            classes = appendClass(classes, parseValue(arg));
          }
        }
        return classes;
      }
      function parseValue(arg) {
        if (typeof arg === "string" || typeof arg === "number") {
          return arg;
        }
        if (typeof arg !== "object") {
          return "";
        }
        if (Array.isArray(arg)) {
          return classNames.apply(null, arg);
        }
        if (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes("[native code]")) {
          return arg.toString();
        }
        var classes = "";
        for (var key in arg) {
          if (hasOwn.call(arg, key) && arg[key]) {
            classes = appendClass(classes, key);
          }
        }
        return classes;
      }
      function appendClass(value, newClass) {
        if (!newClass) {
          return value;
        }
        if (value) {
          return value + " " + newClass;
        }
        return value + newClass;
      }
      if (typeof module !== "undefined" && module.exports) {
        classNames.default = classNames;
        module.exports = classNames;
      } else if (typeof define === "function" && typeof define.amd === "object" && define.amd) {
        define("classnames", [], function() {
          return classNames;
        });
      } else {
        window.classNames = classNames;
      }
    })();
  }
});

// node_modules/@babel/runtime/helpers/interopRequireDefault.js
var require_interopRequireDefault = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireDefault.js"(exports, module) {
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : {
        "default": e
      };
    }
    module.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/arrayLikeToArray.js
var require_arrayLikeToArray = __commonJS({
  "node_modules/@babel/runtime/helpers/arrayLikeToArray.js"(exports, module) {
    function _arrayLikeToArray(r3, a5) {
      (null == a5 || a5 > r3.length) && (a5 = r3.length);
      for (var e = 0, n5 = Array(a5); e < a5; e++) n5[e] = r3[e];
      return n5;
    }
    module.exports = _arrayLikeToArray, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/arrayWithoutHoles.js
var require_arrayWithoutHoles = __commonJS({
  "node_modules/@babel/runtime/helpers/arrayWithoutHoles.js"(exports, module) {
    var arrayLikeToArray = require_arrayLikeToArray();
    function _arrayWithoutHoles(r3) {
      if (Array.isArray(r3)) return arrayLikeToArray(r3);
    }
    module.exports = _arrayWithoutHoles, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/iterableToArray.js
var require_iterableToArray = __commonJS({
  "node_modules/@babel/runtime/helpers/iterableToArray.js"(exports, module) {
    function _iterableToArray(r3) {
      if ("undefined" != typeof Symbol && null != r3[Symbol.iterator] || null != r3["@@iterator"]) return Array.from(r3);
    }
    module.exports = _iterableToArray, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js
var require_unsupportedIterableToArray = __commonJS({
  "node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js"(exports, module) {
    var arrayLikeToArray = require_arrayLikeToArray();
    function _unsupportedIterableToArray(r3, a5) {
      if (r3) {
        if ("string" == typeof r3) return arrayLikeToArray(r3, a5);
        var t4 = {}.toString.call(r3).slice(8, -1);
        return "Object" === t4 && r3.constructor && (t4 = r3.constructor.name), "Map" === t4 || "Set" === t4 ? Array.from(r3) : "Arguments" === t4 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t4) ? arrayLikeToArray(r3, a5) : void 0;
      }
    }
    module.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/nonIterableSpread.js
var require_nonIterableSpread = __commonJS({
  "node_modules/@babel/runtime/helpers/nonIterableSpread.js"(exports, module) {
    function _nonIterableSpread() {
      throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
    }
    module.exports = _nonIterableSpread, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toConsumableArray.js
var require_toConsumableArray = __commonJS({
  "node_modules/@babel/runtime/helpers/toConsumableArray.js"(exports, module) {
    var arrayWithoutHoles = require_arrayWithoutHoles();
    var iterableToArray = require_iterableToArray();
    var unsupportedIterableToArray = require_unsupportedIterableToArray();
    var nonIterableSpread = require_nonIterableSpread();
    function _toConsumableArray(r3) {
      return arrayWithoutHoles(r3) || iterableToArray(r3) || unsupportedIterableToArray(r3) || nonIterableSpread();
    }
    module.exports = _toConsumableArray, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@jiaminghi/color/lib/config/keywords.js
var require_keywords = __commonJS({
  "node_modules/@jiaminghi/color/lib/config/keywords.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports["default"] = void 0;
    var _default = /* @__PURE__ */ new Map([["transparent", "rgba(0,0,0,0)"], ["black", "#000000"], ["silver", "#C0C0C0"], ["gray", "#808080"], ["white", "#FFFFFF"], ["maroon", "#800000"], ["red", "#FF0000"], ["purple", "#800080"], ["fuchsia", "#FF00FF"], ["green", "#008000"], ["lime", "#00FF00"], ["olive", "#808000"], ["yellow", "#FFFF00"], ["navy", "#000080"], ["blue", "#0000FF"], ["teal", "#008080"], ["aqua", "#00FFFF"], ["aliceblue", "#f0f8ff"], ["antiquewhite", "#faebd7"], ["aquamarine", "#7fffd4"], ["azure", "#f0ffff"], ["beige", "#f5f5dc"], ["bisque", "#ffe4c4"], ["blanchedalmond", "#ffebcd"], ["blueviolet", "#8a2be2"], ["brown", "#a52a2a"], ["burlywood", "#deb887"], ["cadetblue", "#5f9ea0"], ["chartreuse", "#7fff00"], ["chocolate", "#d2691e"], ["coral", "#ff7f50"], ["cornflowerblue", "#6495ed"], ["cornsilk", "#fff8dc"], ["crimson", "#dc143c"], ["cyan", "#00ffff"], ["darkblue", "#00008b"], ["darkcyan", "#008b8b"], ["darkgoldenrod", "#b8860b"], ["darkgray", "#a9a9a9"], ["darkgreen", "#006400"], ["darkgrey", "#a9a9a9"], ["darkkhaki", "#bdb76b"], ["darkmagenta", "#8b008b"], ["darkolivegreen", "#556b2f"], ["darkorange", "#ff8c00"], ["darkorchid", "#9932cc"], ["darkred", "#8b0000"], ["darksalmon", "#e9967a"], ["darkseagreen", "#8fbc8f"], ["darkslateblue", "#483d8b"], ["darkslategray", "#2f4f4f"], ["darkslategrey", "#2f4f4f"], ["darkturquoise", "#00ced1"], ["darkviolet", "#9400d3"], ["deeppink", "#ff1493"], ["deepskyblue", "#00bfff"], ["dimgray", "#696969"], ["dimgrey", "#696969"], ["dodgerblue", "#1e90ff"], ["firebrick", "#b22222"], ["floralwhite", "#fffaf0"], ["forestgreen", "#228b22"], ["gainsboro", "#dcdcdc"], ["ghostwhite", "#f8f8ff"], ["gold", "#ffd700"], ["goldenrod", "#daa520"], ["greenyellow", "#adff2f"], ["grey", "#808080"], ["honeydew", "#f0fff0"], ["hotpink", "#ff69b4"], ["indianred", "#cd5c5c"], ["indigo", "#4b0082"], ["ivory", "#fffff0"], ["khaki", "#f0e68c"], ["lavender", "#e6e6fa"], ["lavenderblush", "#fff0f5"], ["lawngreen", "#7cfc00"], ["lemonchiffon", "#fffacd"], ["lightblue", "#add8e6"], ["lightcoral", "#f08080"], ["lightcyan", "#e0ffff"], ["lightgoldenrodyellow", "#fafad2"], ["lightgray", "#d3d3d3"], ["lightgreen", "#90ee90"], ["lightgrey", "#d3d3d3"], ["lightpink", "#ffb6c1"], ["lightsalmon", "#ffa07a"], ["lightseagreen", "#20b2aa"], ["lightskyblue", "#87cefa"], ["lightslategray", "#778899"], ["lightslategrey", "#778899"], ["lightsteelblue", "#b0c4de"], ["lightyellow", "#ffffe0"], ["limegreen", "#32cd32"], ["linen", "#faf0e6"], ["magenta", "#ff00ff"], ["mediumaquamarine", "#66cdaa"], ["mediumblue", "#0000cd"], ["mediumorchid", "#ba55d3"], ["mediumpurple", "#9370db"], ["mediumseagreen", "#3cb371"], ["mediumslateblue", "#7b68ee"], ["mediumspringgreen", "#00fa9a"], ["mediumturquoise", "#48d1cc"], ["mediumvioletred", "#c71585"], ["midnightblue", "#191970"], ["mintcream", "#f5fffa"], ["mistyrose", "#ffe4e1"], ["moccasin", "#ffe4b5"], ["navajowhite", "#ffdead"], ["oldlace", "#fdf5e6"], ["olivedrab", "#6b8e23"], ["orange", "#ffa500"], ["orangered", "#ff4500"], ["orchid", "#da70d6"], ["palegoldenrod", "#eee8aa"], ["palegreen", "#98fb98"], ["paleturquoise", "#afeeee"], ["palevioletred", "#db7093"], ["papayawhip", "#ffefd5"], ["peachpuff", "#ffdab9"], ["peru", "#cd853f"], ["pink", "#ffc0cb"], ["plum", "#dda0dd"], ["powderblue", "#b0e0e6"], ["rosybrown", "#bc8f8f"], ["royalblue", "#4169e1"], ["saddlebrown", "#8b4513"], ["salmon", "#fa8072"], ["sandybrown", "#f4a460"], ["seagreen", "#2e8b57"], ["seashell", "#fff5ee"], ["sienna", "#a0522d"], ["skyblue", "#87ceeb"], ["slateblue", "#6a5acd"], ["slategray", "#708090"], ["slategrey", "#708090"], ["snow", "#fffafa"], ["springgreen", "#00ff7f"], ["steelblue", "#4682b4"], ["tan", "#d2b48c"], ["thistle", "#d8bfd8"], ["tomato", "#ff6347"], ["turquoise", "#40e0d0"], ["violet", "#ee82ee"], ["wheat", "#f5deb3"], ["whitesmoke", "#f5f5f5"], ["yellowgreen", "#9acd32"]]);
    exports["default"] = _default;
  }
});

// node_modules/@jiaminghi/color/lib/index.js
var require_lib = __commonJS({
  "node_modules/@jiaminghi/color/lib/index.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.getRgbValue = getRgbValue;
    exports.getRgbaValue = getRgbaValue;
    exports.getOpacity = getOpacity;
    exports.toRgb = toRgb;
    exports.toHex = toHex;
    exports.getColorFromRgbValue = getColorFromRgbValue;
    exports.darken = darken;
    exports.lighten = lighten;
    exports.fade = fade;
    exports["default"] = void 0;
    var _toConsumableArray2 = _interopRequireDefault(require_toConsumableArray());
    var _keywords = _interopRequireDefault(require_keywords());
    var hexReg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
    var rgbReg = /^(rgb|rgba|RGB|RGBA)/;
    var rgbaReg = /^(rgba|RGBA)/;
    function validator(color) {
      var isHex = hexReg.test(color);
      var isRgb = rgbReg.test(color);
      if (isHex || isRgb) return color;
      color = getColorByKeyword(color);
      if (!color) {
        console.error("Color: Invalid color!");
        return false;
      }
      return color;
    }
    function getColorByKeyword(keyword) {
      if (!keyword) {
        console.error("getColorByKeywords: Missing parameters!");
        return false;
      }
      if (!_keywords["default"].has(keyword)) return false;
      return _keywords["default"].get(keyword);
    }
    function getRgbValue(color) {
      if (!color) {
        console.error("getRgbValue: Missing parameters!");
        return false;
      }
      color = validator(color);
      if (!color) return false;
      var isHex = hexReg.test(color);
      var isRgb = rgbReg.test(color);
      var lowerColor = color.toLowerCase();
      if (isHex) return getRgbValueFromHex(lowerColor);
      if (isRgb) return getRgbValueFromRgb(lowerColor);
    }
    function getRgbValueFromHex(color) {
      color = color.replace("#", "");
      if (color.length === 3) color = Array.from(color).map(function(hexNum) {
        return hexNum + hexNum;
      }).join("");
      color = color.split("");
      return new Array(3).fill(0).map(function(t4, i3) {
        return parseInt("0x".concat(color[i3 * 2]).concat(color[i3 * 2 + 1]));
      });
    }
    function getRgbValueFromRgb(color) {
      return color.replace(/rgb\(|rgba\(|\)/g, "").split(",").slice(0, 3).map(function(n5) {
        return parseInt(n5);
      });
    }
    function getRgbaValue(color) {
      if (!color) {
        console.error("getRgbaValue: Missing parameters!");
        return false;
      }
      var colorValue = getRgbValue(color);
      if (!colorValue) return false;
      colorValue.push(getOpacity(color));
      return colorValue;
    }
    function getOpacity(color) {
      if (!color) {
        console.error("getOpacity: Missing parameters!");
        return false;
      }
      color = validator(color);
      if (!color) return false;
      var isRgba = rgbaReg.test(color);
      if (!isRgba) return 1;
      color = color.toLowerCase();
      return Number(color.split(",").slice(-1)[0].replace(/[)|\s]/g, ""));
    }
    function toRgb(color, opacity) {
      if (!color) {
        console.error("toRgb: Missing parameters!");
        return false;
      }
      var rgbValue = getRgbValue(color);
      if (!rgbValue) return false;
      var addOpacity = typeof opacity === "number";
      if (addOpacity) return "rgba(" + rgbValue.join(",") + ",".concat(opacity, ")");
      return "rgb(" + rgbValue.join(",") + ")";
    }
    function toHex(color) {
      if (!color) {
        console.error("toHex: Missing parameters!");
        return false;
      }
      if (hexReg.test(color)) return color;
      color = getRgbValue(color);
      if (!color) return false;
      return "#" + color.map(function(n5) {
        return Number(n5).toString(16);
      }).map(function(n5) {
        return n5 === "0" ? "00" : n5;
      }).join("");
    }
    function getColorFromRgbValue(value) {
      if (!value) {
        console.error("getColorFromRgbValue: Missing parameters!");
        return false;
      }
      var valueLength = value.length;
      if (valueLength !== 3 && valueLength !== 4) {
        console.error("getColorFromRgbValue: Value is illegal!");
        return false;
      }
      var color = valueLength === 3 ? "rgb(" : "rgba(";
      color += value.join(",") + ")";
      return color;
    }
    function darken(color) {
      var percent = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      if (!color) {
        console.error("darken: Missing parameters!");
        return false;
      }
      var rgbaValue = getRgbaValue(color);
      if (!rgbaValue) return false;
      rgbaValue = rgbaValue.map(function(v7, i3) {
        return i3 === 3 ? v7 : v7 - Math.ceil(2.55 * percent);
      }).map(function(v7) {
        return v7 < 0 ? 0 : v7;
      });
      return getColorFromRgbValue(rgbaValue);
    }
    function lighten(color) {
      var percent = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      if (!color) {
        console.error("lighten: Missing parameters!");
        return false;
      }
      var rgbaValue = getRgbaValue(color);
      if (!rgbaValue) return false;
      rgbaValue = rgbaValue.map(function(v7, i3) {
        return i3 === 3 ? v7 : v7 + Math.ceil(2.55 * percent);
      }).map(function(v7) {
        return v7 > 255 ? 255 : v7;
      });
      return getColorFromRgbValue(rgbaValue);
    }
    function fade(color) {
      var percent = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;
      if (!color) {
        console.error("fade: Missing parameters!");
        return false;
      }
      var rgbValue = getRgbValue(color);
      if (!rgbValue) return false;
      var rgbaValue = [].concat((0, _toConsumableArray2["default"])(rgbValue), [percent / 100]);
      return getColorFromRgbValue(rgbaValue);
    }
    var _default = {
      fade,
      toHex,
      toRgb,
      darken,
      lighten,
      getOpacity,
      getRgbValue,
      getRgbaValue,
      getColorFromRgbValue
    };
    exports["default"] = _default;
  }
});

// node_modules/@dataview/datav-vue3/es/utils/styled.mjs
var f = "dv-";
function $(e) {
  f = e;
}
function c(e, n5 = true) {
  return `${n5 ? "." : ""}${f || ""}${e}`;
}
function x(e) {
  return c(e, false);
}
function v(e, n5) {
  const r3 = c(n5);
  return `.__STYLED__ {${e.toString()}}`.replaceAll(".__STYLED__", r3);
}
function t(e) {
  return (n5) => {
    const r3 = document.createElement("style"), i3 = (a5) => {
      r3.innerHTML = v(n5, a5), document.querySelector("head").appendChild(r3);
    }, o2 = () => document.querySelector("head").removeChild(r3);
    return (a5) => {
      const m8 = e, p9 = c(a5, false);
      return defineComponent({
        setup(y6, {
          slots: u6
        }) {
          return onMounted(() => {
            i3(a5);
          }), onUnmounted(() => {
            o2();
          }), () => createVNode(m8, mergeProps(y6, {
            class: p9
          }), {
            default: () => {
              var l5;
              return [(l5 = u6 == null ? void 0 : u6.default) == null ? void 0 : l5.call(u6)];
            }
          });
        }
      });
    };
  };
}
t.span = t((e, {
  slots: n5
}) => createVNode("span", e, [n5 == null ? void 0 : n5.default()]));
t.div = t((e, {
  slots: n5
}) => createVNode("div", e, [n5 == null ? void 0 : n5.default()]));
t.img = t((e) => createVNode("img", e, null));
t.svg = t((e, {
  slots: n5
}) => createVNode("svg", e, [n5 == null ? void 0 : n5.default()]));

// node_modules/@dataview/datav-vue3/es/hooks/useResize.mjs
function m(e, n5) {
  const t4 = new MutationObserver(n5);
  return t4.observe(e, {
    attributes: true,
    attributeFilter: ["class", "style"],
    attributeOldValue: true
  }), t4;
}
function l(e, n5) {
  const { clientWidth: t4 = 0, clientHeight: o2 = 0 } = e || {};
  e ? (!t4 || !o2) && console.warn("DataV: Component width or height is 0px, rendering abnormality may occur!") : console.warn("DataV: Failed to get dom node, component rendering may be abnormal!"), n5.width = t4, n5.height = o2;
}
function f2() {
  const e = ref(), n5 = [], t4 = reactive({
    width: 0,
    height: 0
  }), o2 = () => {
    l(e.value, t4);
  }, i3 = debounce_default(o2, 100);
  return onMounted(() => {
    o2();
    const r3 = m(e.value, i3);
    window.addEventListener("resize", i3), n5.push(
      () => {
        r3.disconnect();
      },
      () => {
        window.removeEventListener("resize", i3);
      }
    );
  }), onUnmounted(() => {
    n5.forEach((r3) => r3());
  }), {
    domRef: e,
    domSize: t4
  };
}

// node_modules/@dataview/datav-vue3/es/utils/common.mjs
function s(t4) {
  const n5 = t4;
  return n5.install = function(o2) {
    o2.component(n5.displayName || n5.name, t4);
  }, t4;
}
var c2 = (t4) => t4;
var i = (t4, n5) => {
  const o2 = Math.abs(t4[0] - n5[0]), e = Math.abs(t4[1] - n5[1]);
  return Math.sqrt(Math.pow(o2, 2) + Math.pow(e, 2));
};
function u(t4, n5 = []) {
  return merge_default(t4, n5);
}

// node_modules/@dataview/datav-vue3/es/utils/borderBox.mjs
function m2() {
  return {
    color: {
      type: c2(Array),
      default: () => []
    },
    backgroundColor: {
      type: String,
      default: "transparent"
    }
  };
}
function a(r3, o2 = []) {
  return merge_default(r3, o2);
}

// node_modules/@dataview/datav-vue3/es/components/styled/borderBox.mjs
var i2 = t.div`
  position: relative;
  width: 100%;
  height: 100%;
`("border-box");
var r = t.div`
  position: relative;
  width: 100%;
  height: 100%;
`("border-box-content");

// node_modules/@dataview/datav-vue3/es/components/border-box-1/index.mjs
var _ = ["#4fd2dd", "#235fa7"];
var x2 = ["left-top", "right-top", "left-bottom", "right-bottom"];
var l2 = t.svg`
  position: absolute;
  display: block;
}
.__STYLED__.right-top {
  right: 0px;
  transform: rotateY(180deg);
}
.__STYLED__.left-bottom {
  bottom: 0px;
  transform: rotateX(180deg);
}
.__STYLED__.right-bottom {
  right: 0px;
  bottom: 0px;
  transform: rotateX(180deg) rotateY(180deg);
`("border");
var D = s(defineComponent({
  name: "BorderBox1",
  props: m2(),
  setup($10, {
    slots: n5
  }) {
    const {
      domRef: a5,
      domSize: d8
    } = f2();
    return () => {
      const {
        color: m8,
        backgroundColor: p9
      } = $10, {
        width: t4,
        height: o2
      } = d8, e = a(_, m8);
      return createVNode(i2, {
        class: x("border-box-1"),
        ref: (i3) => a5.value = i3.$el
      }, {
        default: () => [createVNode(l2, {
          width: t4,
          height: o2
        }, {
          default: () => [createVNode("polygon", {
            fill: p9,
            points: `10, 27 10, ${o2 - 27} 13, ${o2 - 24} 13, ${o2 - 21} 24, ${o2 - 11} 38, ${o2 - 11}
                41, ${o2 - 8} 73, ${o2 - 8} 75, ${o2 - 10} 81, ${o2 - 10} 85, ${o2 - 6}
                ${t4 - 85}, ${o2 - 6} ${t4 - 81}, ${o2 - 10} ${t4 - 75}, ${o2 - 10}
                ${t4 - 73}, ${o2 - 8} ${t4 - 41}, ${o2 - 8} ${t4 - 38}, ${o2 - 11}
                ${t4 - 24}, ${o2 - 11} ${t4 - 13}, ${o2 - 21} ${t4 - 13}, ${o2 - 24}
                ${t4 - 10}, ${o2 - 27} ${t4 - 10}, 27 ${t4 - 13}, 25 ${t4 - 13}, 21
                ${t4 - 24}, 11 ${t4 - 38}, 11 ${t4 - 41}, 8 ${t4 - 73}, 8 ${t4 - 75}, 10
                ${t4 - 81}, 10 ${t4 - 85}, 6 85, 6 81, 10 75, 10 73, 8 41, 8 38, 11 24, 11 13, 21 13, 24`
          }, null)]
        }), x2.map((i3) => createVNode(l2, {
          key: i3,
          width: "150",
          height: "150",
          class: i3
        }, {
          default: () => [createVNode("polygon", {
            fill: e[0],
            points: "6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
          }, [createVNode("animate", {
            attributeName: "fill",
            values: `${e[0]};${e[1]};${e[0]}`,
            dur: "0.5s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("polygon", {
            fill: e[1],
            points: "27.6,4.8 38.4,4.8 35.4,7.8 30.6,7.8"
          }, [createVNode("animate", {
            attributeName: "fill",
            values: `${e[1]};${e[0]};${e[1]}`,
            dur: "0.5s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("polygon", {
            fill: e[0],
            points: "9,54 9,63 7.2,66 7.2,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
          }, [createVNode("animate", {
            attributeName: "fill",
            values: `${e[0]};${e[1]};transparent`,
            dur: "1s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)])]
        })), createVNode(r, null, {
          default: () => {
            var i3;
            return [(i3 = n5.default) == null ? void 0 : i3.call(n5)];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-2/index.mjs
var C = ["#fff", "rgba(255, 255, 255, 0.6)"];
var y = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-width: 1;
`("border-svg-container");
var F = s(defineComponent({
  name: "BorderBox2",
  props: m2(),
  setup(i3, {
    slots: n5
  }) {
    const {
      domRef: c5,
      domSize: d8
    } = f2();
    return () => {
      const {
        color: f10,
        backgroundColor: p9
      } = i3, {
        width: r3,
        height: e
      } = d8, l5 = a(C, f10);
      return createVNode(i2, {
        class: x("border-box-2"),
        ref: (t4) => c5.value = t4.$el
      }, {
        default: () => [createVNode(y, {
          width: r3,
          height: e
        }, {
          default: () => [createVNode("polygon", {
            fill: p9,
            points: `7, 7 ${r3 - 7}, 7 ${r3 - 7}, ${e - 7} 7, ${e - 7}`
          }, null), createVNode("polyline", {
            stroke: l5[0],
            points: `2, 2 ${r3 - 2} ,2 ${r3 - 2}, ${e - 2} 2, ${e - 2} 2, 2`
          }, null), createVNode("polyline", {
            stroke: l5[1],
            points: `6, 6 ${r3 - 6}, 6 ${r3 - 6}, ${e - 6} 6, ${e - 6} 6, 6`
          }, null), createVNode("circle", {
            fill: l5[0],
            cx: "11",
            cy: "11",
            r: "1"
          }, null), createVNode("circle", {
            fill: l5[0],
            cx: r3 - 11,
            cy: "11",
            r: "1"
          }, null), createVNode("circle", {
            fill: l5[0],
            cx: r3 - 11,
            cy: e - 11,
            r: "1"
          }, null), createVNode("circle", {
            fill: l5[0],
            cx: "11",
            cy: e - 11,
            r: "1"
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var t4;
            return [createVNode("slot", null, [(t4 = n5.default) == null ? void 0 : t4.call(n5)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-3/index.mjs
var _2 = ["#2862b7", "#2862b7"];
var g = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
}
.__STYLED__ .stroke-width-1 {
  stroke-width: 1;
}
.__STYLED__ .stroke-width-3 {
  stroke-width: 3;
`("border-svg-container");
var E = s(defineComponent({
  name: "BorderBox3",
  props: m2(),
  setup(i3, {
    slots: n5
  }) {
    const {
      domRef: s4,
      domSize: d8
    } = f2();
    return () => {
      const {
        color: p9,
        backgroundColor: a5
      } = i3, {
        width: o2,
        height: e
      } = d8, r3 = a(_2, p9);
      return createVNode(i2, {
        class: x("border-box-3"),
        ref: (l5) => s4.value = l5.$el
      }, {
        default: () => [createVNode(g, {
          width: o2,
          height: e
        }, {
          default: () => [createVNode("polygon", {
            fill: a5,
            points: `23, 23 ${o2 - 24}, 23 ${o2 - 24}, ${e - 24} 23, ${e - 24}`
          }, null), createVNode("polyline", {
            class: "stroke-width-3",
            stroke: r3[0],
            points: `4, 4 ${o2 - 22} ,4 ${o2 - 22}, ${e - 22} 4, ${e - 22} 4, 4`
          }, null), createVNode("polyline", {
            class: "stroke-width-1",
            stroke: r3[1],
            points: `10, 10 ${o2 - 16}, 10 ${o2 - 16}, ${e - 16} 10, ${e - 16} 10, 10`
          }, null), createVNode("polyline", {
            class: "stroke-width-1",
            stroke: r3[1],
            points: `16, 16 ${o2 - 10}, 16 ${o2 - 10}, ${e - 10} 16, ${e - 10} 16, 16`
          }, null), createVNode("polyline", {
            class: "stroke-width-1",
            stroke: r3[1],
            points: `22, 22 ${o2 - 4}, 22 ${o2 - 4}, ${e - 4} 22, ${e - 4} 22, 22`
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var l5;
            return [createVNode("slot", null, [(l5 = n5.default) == null ? void 0 : l5.call(n5)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-4/index.mjs
var import_classnames = __toESM(require_classnames(), 1);
var f3 = Object.defineProperty;
var w = Object.defineProperties;
var y2 = Object.getOwnPropertyDescriptors;
var p = Object.getOwnPropertySymbols;
var $2 = Object.prototype.hasOwnProperty;
var _3 = Object.prototype.propertyIsEnumerable;
var d = (l5, o2, r3) => o2 in l5 ? f3(l5, o2, { enumerable: true, configurable: true, writable: true, value: r3 }) : l5[o2] = r3;
var a2 = (l5, o2) => {
  for (var r3 in o2 || (o2 = {}))
    $2.call(o2, r3) && d(l5, r3, o2[r3]);
  if (p)
    for (var r3 of p(o2))
      _3.call(o2, r3) && d(l5, r3, o2[r3]);
  return l5;
};
var k = (l5, o2) => w(l5, y2(o2));
var T = ["red", "rgba(0,0,255,0.8)"];
var Y = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
}
.__STYLED__.reverse {
  transform: rotate(180deg);
}
.__STYLED__ .stroke-width1 {
  stroke-width: 1;
}
.__STYLED__ .stroke-width3 {
  stroke-width: 3px;
  stroke-linecap: round;
`("border-svg-container");
var z = () => k(a2({}, m2()), {
  reverse: {
    type: Boolean,
    default: false
  }
});
var G = s(defineComponent({
  name: "BorderBox4",
  props: z(),
  setup(l5, {
    slots: o2
  }) {
    const {
      domRef: r3,
      domSize: u6
    } = f2();
    return () => {
      const {
        color: c5,
        backgroundColor: h7,
        reverse: m8
      } = l5, {
        width: n5,
        height: t4
      } = u6, s4 = a(T, c5);
      return createVNode(i2, {
        class: x("border-box-4"),
        ref: (i3) => r3.value = i3.$el
      }, {
        default: () => [createVNode(Y, {
          class: (0, import_classnames.default)({
            reverse: m8
          }),
          width: n5,
          height: t4
        }, {
          default: () => [createVNode("polygon", {
            fill: h7,
            points: `${n5 - 15}, 22 170, 22 150, 7 40, 7 28, 21 32, 24
                16, 42 16, ${t4 - 32} 41, ${t4 - 7} ${n5 - 15}, ${t4 - 7}`
          }, null), createVNode("polyline", {
            class: "stroke-width1",
            stroke: s4[0],
            points: `145, ${t4 - 5} 40, ${t4 - 5} 10, ${t4 - 35} 10, 40 40, 5 150, 5 170, 20 ${n5 - 15}, 20`
          }, null), createVNode("polyline", {
            stroke: s4[1],
            class: "stroke-width1",
            points: `245, ${t4 - 1} 36, ${t4 - 1} 14, ${t4 - 23} 14, ${t4 - 100}`
          }, null), createVNode("polyline", {
            class: "stroke-width3",
            stroke: s4[0],
            points: `7, ${t4 - 40} 7, ${t4 - 75}`
          }, null), createVNode("polyline", {
            class: "stroke-width3",
            stroke: s4[0],
            points: "28, 24 13, 41 13, 64"
          }, null), createVNode("polyline", {
            class: "stroke-width1",
            stroke: s4[0],
            points: "5, 45 5, 140"
          }, null), createVNode("polyline", {
            class: "stroke-width1",
            stroke: s4[1],
            points: "14, 75 14, 180"
          }, null), createVNode("polyline", {
            class: "stroke-width1",
            stroke: s4[1],
            points: "55, 11 147, 11 167, 26 250, 26"
          }, null), createVNode("polyline", {
            class: "stroke-width3",
            stroke: s4[1],
            points: "158, 5 173, 16"
          }, null), createVNode("polyline", {
            class: "stroke-width3",
            style: {
              strokeDasharray: "100 250"
            },
            stroke: s4[0],
            points: `200, 17 ${n5 - 10}, 17`
          }, null), createVNode("polyline", {
            class: "stroke-width1",
            style: {
              strokeDasharray: "80 270"
            },
            stroke: s4[1],
            points: `385, 17 ${n5 - 10}, 17`
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var i3;
            return [createVNode("slot", null, [(i3 = o2.default) == null ? void 0 : i3.call(o2)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-5/index.mjs
var import_classnames2 = __toESM(require_classnames(), 1);
var f4 = Object.defineProperty;
var _4 = Object.defineProperties;
var k2 = Object.getOwnPropertyDescriptors;
var d2 = Object.getOwnPropertySymbols;
var w2 = Object.prototype.hasOwnProperty;
var g3 = Object.prototype.propertyIsEnumerable;
var p2 = (l5, o2, r3) => o2 in l5 ? f4(l5, o2, { enumerable: true, configurable: true, writable: true, value: r3 }) : l5[o2] = r3;
var a3 = (l5, o2) => {
  for (var r3 in o2 || (o2 = {}))
    w2.call(o2, r3) && p2(l5, r3, o2[r3]);
  if (d2)
    for (var r3 of d2(o2))
      g3.call(o2, r3) && p2(l5, r3, o2[r3]);
  return l5;
};
var $3 = (l5, o2) => _4(l5, k2(o2));
var T2 = ["rgba(255, 255, 255, 0.35)", "rgba(255, 255, 255, 0.20)"];
var Y2 = t.svg`
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}
.__STYLED__ > polyline {
  fill: none;
}
.__STYLED__.reverse {
  transform: rotate(180deg);
}
.__STYLED__ .stroke-width1 {
  stroke-width: 1;
}
.__STYLED__ .stroke-width2 {
  stroke-width: 2px;
}
.__STYLED__ .stroke-width5 {
  stroke-width: 5px;
`("border-svg-container");
var z2 = () => $3(a3({}, m2()), {
  reverse: {
    type: Boolean,
    default: false
  }
});
var G2 = s(defineComponent({
  name: "BorderBox5",
  props: z2(),
  setup(l5, {
    slots: o2
  }) {
    const {
      domRef: r3,
      domSize: m8
    } = f2();
    return () => {
      const {
        color: u6,
        backgroundColor: c5,
        reverse: h7
      } = l5, {
        width: e,
        height: t4
      } = m8, n5 = a(T2, u6);
      return createVNode(i2, {
        class: x("border-box-5"),
        ref: (i3) => r3.value = i3.$el
      }, {
        default: () => [createVNode(Y2, {
          class: (0, import_classnames2.default)({
            reverse: h7
          }),
          width: e,
          height: t4
        }, {
          default: () => [createVNode("polygon", {
            fill: c5,
            points: `
                  10, 22 ${e - 22}, 22 ${e - 22}, ${t4 - 86} ${e - 84}, ${t4 - 24} 10, ${t4 - 24}`
          }, null), createVNode("polyline", {
            class: "stroke-width1",
            stroke: n5[0],
            points: `8, 5 ${e - 5}, 5 ${e - 5}, ${t4 - 100}
                  ${e - 100}, ${t4 - 5} 8, ${t4 - 5} 8, 5`
          }, null), createVNode("polyline", {
            class: "stroke-width1",
            stroke: n5[1],
            points: `3, 5 ${e - 20}, 5 ${e - 20}, ${t4 - 60}
                  ${e - 74}, ${t4 - 5} 3, ${t4 - 5} 3, 5`
          }, null), createVNode("polyline", {
            class: "stroke-width5",
            stroke: n5[1],
            points: `50, 13 ${e - 35}, 13`
          }, null), createVNode("polyline", {
            class: "stroke-width2",
            stroke: n5[1],
            points: `15, 20 ${e - 35}, 20`
          }, null), createVNode("polyline", {
            class: "stroke-width2",
            stroke: n5[1],
            points: `15, ${t4 - 20} ${e - 110}, ${t4 - 20}`
          }, null), createVNode("polyline", {
            class: "stroke-width5",
            stroke: n5[1],
            points: `15, ${t4 - 13} ${e - 110}, ${t4 - 13}`
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var i3;
            return [createVNode("slot", null, [(i3 = o2.default) == null ? void 0 : i3.call(o2)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-6/index.mjs
var B = ["rgba(255, 255, 255, 0.35)", "gray"];
var h = t.svg`
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-width: 1;
`("border-svg-container");
var F2 = s(defineComponent({
  name: "BorderBox6",
  props: m2(),
  setup(i3, {
    slots: t4
  }) {
    const {
      domRef: p9,
      domSize: s4
    } = f2();
    return () => {
      const {
        color: u6,
        backgroundColor: c5
      } = i3, {
        width: l5,
        height: e
      } = s4, n5 = a(B, u6);
      return createVNode(i2, {
        class: x("border-box-6"),
        ref: (r3) => p9.value = r3.$el
      }, {
        default: () => [createVNode(h, {
          width: l5,
          height: e
        }, {
          default: () => [createVNode("polygon", {
            fill: c5,
            points: `
              9, 7 ${l5 - 9}, 7 ${l5 - 9}, ${e - 7} 9, ${e - 7}`
          }, null), createVNode("circle", {
            fill: n5[1],
            cx: "5",
            cy: "5",
            r: "2"
          }, null), createVNode("circle", {
            fill: n5[1],
            cx: l5 - 5,
            cy: "5",
            r: "2"
          }, null), createVNode("circle", {
            fill: n5[1],
            cx: l5 - 5,
            cy: e - 5,
            r: "2"
          }, null), createVNode("circle", {
            fill: n5[1],
            cx: "5",
            cy: e - 5,
            r: "2"
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `10, 4 ${l5 - 10}, 4`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `10, ${e - 4} ${l5 - 10}, ${e - 4}`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `5, 70 5, ${e - 70}`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `${l5 - 5}, 70 ${l5 - 5}, ${e - 70}`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: "3, 10, 3, 50"
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: "7, 30 7, 80"
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `${l5 - 3}, 10 ${l5 - 3}, 50`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `${l5 - 7}, 30 ${l5 - 7}, 80`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `3, ${e - 10} 3, ${e - 50}`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `7, ${e - 30} 7, ${e - 80}`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `${l5 - 3}, ${e - 10} ${l5 - 3}, ${e - 50}`
          }, null), createVNode("polyline", {
            stroke: n5[0],
            points: `${l5 - 7}, ${e - 30} ${l5 - 7}, ${e - 80}`
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var r3;
            return [createVNode("slot", null, [(r3 = t4.default) == null ? void 0 : r3.call(t4)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-7/index.mjs
var g4 = ["rgba(128,128,128,0.3)", "rgba(128,128,128,0.5)"];
var B2 = t.svg`
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-linecap: round;
}
.__STYLED__ .stroke-width2 {
  stroke-width: 2px;
}
.__STYLED__ .stroke-width5 {
  stroke-width: 5px;
`("border-svg-container");
var E2 = s(defineComponent({
  name: "BorderBox7",
  props: m2(),
  setup(n5, {
    slots: s4
  }) {
    const {
      domRef: i3,
      domSize: d8
    } = f2();
    return () => {
      const {
        color: p9,
        backgroundColor: a5
      } = n5, {
        width: o2,
        height: t4
      } = d8, r3 = a(g4, p9);
      return createVNode(i2, {
        class: x("border-box-7"),
        ref: (l5) => i3.value = l5.$el,
        style: {
          boxShadow: `inset 0 0 40px ${r3[0]}`,
          border: `1px solid ${r3[0]}`,
          backgroundColor: a5
        }
      }, {
        default: () => [createVNode(B2, {
          width: o2,
          height: t4
        }, {
          default: () => [createVNode("polyline", {
            class: "stroke-width2",
            stroke: r3[0],
            points: "0, 25 0, 0 25, 0"
          }, null), createVNode("polyline", {
            class: "stroke-width2",
            stroke: r3[0],
            points: `${o2 - 25}, 0 ${o2}, 0 ${o2}, 25`
          }, null), createVNode("polyline", {
            class: "stroke-width2",
            stroke: r3[0],
            points: `${o2 - 25}, ${t4} ${o2}, ${t4} ${o2}, ${t4 - 25}`
          }, null), createVNode("polyline", {
            class: "stroke-width2",
            stroke: r3[0],
            points: `0, ${t4 - 25} 0, ${t4} 25, ${t4}`
          }, null), createVNode("polyline", {
            class: "stroke-width5",
            stroke: r3[1],
            points: "0, 10 0, 0 10, 0"
          }, null), createVNode("polyline", {
            class: "stroke-width5",
            stroke: r3[1],
            points: `${o2 - 10}, 0 ${o2}, 0 ${o2}, 10`
          }, null), createVNode("polyline", {
            class: "stroke-width5",
            stroke: r3[1],
            points: `${o2 - 10}, ${t4} ${o2}, ${t4} ${o2}, ${t4 - 10}`
          }, null), createVNode("polyline", {
            class: "stroke-width5",
            stroke: r3[1],
            points: `0, ${t4 - 10} 0, ${t4} 10, ${t4}`
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var l5;
            return [createVNode("slot", null, [(l5 = s4.default) == null ? void 0 : l5.call(s4)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/hooks/useUuid.mjs
function t2() {
  return readonly(reactive({ id: uniqueId_default("datav_uuid") }));
}

// node_modules/@dataview/datav-vue3/es/components/border-box-8/index.mjs
var C2 = Object.defineProperty;
var y3 = Object.defineProperties;
var L = Object.getOwnPropertyDescriptors;
var c3 = Object.getOwnPropertySymbols;
var v2 = Object.prototype.hasOwnProperty;
var w3 = Object.prototype.propertyIsEnumerable;
var $4 = (r3, o2, t4) => o2 in r3 ? C2(r3, o2, { enumerable: true, configurable: true, writable: true, value: t4 }) : r3[o2] = t4;
var h2 = (r3, o2) => {
  for (var t4 in o2 || (o2 = {}))
    v2.call(o2, t4) && $4(r3, t4, o2[t4]);
  if (c3)
    for (var t4 of c3(o2))
      w3.call(o2, t4) && $4(r3, t4, o2[t4]);
  return r3;
};
var x4 = (r3, o2) => y3(r3, L(o2));
var U = ["#235fa7", "#4fd2dd"];
var V = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
`("border-svg-container");
var j = () => x4(h2({}, m2()), {
  dur: {
    type: Number,
    default: 3
  },
  reverse: {
    type: Boolean,
    default: false
  }
});
var W = s(defineComponent({
  name: "BorderBox8",
  props: j(),
  setup(r3, {
    slots: o2
  }) {
    const {
      domRef: t4,
      domSize: g6
    } = f2(), d8 = t2();
    return () => {
      const {
        color: b2,
        backgroundColor: B4,
        dur: s4,
        reverse: k5
      } = r3, {
        width: n5,
        height: l5
      } = g6, u6 = a(U, b2), a5 = `border-box-8-path-${d8.id}`, f10 = `border-box-8-gradient-${d8.id}`, p9 = `border-box-8-mask-${d8.id}`, m8 = k5 ? `M 2.5, 2.5 L 2.5, ${l5 - 2.5} L ${n5 - 2.5}, ${l5 - 2.5} L ${n5 - 2.5}, 2.5 L 2.5, 2.5` : `M2.5, 2.5 L${n5 - 2.5}, 2.5 L${n5 - 2.5}, ${l5 - 2.5} L2.5, ${l5 - 2.5} L2.5, 2.5`;
      return createVNode(i2, {
        class: x("border-box-8"),
        ref: (i3) => t4.value = i3.$el
      }, {
        default: () => [createVNode(V, {
          width: n5,
          height: l5
        }, {
          default: () => [createVNode("defs", null, [createVNode("path", {
            id: a5,
            d: m8,
            fill: "transparent"
          }, null), createVNode("radialGradient", {
            id: f10,
            cx: "50%",
            cy: "50%",
            r: "50%"
          }, [createVNode("stop", {
            offset: "0%",
            "stop-color": "#fff",
            "stop-opacity": "1"
          }, null), createVNode("stop", {
            offset: "100%",
            "stop-color": "#fff",
            "stop-opacity": "0"
          }, null)]), createVNode("mask", {
            id: p9
          }, [createVNode("circle", {
            cx: "0",
            cy: "0",
            r: "150",
            fill: `url(#${f10})`
          }, [createVNode("animateMotion", {
            dur: `${s4}s`,
            path: m8,
            rotate: "auto",
            repeatCount: "indefinite"
          }, null)])])]), createVNode("polygon", {
            fill: B4,
            points: `5, 5 ${n5 - 5}, 5 ${n5 - 5} ${l5 - 5} 5, ${l5 - 5}`
          }, null), createVNode("use", {
            stroke: u6[0],
            "stroke-width": "1",
            "xlink:href": `#${a5}`
          }, null), createVNode("use", {
            stroke: u6[1],
            "stroke-width": "3",
            "xlink:href": `#${a5}`,
            mask: `url(#${p9})`
          }, [createVNode("animate", {
            attributeName: "stroke-dasharray",
            from: `0, ${length}`,
            to: `${length}, 0`,
            dur: `${s4}s`,
            repeatCount: "indefinite"
          }, null)])]
        }), createVNode(r, null, {
          default: () => {
            var i3;
            return [createVNode("slot", null, [(i3 = o2.default) == null ? void 0 : i3.call(o2)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-9/index.mjs
var w4 = ["#11eefd", "#0078d2"];
var v3 = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
`("border-svg-container");
var U2 = s(defineComponent({
  name: "BorderBox9",
  props: m2(),
  setup(f10, {
    slots: r3
  }) {
    const {
      domRef: a5,
      domSize: d8
    } = f2(), i3 = t2();
    return () => {
      const {
        color: u6,
        backgroundColor: p9
      } = f10, {
        width: e,
        height: o2
      } = d8, l5 = a(w4, u6), $10 = `border-box-9-gradient-${i3.id}`, s4 = `border-box-9-mask-${i3.id}`;
      return createVNode(i2, {
        class: x("border-box-9"),
        ref: (n5) => a5.value = n5.$el
      }, {
        default: () => [createVNode(v3, {
          width: e,
          height: o2
        }, {
          default: () => [createVNode("defs", null, [createVNode("linearGradient", {
            id: $10,
            x1: "0%",
            y1: "0%",
            x2: "100%",
            y2: "100%"
          }, [createVNode("animate", {
            attributeName: "x1",
            values: "0%;100%;0%",
            dur: "10s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null), createVNode("animate", {
            attributeName: "x2",
            values: "100%;0%;100%",
            dur: "10s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null), createVNode("stop", {
            offset: "0%",
            "stop-color": l5[0]
          }, [createVNode("animate", {
            attributeName: "stop-color",
            values: `${l5[0]};${l5[1]};${l5[0]}`,
            dur: "10s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("stop", {
            offset: "100%",
            "stop-color": l5[1]
          }, [createVNode("animate", {
            attributeName: "stop-color",
            values: `${l5[1]};${l5[0]};${l5[1]}`,
            dur: "10s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)])]), createVNode("mask", {
            id: s4
          }, [createVNode("polyline", {
            stroke: "#fff",
            "stroke-width": "3",
            fill: "transparent",
            points: `8, ${o2 * 0.4} 8, 3, ${e * 0.4 + 7}, 3`
          }, null), createVNode("polyline", {
            fill: "#fff",
            points: `
                      8, ${o2 * 0.15} 8, 3, ${e * 0.1 + 7}, 3
                      ${e * 0.1}, 8 14, 8 14, ${o2 * 0.15 - 7}
                    `
          }, null), createVNode("polyline", {
            stroke: "#fff",
            "stroke-width": "3",
            fill: "transparent",
            points: `${e * 0.5}, 3 ${e - 3}, 3, ${e - 3}, ${o2 * 0.25}`
          }, null), createVNode("polyline", {
            fill: "#fff",
            points: `
                      ${e * 0.52}, 3 ${e * 0.58}, 3
                      ${e * 0.58 - 7}, 9 ${e * 0.52 + 7}, 9
                    `
          }, null), createVNode("polyline", {
            fill: "#fff",
            points: `
                      ${e * 0.9}, 3 ${e - 3}, 3 ${e - 3}, ${o2 * 0.1}
                      ${e - 9}, ${o2 * 0.1 - 7} ${e - 9}, 9 ${e * 0.9 + 7}, 9
                    `
          }, null), createVNode("polyline", {
            stroke: "#fff",
            "stroke-width": "3",
            fill: "transparent",
            points: `8, ${o2 * 0.5} 8, ${o2 - 3} ${e * 0.3 + 7}, ${o2 - 3}`
          }, null), createVNode("polyline", {
            fill: "#fff",
            points: `
                      8, ${o2 * 0.55} 8, ${o2 * 0.7}
                      2, ${o2 * 0.7 - 7} 2, ${o2 * 0.55 + 7}
                    `
          }, null), createVNode("polyline", {
            stroke: "#fff",
            "stroke-width": "3",
            fill: "transparent",
            points: `${e * 0.35}, ${o2 - 3} ${e - 3}, ${o2 - 3} ${e - 3}, ${o2 * 0.35}`
          }, null), createVNode("polyline", {
            fill: "#fff",
            points: `
                      ${e * 0.92}, ${o2 - 3} ${e - 3}, ${o2 - 3} ${e - 3}, ${o2 * 0.8} ${e - 9}, ${o2 * 0.8 + 7} ${e - 9}, ${o2 - 9} ${e * 0.92 + 7}, ${o2 - 9}`
          }, null)])]), createVNode("polygon", {
            fill: p9,
            points: `
                  15, 9 ${e * 0.1 + 1}, 9 ${e * 0.1 + 4}, 6 ${e * 0.52 + 2}, 6
                  ${e * 0.52 + 6}, 10 ${e * 0.58 - 7}, 10 ${e * 0.58 - 2}, 6
                  ${e * 0.9 + 2}, 6 ${e * 0.9 + 6}, 10 ${e - 10}, 10 ${e - 10}, ${o2 * 0.1 - 6}
                  ${e - 6}, ${o2 * 0.1 - 1} ${e - 6}, ${o2 * 0.8 + 1} ${e - 10}, ${o2 * 0.8 + 6}
                  ${e - 10}, ${o2 - 10} ${e * 0.92 + 7}, ${o2 - 10}  ${e * 0.92 + 2}, ${o2 - 6}
                  11, ${o2 - 6} 11, ${o2 * 0.15 - 2} 15, ${o2 * 0.15 - 7}
                `
          }, null), createVNode("rect", {
            x: "0",
            y: "0",
            width: e,
            height: o2,
            fill: `url(#${$10})`,
            mask: `url(#${s4})`
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var n5;
            return [createVNode("slot", null, [(n5 = r3.default) == null ? void 0 : n5.call(r3)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-10/index.mjs
var C3 = ["#1d48c4", "#d3e1f8"];
var $5 = ["left-top", "right-top", "left-bottom", "right-bottom"];
var d3 = t.svg`
  position: absolute;
  display: block;
}
.__STYLED__.right-top {
  right: 0px;
  transform: rotateY(180deg);
}
.__STYLED__.left-bottom {
  bottom: 0px;
  transform: rotateX(180deg);
}
.__STYLED__.right-bottom {
  right: 0px;
  bottom: 0px;
  transform: rotateX(180deg) rotateY(180deg);
`("border-svg-container");
var E3 = s(defineComponent({
  name: "BorderBox10",
  props: m2(),
  setup(i3, {
    slots: n5
  }) {
    const {
      domRef: p9,
      domSize: m8
    } = f2();
    return () => {
      const {
        width: r3,
        height: e
      } = m8, {
        backgroundColor: a5,
        color: s4
      } = i3, l5 = a(C3, s4);
      return createVNode(i2, {
        class: x("border-box-10"),
        ref: (o2) => p9.value = o2.$el,
        style: {
          boxShadow: `inset 0 0 25px 3px ${l5[0]}`
        }
      }, {
        default: () => [createVNode(d3, {
          width: r3,
          height: e
        }, {
          default: () => [createVNode("polygon", {
            fill: a5,
            points: `
                  4, 0 ${r3 - 4}, 0 ${r3}, 4 ${r3}, ${e - 4} ${r3 - 4}, ${e}
                  4, ${e} 0, ${e - 4} 0, 4
                `
          }, null)]
        }), $5.map((o2) => createVNode(d3, {
          width: "150px",
          height: "150px",
          key: o2,
          class: o2
        }, {
          default: () => [createVNode("polygon", {
            fill: l5[1],
            points: "40, 0 5, 0 0, 5 0, 16 3, 19 3, 7 7, 3 35, 3"
          }, null)]
        })), createVNode(r, null, {
          default: () => {
            var o2;
            return [createVNode("slot", null, [(o2 = n5.default) == null ? void 0 : o2.call(n5)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-11/index.mjs
var import_color = __toESM(require_lib(), 1);
var C4 = Object.defineProperty;
var x5 = Object.defineProperties;
var B3 = Object.getOwnPropertyDescriptors;
var p3 = Object.getOwnPropertySymbols;
var v4 = Object.prototype.hasOwnProperty;
var w5 = Object.prototype.propertyIsEnumerable;
var s2 = ($10, i3, n5) => i3 in $10 ? C4($10, i3, { enumerable: true, configurable: true, writable: true, value: n5 }) : $10[i3] = n5;
var f5 = ($10, i3) => {
  for (var n5 in i3 || (i3 = {}))
    v4.call(i3, n5) && s2($10, n5, i3[n5]);
  if (p3)
    for (var n5 of p3(i3))
      w5.call(i3, n5) && s2($10, n5, i3[n5]);
  return $10;
};
var d4 = ($10, i3) => x5($10, B3(i3));
var m3 = ["#8aaafb", "#1f33a2"];
var R = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-width: 1;
`("border-svg-container");
var W2 = () => d4(f5({}, m2()), {
  titleWidth: {
    type: Number,
    default: 250
  },
  title: {
    type: String,
    default: ""
  }
});
var J = s(defineComponent({
  name: "BorderBox11",
  props: W2(),
  setup($10, {
    slots: i3
  }) {
    const {
      domRef: n5,
      domSize: c5
    } = f2(), g6 = t2();
    return () => {
      const {
        color: y6,
        backgroundColor: b2,
        titleWidth: t4,
        title: h7
      } = $10, {
        width: e,
        height: l5
      } = c5, r3 = a(m3, y6), a5 = `border-box-11-filterId-${g6}`;
      return createVNode(i2, {
        class: x("border-box-11"),
        ref: (u6) => n5.value = u6.$el
      }, {
        default: () => [createVNode(R, {
          width: e,
          height: l5
        }, {
          default: () => [createVNode("defs", null, [createVNode("filter", {
            id: a5,
            height: "150%",
            width: "150%",
            x: "-25%",
            y: "-25%"
          }, [createVNode("feMorphology", {
            operator: "dilate",
            radius: "2",
            in: "SourceAlpha",
            result: "thicken"
          }, null), createVNode("feGaussianBlur", {
            in: "thicken",
            stdDeviation: "3",
            result: "blurred"
          }, null), createVNode("feFlood", {
            "flood-color": r3[1],
            result: "glowColor"
          }, null), createVNode("feComposite", {
            in: "glowColor",
            in2: "blurred",
            operator: "in",
            result: "softGlowColored"
          }, null), createVNode("feMerge", null, [createVNode("feMergeNode", {
            in: "softGlowColored"
          }, null), createVNode("feMergeNode", {
            in: "SourceGraphic"
          }, null)])])]), createVNode("polygon", {
            fill: b2,
            points: `
                  20, 32 ${e * 0.5 - t4 / 2}, 32 ${e * 0.5 - t4 / 2 + 20}, 53
                  ${e * 0.5 + t4 / 2 - 20}, 53 ${e * 0.5 + t4 / 2}, 32
                  ${e - 20}, 32 ${e - 8}, 48 ${e - 8}, ${l5 - 25} ${e - 20}, ${l5 - 8}
                  20, ${l5 - 8} 8, ${l5 - 25} 8, 50
                `
          }, null), createVNode("polyline", {
            stroke: r3[0],
            filter: `url(#${a5})`,
            points: `
                  ${(e - t4) / 2}, 30
                  20, 30 7, 50 7, ${50 + (l5 - 167) / 2}
                  13, ${55 + (l5 - 167) / 2} 13, ${135 + (l5 - 167) / 2}
                  7, ${140 + (l5 - 167) / 2} 7, ${l5 - 27}
                  20, ${l5 - 7} ${e - 20}, ${l5 - 7} ${e - 7}, ${l5 - 27}
                  ${e - 7}, ${140 + (l5 - 167) / 2} ${e - 13}, ${135 + (l5 - 167) / 2}
                  ${e - 13}, ${55 + (l5 - 167) / 2} ${e - 7}, ${50 + (l5 - 167) / 2}
                  ${e - 7}, 50 ${e - 20}, 30 ${(e + t4) / 2}, 30
                  ${(e + t4) / 2 - 20}, 7 ${(e - t4) / 2 + 20}, 7
                  ${(e - t4) / 2}, 30 ${(e - t4) / 2 + 20}, 52
                  ${(e + t4) / 2 - 20}, 52 ${(e + t4) / 2}, 30
                `
          }, null), createVNode("polygon", {
            stroke: r3[0],
            fill: "transparent",
            points: `
                  ${(e + t4) / 2 - 5}, 30 ${(e + t4) / 2 - 21}, 11
                  ${(e + t4) / 2 - 27}, 11 ${(e + t4) / 2 - 8}, 34
                `
          }, null), createVNode("polygon", {
            stroke: r3[0],
            fill: "transparent",
            points: `
                  ${(e - t4) / 2 + 5}, 30 ${(e - t4) / 2 + 22}, 49
                  ${(e - t4) / 2 + 28}, 49 ${(e - t4) / 2 + 8}, 26
                `
          }, null), createVNode("polygon", {
            stroke: r3[0],
            fill: (0, import_color.fade)(r3[1] || m3[1], 30),
            filter: `url(#${a5})`,
            points: `
                  ${(e + t4) / 2 - 11}, 37 ${(e + t4) / 2 - 32}, 11
                  ${(e - t4) / 2 + 23}, 11 ${(e - t4) / 2 + 11}, 23
                  ${(e - t4) / 2 + 33}, 49 ${(e + t4) / 2 - 22}, 49
                `
          }, null), createVNode("polygon", {
            filter: `url(#${a5})`,
            fill: r3[0],
            opacity: "1",
            points: `
                  ${(e - t4) / 2 - 10}, 37 ${(e - t4) / 2 - 31}, 37
                  ${(e - t4) / 2 - 25}, 46 ${(e - t4) / 2 - 4}, 46
                `
          }, [createVNode("animate", {
            attributeName: "opacity",
            values: "1;0.7;1",
            dur: "2s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("polygon", {
            filter: `url(#${a5})`,
            fill: r3[0],
            opacity: "0.7",
            points: `
                  ${(e - t4) / 2 - 40}, 37 ${(e - t4) / 2 - 61}, 37
                  ${(e - t4) / 2 - 55}, 46 ${(e - t4) / 2 - 34}, 46
                `
          }, [createVNode("animate", {
            attributeName: "opacity",
            values: "0.7;0.4;0.7",
            dur: "2s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("polygon", {
            filter: `url(#${a5})`,
            fill: r3[0],
            opacity: "0.5",
            points: `
                  ${(e - t4) / 2 - 70}, 37 ${(e - t4) / 2 - 91}, 37
                  ${(e - t4) / 2 - 85}, 46 ${(e - t4) / 2 - 64}, 46
                `
          }, [createVNode("animate", {
            attributeName: "opacity",
            values: "0.5;0.2;0.5",
            dur: "2s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("polygon", {
            filter: `url(#${a5})`,
            fill: r3[0],
            opacity: "1",
            points: `
                  ${(e + t4) / 2 + 30}, 37 ${(e + t4) / 2 + 9}, 37
                  ${(e + t4) / 2 + 3}, 46 ${(e + t4) / 2 + 24}, 46
                `
          }, [createVNode("animate", {
            attributeName: "opacity",
            values: "1;0.7;1",
            dur: "2s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("polygon", {
            filter: `url(#${a5})`,
            fill: r3[0],
            opacity: "0.7",
            points: `
                  ${(e + t4) / 2 + 60}, 37 ${(e + t4) / 2 + 39}, 37
                  ${(e + t4) / 2 + 33}, 46 ${(e + t4) / 2 + 54}, 46
                `
          }, [createVNode("animate", {
            attributeName: "opacity",
            values: "0.7;0.4;0.7",
            dur: "2s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("polygon", {
            filter: `url(#${a5})`,
            fill: r3[0],
            opacity: "0.5",
            points: `
                  ${(e + t4) / 2 + 90}, 37 ${(e + t4) / 2 + 69}, 37
                  ${(e + t4) / 2 + 63}, 46 ${(e + t4) / 2 + 84}, 46
                `
          }, [createVNode("animate", {
            attributeName: "opacity",
            values: "0.5;0.2;0.5",
            dur: "2s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("text", {
            class: "dv-border-box-11-title",
            x: `${e / 2}`,
            y: "32",
            fill: "#fff",
            "font-size": "18",
            "text-anchor": "middle",
            "dominant-baseline": "middle"
          }, [h7]), createVNode("polygon", {
            fill: r3[0],
            filter: `url(#${a5})`,
            points: `
                  7, ${53 + (l5 - 167) / 2} 11, ${57 + (l5 - 167) / 2}
                  11, ${133 + (l5 - 167) / 2} 7, ${137 + (l5 - 167) / 2}
                `
          }, null), createVNode("polygon", {
            fill: r3[0],
            filter: `url(#${a5})`,
            points: `
                  ${e - 7}, ${53 + (l5 - 167) / 2} ${e - 11}, ${57 + (l5 - 167) / 2}
                  ${e - 11}, ${133 + (l5 - 167) / 2} ${e - 7}, ${137 + (l5 - 167) / 2}
                `
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var u6;
            return [createVNode("slot", null, [(u6 = i3.default) == null ? void 0 : u6.call(i3)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-12/index.mjs
var import_color2 = __toESM(require_lib(), 1);
var n = ["#2e6099", "#7ce7fd"];
var M = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
`("border-svg-container");
var R2 = s(defineComponent({
  name: "BorderBox12",
  props: m2(),
  setup(s4, {
    slots: u6
  }) {
    const {
      domRef: a5,
      domSize: f10
    } = f2(), $10 = t2();
    return () => {
      const {
        color: p9,
        backgroundColor: h7
      } = s4, {
        width: o2,
        height: r3
      } = f10, t4 = a(n, p9), l5 = `border-box-12-filterId-${$10}`;
      return createVNode(i2, {
        class: x("border-box-12"),
        ref: (i3) => a5.value = i3.$el
      }, {
        default: () => [createVNode(M, {
          width: o2,
          height: r3
        }, {
          default: () => [createVNode("defs", null, [createVNode("filter", {
            id: l5,
            height: "150%",
            width: "150%",
            x: "-25%",
            y: "-25%"
          }, [createVNode("feMorphology", {
            operator: "dilate",
            radius: "1",
            in: "SourceAlpha",
            result: "thicken"
          }, null), createVNode("feGaussianBlur", {
            in: "thicken",
            stdDeviation: "2",
            result: "blurred"
          }, null), createVNode("feFlood", {
            "flood-color": (0, import_color2.fade)(t4[1] || n[1], 70),
            result: "glowColor"
          }, [createVNode("animate", {
            attributeName: "flood-color",
            values: `
                        ${(0, import_color2.fade)(t4[1] || n[1], 70)};
                        ${(0, import_color2.fade)(t4[1] || n[1], 30)};
                        ${(0, import_color2.fade)(t4[1] || n[1], 70)};
                      `,
            dur: "3s",
            begin: "0s",
            repeatCount: "indefinite"
          }, null)]), createVNode("feComposite", {
            in: "glowColor",
            in2: "blurred",
            operator: "in",
            result: "softGlowColored"
          }, null), createVNode("feMerge", null, [createVNode("feMergeNode", {
            in: "softGlowColored"
          }, null), createVNode("feMergeNode", {
            in: "SourceGraphic"
          }, null)])])]), o2 && r3 && createVNode("path", {
            fill: h7,
            "stroke-width": "2",
            stroke: t4[0],
            d: `
                    M15 5 L ${o2 - 15} 5 Q ${o2 - 5} 5, ${o2 - 5} 15
                    L ${o2 - 5} ${r3 - 15} Q ${o2 - 5} ${r3 - 5}, ${o2 - 15} ${r3 - 5}
                    L 15, ${r3 - 5} Q 5 ${r3 - 5} 5 ${r3 - 15} L 5 15
                    Q 5 5 15 5
                  `
          }, null), createVNode("path", {
            "stroke-width": "2",
            fill: "transparent",
            "stroke-linecap": "round",
            filter: `url(#${l5})`,
            stroke: t4[1],
            d: "M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
          }, null), createVNode("path", {
            "stroke-width": "2",
            fill: "transparent",
            "stroke-linecap": "round",
            filter: `url(#${l5})`,
            stroke: t4[1],
            d: `M ${o2 - 20} 5 L ${o2 - 15} 5 Q ${o2 - 5} 5 ${o2 - 5} 15 L ${o2 - 5} 20`
          }, null), createVNode("path", {
            "stroke-width": "2",
            fill: "transparent",
            "stroke-linecap": "round",
            filter: `url(#${l5})`,
            stroke: t4[1],
            d: `
                  M ${o2 - 20} ${r3 - 5} L ${o2 - 15} ${r3 - 5}
                  Q ${o2 - 5} ${r3 - 5} ${o2 - 5} ${r3 - 15}
                  L ${o2 - 5} ${r3 - 20}
                `
          }, null), createVNode("path", {
            "stroke-width": "2",
            fill: "transparent",
            "stroke-linecap": "round",
            filter: `url(#${l5})`,
            stroke: t4[1],
            d: `
                  M 20 ${r3 - 5} L 15 ${r3 - 5}
                  Q 5 ${r3 - 5} 5 ${r3 - 15}
                  L 5 ${r3 - 20}
                `
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var i3;
            return [createVNode("slot", null, [(i3 = u6.default) == null ? void 0 : i3.call(u6)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/border-box-13/index.mjs
var C5 = ["#6586ec", "#2cf7fe"];
var x6 = t.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
`("border-svg-container");
var F3 = s(defineComponent({
  name: "BorderBox13",
  props: m2(),
  setup(a5, {
    slots: l5
  }) {
    const {
      domRef: d8,
      domSize: i3
    } = f2();
    return () => {
      const {
        color: s4,
        backgroundColor: p9
      } = a5, {
        width: r3,
        height: e
      } = i3, t4 = a(C5, s4);
      return createVNode(i2, {
        class: x("border-box-13"),
        ref: (n5) => d8.value = n5.$el
      }, {
        default: () => [createVNode(x6, {
          width: r3,
          height: e
        }, {
          default: () => [createVNode("path", {
            fill: p9,
            stroke: t4[0],
            d: `
                  M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
                  L ${r3 - 20} 10 L ${r3 - 5} 25
                  L ${r3 - 5} ${e - 5} L 20 ${e - 5}
                  L 5 ${e - 20} L 5 20
                `
          }, null), createVNode("path", {
            fill: "transparent",
            "stroke-width": "3",
            "stroke-linecap": "round",
            "stroke-dasharray": "10, 5",
            stroke: t4[0],
            d: "M 16 9 L 61 9"
          }, null), createVNode("path", {
            fill: "transparent",
            stroke: t4[1],
            d: "M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
          }, null), createVNode("path", {
            fill: "transparent",
            stroke: t4[1],
            d: `M ${r3 - 5} ${e - 30} L ${r3 - 5} ${e - 5} L ${r3 - 30} ${e - 5}`
          }, null)]
        }), createVNode(r, null, {
          default: () => {
            var n5;
            return [createVNode("slot", null, [(n5 = l5.default) == null ? void 0 : n5.call(l5)])];
          }
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/utils/decoration.mjs
function n2() {
  return {
    color: {
      type: c2(Array),
      default: () => []
    }
  };
}
function u2() {
  return {
    reverse: {
      type: Boolean,
      default: false
    }
  };
}
function a4(e) {
  return {
    duration: {
      type: Number,
      default: e
    }
  };
}

// node_modules/@dataview/datav-vue3/es/components/decoration-1/index.mjs
function D2({
  width: u6,
  height: d8,
  rowPoints: a5,
  rowCount: l5
}) {
  const c5 = u6 / (a5 + 1), m8 = d8 / (l5 + 1);
  return new Array(l5).fill(0).map((i3, o2) => new Array(a5).fill(0).map((s4, h7) => [c5 * (h7 + 1), m8 * (o2 + 1)])).reduce((i3, o2) => [...i3, ...o2], []);
}
var R3 = ["#fff", "#0de7c2"];
var f6 = 200;
var p4 = 50;
var g5 = 20;
var z3 = 4;
var t3 = 2.5;
var C6 = t3 / 2;
var v5 = D2({
  width: f6,
  height: p4,
  rowPoints: g5,
  rowCount: z3
});
var n3 = v5[g5 * 2 - 1];
var r2 = v5[g5 * 2 - 3];
var L2 = t.div`
  width: 100%;
  height: 100%;
}
.__STYLED__ svg {
  transform-origin: left top;
`("decoration-1");
var I = s(defineComponent({
  name: "Decoration1",
  props: n2(),
  setup(u6) {
    const {
      domRef: d8,
      domSize: a5
    } = f2();
    return () => {
      const {
        color: l5
      } = u6, {
        width: c5,
        height: m8
      } = a5, i3 = u(R3, l5), o2 = {
        transform: `scale(${c5 / f6},${m8 / p4})`
      };
      return createVNode(L2, {
        ref: (s4) => d8.value = s4.$el
      }, {
        default: () => [createVNode("svg", {
          width: f6,
          height: p4,
          style: o2
        }, [v5.map(([s4, h7], $10) => {
          const w7 = s4 - C6, y6 = h7 - C6;
          return Math.random() > 0.6 ? createVNode("rect", {
            key: $10,
            x: w7,
            y: y6,
            width: t3,
            height: t3,
            fill: i3[0]
          }, [Math.random() > 0.6 && createVNode("animate", {
            attributeName: "fill",
            values: `${i3[0]};transparent`,
            dur: "1s",
            begin: Math.random() * 2,
            repeatCount: "indefinite"
          }, null)]) : null;
        }), createVNode("rect", {
          fill: i3[1],
          x: n3[0] - t3,
          y: n3[1] - t3,
          width: t3 * 2,
          height: t3 * 2
        }, [createVNode("animate", {
          attributeName: "width",
          values: `0;${t3 * 2}`,
          dur: "2s",
          repeatCount: "indefinite"
        }, null), createVNode("animate", {
          attributeName: "height",
          values: `0;${t3 * 2}`,
          dur: "2s",
          repeatCount: "indefinite"
        }, null), createVNode("animate", {
          attributeName: "x",
          values: `${n3[0]};${n3[0] - t3}`,
          dur: "2s",
          repeatCount: "indefinite"
        }, null), createVNode("animate", {
          attributeName: "y",
          values: `${n3[1]};${n3[1] - t3}`,
          dur: "2s",
          repeatCount: "indefinite"
        }, null)]), createVNode("rect", {
          fill: i3[1],
          x: r2[0] - t3,
          y: r2[1] - t3,
          width: t3 * 2,
          height: t3 * 2
        }, [createVNode("animate", {
          attributeName: "width",
          values: "0;40;0",
          dur: "2s",
          repeatCount: "indefinite"
        }, null), createVNode("animate", {
          attributeName: "x",
          values: `${r2[0]};${r2[0] - 40};${r2[0]}`,
          dur: "2s",
          repeatCount: "indefinite"
        }, null)])])]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/decoration-2/index.mjs
var w6 = Object.defineProperty;
var f7 = Object.getOwnPropertySymbols;
var x7 = Object.prototype.hasOwnProperty;
var C7 = Object.prototype.propertyIsEnumerable;
var h3 = (o2, e, t4) => e in o2 ? w6(o2, e, { enumerable: true, configurable: true, writable: true, value: t4 }) : o2[e] = t4;
var s3 = (o2, e) => {
  for (var t4 in e || (e = {}))
    x7.call(e, t4) && h3(o2, t4, e[t4]);
  if (f7)
    for (var t4 of f7(e))
      C7.call(e, t4) && h3(o2, t4, e[t4]);
  return o2;
};
var $6 = ["#3faacb", "#fff"];
function z4() {
  return s3(s3(s3({}, n2()), u2()), a4(6));
}
function M2(o2, e, t4) {
  return o2 ? {
    width: 1,
    height: t4,
    x: e / 2,
    y: 0
  } : {
    width: e,
    height: 1,
    x: 0,
    y: t4 / 2
  };
}
var T3 = t.div`
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
`("decoration-2");
var A = s(defineComponent({
  name: "Decoration2",
  props: z4(),
  setup(o2) {
    const {
      domRef: e,
      domSize: t4
    } = f2();
    return () => {
      const {
        width: n5,
        height: a5
      } = t4, {
        color: u6,
        reverse: i3,
        duration: c5
      } = o2, l5 = u($6, u6), {
        x: d8,
        y: m8,
        width: p9,
        height: g6
      } = M2(i3, n5, a5);
      return createVNode(T3, {
        ref: (y6) => e.value = y6.$el
      }, {
        default: () => [createVNode("svg", {
          width: n5,
          height: a5
        }, [createVNode("rect", {
          x: d8,
          y: m8,
          width: p9,
          height: g6,
          fill: l5[0]
        }, [createVNode("animate", {
          attributeName: i3 ? "height" : "width",
          from: "0",
          to: i3 ? a5 : n5,
          dur: `${c5}s`,
          calcMode: "spline",
          keyTimes: "0;1",
          keySplines: ".42,0,.58,1",
          repeatCount: "indefinite"
        }, null)]), createVNode("rect", {
          x: d8,
          y: m8,
          width: "1",
          height: "1",
          fill: l5[1]
        }, [createVNode("animate", {
          attributeName: i3 ? "y" : "x",
          from: "0",
          to: i3 ? a5 : n5,
          dur: `${c5}s`,
          calcMode: "spline",
          keyTimes: "0;1",
          keySplines: "0.42,0,0.58,1",
          repeatCount: "indefinite"
        }, null)])])]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/decoration-3/index.mjs
function P({
  width: i3,
  height: a5,
  rowPoints: e,
  rowCount: n5
}) {
  const s4 = i3 / (e + 1), c5 = a5 / (n5 + 1);
  return new Array(n5).fill(0).map((o2, t4) => new Array(e).fill(0).map((f10, l5) => [s4 * (l5 + 1), c5 * (t4 + 1)])).reduce((o2, t4) => [...o2, ...t4], []);
}
var D3 = ["#7acaec", "transparent"];
var m4 = 300;
var d6 = 35;
var M3 = 25;
var $7 = 2;
var h4 = 7;
var p5 = h4 / 2;
var x8 = P({
  width: m4,
  height: d6,
  rowPoints: M3,
  rowCount: $7
});
var z5 = t.div`
  width: 100%;
  height: 100%;
}
.__STYLED__ svg {
  transform-origin: left top;
`("decoration-3");
var j2 = s(defineComponent({
  name: "Decoration3",
  props: n2(),
  setup(i3) {
    const {
      domRef: a5,
      domSize: e
    } = f2();
    return () => {
      const {
        width: n5,
        height: s4
      } = e, {
        color: c5
      } = i3, o2 = u(D3, c5);
      return createVNode(z5, {
        ref: (t4) => a5.value = t4.$el
      }, {
        default: () => [createVNode("svg", {
          width: m4,
          height: d6,
          style: {
            transform: `scale(${n5 / m4},${s4 / d6})`
          }
        }, [x8.map(([t4, f10], l5) => {
          const u6 = t4 - p5, g6 = f10 - p5;
          return Math.random() > 0.6 ? createVNode("rect", {
            key: l5,
            x: u6,
            y: g6,
            width: h4,
            height: h4,
            fill: o2[0]
          }, [Math.random() > 0.6 && createVNode("animate", {
            attributeName: "fill",
            values: o2.join(";"),
            dur: `${Math.random() + 1}s`,
            begin: Math.random() * 2,
            repeatCount: "indefinite"
          }, null)]) : null;
        })])]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/decoration-4/index.mjs
var import_classnames3 = __toESM(require_classnames(), 1);
var y4 = Object.defineProperty;
var p6 = Object.getOwnPropertySymbols;
var D4 = Object.prototype.hasOwnProperty;
var x9 = Object.prototype.propertyIsEnumerable;
var m5 = (o2, e, t4) => e in o2 ? y4(o2, e, { enumerable: true, configurable: true, writable: true, value: t4 }) : o2[e] = t4;
var n4 = (o2, e) => {
  for (var t4 in e || (e = {}))
    D4.call(e, t4) && m5(o2, t4, e[t4]);
  if (p6)
    for (var t4 of p6(e))
      x9.call(e, t4) && m5(o2, t4, e[t4]);
  return o2;
};
function z6() {
  return n4(n4(n4({}, n2()), u2()), a4(3));
}
var E4 = ["rgba(255, 255, 255, 0.3)", "rgba(255, 255, 255, 0.3)"];
var L3 = t.div`
  position: relative;
  width: 100%;
  height: 100%;
`("decoration-4");
var T4 = t.div`
  display: flex;
  overflow: hidden;
  position: absolute;
  flex: 1;
}
.__STYLED__.normal {
  animation: ani-height ease-in-out infinite;
  left: 50%;
  margin-left: -2px;
}
.__STYLED__.reverse {
  animation: ani-width ease-in-out infinite;
  top: 50%;
  margin-top: -2px;
}
@keyframes ani-height {
  0% {
    height: 0%;
  }
  70% {
    height: 100%;
  }
  100% {
    height: 100%;
  }
}
@keyframes ani-width {
  0% {
    width: 0%;
  }
  70% {
    width: 100%;
  }
  100% {
    width: 100%;
  }
`("decoration-content");
var A2 = s(defineComponent({
  name: "Decoration4",
  props: z6(),
  setup(o2) {
    const {
      domRef: e,
      domSize: t4
    } = f2();
    return () => {
      const {
        width: s4,
        height: a5
      } = t4, {
        color: g6,
        reverse: r3,
        duration: u6
      } = o2, l5 = u(E4, g6), h7 = r3 ? s4 : 5, c5 = r3 ? 5 : a5, v7 = {
        width: `${h7}px`,
        height: `${c5}px`,
        animationDuration: `${u6}s`
      }, d8 = r3 ? `0, 2.5 ${s4}, 2.5` : `2.5, 0 2.5, ${a5}`;
      return createVNode(L3, {
        ref: (w7) => e.value = w7.$el
      }, {
        default: () => [createVNode(T4, {
          class: (0, import_classnames3.default)(r3 ? "reverse" : "normal"),
          style: v7
        }, {
          default: () => [createVNode("svg", {
            width: h7,
            height: c5
          }, [createVNode("polyline", {
            stroke: l5[0],
            points: d8
          }, null), createVNode("polyline", {
            class: "bold-line",
            stroke: l5[1],
            "stroke-width": "3",
            "stroke-dasharray": "20, 80",
            "stroke-dashoffset": "-30",
            points: d8
          }, null)])]
        })]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/decoration-5/index.mjs
var C8 = Object.defineProperty;
var u3 = Object.getOwnPropertySymbols;
var D5 = Object.prototype.hasOwnProperty;
var S = Object.prototype.propertyIsEnumerable;
var p7 = (o2, e, n5) => e in o2 ? C8(o2, e, { enumerable: true, configurable: true, writable: true, value: n5 }) : o2[e] = n5;
var l3 = (o2, e) => {
  for (var n5 in e || (e = {}))
    D5.call(e, n5) && p7(o2, n5, e[n5]);
  if (u3)
    for (var n5 of u3(e))
      S.call(e, n5) && p7(o2, n5, e[n5]);
  return o2;
};
var R4 = ["#3f96a5", "#3f96a5"];
function V2() {
  return l3(l3({}, n2()), a4(1.2));
}
function d7(o2) {
  return new Array(o2.length - 1).fill(0).map((e, n5) => i(o2[n5], o2[n5 + 1]));
}
function X(o2, e) {
  const n5 = [[0, e * 0.2], [o2 * 0.18, e * 0.2], [o2 * 0.2, e * 0.4], [o2 * 0.25, e * 0.4], [o2 * 0.27, e * 0.6], [o2 * 0.72, e * 0.6], [o2 * 0.75, e * 0.4], [o2 * 0.8, e * 0.4], [o2 * 0.82, e * 0.2], [o2, e * 0.2]], i3 = [[o2 * 0.3, e * 0.8], [o2 * 0.7, e * 0.8]];
  return {
    line1Sum: sum_default(d7(n5)),
    line2Sum: sum_default(d7(i3)),
    line1Point: n5.map((t4) => t4.join(",")).join(" "),
    line2Point: i3.map((t4) => t4.join(",")).join(" ")
  };
}
var x10 = t.div`
  width: 100%;
  height: 100%;
`("decoration-5");
var F4 = s(defineComponent({
  name: "Decoration5",
  props: V2(),
  setup(o2) {
    const {
      domRef: e,
      domSize: n5
    } = f2();
    return () => {
      const {
        width: i3,
        height: t4
      } = n5, {
        color: y6,
        duration: m8
      } = o2, c5 = u(R4, y6), {
        line1Sum: a5,
        line2Sum: s4,
        line1Point: P3,
        line2Point: k5
      } = X(i3, t4);
      return createVNode(x10, {
        ref: ($10) => e.value = $10.$el
      }, {
        default: () => [createVNode("svg", {
          width: i3,
          height: t4
        }, [createVNode("polyline", {
          fill: "transparent",
          stroke: c5[0],
          "stroke-width": "3",
          points: P3
        }, [createVNode("animate", {
          attributeName: "stroke-dasharray",
          attributeType: "XML",
          from: `0, ${a5 / 2}, 0, ${a5 / 2}`,
          to: `0, 0, ${a5}, 0`,
          dur: `${m8}s`,
          begin: "0s",
          calcMode: "spline",
          keyTimes: "0;1",
          keySplines: "0.4,1,0.49,0.98",
          repeatCount: "indefinite"
        }, null)]), createVNode("polyline", {
          fill: "transparent",
          stroke: c5[1],
          "stroke-width": "2",
          points: k5
        }, [createVNode("animate", {
          attributeName: "stroke-dasharray",
          attributeType: "XML",
          from: `0, ${s4 / 2}, 0, ${s4 / 2}`,
          to: `0, 0, ${s4}, 0`,
          dur: `${m8}s`,
          begin: "0s",
          calcMode: "spline",
          keyTimes: "0;1",
          keySplines: ".4,1,.49,.98",
          repeatCount: "indefinite"
        }, null)])])]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/decoration-6/index.mjs
var b = ["#7acaec", "#7acaec"];
var y5 = 300;
var v6 = 35;
var D6 = 1;
var z7 = 40;
var $8 = 7;
var H = $8 / 2;
var N = t.div`
  width: 100%;
  height: 100%;
}
.__STYLED__ .svg-origin {
  transform-origin: left top;
`("decoration-6");
function R5({
  width: d8,
  height: n5,
  rowPoints: r3,
  rowCount: t4
}) {
  const s4 = d8 / (r3 + 1), p9 = n5 / (t4 + 1), f10 = new Array(t4).fill(0).map((o2, e) => new Array(r3).fill(0).map((W3, S2) => [s4 * (S2 + 1), p9 * (e + 1)])).reduce((o2, e) => [...o2, ...e], []), c5 = new Array(t4 * r3).fill(0).map(() => Math.random() > 0.8 ? random_default(0.7 * n5, n5) : random_default(0.2 * n5, 0.5 * n5)), h7 = new Array(t4 * r3).fill(0).map((o2, e) => c5[e] * Math.random()), a5 = new Array(t4 * r3).fill(0).map(() => Math.random() + 1.5);
  return {
    points: f10,
    heights: c5,
    minHeights: h7,
    randoms: a5
  };
}
var {
  points: T5,
  heights: l4,
  minHeights: m6,
  randoms: u4
} = R5({
  width: y5,
  height: v6,
  rowPoints: z7,
  rowCount: D6
});
var Y3 = s(defineComponent({
  name: "Decoration6",
  props: n2(),
  setup(d8) {
    const {
      domRef: n5,
      domSize: r3
    } = f2();
    return () => {
      const {
        width: t4,
        height: s4
      } = r3, {
        color: p9
      } = d8, f10 = u(b, p9), c5 = {
        transform: `scale(${t4 / y5},${s4 / v6})`
      }, h7 = () => f10[Math.random() > 0.5 ? 0 : 1];
      return createVNode(N, {
        ref: (a5) => n5.value = a5.$el
      }, {
        default: () => [createVNode("svg", {
          class: "svg-origin",
          width: t4,
          height: s4,
          style: c5
        }, [T5.map(([a5, o2], e) => createVNode("rect", {
          key: `rect${e}`,
          fill: h7(),
          x: a5 - H,
          y: o2 - l4[e],
          width: $8,
          height: l4[e]
        }, [createVNode("animate", {
          attributeName: "y",
          values: `${o2 - m6[e] / 2};${o2 - l4[e] / 2};${o2 - m6[e] / 2}`,
          dur: u4[e],
          keyTimes: "0;0.5;1",
          calcMode: "spline",
          keySplines: "0.42,0,0.58,1;0.42,0,0.58,1",
          begin: "0s",
          repeatCount: "indefinite"
        }, null), createVNode("animate", {
          attributeName: "height",
          values: `${m6[e]};${l4[e]};${m6[e]}`,
          dur: u4[e],
          keyTimes: "0;0.5;1",
          calcMode: "spline",
          keySplines: "0.42,0,0.58,1;0.42,0,0.58,1",
          begin: "0s",
          repeatCount: "indefinite"
        }, null)]))])]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/decoration-7/index.mjs
var m7 = t.div`
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
`("decoration-7");
var h5 = ["#1dc1f5", "#1dc1f5"];
var C9 = s(defineComponent({
  name: "Decoration7",
  props: n2(),
  setup(n5, {
    slots: r3
  }) {
    const {
      domRef: i3
    } = f2();
    return () => {
      const {
        color: l5
      } = n5, e = u(h5, l5);
      return createVNode(m7, {
        ref: (o2) => i3.value = o2.$el
      }, {
        default: () => {
          var o2;
          return [createVNode("svg", {
            width: "21px",
            height: "20px"
          }, [createVNode("polyline", {
            "stroke-width": "4",
            fill: "transparent",
            stroke: e[0],
            points: "10, 0 19, 10 10, 20"
          }, null), createVNode("polyline", {
            "stroke-width": "2",
            fill: "transparent",
            stroke: e[1],
            points: "2, 0 11, 10 2, 20"
          }, null)]), (o2 = r3.default) == null ? void 0 : o2.call(r3), createVNode("svg", {
            width: "21px",
            height: "20px"
          }, [createVNode("polyline", {
            "stroke-width": "4",
            fill: "transparent",
            stroke: e[0],
            points: "11, 0 2, 10 11, 20"
          }, null), createVNode("polyline", {
            "stroke-width": "2",
            fill: "transparent",
            stroke: e[1],
            points: "19, 0 10, 10 19, 20"
          }, null)])];
        }
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/decoration-8/index.mjs
var u5 = Object.defineProperty;
var c4 = Object.getOwnPropertySymbols;
var h6 = Object.prototype.hasOwnProperty;
var $9 = Object.prototype.propertyIsEnumerable;
var f8 = (t4, e, o2) => e in t4 ? u5(t4, e, { enumerable: true, configurable: true, writable: true, value: o2 }) : t4[e] = o2;
var p8 = (t4, e) => {
  for (var o2 in e || (e = {}))
    h6.call(e, o2) && f8(t4, o2, e[o2]);
  if (c4)
    for (var o2 of c4(e))
      $9.call(e, o2) && f8(t4, o2, e[o2]);
  return t4;
};
var P2 = ["#3f96a5", "#3f96a5"];
function x11() {
  return p8(p8({}, n2()), u2());
}
var R6 = t.div`
  display: flex;
  width: 100%;
  height: 100%;
`("decoration-8");
var q = s(defineComponent({
  name: "Decoration8",
  props: x11(),
  setup(t4) {
    const {
      domRef: e,
      domSize: o2
    } = f2();
    return () => {
      const {
        color: d8,
        reverse: m8
      } = t4, {
        width: l5,
        height: n5
      } = o2, r3 = (s4) => m8 ? l5 - s4 : s4, a5 = u(P2, d8);
      return createVNode(R6, {
        ref: (s4) => e.value = s4.$el
      }, {
        default: () => [createVNode("svg", {
          width: l5,
          height: n5
        }, [createVNode("polyline", {
          stroke: a5[0],
          "stroke-width": "2",
          fill: "transparent",
          points: `${r3(0)}, 0 ${r3(30)}, ${n5 / 2}`
        }, null), createVNode("polyline", {
          stroke: a5[0],
          "stroke-width": "2",
          fill: "transparent",
          points: `${r3(20)}, 0 ${r3(50)}, ${n5 / 2} ${r3(l5)}, ${n5 / 2}`
        }, null), createVNode("polyline", {
          stroke: a5[1],
          fill: "transparent",
          "stroke-width": "3",
          points: `${r3(0)}, ${n5 - 3}, ${r3(200)}, ${n5 - 3}`
        }, null)])]
      });
    };
  }
}));

// node_modules/@dataview/datav-vue3/es/components/loading/index.mjs
var o = t.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.__STYLED__ .loading-tip {
  font-size: 15px;
`("loading");
var f9 = s(defineComponent({
  name: "Loading",
  setup(l5, {
    slots: t4
  }) {
    return () => createVNode(o, null, {
      default: () => {
        var r3;
        return [createVNode("svg", {
          width: "50px",
          height: "50px"
        }, [createVNode("circle", {
          cx: "25",
          cy: "25",
          r: "20",
          fill: "transparent",
          "stroke-width": "3",
          "stroke-dasharray": "31.415, 31.415",
          stroke: "#02bcfe",
          "stroke-linecap": "round"
        }, [createVNode("animateTransform", {
          attributeName: "transform",
          type: "rotate",
          values: "0, 25 25;360, 25 25",
          dur: "1.5s",
          repeatCount: "indefinite"
        }, null), createVNode("animate", {
          attributeName: "stroke",
          values: "#02bcfe;#3be6cb;#02bcfe",
          dur: "3s",
          repeatCount: "indefinite"
        }, null)]), createVNode("circle", {
          cx: "25",
          cy: "25",
          r: "10",
          fill: "transparent",
          "stroke-width": "3",
          "stroke-dasharray": "15.7, 15.7",
          stroke: "#3be6cb",
          "stroke-linecap": "round"
        }, [createVNode("animateTransform", {
          attributeName: "transform",
          type: "rotate",
          values: "360, 25 25;0, 25 25",
          dur: "1.5s",
          repeatCount: "indefinite"
        }, null), createVNode("animate", {
          attributeName: "stroke",
          values: "#3be6cb;#02bcfe;#3be6cb",
          dur: "3s",
          repeatCount: "indefinite"
        }, null)])]), createVNode("div", {
          class: "loading-tip"
        }, [(r3 = t4.default) == null ? void 0 : r3.call(t4)])];
      }
    });
  }
}));

// node_modules/@dataview/datav-vue3/es/index.mjs
var import_classnames4 = __toESM(require_classnames(), 1);
var import_color3 = __toESM(require_lib(), 1);
export {
  D as BorderBox1,
  E3 as BorderBox10,
  J as BorderBox11,
  R2 as BorderBox12,
  F3 as BorderBox13,
  F as BorderBox2,
  E as BorderBox3,
  G as BorderBox4,
  G2 as BorderBox5,
  F2 as BorderBox6,
  E2 as BorderBox7,
  W as BorderBox8,
  U2 as BorderBox9,
  I as Decoration1,
  A as Decoration2,
  j2 as Decoration3,
  A2 as Decoration4,
  F4 as Decoration5,
  Y3 as Decoration6,
  C9 as Decoration7,
  q as Decoration8,
  f9 as Loading,
  $ as setClassPrefix
};
/*! Bundled license information:

classnames/index.js:
  (*!
  	Copyright (c) 2018 Jed Watson.
  	Licensed under the MIT License (MIT), see
  	http://jedwatson.github.io/classnames
  *)
*/
//# sourceMappingURL=@dataview_datav-vue3.js.map
