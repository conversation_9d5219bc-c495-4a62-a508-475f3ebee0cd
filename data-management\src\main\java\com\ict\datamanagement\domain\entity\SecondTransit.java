package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("second_transit")
public class SecondTransit {
    //记录id
    @ApiModelProperty(value = "该条记录id",dataType = "int")
    @TableId(type = IdType.AUTO)
    private int id;
    //二次中转站id
    @ApiModelProperty(value = "二次中转站名称",dataType = "Long")
    private Long secondTransitId;

    //二次中转时长
    @ApiModelProperty(value = "二次中转时长",dataType = "double")
    private double transitTime;
}
