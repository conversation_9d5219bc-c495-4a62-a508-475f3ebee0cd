{"version": 3, "sources": ["../../zrender/lib/core/platform.js", "../../zrender/lib/core/util.js", "../../zrender/lib/core/env.js", "../../zrender/lib/core/matrix.js", "../../zrender/lib/core/vector.js", "../../zrender/lib/tool/color.js", "../../zrender/lib/core/LRU.js", "../../tslib/tslib.es6.js", "../../zrender/lib/graphic/helper/image.js", "../../zrender/lib/core/Point.js", "../../zrender/lib/core/BoundingRect.js", "../../zrender/lib/contain/text.js", "../../zrender/lib/graphic/helper/parseText.js", "../../zrender/lib/core/Eventful.js", "../../zrender/lib/config.js", "../../zrender/lib/graphic/constants.js", "../../zrender/lib/core/Transformable.js", "../../zrender/lib/animation/easing.js", "../../zrender/lib/core/curve.js", "../../zrender/lib/animation/cubicEasing.js", "../../zrender/lib/animation/Clip.js", "../../zrender/lib/svg/helper.js", "../../zrender/lib/animation/Animator.js", "../../zrender/lib/Element.js", "../../zrender/lib/graphic/Displayable.js", "../../zrender/lib/core/bbox.js", "../../zrender/lib/core/PathProxy.js", "../../zrender/lib/contain/line.js", "../../zrender/lib/contain/cubic.js", "../../zrender/lib/contain/quadratic.js", "../../zrender/lib/contain/util.js", "../../zrender/lib/contain/arc.js", "../../zrender/lib/contain/windingLine.js", "../../zrender/lib/contain/path.js", "../../zrender/lib/graphic/Path.js", "../../zrender/lib/graphic/TSpan.js", "../../zrender/lib/graphic/Image.js", "../../zrender/lib/graphic/helper/roundRect.js", "../../zrender/lib/graphic/helper/subPixelOptimize.js", "../../zrender/lib/graphic/shape/Rect.js", "../../zrender/lib/graphic/Text.js", "../../zrender/lib/graphic/Group.js", "../../zrender/lib/graphic/CompoundPath.js", "../../zrender/lib/core/fourPointsTransform.js", "../../zrender/lib/core/dom.js"], "sourcesContent": ["export var DEFAULT_FONT_SIZE = 12;\nexport var DEFAULT_FONT_FAMILY = 'sans-serif';\nexport var DEFAULT_FONT = DEFAULT_FONT_SIZE + \"px \" + DEFAULT_FONT_FAMILY;\nvar OFFSET = 20;\nvar SCALE = 100;\nvar defaultWidthMapStr = \"007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\\\\\WQb\\\\0FWLg\\\\bWb\\\\WQ\\\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\\\FFF5.5N\";\nfunction getTextWidthMap(mapStr) {\n    var map = {};\n    if (typeof JSON === 'undefined') {\n        return map;\n    }\n    for (var i = 0; i < mapStr.length; i++) {\n        var char = String.fromCharCode(i + 32);\n        var size = (mapStr.charCodeAt(i) - OFFSET) / SCALE;\n        map[char] = size;\n    }\n    return map;\n}\nexport var DEFAULT_TEXT_WIDTH_MAP = getTextWidthMap(defaultWidthMapStr);\nexport var platformApi = {\n    createCanvas: function () {\n        return typeof document !== 'undefined'\n            && document.createElement('canvas');\n    },\n    measureText: (function () {\n        var _ctx;\n        var _cachedFont;\n        return function (text, font) {\n            if (!_ctx) {\n                var canvas = platformApi.createCanvas();\n                _ctx = canvas && canvas.getContext('2d');\n            }\n            if (_ctx) {\n                if (_cachedFont !== font) {\n                    _cachedFont = _ctx.font = font || DEFAULT_FONT;\n                }\n                return _ctx.measureText(text);\n            }\n            else {\n                text = text || '';\n                font = font || DEFAULT_FONT;\n                var res = /((?:\\d+)?\\.?\\d*)px/.exec(font);\n                var fontSize = res && +res[1] || DEFAULT_FONT_SIZE;\n                var width = 0;\n                if (font.indexOf('mono') >= 0) {\n                    width = fontSize * text.length;\n                }\n                else {\n                    for (var i = 0; i < text.length; i++) {\n                        var preCalcWidth = DEFAULT_TEXT_WIDTH_MAP[text[i]];\n                        width += preCalcWidth == null ? fontSize : (preCalcWidth * fontSize);\n                    }\n                }\n                return { width: width };\n            }\n        };\n    })(),\n    loadImage: function (src, onload, onerror) {\n        var image = new Image();\n        image.onload = onload;\n        image.onerror = onerror;\n        image.src = src;\n        return image;\n    }\n};\nexport function setPlatformAPI(newPlatformApis) {\n    for (var key in platformApi) {\n        if (newPlatformApis[key]) {\n            platformApi[key] = newPlatformApis[key];\n        }\n    }\n}\n", "import { platformApi } from './platform.js';\nvar BUILTIN_OBJECT = reduce([\n    'Function',\n    'RegExp',\n    'Date',\n    'Error',\n    'CanvasGradient',\n    'CanvasPattern',\n    'Image',\n    'Canvas'\n], function (obj, val) {\n    obj['[object ' + val + ']'] = true;\n    return obj;\n}, {});\nvar TYPED_ARRAY = reduce([\n    'Int8',\n    'Uint8',\n    'Uint8Clamped',\n    'Int16',\n    'Uint16',\n    'Int32',\n    'Uint32',\n    'Float32',\n    'Float64'\n], function (obj, val) {\n    obj['[object ' + val + 'Array]'] = true;\n    return obj;\n}, {});\nvar objToString = Object.prototype.toString;\nvar arrayProto = Array.prototype;\nvar nativeForEach = arrayProto.forEach;\nvar nativeFilter = arrayProto.filter;\nvar nativeSlice = arrayProto.slice;\nvar nativeMap = arrayProto.map;\nvar ctorFunction = function () { }.constructor;\nvar protoFunction = ctorFunction ? ctorFunction.prototype : null;\nvar protoKey = '__proto__';\nvar idStart = 0x0907;\nexport function guid() {\n    return idStart++;\n}\nexport function logError() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    if (typeof console !== 'undefined') {\n        console.error.apply(console, args);\n    }\n}\nexport function clone(source) {\n    if (source == null || typeof source !== 'object') {\n        return source;\n    }\n    var result = source;\n    var typeStr = objToString.call(source);\n    if (typeStr === '[object Array]') {\n        if (!isPrimitive(source)) {\n            result = [];\n            for (var i = 0, len = source.length; i < len; i++) {\n                result[i] = clone(source[i]);\n            }\n        }\n    }\n    else if (TYPED_ARRAY[typeStr]) {\n        if (!isPrimitive(source)) {\n            var Ctor = source.constructor;\n            if (Ctor.from) {\n                result = Ctor.from(source);\n            }\n            else {\n                result = new Ctor(source.length);\n                for (var i = 0, len = source.length; i < len; i++) {\n                    result[i] = source[i];\n                }\n            }\n        }\n    }\n    else if (!BUILTIN_OBJECT[typeStr] && !isPrimitive(source) && !isDom(source)) {\n        result = {};\n        for (var key in source) {\n            if (source.hasOwnProperty(key) && key !== protoKey) {\n                result[key] = clone(source[key]);\n            }\n        }\n    }\n    return result;\n}\nexport function merge(target, source, overwrite) {\n    if (!isObject(source) || !isObject(target)) {\n        return overwrite ? clone(source) : target;\n    }\n    for (var key in source) {\n        if (source.hasOwnProperty(key) && key !== protoKey) {\n            var targetProp = target[key];\n            var sourceProp = source[key];\n            if (isObject(sourceProp)\n                && isObject(targetProp)\n                && !isArray(sourceProp)\n                && !isArray(targetProp)\n                && !isDom(sourceProp)\n                && !isDom(targetProp)\n                && !isBuiltInObject(sourceProp)\n                && !isBuiltInObject(targetProp)\n                && !isPrimitive(sourceProp)\n                && !isPrimitive(targetProp)) {\n                merge(targetProp, sourceProp, overwrite);\n            }\n            else if (overwrite || !(key in target)) {\n                target[key] = clone(source[key]);\n            }\n        }\n    }\n    return target;\n}\nexport function mergeAll(targetAndSources, overwrite) {\n    var result = targetAndSources[0];\n    for (var i = 1, len = targetAndSources.length; i < len; i++) {\n        result = merge(result, targetAndSources[i], overwrite);\n    }\n    return result;\n}\nexport function extend(target, source) {\n    if (Object.assign) {\n        Object.assign(target, source);\n    }\n    else {\n        for (var key in source) {\n            if (source.hasOwnProperty(key) && key !== protoKey) {\n                target[key] = source[key];\n            }\n        }\n    }\n    return target;\n}\nexport function defaults(target, source, overlay) {\n    var keysArr = keys(source);\n    for (var i = 0, len = keysArr.length; i < len; i++) {\n        var key = keysArr[i];\n        if ((overlay ? source[key] != null : target[key] == null)) {\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nexport var createCanvas = platformApi.createCanvas;\nexport function indexOf(array, value) {\n    if (array) {\n        if (array.indexOf) {\n            return array.indexOf(value);\n        }\n        for (var i = 0, len = array.length; i < len; i++) {\n            if (array[i] === value) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\nexport function inherits(clazz, baseClazz) {\n    var clazzPrototype = clazz.prototype;\n    function F() { }\n    F.prototype = baseClazz.prototype;\n    clazz.prototype = new F();\n    for (var prop in clazzPrototype) {\n        if (clazzPrototype.hasOwnProperty(prop)) {\n            clazz.prototype[prop] = clazzPrototype[prop];\n        }\n    }\n    clazz.prototype.constructor = clazz;\n    clazz.superClass = baseClazz;\n}\nexport function mixin(target, source, override) {\n    target = 'prototype' in target ? target.prototype : target;\n    source = 'prototype' in source ? source.prototype : source;\n    if (Object.getOwnPropertyNames) {\n        var keyList = Object.getOwnPropertyNames(source);\n        for (var i = 0; i < keyList.length; i++) {\n            var key = keyList[i];\n            if (key !== 'constructor') {\n                if ((override ? source[key] != null : target[key] == null)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n    }\n    else {\n        defaults(target, source, override);\n    }\n}\nexport function isArrayLike(data) {\n    if (!data) {\n        return false;\n    }\n    if (typeof data === 'string') {\n        return false;\n    }\n    return typeof data.length === 'number';\n}\nexport function each(arr, cb, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    if (arr.forEach && arr.forEach === nativeForEach) {\n        arr.forEach(cb, context);\n    }\n    else if (arr.length === +arr.length) {\n        for (var i = 0, len = arr.length; i < len; i++) {\n            cb.call(context, arr[i], i, arr);\n        }\n    }\n    else {\n        for (var key in arr) {\n            if (arr.hasOwnProperty(key)) {\n                cb.call(context, arr[key], key, arr);\n            }\n        }\n    }\n}\nexport function map(arr, cb, context) {\n    if (!arr) {\n        return [];\n    }\n    if (!cb) {\n        return slice(arr);\n    }\n    if (arr.map && arr.map === nativeMap) {\n        return arr.map(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = arr.length; i < len; i++) {\n            result.push(cb.call(context, arr[i], i, arr));\n        }\n        return result;\n    }\n}\nexport function reduce(arr, cb, memo, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    for (var i = 0, len = arr.length; i < len; i++) {\n        memo = cb.call(context, memo, arr[i], i, arr);\n    }\n    return memo;\n}\nexport function filter(arr, cb, context) {\n    if (!arr) {\n        return [];\n    }\n    if (!cb) {\n        return slice(arr);\n    }\n    if (arr.filter && arr.filter === nativeFilter) {\n        return arr.filter(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = arr.length; i < len; i++) {\n            if (cb.call(context, arr[i], i, arr)) {\n                result.push(arr[i]);\n            }\n        }\n        return result;\n    }\n}\nexport function find(arr, cb, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    for (var i = 0, len = arr.length; i < len; i++) {\n        if (cb.call(context, arr[i], i, arr)) {\n            return arr[i];\n        }\n    }\n}\nexport function keys(obj) {\n    if (!obj) {\n        return [];\n    }\n    if (Object.keys) {\n        return Object.keys(obj);\n    }\n    var keyList = [];\n    for (var key in obj) {\n        if (obj.hasOwnProperty(key)) {\n            keyList.push(key);\n        }\n    }\n    return keyList;\n}\nfunction bindPolyfill(func, context) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    return function () {\n        return func.apply(context, args.concat(nativeSlice.call(arguments)));\n    };\n}\nexport var bind = (protoFunction && isFunction(protoFunction.bind))\n    ? protoFunction.call.bind(protoFunction.bind)\n    : bindPolyfill;\nfunction curry(func) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return function () {\n        return func.apply(this, args.concat(nativeSlice.call(arguments)));\n    };\n}\nexport { curry };\nexport function isArray(value) {\n    if (Array.isArray) {\n        return Array.isArray(value);\n    }\n    return objToString.call(value) === '[object Array]';\n}\nexport function isFunction(value) {\n    return typeof value === 'function';\n}\nexport function isString(value) {\n    return typeof value === 'string';\n}\nexport function isStringSafe(value) {\n    return objToString.call(value) === '[object String]';\n}\nexport function isNumber(value) {\n    return typeof value === 'number';\n}\nexport function isObject(value) {\n    var type = typeof value;\n    return type === 'function' || (!!value && type === 'object');\n}\nexport function isBuiltInObject(value) {\n    return !!BUILTIN_OBJECT[objToString.call(value)];\n}\nexport function isTypedArray(value) {\n    return !!TYPED_ARRAY[objToString.call(value)];\n}\nexport function isDom(value) {\n    return typeof value === 'object'\n        && typeof value.nodeType === 'number'\n        && typeof value.ownerDocument === 'object';\n}\nexport function isGradientObject(value) {\n    return value.colorStops != null;\n}\nexport function isImagePatternObject(value) {\n    return value.image != null;\n}\nexport function isRegExp(value) {\n    return objToString.call(value) === '[object RegExp]';\n}\nexport function eqNaN(value) {\n    return value !== value;\n}\nexport function retrieve() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    for (var i = 0, len = args.length; i < len; i++) {\n        if (args[i] != null) {\n            return args[i];\n        }\n    }\n}\nexport function retrieve2(value0, value1) {\n    return value0 != null\n        ? value0\n        : value1;\n}\nexport function retrieve3(value0, value1, value2) {\n    return value0 != null\n        ? value0\n        : value1 != null\n            ? value1\n            : value2;\n}\nexport function slice(arr) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return nativeSlice.apply(arr, args);\n}\nexport function normalizeCssArray(val) {\n    if (typeof (val) === 'number') {\n        return [val, val, val, val];\n    }\n    var len = val.length;\n    if (len === 2) {\n        return [val[0], val[1], val[0], val[1]];\n    }\n    else if (len === 3) {\n        return [val[0], val[1], val[2], val[1]];\n    }\n    return val;\n}\nexport function assert(condition, message) {\n    if (!condition) {\n        throw new Error(message);\n    }\n}\nexport function trim(str) {\n    if (str == null) {\n        return null;\n    }\n    else if (typeof str.trim === 'function') {\n        return str.trim();\n    }\n    else {\n        return str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n    }\n}\nvar primitiveKey = '__ec_primitive__';\nexport function setAsPrimitive(obj) {\n    obj[primitiveKey] = true;\n}\nexport function isPrimitive(obj) {\n    return obj[primitiveKey];\n}\nvar MapPolyfill = (function () {\n    function MapPolyfill() {\n        this.data = {};\n    }\n    MapPolyfill.prototype[\"delete\"] = function (key) {\n        var existed = this.has(key);\n        if (existed) {\n            delete this.data[key];\n        }\n        return existed;\n    };\n    MapPolyfill.prototype.has = function (key) {\n        return this.data.hasOwnProperty(key);\n    };\n    MapPolyfill.prototype.get = function (key) {\n        return this.data[key];\n    };\n    MapPolyfill.prototype.set = function (key, value) {\n        this.data[key] = value;\n        return this;\n    };\n    MapPolyfill.prototype.keys = function () {\n        return keys(this.data);\n    };\n    MapPolyfill.prototype.forEach = function (callback) {\n        var data = this.data;\n        for (var key in data) {\n            if (data.hasOwnProperty(key)) {\n                callback(data[key], key);\n            }\n        }\n    };\n    return MapPolyfill;\n}());\nvar isNativeMapSupported = typeof Map === 'function';\nfunction maybeNativeMap() {\n    return (isNativeMapSupported ? new Map() : new MapPolyfill());\n}\nvar HashMap = (function () {\n    function HashMap(obj) {\n        var isArr = isArray(obj);\n        this.data = maybeNativeMap();\n        var thisMap = this;\n        (obj instanceof HashMap)\n            ? obj.each(visit)\n            : (obj && each(obj, visit));\n        function visit(value, key) {\n            isArr ? thisMap.set(value, key) : thisMap.set(key, value);\n        }\n    }\n    HashMap.prototype.hasKey = function (key) {\n        return this.data.has(key);\n    };\n    HashMap.prototype.get = function (key) {\n        return this.data.get(key);\n    };\n    HashMap.prototype.set = function (key, value) {\n        this.data.set(key, value);\n        return value;\n    };\n    HashMap.prototype.each = function (cb, context) {\n        this.data.forEach(function (value, key) {\n            cb.call(context, value, key);\n        });\n    };\n    HashMap.prototype.keys = function () {\n        var keys = this.data.keys();\n        return isNativeMapSupported\n            ? Array.from(keys)\n            : keys;\n    };\n    HashMap.prototype.removeKey = function (key) {\n        this.data[\"delete\"](key);\n    };\n    return HashMap;\n}());\nexport { HashMap };\nexport function createHashMap(obj) {\n    return new HashMap(obj);\n}\nexport function concatArray(a, b) {\n    var newArray = new a.constructor(a.length + b.length);\n    for (var i = 0; i < a.length; i++) {\n        newArray[i] = a[i];\n    }\n    var offset = a.length;\n    for (var i = 0; i < b.length; i++) {\n        newArray[i + offset] = b[i];\n    }\n    return newArray;\n}\nexport function createObject(proto, properties) {\n    var obj;\n    if (Object.create) {\n        obj = Object.create(proto);\n    }\n    else {\n        var StyleCtor = function () { };\n        StyleCtor.prototype = proto;\n        obj = new StyleCtor();\n    }\n    if (properties) {\n        extend(obj, properties);\n    }\n    return obj;\n}\nexport function disableUserSelect(dom) {\n    var domStyle = dom.style;\n    domStyle.webkitUserSelect = 'none';\n    domStyle.userSelect = 'none';\n    domStyle.webkitTapHighlightColor = 'rgba(0,0,0,0)';\n    domStyle['-webkit-touch-callout'] = 'none';\n}\nexport function hasOwn(own, prop) {\n    return own.hasOwnProperty(prop);\n}\nexport function noop() { }\nexport var RADIAN_TO_DEGREE = 180 / Math.PI;\n", "var Browser = (function () {\n    function Browser() {\n        this.firefox = false;\n        this.ie = false;\n        this.edge = false;\n        this.newEdge = false;\n        this.weChat = false;\n    }\n    return Browser;\n}());\nvar Env = (function () {\n    function Env() {\n        this.browser = new Browser();\n        this.node = false;\n        this.wxa = false;\n        this.worker = false;\n        this.svgSupported = false;\n        this.touchEventsSupported = false;\n        this.pointerEventsSupported = false;\n        this.domSupported = false;\n        this.transformSupported = false;\n        this.transform3dSupported = false;\n        this.hasGlobalWindow = typeof window !== 'undefined';\n    }\n    return Env;\n}());\nvar env = new Env();\nif (typeof wx === 'object' && typeof wx.getSystemInfoSync === 'function') {\n    env.wxa = true;\n    env.touchEventsSupported = true;\n}\nelse if (typeof document === 'undefined' && typeof self !== 'undefined') {\n    env.worker = true;\n}\nelse if (!env.hasGlobalWindow || 'Deno' in window) {\n    env.node = true;\n    env.svgSupported = true;\n}\nelse {\n    detect(navigator.userAgent, env);\n}\nfunction detect(ua, env) {\n    var browser = env.browser;\n    var firefox = ua.match(/Firefox\\/([\\d.]+)/);\n    var ie = ua.match(/MSIE\\s([\\d.]+)/)\n        || ua.match(/Trident\\/.+?rv:(([\\d.]+))/);\n    var edge = ua.match(/Edge?\\/([\\d.]+)/);\n    var weChat = (/micromessenger/i).test(ua);\n    if (firefox) {\n        browser.firefox = true;\n        browser.version = firefox[1];\n    }\n    if (ie) {\n        browser.ie = true;\n        browser.version = ie[1];\n    }\n    if (edge) {\n        browser.edge = true;\n        browser.version = edge[1];\n        browser.newEdge = +edge[1].split('.')[0] > 18;\n    }\n    if (weChat) {\n        browser.weChat = true;\n    }\n    env.svgSupported = typeof SVGRect !== 'undefined';\n    env.touchEventsSupported = 'ontouchstart' in window && !browser.ie && !browser.edge;\n    env.pointerEventsSupported = 'onpointerdown' in window\n        && (browser.edge || (browser.ie && +browser.version >= 11));\n    env.domSupported = typeof document !== 'undefined';\n    var style = document.documentElement.style;\n    env.transform3dSupported = ((browser.ie && 'transition' in style)\n        || browser.edge\n        || (('WebKitCSSMatrix' in window) && ('m11' in new WebKitCSSMatrix()))\n        || 'MozPerspective' in style)\n        && !('OTransition' in style);\n    env.transformSupported = env.transform3dSupported\n        || (browser.ie && +browser.version >= 9);\n}\nexport default env;\n", "export function create() {\n    return [1, 0, 0, 1, 0, 0];\n}\nexport function identity(out) {\n    out[0] = 1;\n    out[1] = 0;\n    out[2] = 0;\n    out[3] = 1;\n    out[4] = 0;\n    out[5] = 0;\n    return out;\n}\nexport function copy(out, m) {\n    out[0] = m[0];\n    out[1] = m[1];\n    out[2] = m[2];\n    out[3] = m[3];\n    out[4] = m[4];\n    out[5] = m[5];\n    return out;\n}\nexport function mul(out, m1, m2) {\n    var out0 = m1[0] * m2[0] + m1[2] * m2[1];\n    var out1 = m1[1] * m2[0] + m1[3] * m2[1];\n    var out2 = m1[0] * m2[2] + m1[2] * m2[3];\n    var out3 = m1[1] * m2[2] + m1[3] * m2[3];\n    var out4 = m1[0] * m2[4] + m1[2] * m2[5] + m1[4];\n    var out5 = m1[1] * m2[4] + m1[3] * m2[5] + m1[5];\n    out[0] = out0;\n    out[1] = out1;\n    out[2] = out2;\n    out[3] = out3;\n    out[4] = out4;\n    out[5] = out5;\n    return out;\n}\nexport function translate(out, a, v) {\n    out[0] = a[0];\n    out[1] = a[1];\n    out[2] = a[2];\n    out[3] = a[3];\n    out[4] = a[4] + v[0];\n    out[5] = a[5] + v[1];\n    return out;\n}\nexport function rotate(out, a, rad, pivot) {\n    if (pivot === void 0) { pivot = [0, 0]; }\n    var aa = a[0];\n    var ac = a[2];\n    var atx = a[4];\n    var ab = a[1];\n    var ad = a[3];\n    var aty = a[5];\n    var st = Math.sin(rad);\n    var ct = Math.cos(rad);\n    out[0] = aa * ct + ab * st;\n    out[1] = -aa * st + ab * ct;\n    out[2] = ac * ct + ad * st;\n    out[3] = -ac * st + ct * ad;\n    out[4] = ct * (atx - pivot[0]) + st * (aty - pivot[1]) + pivot[0];\n    out[5] = ct * (aty - pivot[1]) - st * (atx - pivot[0]) + pivot[1];\n    return out;\n}\nexport function scale(out, a, v) {\n    var vx = v[0];\n    var vy = v[1];\n    out[0] = a[0] * vx;\n    out[1] = a[1] * vy;\n    out[2] = a[2] * vx;\n    out[3] = a[3] * vy;\n    out[4] = a[4] * vx;\n    out[5] = a[5] * vy;\n    return out;\n}\nexport function invert(out, a) {\n    var aa = a[0];\n    var ac = a[2];\n    var atx = a[4];\n    var ab = a[1];\n    var ad = a[3];\n    var aty = a[5];\n    var det = aa * ad - ab * ac;\n    if (!det) {\n        return null;\n    }\n    det = 1.0 / det;\n    out[0] = ad * det;\n    out[1] = -ab * det;\n    out[2] = -ac * det;\n    out[3] = aa * det;\n    out[4] = (ac * aty - ad * atx) * det;\n    out[5] = (ab * atx - aa * aty) * det;\n    return out;\n}\nexport function clone(a) {\n    var b = create();\n    copy(b, a);\n    return b;\n}\n", "export function create(x, y) {\n    if (x == null) {\n        x = 0;\n    }\n    if (y == null) {\n        y = 0;\n    }\n    return [x, y];\n}\nexport function copy(out, v) {\n    out[0] = v[0];\n    out[1] = v[1];\n    return out;\n}\nexport function clone(v) {\n    return [v[0], v[1]];\n}\nexport function set(out, a, b) {\n    out[0] = a;\n    out[1] = b;\n    return out;\n}\nexport function add(out, v1, v2) {\n    out[0] = v1[0] + v2[0];\n    out[1] = v1[1] + v2[1];\n    return out;\n}\nexport function scaleAndAdd(out, v1, v2, a) {\n    out[0] = v1[0] + v2[0] * a;\n    out[1] = v1[1] + v2[1] * a;\n    return out;\n}\nexport function sub(out, v1, v2) {\n    out[0] = v1[0] - v2[0];\n    out[1] = v1[1] - v2[1];\n    return out;\n}\nexport function len(v) {\n    return Math.sqrt(lenSquare(v));\n}\nexport var length = len;\nexport function lenSquare(v) {\n    return v[0] * v[0] + v[1] * v[1];\n}\nexport var lengthSquare = lenSquare;\nexport function mul(out, v1, v2) {\n    out[0] = v1[0] * v2[0];\n    out[1] = v1[1] * v2[1];\n    return out;\n}\nexport function div(out, v1, v2) {\n    out[0] = v1[0] / v2[0];\n    out[1] = v1[1] / v2[1];\n    return out;\n}\nexport function dot(v1, v2) {\n    return v1[0] * v2[0] + v1[1] * v2[1];\n}\nexport function scale(out, v, s) {\n    out[0] = v[0] * s;\n    out[1] = v[1] * s;\n    return out;\n}\nexport function normalize(out, v) {\n    var d = len(v);\n    if (d === 0) {\n        out[0] = 0;\n        out[1] = 0;\n    }\n    else {\n        out[0] = v[0] / d;\n        out[1] = v[1] / d;\n    }\n    return out;\n}\nexport function distance(v1, v2) {\n    return Math.sqrt((v1[0] - v2[0]) * (v1[0] - v2[0])\n        + (v1[1] - v2[1]) * (v1[1] - v2[1]));\n}\nexport var dist = distance;\nexport function distanceSquare(v1, v2) {\n    return (v1[0] - v2[0]) * (v1[0] - v2[0])\n        + (v1[1] - v2[1]) * (v1[1] - v2[1]);\n}\nexport var distSquare = distanceSquare;\nexport function negate(out, v) {\n    out[0] = -v[0];\n    out[1] = -v[1];\n    return out;\n}\nexport function lerp(out, v1, v2, t) {\n    out[0] = v1[0] + t * (v2[0] - v1[0]);\n    out[1] = v1[1] + t * (v2[1] - v1[1]);\n    return out;\n}\nexport function applyTransform(out, v, m) {\n    var x = v[0];\n    var y = v[1];\n    out[0] = m[0] * x + m[2] * y + m[4];\n    out[1] = m[1] * x + m[3] * y + m[5];\n    return out;\n}\nexport function min(out, v1, v2) {\n    out[0] = Math.min(v1[0], v2[0]);\n    out[1] = Math.min(v1[1], v2[1]);\n    return out;\n}\nexport function max(out, v1, v2) {\n    out[0] = Math.max(v1[0], v2[0]);\n    out[1] = Math.max(v1[1], v2[1]);\n    return out;\n}\n", "import LRU from '../core/LRU.js';\nimport { extend, isGradientObject, isString, map } from '../core/util.js';\nvar kCSSColorTable = {\n    'transparent': [0, 0, 0, 0], 'aliceblue': [240, 248, 255, 1],\n    'antiquewhite': [250, 235, 215, 1], 'aqua': [0, 255, 255, 1],\n    'aquamarine': [127, 255, 212, 1], 'azure': [240, 255, 255, 1],\n    'beige': [245, 245, 220, 1], 'bisque': [255, 228, 196, 1],\n    'black': [0, 0, 0, 1], 'blanchedalmond': [255, 235, 205, 1],\n    'blue': [0, 0, 255, 1], 'blueviolet': [138, 43, 226, 1],\n    'brown': [165, 42, 42, 1], 'burlywood': [222, 184, 135, 1],\n    'cadetblue': [95, 158, 160, 1], 'chartreuse': [127, 255, 0, 1],\n    'chocolate': [210, 105, 30, 1], 'coral': [255, 127, 80, 1],\n    'cornflowerblue': [100, 149, 237, 1], 'cornsilk': [255, 248, 220, 1],\n    'crimson': [220, 20, 60, 1], 'cyan': [0, 255, 255, 1],\n    'darkblue': [0, 0, 139, 1], 'darkcyan': [0, 139, 139, 1],\n    'darkgoldenrod': [184, 134, 11, 1], 'darkgray': [169, 169, 169, 1],\n    'darkgreen': [0, 100, 0, 1], 'darkgrey': [169, 169, 169, 1],\n    'darkkhaki': [189, 183, 107, 1], 'darkmagenta': [139, 0, 139, 1],\n    'darkolivegreen': [85, 107, 47, 1], 'darkorange': [255, 140, 0, 1],\n    'darkorchid': [153, 50, 204, 1], 'darkred': [139, 0, 0, 1],\n    'darksalmon': [233, 150, 122, 1], 'darkseagreen': [143, 188, 143, 1],\n    'darkslateblue': [72, 61, 139, 1], 'darkslategray': [47, 79, 79, 1],\n    'darkslategrey': [47, 79, 79, 1], 'darkturquoise': [0, 206, 209, 1],\n    'darkviolet': [148, 0, 211, 1], 'deeppink': [255, 20, 147, 1],\n    'deepskyblue': [0, 191, 255, 1], 'dimgray': [105, 105, 105, 1],\n    'dimgrey': [105, 105, 105, 1], 'dodgerblue': [30, 144, 255, 1],\n    'firebrick': [178, 34, 34, 1], 'floralwhite': [255, 250, 240, 1],\n    'forestgreen': [34, 139, 34, 1], 'fuchsia': [255, 0, 255, 1],\n    'gainsboro': [220, 220, 220, 1], 'ghostwhite': [248, 248, 255, 1],\n    'gold': [255, 215, 0, 1], 'goldenrod': [218, 165, 32, 1],\n    'gray': [128, 128, 128, 1], 'green': [0, 128, 0, 1],\n    'greenyellow': [173, 255, 47, 1], 'grey': [128, 128, 128, 1],\n    'honeydew': [240, 255, 240, 1], 'hotpink': [255, 105, 180, 1],\n    'indianred': [205, 92, 92, 1], 'indigo': [75, 0, 130, 1],\n    'ivory': [255, 255, 240, 1], 'khaki': [240, 230, 140, 1],\n    'lavender': [230, 230, 250, 1], 'lavenderblush': [255, 240, 245, 1],\n    'lawngreen': [124, 252, 0, 1], 'lemonchiffon': [255, 250, 205, 1],\n    'lightblue': [173, 216, 230, 1], 'lightcoral': [240, 128, 128, 1],\n    'lightcyan': [224, 255, 255, 1], 'lightgoldenrodyellow': [250, 250, 210, 1],\n    'lightgray': [211, 211, 211, 1], 'lightgreen': [144, 238, 144, 1],\n    'lightgrey': [211, 211, 211, 1], 'lightpink': [255, 182, 193, 1],\n    'lightsalmon': [255, 160, 122, 1], 'lightseagreen': [32, 178, 170, 1],\n    'lightskyblue': [135, 206, 250, 1], 'lightslategray': [119, 136, 153, 1],\n    'lightslategrey': [119, 136, 153, 1], 'lightsteelblue': [176, 196, 222, 1],\n    'lightyellow': [255, 255, 224, 1], 'lime': [0, 255, 0, 1],\n    'limegreen': [50, 205, 50, 1], 'linen': [250, 240, 230, 1],\n    'magenta': [255, 0, 255, 1], 'maroon': [128, 0, 0, 1],\n    'mediumaquamarine': [102, 205, 170, 1], 'mediumblue': [0, 0, 205, 1],\n    'mediumorchid': [186, 85, 211, 1], 'mediumpurple': [147, 112, 219, 1],\n    'mediumseagreen': [60, 179, 113, 1], 'mediumslateblue': [123, 104, 238, 1],\n    'mediumspringgreen': [0, 250, 154, 1], 'mediumturquoise': [72, 209, 204, 1],\n    'mediumvioletred': [199, 21, 133, 1], 'midnightblue': [25, 25, 112, 1],\n    'mintcream': [245, 255, 250, 1], 'mistyrose': [255, 228, 225, 1],\n    'moccasin': [255, 228, 181, 1], 'navajowhite': [255, 222, 173, 1],\n    'navy': [0, 0, 128, 1], 'oldlace': [253, 245, 230, 1],\n    'olive': [128, 128, 0, 1], 'olivedrab': [107, 142, 35, 1],\n    'orange': [255, 165, 0, 1], 'orangered': [255, 69, 0, 1],\n    'orchid': [218, 112, 214, 1], 'palegoldenrod': [238, 232, 170, 1],\n    'palegreen': [152, 251, 152, 1], 'paleturquoise': [175, 238, 238, 1],\n    'palevioletred': [219, 112, 147, 1], 'papayawhip': [255, 239, 213, 1],\n    'peachpuff': [255, 218, 185, 1], 'peru': [205, 133, 63, 1],\n    'pink': [255, 192, 203, 1], 'plum': [221, 160, 221, 1],\n    'powderblue': [176, 224, 230, 1], 'purple': [128, 0, 128, 1],\n    'red': [255, 0, 0, 1], 'rosybrown': [188, 143, 143, 1],\n    'royalblue': [65, 105, 225, 1], 'saddlebrown': [139, 69, 19, 1],\n    'salmon': [250, 128, 114, 1], 'sandybrown': [244, 164, 96, 1],\n    'seagreen': [46, 139, 87, 1], 'seashell': [255, 245, 238, 1],\n    'sienna': [160, 82, 45, 1], 'silver': [192, 192, 192, 1],\n    'skyblue': [135, 206, 235, 1], 'slateblue': [106, 90, 205, 1],\n    'slategray': [112, 128, 144, 1], 'slategrey': [112, 128, 144, 1],\n    'snow': [255, 250, 250, 1], 'springgreen': [0, 255, 127, 1],\n    'steelblue': [70, 130, 180, 1], 'tan': [210, 180, 140, 1],\n    'teal': [0, 128, 128, 1], 'thistle': [216, 191, 216, 1],\n    'tomato': [255, 99, 71, 1], 'turquoise': [64, 224, 208, 1],\n    'violet': [238, 130, 238, 1], 'wheat': [245, 222, 179, 1],\n    'white': [255, 255, 255, 1], 'whitesmoke': [245, 245, 245, 1],\n    'yellow': [255, 255, 0, 1], 'yellowgreen': [154, 205, 50, 1]\n};\nfunction clampCssByte(i) {\n    i = Math.round(i);\n    return i < 0 ? 0 : i > 255 ? 255 : i;\n}\nfunction clampCssAngle(i) {\n    i = Math.round(i);\n    return i < 0 ? 0 : i > 360 ? 360 : i;\n}\nfunction clampCssFloat(f) {\n    return f < 0 ? 0 : f > 1 ? 1 : f;\n}\nfunction parseCssInt(val) {\n    var str = val;\n    if (str.length && str.charAt(str.length - 1) === '%') {\n        return clampCssByte(parseFloat(str) / 100 * 255);\n    }\n    return clampCssByte(parseInt(str, 10));\n}\nfunction parseCssFloat(val) {\n    var str = val;\n    if (str.length && str.charAt(str.length - 1) === '%') {\n        return clampCssFloat(parseFloat(str) / 100);\n    }\n    return clampCssFloat(parseFloat(str));\n}\nfunction cssHueToRgb(m1, m2, h) {\n    if (h < 0) {\n        h += 1;\n    }\n    else if (h > 1) {\n        h -= 1;\n    }\n    if (h * 6 < 1) {\n        return m1 + (m2 - m1) * h * 6;\n    }\n    if (h * 2 < 1) {\n        return m2;\n    }\n    if (h * 3 < 2) {\n        return m1 + (m2 - m1) * (2 / 3 - h) * 6;\n    }\n    return m1;\n}\nfunction lerpNumber(a, b, p) {\n    return a + (b - a) * p;\n}\nfunction setRgba(out, r, g, b, a) {\n    out[0] = r;\n    out[1] = g;\n    out[2] = b;\n    out[3] = a;\n    return out;\n}\nfunction copyRgba(out, a) {\n    out[0] = a[0];\n    out[1] = a[1];\n    out[2] = a[2];\n    out[3] = a[3];\n    return out;\n}\nvar colorCache = new LRU(20);\nvar lastRemovedArr = null;\nfunction putToCache(colorStr, rgbaArr) {\n    if (lastRemovedArr) {\n        copyRgba(lastRemovedArr, rgbaArr);\n    }\n    lastRemovedArr = colorCache.put(colorStr, lastRemovedArr || (rgbaArr.slice()));\n}\nexport function parse(colorStr, rgbaArr) {\n    if (!colorStr) {\n        return;\n    }\n    rgbaArr = rgbaArr || [];\n    var cached = colorCache.get(colorStr);\n    if (cached) {\n        return copyRgba(rgbaArr, cached);\n    }\n    colorStr = colorStr + '';\n    var str = colorStr.replace(/ /g, '').toLowerCase();\n    if (str in kCSSColorTable) {\n        copyRgba(rgbaArr, kCSSColorTable[str]);\n        putToCache(colorStr, rgbaArr);\n        return rgbaArr;\n    }\n    var strLen = str.length;\n    if (str.charAt(0) === '#') {\n        if (strLen === 4 || strLen === 5) {\n            var iv = parseInt(str.slice(1, 4), 16);\n            if (!(iv >= 0 && iv <= 0xfff)) {\n                setRgba(rgbaArr, 0, 0, 0, 1);\n                return;\n            }\n            setRgba(rgbaArr, ((iv & 0xf00) >> 4) | ((iv & 0xf00) >> 8), (iv & 0xf0) | ((iv & 0xf0) >> 4), (iv & 0xf) | ((iv & 0xf) << 4), strLen === 5 ? parseInt(str.slice(4), 16) / 0xf : 1);\n            putToCache(colorStr, rgbaArr);\n            return rgbaArr;\n        }\n        else if (strLen === 7 || strLen === 9) {\n            var iv = parseInt(str.slice(1, 7), 16);\n            if (!(iv >= 0 && iv <= 0xffffff)) {\n                setRgba(rgbaArr, 0, 0, 0, 1);\n                return;\n            }\n            setRgba(rgbaArr, (iv & 0xff0000) >> 16, (iv & 0xff00) >> 8, iv & 0xff, strLen === 9 ? parseInt(str.slice(7), 16) / 0xff : 1);\n            putToCache(colorStr, rgbaArr);\n            return rgbaArr;\n        }\n        return;\n    }\n    var op = str.indexOf('(');\n    var ep = str.indexOf(')');\n    if (op !== -1 && ep + 1 === strLen) {\n        var fname = str.substr(0, op);\n        var params = str.substr(op + 1, ep - (op + 1)).split(',');\n        var alpha = 1;\n        switch (fname) {\n            case 'rgba':\n                if (params.length !== 4) {\n                    return params.length === 3\n                        ? setRgba(rgbaArr, +params[0], +params[1], +params[2], 1)\n                        : setRgba(rgbaArr, 0, 0, 0, 1);\n                }\n                alpha = parseCssFloat(params.pop());\n            case 'rgb':\n                if (params.length >= 3) {\n                    setRgba(rgbaArr, parseCssInt(params[0]), parseCssInt(params[1]), parseCssInt(params[2]), params.length === 3 ? alpha : parseCssFloat(params[3]));\n                    putToCache(colorStr, rgbaArr);\n                    return rgbaArr;\n                }\n                else {\n                    setRgba(rgbaArr, 0, 0, 0, 1);\n                    return;\n                }\n            case 'hsla':\n                if (params.length !== 4) {\n                    setRgba(rgbaArr, 0, 0, 0, 1);\n                    return;\n                }\n                params[3] = parseCssFloat(params[3]);\n                hsla2rgba(params, rgbaArr);\n                putToCache(colorStr, rgbaArr);\n                return rgbaArr;\n            case 'hsl':\n                if (params.length !== 3) {\n                    setRgba(rgbaArr, 0, 0, 0, 1);\n                    return;\n                }\n                hsla2rgba(params, rgbaArr);\n                putToCache(colorStr, rgbaArr);\n                return rgbaArr;\n            default:\n                return;\n        }\n    }\n    setRgba(rgbaArr, 0, 0, 0, 1);\n    return;\n}\nfunction hsla2rgba(hsla, rgba) {\n    var h = (((parseFloat(hsla[0]) % 360) + 360) % 360) / 360;\n    var s = parseCssFloat(hsla[1]);\n    var l = parseCssFloat(hsla[2]);\n    var m2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n    var m1 = l * 2 - m2;\n    rgba = rgba || [];\n    setRgba(rgba, clampCssByte(cssHueToRgb(m1, m2, h + 1 / 3) * 255), clampCssByte(cssHueToRgb(m1, m2, h) * 255), clampCssByte(cssHueToRgb(m1, m2, h - 1 / 3) * 255), 1);\n    if (hsla.length === 4) {\n        rgba[3] = hsla[3];\n    }\n    return rgba;\n}\nfunction rgba2hsla(rgba) {\n    if (!rgba) {\n        return;\n    }\n    var R = rgba[0] / 255;\n    var G = rgba[1] / 255;\n    var B = rgba[2] / 255;\n    var vMin = Math.min(R, G, B);\n    var vMax = Math.max(R, G, B);\n    var delta = vMax - vMin;\n    var L = (vMax + vMin) / 2;\n    var H;\n    var S;\n    if (delta === 0) {\n        H = 0;\n        S = 0;\n    }\n    else {\n        if (L < 0.5) {\n            S = delta / (vMax + vMin);\n        }\n        else {\n            S = delta / (2 - vMax - vMin);\n        }\n        var deltaR = (((vMax - R) / 6) + (delta / 2)) / delta;\n        var deltaG = (((vMax - G) / 6) + (delta / 2)) / delta;\n        var deltaB = (((vMax - B) / 6) + (delta / 2)) / delta;\n        if (R === vMax) {\n            H = deltaB - deltaG;\n        }\n        else if (G === vMax) {\n            H = (1 / 3) + deltaR - deltaB;\n        }\n        else if (B === vMax) {\n            H = (2 / 3) + deltaG - deltaR;\n        }\n        if (H < 0) {\n            H += 1;\n        }\n        if (H > 1) {\n            H -= 1;\n        }\n    }\n    var hsla = [H * 360, S, L];\n    if (rgba[3] != null) {\n        hsla.push(rgba[3]);\n    }\n    return hsla;\n}\nexport function lift(color, level) {\n    var colorArr = parse(color);\n    if (colorArr) {\n        for (var i = 0; i < 3; i++) {\n            if (level < 0) {\n                colorArr[i] = colorArr[i] * (1 - level) | 0;\n            }\n            else {\n                colorArr[i] = ((255 - colorArr[i]) * level + colorArr[i]) | 0;\n            }\n            if (colorArr[i] > 255) {\n                colorArr[i] = 255;\n            }\n            else if (colorArr[i] < 0) {\n                colorArr[i] = 0;\n            }\n        }\n        return stringify(colorArr, colorArr.length === 4 ? 'rgba' : 'rgb');\n    }\n}\nexport function toHex(color) {\n    var colorArr = parse(color);\n    if (colorArr) {\n        return ((1 << 24) + (colorArr[0] << 16) + (colorArr[1] << 8) + (+colorArr[2])).toString(16).slice(1);\n    }\n}\nexport function fastLerp(normalizedValue, colors, out) {\n    if (!(colors && colors.length)\n        || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n        return;\n    }\n    out = out || [];\n    var value = normalizedValue * (colors.length - 1);\n    var leftIndex = Math.floor(value);\n    var rightIndex = Math.ceil(value);\n    var leftColor = colors[leftIndex];\n    var rightColor = colors[rightIndex];\n    var dv = value - leftIndex;\n    out[0] = clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv));\n    out[1] = clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv));\n    out[2] = clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv));\n    out[3] = clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv));\n    return out;\n}\nexport var fastMapToColor = fastLerp;\nexport function lerp(normalizedValue, colors, fullOutput) {\n    if (!(colors && colors.length)\n        || !(normalizedValue >= 0 && normalizedValue <= 1)) {\n        return;\n    }\n    var value = normalizedValue * (colors.length - 1);\n    var leftIndex = Math.floor(value);\n    var rightIndex = Math.ceil(value);\n    var leftColor = parse(colors[leftIndex]);\n    var rightColor = parse(colors[rightIndex]);\n    var dv = value - leftIndex;\n    var color = stringify([\n        clampCssByte(lerpNumber(leftColor[0], rightColor[0], dv)),\n        clampCssByte(lerpNumber(leftColor[1], rightColor[1], dv)),\n        clampCssByte(lerpNumber(leftColor[2], rightColor[2], dv)),\n        clampCssFloat(lerpNumber(leftColor[3], rightColor[3], dv))\n    ], 'rgba');\n    return fullOutput\n        ? {\n            color: color,\n            leftIndex: leftIndex,\n            rightIndex: rightIndex,\n            value: value\n        }\n        : color;\n}\nexport var mapToColor = lerp;\nexport function modifyHSL(color, h, s, l) {\n    var colorArr = parse(color);\n    if (color) {\n        colorArr = rgba2hsla(colorArr);\n        h != null && (colorArr[0] = clampCssAngle(h));\n        s != null && (colorArr[1] = parseCssFloat(s));\n        l != null && (colorArr[2] = parseCssFloat(l));\n        return stringify(hsla2rgba(colorArr), 'rgba');\n    }\n}\nexport function modifyAlpha(color, alpha) {\n    var colorArr = parse(color);\n    if (colorArr && alpha != null) {\n        colorArr[3] = clampCssFloat(alpha);\n        return stringify(colorArr, 'rgba');\n    }\n}\nexport function stringify(arrColor, type) {\n    if (!arrColor || !arrColor.length) {\n        return;\n    }\n    var colorStr = arrColor[0] + ',' + arrColor[1] + ',' + arrColor[2];\n    if (type === 'rgba' || type === 'hsva' || type === 'hsla') {\n        colorStr += ',' + arrColor[3];\n    }\n    return type + '(' + colorStr + ')';\n}\nexport function lum(color, backgroundLum) {\n    var arr = parse(color);\n    return arr\n        ? (0.299 * arr[0] + 0.587 * arr[1] + 0.114 * arr[2]) * arr[3] / 255\n            + (1 - arr[3]) * backgroundLum\n        : 0;\n}\nexport function random() {\n    return stringify([\n        Math.round(Math.random() * 255),\n        Math.round(Math.random() * 255),\n        Math.round(Math.random() * 255)\n    ], 'rgb');\n}\nvar liftedColorCache = new LRU(100);\nexport function liftColor(color) {\n    if (isString(color)) {\n        var liftedColor = liftedColorCache.get(color);\n        if (!liftedColor) {\n            liftedColor = lift(color, -0.1);\n            liftedColorCache.put(color, liftedColor);\n        }\n        return liftedColor;\n    }\n    else if (isGradientObject(color)) {\n        var ret = extend({}, color);\n        ret.colorStops = map(color.colorStops, function (stop) { return ({\n            offset: stop.offset,\n            color: lift(stop.color, -0.1)\n        }); });\n        return ret;\n    }\n    return color;\n}\n", "var Entry = (function () {\n    function Entry(val) {\n        this.value = val;\n    }\n    return Entry;\n}());\nexport { Entry };\nvar LinkedList = (function () {\n    function LinkedList() {\n        this._len = 0;\n    }\n    LinkedList.prototype.insert = function (val) {\n        var entry = new Entry(val);\n        this.insertEntry(entry);\n        return entry;\n    };\n    LinkedList.prototype.insertEntry = function (entry) {\n        if (!this.head) {\n            this.head = this.tail = entry;\n        }\n        else {\n            this.tail.next = entry;\n            entry.prev = this.tail;\n            entry.next = null;\n            this.tail = entry;\n        }\n        this._len++;\n    };\n    LinkedList.prototype.remove = function (entry) {\n        var prev = entry.prev;\n        var next = entry.next;\n        if (prev) {\n            prev.next = next;\n        }\n        else {\n            this.head = next;\n        }\n        if (next) {\n            next.prev = prev;\n        }\n        else {\n            this.tail = prev;\n        }\n        entry.next = entry.prev = null;\n        this._len--;\n    };\n    LinkedList.prototype.len = function () {\n        return this._len;\n    };\n    LinkedList.prototype.clear = function () {\n        this.head = this.tail = null;\n        this._len = 0;\n    };\n    return LinkedList;\n}());\nexport { LinkedList };\nvar LRU = (function () {\n    function LRU(maxSize) {\n        this._list = new LinkedList();\n        this._maxSize = 10;\n        this._map = {};\n        this._maxSize = maxSize;\n    }\n    LRU.prototype.put = function (key, value) {\n        var list = this._list;\n        var map = this._map;\n        var removed = null;\n        if (map[key] == null) {\n            var len = list.len();\n            var entry = this._lastRemovedEntry;\n            if (len >= this._maxSize && len > 0) {\n                var leastUsedEntry = list.head;\n                list.remove(leastUsedEntry);\n                delete map[leastUsedEntry.key];\n                removed = leastUsedEntry.value;\n                this._lastRemovedEntry = leastUsedEntry;\n            }\n            if (entry) {\n                entry.value = value;\n            }\n            else {\n                entry = new Entry(value);\n            }\n            entry.key = key;\n            list.insertEntry(entry);\n            map[key] = entry;\n        }\n        return removed;\n    };\n    LRU.prototype.get = function (key) {\n        var entry = this._map[key];\n        var list = this._list;\n        if (entry != null) {\n            if (entry !== list.tail) {\n                list.remove(entry);\n                list.insertEntry(entry);\n            }\n            return entry.value;\n        }\n    };\n    LRU.prototype.clear = function () {\n        this._list.clear();\n        this._map = {};\n    };\n    LRU.prototype.len = function () {\n        return this._list.len();\n    };\n    return LRU;\n}());\nexport default LRU;\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || from);\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n", "import LRU from '../../core/LRU.js';\nimport { platformApi } from '../../core/platform.js';\nvar globalImageCache = new LRU(50);\nexport function findExistImage(newImageOrSrc) {\n    if (typeof newImageOrSrc === 'string') {\n        var cachedImgObj = globalImageCache.get(newImageOrSrc);\n        return cachedImgObj && cachedImgObj.image;\n    }\n    else {\n        return newImageOrSrc;\n    }\n}\nexport function createOrUpdateImage(newImageOrSrc, image, hostEl, onload, cbPayload) {\n    if (!newImageOrSrc) {\n        return image;\n    }\n    else if (typeof newImageOrSrc === 'string') {\n        if ((image && image.__zrImageSrc === newImageOrSrc) || !hostEl) {\n            return image;\n        }\n        var cachedImgObj = globalImageCache.get(newImageOrSrc);\n        var pendingWrap = { hostEl: hostEl, cb: onload, cbPayload: cbPayload };\n        if (cachedImgObj) {\n            image = cachedImgObj.image;\n            !isImageReady(image) && cachedImgObj.pending.push(pendingWrap);\n        }\n        else {\n            image = platformApi.loadImage(newImageOrSrc, imageOnLoad, imageOnLoad);\n            image.__zrImageSrc = newImageOrSrc;\n            globalImageCache.put(newImageOrSrc, image.__cachedImgObj = {\n                image: image,\n                pending: [pendingWrap]\n            });\n        }\n        return image;\n    }\n    else {\n        return newImageOrSrc;\n    }\n}\nfunction imageOnLoad() {\n    var cachedImgObj = this.__cachedImgObj;\n    this.onload = this.onerror = this.__cachedImgObj = null;\n    for (var i = 0; i < cachedImgObj.pending.length; i++) {\n        var pendingWrap = cachedImgObj.pending[i];\n        var cb = pendingWrap.cb;\n        cb && cb(this, pendingWrap.cbPayload);\n        pendingWrap.hostEl.dirty();\n    }\n    cachedImgObj.pending.length = 0;\n}\nexport function isImageReady(image) {\n    return image && image.width && image.height;\n}\n", "var Point = (function () {\n    function Point(x, y) {\n        this.x = x || 0;\n        this.y = y || 0;\n    }\n    Point.prototype.copy = function (other) {\n        this.x = other.x;\n        this.y = other.y;\n        return this;\n    };\n    Point.prototype.clone = function () {\n        return new Point(this.x, this.y);\n    };\n    Point.prototype.set = function (x, y) {\n        this.x = x;\n        this.y = y;\n        return this;\n    };\n    Point.prototype.equal = function (other) {\n        return other.x === this.x && other.y === this.y;\n    };\n    Point.prototype.add = function (other) {\n        this.x += other.x;\n        this.y += other.y;\n        return this;\n    };\n    Point.prototype.scale = function (scalar) {\n        this.x *= scalar;\n        this.y *= scalar;\n    };\n    Point.prototype.scaleAndAdd = function (other, scalar) {\n        this.x += other.x * scalar;\n        this.y += other.y * scalar;\n    };\n    Point.prototype.sub = function (other) {\n        this.x -= other.x;\n        this.y -= other.y;\n        return this;\n    };\n    Point.prototype.dot = function (other) {\n        return this.x * other.x + this.y * other.y;\n    };\n    Point.prototype.len = function () {\n        return Math.sqrt(this.x * this.x + this.y * this.y);\n    };\n    Point.prototype.lenSquare = function () {\n        return this.x * this.x + this.y * this.y;\n    };\n    Point.prototype.normalize = function () {\n        var len = this.len();\n        this.x /= len;\n        this.y /= len;\n        return this;\n    };\n    Point.prototype.distance = function (other) {\n        var dx = this.x - other.x;\n        var dy = this.y - other.y;\n        return Math.sqrt(dx * dx + dy * dy);\n    };\n    Point.prototype.distanceSquare = function (other) {\n        var dx = this.x - other.x;\n        var dy = this.y - other.y;\n        return dx * dx + dy * dy;\n    };\n    Point.prototype.negate = function () {\n        this.x = -this.x;\n        this.y = -this.y;\n        return this;\n    };\n    Point.prototype.transform = function (m) {\n        if (!m) {\n            return;\n        }\n        var x = this.x;\n        var y = this.y;\n        this.x = m[0] * x + m[2] * y + m[4];\n        this.y = m[1] * x + m[3] * y + m[5];\n        return this;\n    };\n    Point.prototype.toArray = function (out) {\n        out[0] = this.x;\n        out[1] = this.y;\n        return out;\n    };\n    Point.prototype.fromArray = function (input) {\n        this.x = input[0];\n        this.y = input[1];\n    };\n    Point.set = function (p, x, y) {\n        p.x = x;\n        p.y = y;\n    };\n    Point.copy = function (p, p2) {\n        p.x = p2.x;\n        p.y = p2.y;\n    };\n    Point.len = function (p) {\n        return Math.sqrt(p.x * p.x + p.y * p.y);\n    };\n    Point.lenSquare = function (p) {\n        return p.x * p.x + p.y * p.y;\n    };\n    Point.dot = function (p0, p1) {\n        return p0.x * p1.x + p0.y * p1.y;\n    };\n    Point.add = function (out, p0, p1) {\n        out.x = p0.x + p1.x;\n        out.y = p0.y + p1.y;\n    };\n    Point.sub = function (out, p0, p1) {\n        out.x = p0.x - p1.x;\n        out.y = p0.y - p1.y;\n    };\n    Point.scale = function (out, p0, scalar) {\n        out.x = p0.x * scalar;\n        out.y = p0.y * scalar;\n    };\n    Point.scaleAndAdd = function (out, p0, p1, scalar) {\n        out.x = p0.x + p1.x * scalar;\n        out.y = p0.y + p1.y * scalar;\n    };\n    Point.lerp = function (out, p0, p1, t) {\n        var onet = 1 - t;\n        out.x = onet * p0.x + t * p1.x;\n        out.y = onet * p0.y + t * p1.y;\n    };\n    return Point;\n}());\nexport default Point;\n", "import * as matrix from './matrix.js';\nimport Point from './Point.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar lt = new Point();\nvar rb = new Point();\nvar lb = new Point();\nvar rt = new Point();\nvar minTv = new Point();\nvar maxTv = new Point();\nvar BoundingRect = (function () {\n    function BoundingRect(x, y, width, height) {\n        if (width < 0) {\n            x = x + width;\n            width = -width;\n        }\n        if (height < 0) {\n            y = y + height;\n            height = -height;\n        }\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n    }\n    BoundingRect.prototype.union = function (other) {\n        var x = mathMin(other.x, this.x);\n        var y = mathMin(other.y, this.y);\n        if (isFinite(this.x) && isFinite(this.width)) {\n            this.width = mathMax(other.x + other.width, this.x + this.width) - x;\n        }\n        else {\n            this.width = other.width;\n        }\n        if (isFinite(this.y) && isFinite(this.height)) {\n            this.height = mathMax(other.y + other.height, this.y + this.height) - y;\n        }\n        else {\n            this.height = other.height;\n        }\n        this.x = x;\n        this.y = y;\n    };\n    BoundingRect.prototype.applyTransform = function (m) {\n        BoundingRect.applyTransform(this, this, m);\n    };\n    BoundingRect.prototype.calculateTransform = function (b) {\n        var a = this;\n        var sx = b.width / a.width;\n        var sy = b.height / a.height;\n        var m = matrix.create();\n        matrix.translate(m, m, [-a.x, -a.y]);\n        matrix.scale(m, m, [sx, sy]);\n        matrix.translate(m, m, [b.x, b.y]);\n        return m;\n    };\n    BoundingRect.prototype.intersect = function (b, mtv) {\n        if (!b) {\n            return false;\n        }\n        if (!(b instanceof BoundingRect)) {\n            b = BoundingRect.create(b);\n        }\n        var a = this;\n        var ax0 = a.x;\n        var ax1 = a.x + a.width;\n        var ay0 = a.y;\n        var ay1 = a.y + a.height;\n        var bx0 = b.x;\n        var bx1 = b.x + b.width;\n        var by0 = b.y;\n        var by1 = b.y + b.height;\n        var overlap = !(ax1 < bx0 || bx1 < ax0 || ay1 < by0 || by1 < ay0);\n        if (mtv) {\n            var dMin = Infinity;\n            var dMax = 0;\n            var d0 = Math.abs(ax1 - bx0);\n            var d1 = Math.abs(bx1 - ax0);\n            var d2 = Math.abs(ay1 - by0);\n            var d3 = Math.abs(by1 - ay0);\n            var dx = Math.min(d0, d1);\n            var dy = Math.min(d2, d3);\n            if (ax1 < bx0 || bx1 < ax0) {\n                if (dx > dMax) {\n                    dMax = dx;\n                    if (d0 < d1) {\n                        Point.set(maxTv, -d0, 0);\n                    }\n                    else {\n                        Point.set(maxTv, d1, 0);\n                    }\n                }\n            }\n            else {\n                if (dx < dMin) {\n                    dMin = dx;\n                    if (d0 < d1) {\n                        Point.set(minTv, d0, 0);\n                    }\n                    else {\n                        Point.set(minTv, -d1, 0);\n                    }\n                }\n            }\n            if (ay1 < by0 || by1 < ay0) {\n                if (dy > dMax) {\n                    dMax = dy;\n                    if (d2 < d3) {\n                        Point.set(maxTv, 0, -d2);\n                    }\n                    else {\n                        Point.set(maxTv, 0, d3);\n                    }\n                }\n            }\n            else {\n                if (dx < dMin) {\n                    dMin = dx;\n                    if (d2 < d3) {\n                        Point.set(minTv, 0, d2);\n                    }\n                    else {\n                        Point.set(minTv, 0, -d3);\n                    }\n                }\n            }\n        }\n        if (mtv) {\n            Point.copy(mtv, overlap ? minTv : maxTv);\n        }\n        return overlap;\n    };\n    BoundingRect.prototype.contain = function (x, y) {\n        var rect = this;\n        return x >= rect.x\n            && x <= (rect.x + rect.width)\n            && y >= rect.y\n            && y <= (rect.y + rect.height);\n    };\n    BoundingRect.prototype.clone = function () {\n        return new BoundingRect(this.x, this.y, this.width, this.height);\n    };\n    BoundingRect.prototype.copy = function (other) {\n        BoundingRect.copy(this, other);\n    };\n    BoundingRect.prototype.plain = function () {\n        return {\n            x: this.x,\n            y: this.y,\n            width: this.width,\n            height: this.height\n        };\n    };\n    BoundingRect.prototype.isFinite = function () {\n        return isFinite(this.x)\n            && isFinite(this.y)\n            && isFinite(this.width)\n            && isFinite(this.height);\n    };\n    BoundingRect.prototype.isZero = function () {\n        return this.width === 0 || this.height === 0;\n    };\n    BoundingRect.create = function (rect) {\n        return new BoundingRect(rect.x, rect.y, rect.width, rect.height);\n    };\n    BoundingRect.copy = function (target, source) {\n        target.x = source.x;\n        target.y = source.y;\n        target.width = source.width;\n        target.height = source.height;\n    };\n    BoundingRect.applyTransform = function (target, source, m) {\n        if (!m) {\n            if (target !== source) {\n                BoundingRect.copy(target, source);\n            }\n            return;\n        }\n        if (m[1] < 1e-5 && m[1] > -1e-5 && m[2] < 1e-5 && m[2] > -1e-5) {\n            var sx = m[0];\n            var sy = m[3];\n            var tx = m[4];\n            var ty = m[5];\n            target.x = source.x * sx + tx;\n            target.y = source.y * sy + ty;\n            target.width = source.width * sx;\n            target.height = source.height * sy;\n            if (target.width < 0) {\n                target.x += target.width;\n                target.width = -target.width;\n            }\n            if (target.height < 0) {\n                target.y += target.height;\n                target.height = -target.height;\n            }\n            return;\n        }\n        lt.x = lb.x = source.x;\n        lt.y = rt.y = source.y;\n        rb.x = rt.x = source.x + source.width;\n        rb.y = lb.y = source.y + source.height;\n        lt.transform(m);\n        rt.transform(m);\n        rb.transform(m);\n        lb.transform(m);\n        target.x = mathMin(lt.x, rb.x, lb.x, rt.x);\n        target.y = mathMin(lt.y, rb.y, lb.y, rt.y);\n        var maxX = mathMax(lt.x, rb.x, lb.x, rt.x);\n        var maxY = mathMax(lt.y, rb.y, lb.y, rt.y);\n        target.width = maxX - target.x;\n        target.height = maxY - target.y;\n    };\n    return BoundingRect;\n}());\nexport default BoundingRect;\n", "import BoundingRect from '../core/BoundingRect.js';\nimport LRU from '../core/LRU.js';\nimport { DEFAULT_FONT, platformApi } from '../core/platform.js';\nvar textWidthCache = {};\nexport function getWidth(text, font) {\n    font = font || DEFAULT_FONT;\n    var cacheOfFont = textWidthCache[font];\n    if (!cacheOfFont) {\n        cacheOfFont = textWidthCache[font] = new LRU(500);\n    }\n    var width = cacheOfFont.get(text);\n    if (width == null) {\n        width = platformApi.measureText(text, font).width;\n        cacheOfFont.put(text, width);\n    }\n    return width;\n}\nexport function innerGetBoundingRect(text, font, textAlign, textBaseline) {\n    var width = getWidth(text, font);\n    var height = getLineHeight(font);\n    var x = adjustTextX(0, width, textAlign);\n    var y = adjustTextY(0, height, textBaseline);\n    var rect = new BoundingRect(x, y, width, height);\n    return rect;\n}\nexport function getBoundingRect(text, font, textAlign, textBaseline) {\n    var textLines = ((text || '') + '').split('\\n');\n    var len = textLines.length;\n    if (len === 1) {\n        return innerGetBoundingRect(textLines[0], font, textAlign, textBaseline);\n    }\n    else {\n        var uniondRect = new BoundingRect(0, 0, 0, 0);\n        for (var i = 0; i < textLines.length; i++) {\n            var rect = innerGetBoundingRect(textLines[i], font, textAlign, textBaseline);\n            i === 0 ? uniondRect.copy(rect) : uniondRect.union(rect);\n        }\n        return uniondRect;\n    }\n}\nexport function adjustTextX(x, width, textAlign) {\n    if (textAlign === 'right') {\n        x -= width;\n    }\n    else if (textAlign === 'center') {\n        x -= width / 2;\n    }\n    return x;\n}\nexport function adjustTextY(y, height, verticalAlign) {\n    if (verticalAlign === 'middle') {\n        y -= height / 2;\n    }\n    else if (verticalAlign === 'bottom') {\n        y -= height;\n    }\n    return y;\n}\nexport function getLineHeight(font) {\n    return getWidth('国', font);\n}\nexport function measureText(text, font) {\n    return platformApi.measureText(text, font);\n}\nexport function parsePercent(value, maxValue) {\n    if (typeof value === 'string') {\n        if (value.lastIndexOf('%') >= 0) {\n            return parseFloat(value) / 100 * maxValue;\n        }\n        return parseFloat(value);\n    }\n    return value;\n}\nexport function calculateTextPosition(out, opts, rect) {\n    var textPosition = opts.position || 'inside';\n    var distance = opts.distance != null ? opts.distance : 5;\n    var height = rect.height;\n    var width = rect.width;\n    var halfHeight = height / 2;\n    var x = rect.x;\n    var y = rect.y;\n    var textAlign = 'left';\n    var textVerticalAlign = 'top';\n    if (textPosition instanceof Array) {\n        x += parsePercent(textPosition[0], rect.width);\n        y += parsePercent(textPosition[1], rect.height);\n        textAlign = null;\n        textVerticalAlign = null;\n    }\n    else {\n        switch (textPosition) {\n            case 'left':\n                x -= distance;\n                y += halfHeight;\n                textAlign = 'right';\n                textVerticalAlign = 'middle';\n                break;\n            case 'right':\n                x += distance + width;\n                y += halfHeight;\n                textVerticalAlign = 'middle';\n                break;\n            case 'top':\n                x += width / 2;\n                y -= distance;\n                textAlign = 'center';\n                textVerticalAlign = 'bottom';\n                break;\n            case 'bottom':\n                x += width / 2;\n                y += height + distance;\n                textAlign = 'center';\n                break;\n            case 'inside':\n                x += width / 2;\n                y += halfHeight;\n                textAlign = 'center';\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideLeft':\n                x += distance;\n                y += halfHeight;\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideRight':\n                x += width - distance;\n                y += halfHeight;\n                textAlign = 'right';\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideTop':\n                x += width / 2;\n                y += distance;\n                textAlign = 'center';\n                break;\n            case 'insideBottom':\n                x += width / 2;\n                y += height - distance;\n                textAlign = 'center';\n                textVerticalAlign = 'bottom';\n                break;\n            case 'insideTopLeft':\n                x += distance;\n                y += distance;\n                break;\n            case 'insideTopRight':\n                x += width - distance;\n                y += distance;\n                textAlign = 'right';\n                break;\n            case 'insideBottomLeft':\n                x += distance;\n                y += height - distance;\n                textVerticalAlign = 'bottom';\n                break;\n            case 'insideBottomRight':\n                x += width - distance;\n                y += height - distance;\n                textAlign = 'right';\n                textVerticalAlign = 'bottom';\n                break;\n        }\n    }\n    out = out || {};\n    out.x = x;\n    out.y = y;\n    out.align = textAlign;\n    out.verticalAlign = textVerticalAlign;\n    return out;\n}\n", "import * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce } from '../../core/util.js';\nimport { getLineHeight, getWidth, parsePercent } from '../../contain/text.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n    var out = {};\n    truncateText2(out, text, containerWidth, font, ellipsis, options);\n    return out.text;\n}\nfunction truncateText2(out, text, containerWidth, font, ellipsis, options) {\n    if (!containerWidth) {\n        out.text = '';\n        out.isTruncated = false;\n        return;\n    }\n    var textLines = (text + '').split('\\n');\n    options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n    var isTruncated = false;\n    var truncateOut = {};\n    for (var i = 0, len = textLines.length; i < len; i++) {\n        truncateSingleLine(truncateOut, textLines[i], options);\n        textLines[i] = truncateOut.textLine;\n        isTruncated = isTruncated || truncateOut.isTruncated;\n    }\n    out.text = textLines.join('\\n');\n    out.isTruncated = isTruncated;\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n    options = options || {};\n    var preparedOpts = extend({}, options);\n    preparedOpts.font = font;\n    ellipsis = retrieve2(ellipsis, '...');\n    preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n    var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n    preparedOpts.cnCharWidth = getWidth('国', font);\n    var ascCharWidth = preparedOpts.ascCharWidth = getWidth('a', font);\n    preparedOpts.placeholder = retrieve2(options.placeholder, '');\n    var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n    for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n        contentWidth -= ascCharWidth;\n    }\n    var ellipsisWidth = getWidth(ellipsis, font);\n    if (ellipsisWidth > contentWidth) {\n        ellipsis = '';\n        ellipsisWidth = 0;\n    }\n    contentWidth = containerWidth - ellipsisWidth;\n    preparedOpts.ellipsis = ellipsis;\n    preparedOpts.ellipsisWidth = ellipsisWidth;\n    preparedOpts.contentWidth = contentWidth;\n    preparedOpts.containerWidth = containerWidth;\n    return preparedOpts;\n}\nfunction truncateSingleLine(out, textLine, options) {\n    var containerWidth = options.containerWidth;\n    var font = options.font;\n    var contentWidth = options.contentWidth;\n    if (!containerWidth) {\n        out.textLine = '';\n        out.isTruncated = false;\n        return;\n    }\n    var lineWidth = getWidth(textLine, font);\n    if (lineWidth <= containerWidth) {\n        out.textLine = textLine;\n        out.isTruncated = false;\n        return;\n    }\n    for (var j = 0;; j++) {\n        if (lineWidth <= contentWidth || j >= options.maxIterations) {\n            textLine += options.ellipsis;\n            break;\n        }\n        var subLength = j === 0\n            ? estimateLength(textLine, contentWidth, options.ascCharWidth, options.cnCharWidth)\n            : lineWidth > 0\n                ? Math.floor(textLine.length * contentWidth / lineWidth)\n                : 0;\n        textLine = textLine.substr(0, subLength);\n        lineWidth = getWidth(textLine, font);\n    }\n    if (textLine === '') {\n        textLine = options.placeholder;\n    }\n    out.textLine = textLine;\n    out.isTruncated = true;\n}\nfunction estimateLength(text, contentWidth, ascCharWidth, cnCharWidth) {\n    var width = 0;\n    var i = 0;\n    for (var len = text.length; i < len && width < contentWidth; i++) {\n        var charCode = text.charCodeAt(i);\n        width += (0 <= charCode && charCode <= 127) ? ascCharWidth : cnCharWidth;\n    }\n    return i;\n}\nexport function parsePlainText(text, style) {\n    text != null && (text += '');\n    var overflow = style.overflow;\n    var padding = style.padding;\n    var font = style.font;\n    var truncate = overflow === 'truncate';\n    var calculatedLineHeight = getLineHeight(font);\n    var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n    var bgColorDrawn = !!(style.backgroundColor);\n    var truncateLineOverflow = style.lineOverflow === 'truncate';\n    var isTruncated = false;\n    var width = style.width;\n    var lines;\n    if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n        lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n    }\n    else {\n        lines = text ? text.split('\\n') : [];\n    }\n    var contentHeight = lines.length * lineHeight;\n    var height = retrieve2(style.height, contentHeight);\n    if (contentHeight > height && truncateLineOverflow) {\n        var lineCount = Math.floor(height / lineHeight);\n        isTruncated = isTruncated || (lines.length > lineCount);\n        lines = lines.slice(0, lineCount);\n    }\n    if (text && truncate && width != null) {\n        var options = prepareTruncateOptions(width, font, style.ellipsis, {\n            minChar: style.truncateMinChar,\n            placeholder: style.placeholder\n        });\n        var singleOut = {};\n        for (var i = 0; i < lines.length; i++) {\n            truncateSingleLine(singleOut, lines[i], options);\n            lines[i] = singleOut.textLine;\n            isTruncated = isTruncated || singleOut.isTruncated;\n        }\n    }\n    var outerHeight = height;\n    var contentWidth = 0;\n    for (var i = 0; i < lines.length; i++) {\n        contentWidth = Math.max(getWidth(lines[i], font), contentWidth);\n    }\n    if (width == null) {\n        width = contentWidth;\n    }\n    var outerWidth = contentWidth;\n    if (padding) {\n        outerHeight += padding[0] + padding[2];\n        outerWidth += padding[1] + padding[3];\n        width += padding[1] + padding[3];\n    }\n    if (bgColorDrawn) {\n        outerWidth = width;\n    }\n    return {\n        lines: lines,\n        height: height,\n        outerWidth: outerWidth,\n        outerHeight: outerHeight,\n        lineHeight: lineHeight,\n        calculatedLineHeight: calculatedLineHeight,\n        contentWidth: contentWidth,\n        contentHeight: contentHeight,\n        width: width,\n        isTruncated: isTruncated\n    };\n}\nvar RichTextToken = (function () {\n    function RichTextToken() {\n    }\n    return RichTextToken;\n}());\nvar RichTextLine = (function () {\n    function RichTextLine(tokens) {\n        this.tokens = [];\n        if (tokens) {\n            this.tokens = tokens;\n        }\n    }\n    return RichTextLine;\n}());\nvar RichTextContentBlock = (function () {\n    function RichTextContentBlock() {\n        this.width = 0;\n        this.height = 0;\n        this.contentWidth = 0;\n        this.contentHeight = 0;\n        this.outerWidth = 0;\n        this.outerHeight = 0;\n        this.lines = [];\n        this.isTruncated = false;\n    }\n    return RichTextContentBlock;\n}());\nexport { RichTextContentBlock };\nexport function parseRichText(text, style) {\n    var contentBlock = new RichTextContentBlock();\n    text != null && (text += '');\n    if (!text) {\n        return contentBlock;\n    }\n    var topWidth = style.width;\n    var topHeight = style.height;\n    var overflow = style.overflow;\n    var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null\n        ? { width: topWidth, accumWidth: 0, breakAll: overflow === 'breakAll' }\n        : null;\n    var lastIndex = STYLE_REG.lastIndex = 0;\n    var result;\n    while ((result = STYLE_REG.exec(text)) != null) {\n        var matchedIndex = result.index;\n        if (matchedIndex > lastIndex) {\n            pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n        }\n        pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n        lastIndex = STYLE_REG.lastIndex;\n    }\n    if (lastIndex < text.length) {\n        pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n    }\n    var pendingList = [];\n    var calculatedHeight = 0;\n    var calculatedWidth = 0;\n    var stlPadding = style.padding;\n    var truncate = overflow === 'truncate';\n    var truncateLine = style.lineOverflow === 'truncate';\n    var tmpTruncateOut = {};\n    function finishLine(line, lineWidth, lineHeight) {\n        line.width = lineWidth;\n        line.lineHeight = lineHeight;\n        calculatedHeight += lineHeight;\n        calculatedWidth = Math.max(calculatedWidth, lineWidth);\n    }\n    outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n        var line = contentBlock.lines[i];\n        var lineHeight = 0;\n        var lineWidth = 0;\n        for (var j = 0; j < line.tokens.length; j++) {\n            var token = line.tokens[j];\n            var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n            var textPadding = token.textPadding = tokenStyle.padding;\n            var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n            var font = token.font = tokenStyle.font || style.font;\n            token.contentHeight = getLineHeight(font);\n            var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n            token.innerHeight = tokenHeight;\n            textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n            token.height = tokenHeight;\n            token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n            token.align = tokenStyle && tokenStyle.align || style.align;\n            token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n            if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n                var originalLength = contentBlock.lines.length;\n                if (j > 0) {\n                    line.tokens = line.tokens.slice(0, j);\n                    finishLine(line, lineWidth, lineHeight);\n                    contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n                }\n                else {\n                    contentBlock.lines = contentBlock.lines.slice(0, i);\n                }\n                contentBlock.isTruncated = contentBlock.isTruncated || (contentBlock.lines.length < originalLength);\n                break outer;\n            }\n            var styleTokenWidth = tokenStyle.width;\n            var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n            if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n                token.percentWidth = styleTokenWidth;\n                pendingList.push(token);\n                token.contentWidth = getWidth(token.text, font);\n            }\n            else {\n                if (tokenWidthNotSpecified) {\n                    var textBackgroundColor = tokenStyle.backgroundColor;\n                    var bgImg = textBackgroundColor && textBackgroundColor.image;\n                    if (bgImg) {\n                        bgImg = imageHelper.findExistImage(bgImg);\n                        if (imageHelper.isImageReady(bgImg)) {\n                            token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n                        }\n                    }\n                }\n                var remainTruncWidth = truncate && topWidth != null\n                    ? topWidth - lineWidth : null;\n                if (remainTruncWidth != null && remainTruncWidth < token.width) {\n                    if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n                        token.text = '';\n                        token.width = token.contentWidth = 0;\n                    }\n                    else {\n                        truncateText2(tmpTruncateOut, token.text, remainTruncWidth - paddingH, font, style.ellipsis, { minChar: style.truncateMinChar });\n                        token.text = tmpTruncateOut.text;\n                        contentBlock.isTruncated = contentBlock.isTruncated || tmpTruncateOut.isTruncated;\n                        token.width = token.contentWidth = getWidth(token.text, font);\n                    }\n                }\n                else {\n                    token.contentWidth = getWidth(token.text, font);\n                }\n            }\n            token.width += paddingH;\n            lineWidth += token.width;\n            tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n        }\n        finishLine(line, lineWidth, lineHeight);\n    }\n    contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n    contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n    contentBlock.contentHeight = calculatedHeight;\n    contentBlock.contentWidth = calculatedWidth;\n    if (stlPadding) {\n        contentBlock.outerWidth += stlPadding[1] + stlPadding[3];\n        contentBlock.outerHeight += stlPadding[0] + stlPadding[2];\n    }\n    for (var i = 0; i < pendingList.length; i++) {\n        var token = pendingList[i];\n        var percentWidth = token.percentWidth;\n        token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n    }\n    return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n    var isEmptyStr = str === '';\n    var tokenStyle = styleName && style.rich[styleName] || {};\n    var lines = block.lines;\n    var font = tokenStyle.font || style.font;\n    var newLine = false;\n    var strLines;\n    var linesWidths;\n    if (wrapInfo) {\n        var tokenPadding = tokenStyle.padding;\n        var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n        if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n            var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n            if (lines.length > 0) {\n                if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n                    strLines = str.split('\\n');\n                    newLine = true;\n                }\n            }\n            wrapInfo.accumWidth = outerWidth_1;\n        }\n        else {\n            var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n            wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n            linesWidths = res.linesWidths;\n            strLines = res.lines;\n        }\n    }\n    else {\n        strLines = str.split('\\n');\n    }\n    for (var i = 0; i < strLines.length; i++) {\n        var text = strLines[i];\n        var token = new RichTextToken();\n        token.styleName = styleName;\n        token.text = text;\n        token.isLineHolder = !text && !isEmptyStr;\n        if (typeof tokenStyle.width === 'number') {\n            token.width = tokenStyle.width;\n        }\n        else {\n            token.width = linesWidths\n                ? linesWidths[i]\n                : getWidth(text, font);\n        }\n        if (!i && !newLine) {\n            var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n            var tokensLen = tokens.length;\n            (tokensLen === 1 && tokens[0].isLineHolder)\n                ? (tokens[0] = token)\n                : ((text || !tokensLen || isEmptyStr) && tokens.push(token));\n        }\n        else {\n            lines.push(new RichTextLine([token]));\n        }\n    }\n}\nfunction isAlphabeticLetter(ch) {\n    var code = ch.charCodeAt(0);\n    return code >= 0x20 && code <= 0x24F\n        || code >= 0x370 && code <= 0x10FF\n        || code >= 0x1200 && code <= 0x13FF\n        || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n    obj[ch] = true;\n    return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n    if (isAlphabeticLetter(ch)) {\n        if (breakCharMap[ch]) {\n            return true;\n        }\n        return false;\n    }\n    return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n    var lines = [];\n    var linesWidths = [];\n    var line = '';\n    var currentWord = '';\n    var currentWordWidth = 0;\n    var accumWidth = 0;\n    for (var i = 0; i < text.length; i++) {\n        var ch = text.charAt(i);\n        if (ch === '\\n') {\n            if (currentWord) {\n                line += currentWord;\n                accumWidth += currentWordWidth;\n            }\n            lines.push(line);\n            linesWidths.push(accumWidth);\n            line = '';\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = 0;\n            continue;\n        }\n        var chWidth = getWidth(ch, font);\n        var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n        if (!lines.length\n            ? lastAccumWidth + accumWidth + chWidth > lineWidth\n            : accumWidth + chWidth > lineWidth) {\n            if (!accumWidth) {\n                if (inWord) {\n                    lines.push(currentWord);\n                    linesWidths.push(currentWordWidth);\n                    currentWord = ch;\n                    currentWordWidth = chWidth;\n                }\n                else {\n                    lines.push(ch);\n                    linesWidths.push(chWidth);\n                }\n            }\n            else if (line || currentWord) {\n                if (inWord) {\n                    if (!line) {\n                        line = currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                        accumWidth = currentWordWidth;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth - currentWordWidth);\n                    currentWord += ch;\n                    currentWordWidth += chWidth;\n                    line = '';\n                    accumWidth = currentWordWidth;\n                }\n                else {\n                    if (currentWord) {\n                        line += currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth);\n                    line = ch;\n                    accumWidth = chWidth;\n                }\n            }\n            continue;\n        }\n        accumWidth += chWidth;\n        if (inWord) {\n            currentWord += ch;\n            currentWordWidth += chWidth;\n        }\n        else {\n            if (currentWord) {\n                line += currentWord;\n                currentWord = '';\n                currentWordWidth = 0;\n            }\n            line += ch;\n        }\n    }\n    if (!lines.length && !line) {\n        line = text;\n        currentWord = '';\n        currentWordWidth = 0;\n    }\n    if (currentWord) {\n        line += currentWord;\n    }\n    if (line) {\n        lines.push(line);\n        linesWidths.push(accumWidth);\n    }\n    if (lines.length === 1) {\n        accumWidth += lastAccumWidth;\n    }\n    return {\n        accumWidth: accumWidth,\n        lines: lines,\n        linesWidths: linesWidths\n    };\n}\n", "var Eventful = (function () {\n    function Eventful(eventProcessors) {\n        if (eventProcessors) {\n            this._$eventProcessor = eventProcessors;\n        }\n    }\n    Eventful.prototype.on = function (event, query, handler, context) {\n        if (!this._$handlers) {\n            this._$handlers = {};\n        }\n        var _h = this._$handlers;\n        if (typeof query === 'function') {\n            context = handler;\n            handler = query;\n            query = null;\n        }\n        if (!handler || !event) {\n            return this;\n        }\n        var eventProcessor = this._$eventProcessor;\n        if (query != null && eventProcessor && eventProcessor.normalizeQuery) {\n            query = eventProcessor.normalizeQuery(query);\n        }\n        if (!_h[event]) {\n            _h[event] = [];\n        }\n        for (var i = 0; i < _h[event].length; i++) {\n            if (_h[event][i].h === handler) {\n                return this;\n            }\n        }\n        var wrap = {\n            h: handler,\n            query: query,\n            ctx: (context || this),\n            callAtLast: handler.zrEventfulCallAtLast\n        };\n        var lastIndex = _h[event].length - 1;\n        var lastWrap = _h[event][lastIndex];\n        (lastWrap && lastWrap.callAtLast)\n            ? _h[event].splice(lastIndex, 0, wrap)\n            : _h[event].push(wrap);\n        return this;\n    };\n    Eventful.prototype.isSilent = function (eventName) {\n        var _h = this._$handlers;\n        return !_h || !_h[eventName] || !_h[eventName].length;\n    };\n    Eventful.prototype.off = function (eventType, handler) {\n        var _h = this._$handlers;\n        if (!_h) {\n            return this;\n        }\n        if (!eventType) {\n            this._$handlers = {};\n            return this;\n        }\n        if (handler) {\n            if (_h[eventType]) {\n                var newList = [];\n                for (var i = 0, l = _h[eventType].length; i < l; i++) {\n                    if (_h[eventType][i].h !== handler) {\n                        newList.push(_h[eventType][i]);\n                    }\n                }\n                _h[eventType] = newList;\n            }\n            if (_h[eventType] && _h[eventType].length === 0) {\n                delete _h[eventType];\n            }\n        }\n        else {\n            delete _h[eventType];\n        }\n        return this;\n    };\n    Eventful.prototype.trigger = function (eventType) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (!this._$handlers) {\n            return this;\n        }\n        var _h = this._$handlers[eventType];\n        var eventProcessor = this._$eventProcessor;\n        if (_h) {\n            var argLen = args.length;\n            var len = _h.length;\n            for (var i = 0; i < len; i++) {\n                var hItem = _h[i];\n                if (eventProcessor\n                    && eventProcessor.filter\n                    && hItem.query != null\n                    && !eventProcessor.filter(eventType, hItem.query)) {\n                    continue;\n                }\n                switch (argLen) {\n                    case 0:\n                        hItem.h.call(hItem.ctx);\n                        break;\n                    case 1:\n                        hItem.h.call(hItem.ctx, args[0]);\n                        break;\n                    case 2:\n                        hItem.h.call(hItem.ctx, args[0], args[1]);\n                        break;\n                    default:\n                        hItem.h.apply(hItem.ctx, args);\n                        break;\n                }\n            }\n        }\n        eventProcessor && eventProcessor.afterTrigger\n            && eventProcessor.afterTrigger(eventType);\n        return this;\n    };\n    Eventful.prototype.triggerWithContext = function (type) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (!this._$handlers) {\n            return this;\n        }\n        var _h = this._$handlers[type];\n        var eventProcessor = this._$eventProcessor;\n        if (_h) {\n            var argLen = args.length;\n            var ctx = args[argLen - 1];\n            var len = _h.length;\n            for (var i = 0; i < len; i++) {\n                var hItem = _h[i];\n                if (eventProcessor\n                    && eventProcessor.filter\n                    && hItem.query != null\n                    && !eventProcessor.filter(type, hItem.query)) {\n                    continue;\n                }\n                switch (argLen) {\n                    case 0:\n                        hItem.h.call(ctx);\n                        break;\n                    case 1:\n                        hItem.h.call(ctx, args[0]);\n                        break;\n                    case 2:\n                        hItem.h.call(ctx, args[0], args[1]);\n                        break;\n                    default:\n                        hItem.h.apply(ctx, args.slice(1, argLen - 1));\n                        break;\n                }\n            }\n        }\n        eventProcessor && eventProcessor.afterTrigger\n            && eventProcessor.afterTrigger(type);\n        return this;\n    };\n    return Eventful;\n}());\nexport default Eventful;\n", "import env from './core/env.js';\nvar dpr = 1;\nif (env.hasGlobalWindow) {\n    dpr = Math.max(window.devicePixelRatio\n        || (window.screen && window.screen.deviceXDPI / window.screen.logicalXDPI)\n        || 1, 1);\n}\nexport var debugMode = 0;\nexport var devicePixelRatio = dpr;\nexport var DARK_MODE_THRESHOLD = 0.4;\nexport var DARK_LABEL_COLOR = '#333';\nexport var LIGHT_LABEL_COLOR = '#ccc';\nexport var LIGHTER_LABEL_COLOR = '#eee';\n", "export var REDRAW_BIT = 1;\nexport var STYLE_CHANGED_BIT = 2;\nexport var SHAPE_CHANGED_BIT = 4;\n", "import * as matrix from './matrix.js';\nimport * as vector from './vector.js';\nvar mIdentity = matrix.identity;\nvar EPSILON = 5e-5;\nfunction isNotAroundZero(val) {\n    return val > EPSILON || val < -EPSILON;\n}\nvar scaleTmp = [];\nvar tmpTransform = [];\nvar originTransform = matrix.create();\nvar abs = Math.abs;\nvar Transformable = (function () {\n    function Transformable() {\n    }\n    Transformable.prototype.getLocalTransform = function (m) {\n        return Transformable.getLocalTransform(this, m);\n    };\n    Transformable.prototype.setPosition = function (arr) {\n        this.x = arr[0];\n        this.y = arr[1];\n    };\n    Transformable.prototype.setScale = function (arr) {\n        this.scaleX = arr[0];\n        this.scaleY = arr[1];\n    };\n    Transformable.prototype.setSkew = function (arr) {\n        this.skewX = arr[0];\n        this.skewY = arr[1];\n    };\n    Transformable.prototype.setOrigin = function (arr) {\n        this.originX = arr[0];\n        this.originY = arr[1];\n    };\n    Transformable.prototype.needLocalTransform = function () {\n        return isNotAroundZero(this.rotation)\n            || isNotAroundZero(this.x)\n            || isNotAroundZero(this.y)\n            || isNotAroundZero(this.scaleX - 1)\n            || isNotAroundZero(this.scaleY - 1)\n            || isNotAroundZero(this.skewX)\n            || isNotAroundZero(this.skewY);\n    };\n    Transformable.prototype.updateTransform = function () {\n        var parentTransform = this.parent && this.parent.transform;\n        var needLocalTransform = this.needLocalTransform();\n        var m = this.transform;\n        if (!(needLocalTransform || parentTransform)) {\n            if (m) {\n                mIdentity(m);\n                this.invTransform = null;\n            }\n            return;\n        }\n        m = m || matrix.create();\n        if (needLocalTransform) {\n            this.getLocalTransform(m);\n        }\n        else {\n            mIdentity(m);\n        }\n        if (parentTransform) {\n            if (needLocalTransform) {\n                matrix.mul(m, parentTransform, m);\n            }\n            else {\n                matrix.copy(m, parentTransform);\n            }\n        }\n        this.transform = m;\n        this._resolveGlobalScaleRatio(m);\n    };\n    Transformable.prototype._resolveGlobalScaleRatio = function (m) {\n        var globalScaleRatio = this.globalScaleRatio;\n        if (globalScaleRatio != null && globalScaleRatio !== 1) {\n            this.getGlobalScale(scaleTmp);\n            var relX = scaleTmp[0] < 0 ? -1 : 1;\n            var relY = scaleTmp[1] < 0 ? -1 : 1;\n            var sx = ((scaleTmp[0] - relX) * globalScaleRatio + relX) / scaleTmp[0] || 0;\n            var sy = ((scaleTmp[1] - relY) * globalScaleRatio + relY) / scaleTmp[1] || 0;\n            m[0] *= sx;\n            m[1] *= sx;\n            m[2] *= sy;\n            m[3] *= sy;\n        }\n        this.invTransform = this.invTransform || matrix.create();\n        matrix.invert(this.invTransform, m);\n    };\n    Transformable.prototype.getComputedTransform = function () {\n        var transformNode = this;\n        var ancestors = [];\n        while (transformNode) {\n            ancestors.push(transformNode);\n            transformNode = transformNode.parent;\n        }\n        while (transformNode = ancestors.pop()) {\n            transformNode.updateTransform();\n        }\n        return this.transform;\n    };\n    Transformable.prototype.setLocalTransform = function (m) {\n        if (!m) {\n            return;\n        }\n        var sx = m[0] * m[0] + m[1] * m[1];\n        var sy = m[2] * m[2] + m[3] * m[3];\n        var rotation = Math.atan2(m[1], m[0]);\n        var shearX = Math.PI / 2 + rotation - Math.atan2(m[3], m[2]);\n        sy = Math.sqrt(sy) * Math.cos(shearX);\n        sx = Math.sqrt(sx);\n        this.skewX = shearX;\n        this.skewY = 0;\n        this.rotation = -rotation;\n        this.x = +m[4];\n        this.y = +m[5];\n        this.scaleX = sx;\n        this.scaleY = sy;\n        this.originX = 0;\n        this.originY = 0;\n    };\n    Transformable.prototype.decomposeTransform = function () {\n        if (!this.transform) {\n            return;\n        }\n        var parent = this.parent;\n        var m = this.transform;\n        if (parent && parent.transform) {\n            parent.invTransform = parent.invTransform || matrix.create();\n            matrix.mul(tmpTransform, parent.invTransform, m);\n            m = tmpTransform;\n        }\n        var ox = this.originX;\n        var oy = this.originY;\n        if (ox || oy) {\n            originTransform[4] = ox;\n            originTransform[5] = oy;\n            matrix.mul(tmpTransform, m, originTransform);\n            tmpTransform[4] -= ox;\n            tmpTransform[5] -= oy;\n            m = tmpTransform;\n        }\n        this.setLocalTransform(m);\n    };\n    Transformable.prototype.getGlobalScale = function (out) {\n        var m = this.transform;\n        out = out || [];\n        if (!m) {\n            out[0] = 1;\n            out[1] = 1;\n            return out;\n        }\n        out[0] = Math.sqrt(m[0] * m[0] + m[1] * m[1]);\n        out[1] = Math.sqrt(m[2] * m[2] + m[3] * m[3]);\n        if (m[0] < 0) {\n            out[0] = -out[0];\n        }\n        if (m[3] < 0) {\n            out[1] = -out[1];\n        }\n        return out;\n    };\n    Transformable.prototype.transformCoordToLocal = function (x, y) {\n        var v2 = [x, y];\n        var invTransform = this.invTransform;\n        if (invTransform) {\n            vector.applyTransform(v2, v2, invTransform);\n        }\n        return v2;\n    };\n    Transformable.prototype.transformCoordToGlobal = function (x, y) {\n        var v2 = [x, y];\n        var transform = this.transform;\n        if (transform) {\n            vector.applyTransform(v2, v2, transform);\n        }\n        return v2;\n    };\n    Transformable.prototype.getLineScale = function () {\n        var m = this.transform;\n        return m && abs(m[0] - 1) > 1e-10 && abs(m[3] - 1) > 1e-10\n            ? Math.sqrt(abs(m[0] * m[3] - m[2] * m[1]))\n            : 1;\n    };\n    Transformable.prototype.copyTransform = function (source) {\n        copyTransform(this, source);\n    };\n    Transformable.getLocalTransform = function (target, m) {\n        m = m || [];\n        var ox = target.originX || 0;\n        var oy = target.originY || 0;\n        var sx = target.scaleX;\n        var sy = target.scaleY;\n        var ax = target.anchorX;\n        var ay = target.anchorY;\n        var rotation = target.rotation || 0;\n        var x = target.x;\n        var y = target.y;\n        var skewX = target.skewX ? Math.tan(target.skewX) : 0;\n        var skewY = target.skewY ? Math.tan(-target.skewY) : 0;\n        if (ox || oy || ax || ay) {\n            var dx = ox + ax;\n            var dy = oy + ay;\n            m[4] = -dx * sx - skewX * dy * sy;\n            m[5] = -dy * sy - skewY * dx * sx;\n        }\n        else {\n            m[4] = m[5] = 0;\n        }\n        m[0] = sx;\n        m[3] = sy;\n        m[1] = skewY * sx;\n        m[2] = skewX * sy;\n        rotation && matrix.rotate(m, m, rotation);\n        m[4] += ox + x;\n        m[5] += oy + y;\n        return m;\n    };\n    Transformable.initDefaultProps = (function () {\n        var proto = Transformable.prototype;\n        proto.scaleX =\n            proto.scaleY =\n                proto.globalScaleRatio = 1;\n        proto.x =\n            proto.y =\n                proto.originX =\n                    proto.originY =\n                        proto.skewX =\n                            proto.skewY =\n                                proto.rotation =\n                                    proto.anchorX =\n                                        proto.anchorY = 0;\n    })();\n    return Transformable;\n}());\n;\nexport var TRANSFORMABLE_PROPS = [\n    'x', 'y', 'originX', 'originY', 'anchorX', 'anchorY', 'rotation', 'scaleX', 'scaleY', 'skewX', 'skewY'\n];\nexport function copyTransform(target, source) {\n    for (var i = 0; i < TRANSFORMABLE_PROPS.length; i++) {\n        var propName = TRANSFORMABLE_PROPS[i];\n        target[propName] = source[propName];\n    }\n}\nexport default Transformable;\n", "var easingFuncs = {\n    linear: function (k) {\n        return k;\n    },\n    quadraticIn: function (k) {\n        return k * k;\n    },\n    quadraticOut: function (k) {\n        return k * (2 - k);\n    },\n    quadraticInOut: function (k) {\n        if ((k *= 2) < 1) {\n            return 0.5 * k * k;\n        }\n        return -0.5 * (--k * (k - 2) - 1);\n    },\n    cubicIn: function (k) {\n        return k * k * k;\n    },\n    cubicOut: function (k) {\n        return --k * k * k + 1;\n    },\n    cubicInOut: function (k) {\n        if ((k *= 2) < 1) {\n            return 0.5 * k * k * k;\n        }\n        return 0.5 * ((k -= 2) * k * k + 2);\n    },\n    quarticIn: function (k) {\n        return k * k * k * k;\n    },\n    quarticOut: function (k) {\n        return 1 - (--k * k * k * k);\n    },\n    quarticInOut: function (k) {\n        if ((k *= 2) < 1) {\n            return 0.5 * k * k * k * k;\n        }\n        return -0.5 * ((k -= 2) * k * k * k - 2);\n    },\n    quinticIn: function (k) {\n        return k * k * k * k * k;\n    },\n    quinticOut: function (k) {\n        return --k * k * k * k * k + 1;\n    },\n    quinticInOut: function (k) {\n        if ((k *= 2) < 1) {\n            return 0.5 * k * k * k * k * k;\n        }\n        return 0.5 * ((k -= 2) * k * k * k * k + 2);\n    },\n    sinusoidalIn: function (k) {\n        return 1 - Math.cos(k * Math.PI / 2);\n    },\n    sinusoidalOut: function (k) {\n        return Math.sin(k * Math.PI / 2);\n    },\n    sinusoidalInOut: function (k) {\n        return 0.5 * (1 - Math.cos(Math.PI * k));\n    },\n    exponentialIn: function (k) {\n        return k === 0 ? 0 : Math.pow(1024, k - 1);\n    },\n    exponentialOut: function (k) {\n        return k === 1 ? 1 : 1 - Math.pow(2, -10 * k);\n    },\n    exponentialInOut: function (k) {\n        if (k === 0) {\n            return 0;\n        }\n        if (k === 1) {\n            return 1;\n        }\n        if ((k *= 2) < 1) {\n            return 0.5 * Math.pow(1024, k - 1);\n        }\n        return 0.5 * (-Math.pow(2, -10 * (k - 1)) + 2);\n    },\n    circularIn: function (k) {\n        return 1 - Math.sqrt(1 - k * k);\n    },\n    circularOut: function (k) {\n        return Math.sqrt(1 - (--k * k));\n    },\n    circularInOut: function (k) {\n        if ((k *= 2) < 1) {\n            return -0.5 * (Math.sqrt(1 - k * k) - 1);\n        }\n        return 0.5 * (Math.sqrt(1 - (k -= 2) * k) + 1);\n    },\n    elasticIn: function (k) {\n        var s;\n        var a = 0.1;\n        var p = 0.4;\n        if (k === 0) {\n            return 0;\n        }\n        if (k === 1) {\n            return 1;\n        }\n        if (!a || a < 1) {\n            a = 1;\n            s = p / 4;\n        }\n        else {\n            s = p * Math.asin(1 / a) / (2 * Math.PI);\n        }\n        return -(a * Math.pow(2, 10 * (k -= 1))\n            * Math.sin((k - s) * (2 * Math.PI) / p));\n    },\n    elasticOut: function (k) {\n        var s;\n        var a = 0.1;\n        var p = 0.4;\n        if (k === 0) {\n            return 0;\n        }\n        if (k === 1) {\n            return 1;\n        }\n        if (!a || a < 1) {\n            a = 1;\n            s = p / 4;\n        }\n        else {\n            s = p * Math.asin(1 / a) / (2 * Math.PI);\n        }\n        return (a * Math.pow(2, -10 * k)\n            * Math.sin((k - s) * (2 * Math.PI) / p) + 1);\n    },\n    elasticInOut: function (k) {\n        var s;\n        var a = 0.1;\n        var p = 0.4;\n        if (k === 0) {\n            return 0;\n        }\n        if (k === 1) {\n            return 1;\n        }\n        if (!a || a < 1) {\n            a = 1;\n            s = p / 4;\n        }\n        else {\n            s = p * Math.asin(1 / a) / (2 * Math.PI);\n        }\n        if ((k *= 2) < 1) {\n            return -0.5 * (a * Math.pow(2, 10 * (k -= 1))\n                * Math.sin((k - s) * (2 * Math.PI) / p));\n        }\n        return a * Math.pow(2, -10 * (k -= 1))\n            * Math.sin((k - s) * (2 * Math.PI) / p) * 0.5 + 1;\n    },\n    backIn: function (k) {\n        var s = 1.70158;\n        return k * k * ((s + 1) * k - s);\n    },\n    backOut: function (k) {\n        var s = 1.70158;\n        return --k * k * ((s + 1) * k + s) + 1;\n    },\n    backInOut: function (k) {\n        var s = 1.70158 * 1.525;\n        if ((k *= 2) < 1) {\n            return 0.5 * (k * k * ((s + 1) * k - s));\n        }\n        return 0.5 * ((k -= 2) * k * ((s + 1) * k + s) + 2);\n    },\n    bounceIn: function (k) {\n        return 1 - easingFuncs.bounceOut(1 - k);\n    },\n    bounceOut: function (k) {\n        if (k < (1 / 2.75)) {\n            return 7.5625 * k * k;\n        }\n        else if (k < (2 / 2.75)) {\n            return 7.5625 * (k -= (1.5 / 2.75)) * k + 0.75;\n        }\n        else if (k < (2.5 / 2.75)) {\n            return 7.5625 * (k -= (2.25 / 2.75)) * k + 0.9375;\n        }\n        else {\n            return 7.5625 * (k -= (2.625 / 2.75)) * k + 0.984375;\n        }\n    },\n    bounceInOut: function (k) {\n        if (k < 0.5) {\n            return easingFuncs.bounceIn(k * 2) * 0.5;\n        }\n        return easingFuncs.bounceOut(k * 2 - 1) * 0.5 + 0.5;\n    }\n};\nexport default easingFuncs;\n", "import { create as v2Create, distSquare as v2DistSquare } from './vector.js';\nvar mathPow = Math.pow;\nvar mathSqrt = Math.sqrt;\nvar EPSILON = 1e-8;\nvar EPSILON_NUMERIC = 1e-4;\nvar THREE_SQRT = mathSqrt(3);\nvar ONE_THIRD = 1 / 3;\nvar _v0 = v2Create();\nvar _v1 = v2Create();\nvar _v2 = v2Create();\nfunction isAroundZero(val) {\n    return val > -EPSILON && val < EPSILON;\n}\nfunction isNotAroundZero(val) {\n    return val > EPSILON || val < -EPSILON;\n}\nexport function cubicAt(p0, p1, p2, p3, t) {\n    var onet = 1 - t;\n    return onet * onet * (onet * p0 + 3 * t * p1)\n        + t * t * (t * p3 + 3 * onet * p2);\n}\nexport function cubicDerivativeAt(p0, p1, p2, p3, t) {\n    var onet = 1 - t;\n    return 3 * (((p1 - p0) * onet + 2 * (p2 - p1) * t) * onet\n        + (p3 - p2) * t * t);\n}\nexport function cubicRootAt(p0, p1, p2, p3, val, roots) {\n    var a = p3 + 3 * (p1 - p2) - p0;\n    var b = 3 * (p2 - p1 * 2 + p0);\n    var c = 3 * (p1 - p0);\n    var d = p0 - val;\n    var A = b * b - 3 * a * c;\n    var B = b * c - 9 * a * d;\n    var C = c * c - 3 * b * d;\n    var n = 0;\n    if (isAroundZero(A) && isAroundZero(B)) {\n        if (isAroundZero(b)) {\n            roots[0] = 0;\n        }\n        else {\n            var t1 = -c / b;\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n    }\n    else {\n        var disc = B * B - 4 * A * C;\n        if (isAroundZero(disc)) {\n            var K = B / A;\n            var t1 = -b / a + K;\n            var t2 = -K / 2;\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                roots[n++] = t2;\n            }\n        }\n        else if (disc > 0) {\n            var discSqrt = mathSqrt(disc);\n            var Y1 = A * b + 1.5 * a * (-B + discSqrt);\n            var Y2 = A * b + 1.5 * a * (-B - discSqrt);\n            if (Y1 < 0) {\n                Y1 = -mathPow(-Y1, ONE_THIRD);\n            }\n            else {\n                Y1 = mathPow(Y1, ONE_THIRD);\n            }\n            if (Y2 < 0) {\n                Y2 = -mathPow(-Y2, ONE_THIRD);\n            }\n            else {\n                Y2 = mathPow(Y2, ONE_THIRD);\n            }\n            var t1 = (-b - (Y1 + Y2)) / (3 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n        else {\n            var T = (2 * A * b - 3 * a * B) / (2 * mathSqrt(A * A * A));\n            var theta = Math.acos(T) / 3;\n            var ASqrt = mathSqrt(A);\n            var tmp = Math.cos(theta);\n            var t1 = (-b - 2 * ASqrt * tmp) / (3 * a);\n            var t2 = (-b + ASqrt * (tmp + THREE_SQRT * Math.sin(theta))) / (3 * a);\n            var t3 = (-b + ASqrt * (tmp - THREE_SQRT * Math.sin(theta))) / (3 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                roots[n++] = t2;\n            }\n            if (t3 >= 0 && t3 <= 1) {\n                roots[n++] = t3;\n            }\n        }\n    }\n    return n;\n}\nexport function cubicExtrema(p0, p1, p2, p3, extrema) {\n    var b = 6 * p2 - 12 * p1 + 6 * p0;\n    var a = 9 * p1 + 3 * p3 - 3 * p0 - 9 * p2;\n    var c = 3 * p1 - 3 * p0;\n    var n = 0;\n    if (isAroundZero(a)) {\n        if (isNotAroundZero(b)) {\n            var t1 = -c / b;\n            if (t1 >= 0 && t1 <= 1) {\n                extrema[n++] = t1;\n            }\n        }\n    }\n    else {\n        var disc = b * b - 4 * a * c;\n        if (isAroundZero(disc)) {\n            extrema[0] = -b / (2 * a);\n        }\n        else if (disc > 0) {\n            var discSqrt = mathSqrt(disc);\n            var t1 = (-b + discSqrt) / (2 * a);\n            var t2 = (-b - discSqrt) / (2 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                extrema[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                extrema[n++] = t2;\n            }\n        }\n    }\n    return n;\n}\nexport function cubicSubdivide(p0, p1, p2, p3, t, out) {\n    var p01 = (p1 - p0) * t + p0;\n    var p12 = (p2 - p1) * t + p1;\n    var p23 = (p3 - p2) * t + p2;\n    var p012 = (p12 - p01) * t + p01;\n    var p123 = (p23 - p12) * t + p12;\n    var p0123 = (p123 - p012) * t + p012;\n    out[0] = p0;\n    out[1] = p01;\n    out[2] = p012;\n    out[3] = p0123;\n    out[4] = p0123;\n    out[5] = p123;\n    out[6] = p23;\n    out[7] = p3;\n}\nexport function cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, out) {\n    var t;\n    var interval = 0.005;\n    var d = Infinity;\n    var prev;\n    var next;\n    var d1;\n    var d2;\n    _v0[0] = x;\n    _v0[1] = y;\n    for (var _t = 0; _t < 1; _t += 0.05) {\n        _v1[0] = cubicAt(x0, x1, x2, x3, _t);\n        _v1[1] = cubicAt(y0, y1, y2, y3, _t);\n        d1 = v2DistSquare(_v0, _v1);\n        if (d1 < d) {\n            t = _t;\n            d = d1;\n        }\n    }\n    d = Infinity;\n    for (var i = 0; i < 32; i++) {\n        if (interval < EPSILON_NUMERIC) {\n            break;\n        }\n        prev = t - interval;\n        next = t + interval;\n        _v1[0] = cubicAt(x0, x1, x2, x3, prev);\n        _v1[1] = cubicAt(y0, y1, y2, y3, prev);\n        d1 = v2DistSquare(_v1, _v0);\n        if (prev >= 0 && d1 < d) {\n            t = prev;\n            d = d1;\n        }\n        else {\n            _v2[0] = cubicAt(x0, x1, x2, x3, next);\n            _v2[1] = cubicAt(y0, y1, y2, y3, next);\n            d2 = v2DistSquare(_v2, _v0);\n            if (next <= 1 && d2 < d) {\n                t = next;\n                d = d2;\n            }\n            else {\n                interval *= 0.5;\n            }\n        }\n    }\n    if (out) {\n        out[0] = cubicAt(x0, x1, x2, x3, t);\n        out[1] = cubicAt(y0, y1, y2, y3, t);\n    }\n    return mathSqrt(d);\n}\nexport function cubicLength(x0, y0, x1, y1, x2, y2, x3, y3, iteration) {\n    var px = x0;\n    var py = y0;\n    var d = 0;\n    var step = 1 / iteration;\n    for (var i = 1; i <= iteration; i++) {\n        var t = i * step;\n        var x = cubicAt(x0, x1, x2, x3, t);\n        var y = cubicAt(y0, y1, y2, y3, t);\n        var dx = x - px;\n        var dy = y - py;\n        d += Math.sqrt(dx * dx + dy * dy);\n        px = x;\n        py = y;\n    }\n    return d;\n}\nexport function quadraticAt(p0, p1, p2, t) {\n    var onet = 1 - t;\n    return onet * (onet * p0 + 2 * t * p1) + t * t * p2;\n}\nexport function quadraticDerivativeAt(p0, p1, p2, t) {\n    return 2 * ((1 - t) * (p1 - p0) + t * (p2 - p1));\n}\nexport function quadraticRootAt(p0, p1, p2, val, roots) {\n    var a = p0 - 2 * p1 + p2;\n    var b = 2 * (p1 - p0);\n    var c = p0 - val;\n    var n = 0;\n    if (isAroundZero(a)) {\n        if (isNotAroundZero(b)) {\n            var t1 = -c / b;\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n    }\n    else {\n        var disc = b * b - 4 * a * c;\n        if (isAroundZero(disc)) {\n            var t1 = -b / (2 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n        else if (disc > 0) {\n            var discSqrt = mathSqrt(disc);\n            var t1 = (-b + discSqrt) / (2 * a);\n            var t2 = (-b - discSqrt) / (2 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                roots[n++] = t2;\n            }\n        }\n    }\n    return n;\n}\nexport function quadraticExtremum(p0, p1, p2) {\n    var divider = p0 + p2 - 2 * p1;\n    if (divider === 0) {\n        return 0.5;\n    }\n    else {\n        return (p0 - p1) / divider;\n    }\n}\nexport function quadraticSubdivide(p0, p1, p2, t, out) {\n    var p01 = (p1 - p0) * t + p0;\n    var p12 = (p2 - p1) * t + p1;\n    var p012 = (p12 - p01) * t + p01;\n    out[0] = p0;\n    out[1] = p01;\n    out[2] = p012;\n    out[3] = p012;\n    out[4] = p12;\n    out[5] = p2;\n}\nexport function quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, out) {\n    var t;\n    var interval = 0.005;\n    var d = Infinity;\n    _v0[0] = x;\n    _v0[1] = y;\n    for (var _t = 0; _t < 1; _t += 0.05) {\n        _v1[0] = quadraticAt(x0, x1, x2, _t);\n        _v1[1] = quadraticAt(y0, y1, y2, _t);\n        var d1 = v2DistSquare(_v0, _v1);\n        if (d1 < d) {\n            t = _t;\n            d = d1;\n        }\n    }\n    d = Infinity;\n    for (var i = 0; i < 32; i++) {\n        if (interval < EPSILON_NUMERIC) {\n            break;\n        }\n        var prev = t - interval;\n        var next = t + interval;\n        _v1[0] = quadraticAt(x0, x1, x2, prev);\n        _v1[1] = quadraticAt(y0, y1, y2, prev);\n        var d1 = v2DistSquare(_v1, _v0);\n        if (prev >= 0 && d1 < d) {\n            t = prev;\n            d = d1;\n        }\n        else {\n            _v2[0] = quadraticAt(x0, x1, x2, next);\n            _v2[1] = quadraticAt(y0, y1, y2, next);\n            var d2 = v2DistSquare(_v2, _v0);\n            if (next <= 1 && d2 < d) {\n                t = next;\n                d = d2;\n            }\n            else {\n                interval *= 0.5;\n            }\n        }\n    }\n    if (out) {\n        out[0] = quadraticAt(x0, x1, x2, t);\n        out[1] = quadraticAt(y0, y1, y2, t);\n    }\n    return mathSqrt(d);\n}\nexport function quadraticLength(x0, y0, x1, y1, x2, y2, iteration) {\n    var px = x0;\n    var py = y0;\n    var d = 0;\n    var step = 1 / iteration;\n    for (var i = 1; i <= iteration; i++) {\n        var t = i * step;\n        var x = quadraticAt(x0, x1, x2, t);\n        var y = quadraticAt(y0, y1, y2, t);\n        var dx = x - px;\n        var dy = y - py;\n        d += Math.sqrt(dx * dx + dy * dy);\n        px = x;\n        py = y;\n    }\n    return d;\n}\n", "import { cubicAt, cubicRootAt } from '../core/curve.js';\nimport { trim } from '../core/util.js';\nvar regexp = /cubic-bezier\\(([0-9,\\.e ]+)\\)/;\nexport function createCubicEasingFunc(cubicEasingStr) {\n    var cubic = cubicEasingStr && regexp.exec(cubicEasingStr);\n    if (cubic) {\n        var points = cubic[1].split(',');\n        var a_1 = +trim(points[0]);\n        var b_1 = +trim(points[1]);\n        var c_1 = +trim(points[2]);\n        var d_1 = +trim(points[3]);\n        if (isNaN(a_1 + b_1 + c_1 + d_1)) {\n            return;\n        }\n        var roots_1 = [];\n        return function (p) {\n            return p <= 0\n                ? 0 : p >= 1\n                ? 1\n                : cubicRootAt(0, a_1, c_1, 1, p, roots_1) && cubicAt(0, b_1, d_1, 1, roots_1[0]);\n        };\n    }\n}\n", "import easingFuncs from './easing.js';\nimport { isFunction, noop } from '../core/util.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nvar Clip = (function () {\n    function Clip(opts) {\n        this._inited = false;\n        this._startTime = 0;\n        this._pausedTime = 0;\n        this._paused = false;\n        this._life = opts.life || 1000;\n        this._delay = opts.delay || 0;\n        this.loop = opts.loop || false;\n        this.onframe = opts.onframe || noop;\n        this.ondestroy = opts.ondestroy || noop;\n        this.onrestart = opts.onrestart || noop;\n        opts.easing && this.setEasing(opts.easing);\n    }\n    Clip.prototype.step = function (globalTime, deltaTime) {\n        if (!this._inited) {\n            this._startTime = globalTime + this._delay;\n            this._inited = true;\n        }\n        if (this._paused) {\n            this._pausedTime += deltaTime;\n            return;\n        }\n        var life = this._life;\n        var elapsedTime = globalTime - this._startTime - this._pausedTime;\n        var percent = elapsedTime / life;\n        if (percent < 0) {\n            percent = 0;\n        }\n        percent = Math.min(percent, 1);\n        var easingFunc = this.easingFunc;\n        var schedule = easingFunc ? easingFunc(percent) : percent;\n        this.onframe(schedule);\n        if (percent === 1) {\n            if (this.loop) {\n                var remainder = elapsedTime % life;\n                this._startTime = globalTime - remainder;\n                this._pausedTime = 0;\n                this.onrestart();\n            }\n            else {\n                return true;\n            }\n        }\n        return false;\n    };\n    Clip.prototype.pause = function () {\n        this._paused = true;\n    };\n    Clip.prototype.resume = function () {\n        this._paused = false;\n    };\n    Clip.prototype.setEasing = function (easing) {\n        this.easing = easing;\n        this.easingFunc = isFunction(easing)\n            ? easing\n            : easingFuncs[easing] || createCubicEasingFunc(easing);\n    };\n    return Clip;\n}());\nexport default Clip;\n", "import { RADIAN_TO_DEGREE, retrieve2, logError, isFunction } from '../core/util.js';\nimport { parse } from '../tool/color.js';\nimport env from '../core/env.js';\nvar mathRound = Math.round;\nexport function normalizeColor(color) {\n    var opacity;\n    if (!color || color === 'transparent') {\n        color = 'none';\n    }\n    else if (typeof color === 'string' && color.indexOf('rgba') > -1) {\n        var arr = parse(color);\n        if (arr) {\n            color = 'rgb(' + arr[0] + ',' + arr[1] + ',' + arr[2] + ')';\n            opacity = arr[3];\n        }\n    }\n    return {\n        color: color,\n        opacity: opacity == null ? 1 : opacity\n    };\n}\nvar EPSILON = 1e-4;\nexport function isAroundZero(transform) {\n    return transform < EPSILON && transform > -EPSILON;\n}\nexport function round3(transform) {\n    return mathRound(transform * 1e3) / 1e3;\n}\nexport function round4(transform) {\n    return mathRound(transform * 1e4) / 1e4;\n}\nexport function round1(transform) {\n    return mathRound(transform * 10) / 10;\n}\nexport function getMatrixStr(m) {\n    return 'matrix('\n        + round3(m[0]) + ','\n        + round3(m[1]) + ','\n        + round3(m[2]) + ','\n        + round3(m[3]) + ','\n        + round4(m[4]) + ','\n        + round4(m[5])\n        + ')';\n}\nexport var TEXT_ALIGN_TO_ANCHOR = {\n    left: 'start',\n    right: 'end',\n    center: 'middle',\n    middle: 'middle'\n};\nexport function adjustTextY(y, lineHeight, textBaseline) {\n    if (textBaseline === 'top') {\n        y += lineHeight / 2;\n    }\n    else if (textBaseline === 'bottom') {\n        y -= lineHeight / 2;\n    }\n    return y;\n}\nexport function hasShadow(style) {\n    return style\n        && (style.shadowBlur || style.shadowOffsetX || style.shadowOffsetY);\n}\nexport function getShadowKey(displayable) {\n    var style = displayable.style;\n    var globalScale = displayable.getGlobalScale();\n    return [\n        style.shadowColor,\n        (style.shadowBlur || 0).toFixed(2),\n        (style.shadowOffsetX || 0).toFixed(2),\n        (style.shadowOffsetY || 0).toFixed(2),\n        globalScale[0],\n        globalScale[1]\n    ].join(',');\n}\nexport function getClipPathsKey(clipPaths) {\n    var key = [];\n    if (clipPaths) {\n        for (var i = 0; i < clipPaths.length; i++) {\n            var clipPath = clipPaths[i];\n            key.push(clipPath.id);\n        }\n    }\n    return key.join(',');\n}\nexport function isImagePattern(val) {\n    return val && (!!val.image);\n}\nexport function isSVGPattern(val) {\n    return val && (!!val.svgElement);\n}\nexport function isPattern(val) {\n    return isImagePattern(val) || isSVGPattern(val);\n}\nexport function isLinearGradient(val) {\n    return val.type === 'linear';\n}\nexport function isRadialGradient(val) {\n    return val.type === 'radial';\n}\nexport function isGradient(val) {\n    return val && (val.type === 'linear'\n        || val.type === 'radial');\n}\nexport function getIdURL(id) {\n    return \"url(#\" + id + \")\";\n}\nexport function getPathPrecision(el) {\n    var scale = el.getGlobalScale();\n    var size = Math.max(scale[0], scale[1]);\n    return Math.max(Math.ceil(Math.log(size) / Math.log(10)), 1);\n}\nexport function getSRTTransformString(transform) {\n    var x = transform.x || 0;\n    var y = transform.y || 0;\n    var rotation = (transform.rotation || 0) * RADIAN_TO_DEGREE;\n    var scaleX = retrieve2(transform.scaleX, 1);\n    var scaleY = retrieve2(transform.scaleY, 1);\n    var skewX = transform.skewX || 0;\n    var skewY = transform.skewY || 0;\n    var res = [];\n    if (x || y) {\n        res.push(\"translate(\" + x + \"px,\" + y + \"px)\");\n    }\n    if (rotation) {\n        res.push(\"rotate(\" + rotation + \")\");\n    }\n    if (scaleX !== 1 || scaleY !== 1) {\n        res.push(\"scale(\" + scaleX + \",\" + scaleY + \")\");\n    }\n    if (skewX || skewY) {\n        res.push(\"skew(\" + mathRound(skewX * RADIAN_TO_DEGREE) + \"deg, \" + mathRound(skewY * RADIAN_TO_DEGREE) + \"deg)\");\n    }\n    return res.join(' ');\n}\nexport var encodeBase64 = (function () {\n    if (env.hasGlobalWindow && isFunction(window.btoa)) {\n        return function (str) {\n            return window.btoa(unescape(encodeURIComponent(str)));\n        };\n    }\n    if (typeof Buffer !== 'undefined') {\n        return function (str) {\n            return Buffer.from(str).toString('base64');\n        };\n    }\n    return function (str) {\n        if (process.env.NODE_ENV !== 'production') {\n            logError('Base64 isn\\'t natively supported in the current environment.');\n        }\n        return null;\n    };\n})();\n", "import Clip from './Clip.js';\nimport * as color from '../tool/color.js';\nimport { eqNaN, extend, isArrayLike, isFunction, isGradientObject, isNumber, isString, keys, logError, map } from '../core/util.js';\nimport easingFuncs from './easing.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nimport { isLinearGradient, isRadialGradient } from '../svg/helper.js';\n;\nvar arraySlice = Array.prototype.slice;\nfunction interpolateNumber(p0, p1, percent) {\n    return (p1 - p0) * percent + p0;\n}\nfunction interpolate1DArray(out, p0, p1, percent) {\n    var len = p0.length;\n    for (var i = 0; i < len; i++) {\n        out[i] = interpolateNumber(p0[i], p1[i], percent);\n    }\n    return out;\n}\nfunction interpolate2DArray(out, p0, p1, percent) {\n    var len = p0.length;\n    var len2 = len && p0[0].length;\n    for (var i = 0; i < len; i++) {\n        if (!out[i]) {\n            out[i] = [];\n        }\n        for (var j = 0; j < len2; j++) {\n            out[i][j] = interpolateNumber(p0[i][j], p1[i][j], percent);\n        }\n    }\n    return out;\n}\nfunction add1DArray(out, p0, p1, sign) {\n    var len = p0.length;\n    for (var i = 0; i < len; i++) {\n        out[i] = p0[i] + p1[i] * sign;\n    }\n    return out;\n}\nfunction add2DArray(out, p0, p1, sign) {\n    var len = p0.length;\n    var len2 = len && p0[0].length;\n    for (var i = 0; i < len; i++) {\n        if (!out[i]) {\n            out[i] = [];\n        }\n        for (var j = 0; j < len2; j++) {\n            out[i][j] = p0[i][j] + p1[i][j] * sign;\n        }\n    }\n    return out;\n}\nfunction fillColorStops(val0, val1) {\n    var len0 = val0.length;\n    var len1 = val1.length;\n    var shorterArr = len0 > len1 ? val1 : val0;\n    var shorterLen = Math.min(len0, len1);\n    var last = shorterArr[shorterLen - 1] || { color: [0, 0, 0, 0], offset: 0 };\n    for (var i = shorterLen; i < Math.max(len0, len1); i++) {\n        shorterArr.push({\n            offset: last.offset,\n            color: last.color.slice()\n        });\n    }\n}\nfunction fillArray(val0, val1, arrDim) {\n    var arr0 = val0;\n    var arr1 = val1;\n    if (!arr0.push || !arr1.push) {\n        return;\n    }\n    var arr0Len = arr0.length;\n    var arr1Len = arr1.length;\n    if (arr0Len !== arr1Len) {\n        var isPreviousLarger = arr0Len > arr1Len;\n        if (isPreviousLarger) {\n            arr0.length = arr1Len;\n        }\n        else {\n            for (var i = arr0Len; i < arr1Len; i++) {\n                arr0.push(arrDim === 1 ? arr1[i] : arraySlice.call(arr1[i]));\n            }\n        }\n    }\n    var len2 = arr0[0] && arr0[0].length;\n    for (var i = 0; i < arr0.length; i++) {\n        if (arrDim === 1) {\n            if (isNaN(arr0[i])) {\n                arr0[i] = arr1[i];\n            }\n        }\n        else {\n            for (var j = 0; j < len2; j++) {\n                if (isNaN(arr0[i][j])) {\n                    arr0[i][j] = arr1[i][j];\n                }\n            }\n        }\n    }\n}\nexport function cloneValue(value) {\n    if (isArrayLike(value)) {\n        var len = value.length;\n        if (isArrayLike(value[0])) {\n            var ret = [];\n            for (var i = 0; i < len; i++) {\n                ret.push(arraySlice.call(value[i]));\n            }\n            return ret;\n        }\n        return arraySlice.call(value);\n    }\n    return value;\n}\nfunction rgba2String(rgba) {\n    rgba[0] = Math.floor(rgba[0]) || 0;\n    rgba[1] = Math.floor(rgba[1]) || 0;\n    rgba[2] = Math.floor(rgba[2]) || 0;\n    rgba[3] = rgba[3] == null ? 1 : rgba[3];\n    return 'rgba(' + rgba.join(',') + ')';\n}\nfunction guessArrayDim(value) {\n    return isArrayLike(value && value[0]) ? 2 : 1;\n}\nvar VALUE_TYPE_NUMBER = 0;\nvar VALUE_TYPE_1D_ARRAY = 1;\nvar VALUE_TYPE_2D_ARRAY = 2;\nvar VALUE_TYPE_COLOR = 3;\nvar VALUE_TYPE_LINEAR_GRADIENT = 4;\nvar VALUE_TYPE_RADIAL_GRADIENT = 5;\nvar VALUE_TYPE_UNKOWN = 6;\nfunction isGradientValueType(valType) {\n    return valType === VALUE_TYPE_LINEAR_GRADIENT || valType === VALUE_TYPE_RADIAL_GRADIENT;\n}\nfunction isArrayValueType(valType) {\n    return valType === VALUE_TYPE_1D_ARRAY || valType === VALUE_TYPE_2D_ARRAY;\n}\nvar tmpRgba = [0, 0, 0, 0];\nvar Track = (function () {\n    function Track(propName) {\n        this.keyframes = [];\n        this.discrete = false;\n        this._invalid = false;\n        this._needsSort = false;\n        this._lastFr = 0;\n        this._lastFrP = 0;\n        this.propName = propName;\n    }\n    Track.prototype.isFinished = function () {\n        return this._finished;\n    };\n    Track.prototype.setFinished = function () {\n        this._finished = true;\n        if (this._additiveTrack) {\n            this._additiveTrack.setFinished();\n        }\n    };\n    Track.prototype.needsAnimate = function () {\n        return this.keyframes.length >= 1;\n    };\n    Track.prototype.getAdditiveTrack = function () {\n        return this._additiveTrack;\n    };\n    Track.prototype.addKeyframe = function (time, rawValue, easing) {\n        this._needsSort = true;\n        var keyframes = this.keyframes;\n        var len = keyframes.length;\n        var discrete = false;\n        var valType = VALUE_TYPE_UNKOWN;\n        var value = rawValue;\n        if (isArrayLike(rawValue)) {\n            var arrayDim = guessArrayDim(rawValue);\n            valType = arrayDim;\n            if (arrayDim === 1 && !isNumber(rawValue[0])\n                || arrayDim === 2 && !isNumber(rawValue[0][0])) {\n                discrete = true;\n            }\n        }\n        else {\n            if (isNumber(rawValue) && !eqNaN(rawValue)) {\n                valType = VALUE_TYPE_NUMBER;\n            }\n            else if (isString(rawValue)) {\n                if (!isNaN(+rawValue)) {\n                    valType = VALUE_TYPE_NUMBER;\n                }\n                else {\n                    var colorArray = color.parse(rawValue);\n                    if (colorArray) {\n                        value = colorArray;\n                        valType = VALUE_TYPE_COLOR;\n                    }\n                }\n            }\n            else if (isGradientObject(rawValue)) {\n                var parsedGradient = extend({}, value);\n                parsedGradient.colorStops = map(rawValue.colorStops, function (colorStop) { return ({\n                    offset: colorStop.offset,\n                    color: color.parse(colorStop.color)\n                }); });\n                if (isLinearGradient(rawValue)) {\n                    valType = VALUE_TYPE_LINEAR_GRADIENT;\n                }\n                else if (isRadialGradient(rawValue)) {\n                    valType = VALUE_TYPE_RADIAL_GRADIENT;\n                }\n                value = parsedGradient;\n            }\n        }\n        if (len === 0) {\n            this.valType = valType;\n        }\n        else if (valType !== this.valType || valType === VALUE_TYPE_UNKOWN) {\n            discrete = true;\n        }\n        this.discrete = this.discrete || discrete;\n        var kf = {\n            time: time,\n            value: value,\n            rawValue: rawValue,\n            percent: 0\n        };\n        if (easing) {\n            kf.easing = easing;\n            kf.easingFunc = isFunction(easing)\n                ? easing\n                : easingFuncs[easing] || createCubicEasingFunc(easing);\n        }\n        keyframes.push(kf);\n        return kf;\n    };\n    Track.prototype.prepare = function (maxTime, additiveTrack) {\n        var kfs = this.keyframes;\n        if (this._needsSort) {\n            kfs.sort(function (a, b) {\n                return a.time - b.time;\n            });\n        }\n        var valType = this.valType;\n        var kfsLen = kfs.length;\n        var lastKf = kfs[kfsLen - 1];\n        var isDiscrete = this.discrete;\n        var isArr = isArrayValueType(valType);\n        var isGradient = isGradientValueType(valType);\n        for (var i = 0; i < kfsLen; i++) {\n            var kf = kfs[i];\n            var value = kf.value;\n            var lastValue = lastKf.value;\n            kf.percent = kf.time / maxTime;\n            if (!isDiscrete) {\n                if (isArr && i !== kfsLen - 1) {\n                    fillArray(value, lastValue, valType);\n                }\n                else if (isGradient) {\n                    fillColorStops(value.colorStops, lastValue.colorStops);\n                }\n            }\n        }\n        if (!isDiscrete\n            && valType !== VALUE_TYPE_RADIAL_GRADIENT\n            && additiveTrack\n            && this.needsAnimate()\n            && additiveTrack.needsAnimate()\n            && valType === additiveTrack.valType\n            && !additiveTrack._finished) {\n            this._additiveTrack = additiveTrack;\n            var startValue = kfs[0].value;\n            for (var i = 0; i < kfsLen; i++) {\n                if (valType === VALUE_TYPE_NUMBER) {\n                    kfs[i].additiveValue = kfs[i].value - startValue;\n                }\n                else if (valType === VALUE_TYPE_COLOR) {\n                    kfs[i].additiveValue =\n                        add1DArray([], kfs[i].value, startValue, -1);\n                }\n                else if (isArrayValueType(valType)) {\n                    kfs[i].additiveValue = valType === VALUE_TYPE_1D_ARRAY\n                        ? add1DArray([], kfs[i].value, startValue, -1)\n                        : add2DArray([], kfs[i].value, startValue, -1);\n                }\n            }\n        }\n    };\n    Track.prototype.step = function (target, percent) {\n        if (this._finished) {\n            return;\n        }\n        if (this._additiveTrack && this._additiveTrack._finished) {\n            this._additiveTrack = null;\n        }\n        var isAdditive = this._additiveTrack != null;\n        var valueKey = isAdditive ? 'additiveValue' : 'value';\n        var valType = this.valType;\n        var keyframes = this.keyframes;\n        var kfsNum = keyframes.length;\n        var propName = this.propName;\n        var isValueColor = valType === VALUE_TYPE_COLOR;\n        var frameIdx;\n        var lastFrame = this._lastFr;\n        var mathMin = Math.min;\n        var frame;\n        var nextFrame;\n        if (kfsNum === 1) {\n            frame = nextFrame = keyframes[0];\n        }\n        else {\n            if (percent < 0) {\n                frameIdx = 0;\n            }\n            else if (percent < this._lastFrP) {\n                var start = mathMin(lastFrame + 1, kfsNum - 1);\n                for (frameIdx = start; frameIdx >= 0; frameIdx--) {\n                    if (keyframes[frameIdx].percent <= percent) {\n                        break;\n                    }\n                }\n                frameIdx = mathMin(frameIdx, kfsNum - 2);\n            }\n            else {\n                for (frameIdx = lastFrame; frameIdx < kfsNum; frameIdx++) {\n                    if (keyframes[frameIdx].percent > percent) {\n                        break;\n                    }\n                }\n                frameIdx = mathMin(frameIdx - 1, kfsNum - 2);\n            }\n            nextFrame = keyframes[frameIdx + 1];\n            frame = keyframes[frameIdx];\n        }\n        if (!(frame && nextFrame)) {\n            return;\n        }\n        this._lastFr = frameIdx;\n        this._lastFrP = percent;\n        var interval = (nextFrame.percent - frame.percent);\n        var w = interval === 0 ? 1 : mathMin((percent - frame.percent) / interval, 1);\n        if (nextFrame.easingFunc) {\n            w = nextFrame.easingFunc(w);\n        }\n        var targetArr = isAdditive ? this._additiveValue\n            : (isValueColor ? tmpRgba : target[propName]);\n        if ((isArrayValueType(valType) || isValueColor) && !targetArr) {\n            targetArr = this._additiveValue = [];\n        }\n        if (this.discrete) {\n            target[propName] = w < 1 ? frame.rawValue : nextFrame.rawValue;\n        }\n        else if (isArrayValueType(valType)) {\n            valType === VALUE_TYPE_1D_ARRAY\n                ? interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w)\n                : interpolate2DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n        }\n        else if (isGradientValueType(valType)) {\n            var val = frame[valueKey];\n            var nextVal_1 = nextFrame[valueKey];\n            var isLinearGradient_1 = valType === VALUE_TYPE_LINEAR_GRADIENT;\n            target[propName] = {\n                type: isLinearGradient_1 ? 'linear' : 'radial',\n                x: interpolateNumber(val.x, nextVal_1.x, w),\n                y: interpolateNumber(val.y, nextVal_1.y, w),\n                colorStops: map(val.colorStops, function (colorStop, idx) {\n                    var nextColorStop = nextVal_1.colorStops[idx];\n                    return {\n                        offset: interpolateNumber(colorStop.offset, nextColorStop.offset, w),\n                        color: rgba2String(interpolate1DArray([], colorStop.color, nextColorStop.color, w))\n                    };\n                }),\n                global: nextVal_1.global\n            };\n            if (isLinearGradient_1) {\n                target[propName].x2 = interpolateNumber(val.x2, nextVal_1.x2, w);\n                target[propName].y2 = interpolateNumber(val.y2, nextVal_1.y2, w);\n            }\n            else {\n                target[propName].r = interpolateNumber(val.r, nextVal_1.r, w);\n            }\n        }\n        else if (isValueColor) {\n            interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n            if (!isAdditive) {\n                target[propName] = rgba2String(targetArr);\n            }\n        }\n        else {\n            var value = interpolateNumber(frame[valueKey], nextFrame[valueKey], w);\n            if (isAdditive) {\n                this._additiveValue = value;\n            }\n            else {\n                target[propName] = value;\n            }\n        }\n        if (isAdditive) {\n            this._addToTarget(target);\n        }\n    };\n    Track.prototype._addToTarget = function (target) {\n        var valType = this.valType;\n        var propName = this.propName;\n        var additiveValue = this._additiveValue;\n        if (valType === VALUE_TYPE_NUMBER) {\n            target[propName] = target[propName] + additiveValue;\n        }\n        else if (valType === VALUE_TYPE_COLOR) {\n            color.parse(target[propName], tmpRgba);\n            add1DArray(tmpRgba, tmpRgba, additiveValue, 1);\n            target[propName] = rgba2String(tmpRgba);\n        }\n        else if (valType === VALUE_TYPE_1D_ARRAY) {\n            add1DArray(target[propName], target[propName], additiveValue, 1);\n        }\n        else if (valType === VALUE_TYPE_2D_ARRAY) {\n            add2DArray(target[propName], target[propName], additiveValue, 1);\n        }\n    };\n    return Track;\n}());\nvar Animator = (function () {\n    function Animator(target, loop, allowDiscreteAnimation, additiveTo) {\n        this._tracks = {};\n        this._trackKeys = [];\n        this._maxTime = 0;\n        this._started = 0;\n        this._clip = null;\n        this._target = target;\n        this._loop = loop;\n        if (loop && additiveTo) {\n            logError('Can\\' use additive animation on looped animation.');\n            return;\n        }\n        this._additiveAnimators = additiveTo;\n        this._allowDiscrete = allowDiscreteAnimation;\n    }\n    Animator.prototype.getMaxTime = function () {\n        return this._maxTime;\n    };\n    Animator.prototype.getDelay = function () {\n        return this._delay;\n    };\n    Animator.prototype.getLoop = function () {\n        return this._loop;\n    };\n    Animator.prototype.getTarget = function () {\n        return this._target;\n    };\n    Animator.prototype.changeTarget = function (target) {\n        this._target = target;\n    };\n    Animator.prototype.when = function (time, props, easing) {\n        return this.whenWithKeys(time, props, keys(props), easing);\n    };\n    Animator.prototype.whenWithKeys = function (time, props, propNames, easing) {\n        var tracks = this._tracks;\n        for (var i = 0; i < propNames.length; i++) {\n            var propName = propNames[i];\n            var track = tracks[propName];\n            if (!track) {\n                track = tracks[propName] = new Track(propName);\n                var initialValue = void 0;\n                var additiveTrack = this._getAdditiveTrack(propName);\n                if (additiveTrack) {\n                    var addtiveTrackKfs = additiveTrack.keyframes;\n                    var lastFinalKf = addtiveTrackKfs[addtiveTrackKfs.length - 1];\n                    initialValue = lastFinalKf && lastFinalKf.value;\n                    if (additiveTrack.valType === VALUE_TYPE_COLOR && initialValue) {\n                        initialValue = rgba2String(initialValue);\n                    }\n                }\n                else {\n                    initialValue = this._target[propName];\n                }\n                if (initialValue == null) {\n                    continue;\n                }\n                if (time > 0) {\n                    track.addKeyframe(0, cloneValue(initialValue), easing);\n                }\n                this._trackKeys.push(propName);\n            }\n            track.addKeyframe(time, cloneValue(props[propName]), easing);\n        }\n        this._maxTime = Math.max(this._maxTime, time);\n        return this;\n    };\n    Animator.prototype.pause = function () {\n        this._clip.pause();\n        this._paused = true;\n    };\n    Animator.prototype.resume = function () {\n        this._clip.resume();\n        this._paused = false;\n    };\n    Animator.prototype.isPaused = function () {\n        return !!this._paused;\n    };\n    Animator.prototype.duration = function (duration) {\n        this._maxTime = duration;\n        this._force = true;\n        return this;\n    };\n    Animator.prototype._doneCallback = function () {\n        this._setTracksFinished();\n        this._clip = null;\n        var doneList = this._doneCbs;\n        if (doneList) {\n            var len = doneList.length;\n            for (var i = 0; i < len; i++) {\n                doneList[i].call(this);\n            }\n        }\n    };\n    Animator.prototype._abortedCallback = function () {\n        this._setTracksFinished();\n        var animation = this.animation;\n        var abortedList = this._abortedCbs;\n        if (animation) {\n            animation.removeClip(this._clip);\n        }\n        this._clip = null;\n        if (abortedList) {\n            for (var i = 0; i < abortedList.length; i++) {\n                abortedList[i].call(this);\n            }\n        }\n    };\n    Animator.prototype._setTracksFinished = function () {\n        var tracks = this._tracks;\n        var tracksKeys = this._trackKeys;\n        for (var i = 0; i < tracksKeys.length; i++) {\n            tracks[tracksKeys[i]].setFinished();\n        }\n    };\n    Animator.prototype._getAdditiveTrack = function (trackName) {\n        var additiveTrack;\n        var additiveAnimators = this._additiveAnimators;\n        if (additiveAnimators) {\n            for (var i = 0; i < additiveAnimators.length; i++) {\n                var track = additiveAnimators[i].getTrack(trackName);\n                if (track) {\n                    additiveTrack = track;\n                }\n            }\n        }\n        return additiveTrack;\n    };\n    Animator.prototype.start = function (easing) {\n        if (this._started > 0) {\n            return;\n        }\n        this._started = 1;\n        var self = this;\n        var tracks = [];\n        var maxTime = this._maxTime || 0;\n        for (var i = 0; i < this._trackKeys.length; i++) {\n            var propName = this._trackKeys[i];\n            var track = this._tracks[propName];\n            var additiveTrack = this._getAdditiveTrack(propName);\n            var kfs = track.keyframes;\n            var kfsNum = kfs.length;\n            track.prepare(maxTime, additiveTrack);\n            if (track.needsAnimate()) {\n                if (!this._allowDiscrete && track.discrete) {\n                    var lastKf = kfs[kfsNum - 1];\n                    if (lastKf) {\n                        self._target[track.propName] = lastKf.rawValue;\n                    }\n                    track.setFinished();\n                }\n                else {\n                    tracks.push(track);\n                }\n            }\n        }\n        if (tracks.length || this._force) {\n            var clip = new Clip({\n                life: maxTime,\n                loop: this._loop,\n                delay: this._delay || 0,\n                onframe: function (percent) {\n                    self._started = 2;\n                    var additiveAnimators = self._additiveAnimators;\n                    if (additiveAnimators) {\n                        var stillHasAdditiveAnimator = false;\n                        for (var i = 0; i < additiveAnimators.length; i++) {\n                            if (additiveAnimators[i]._clip) {\n                                stillHasAdditiveAnimator = true;\n                                break;\n                            }\n                        }\n                        if (!stillHasAdditiveAnimator) {\n                            self._additiveAnimators = null;\n                        }\n                    }\n                    for (var i = 0; i < tracks.length; i++) {\n                        tracks[i].step(self._target, percent);\n                    }\n                    var onframeList = self._onframeCbs;\n                    if (onframeList) {\n                        for (var i = 0; i < onframeList.length; i++) {\n                            onframeList[i](self._target, percent);\n                        }\n                    }\n                },\n                ondestroy: function () {\n                    self._doneCallback();\n                }\n            });\n            this._clip = clip;\n            if (this.animation) {\n                this.animation.addClip(clip);\n            }\n            if (easing) {\n                clip.setEasing(easing);\n            }\n        }\n        else {\n            this._doneCallback();\n        }\n        return this;\n    };\n    Animator.prototype.stop = function (forwardToLast) {\n        if (!this._clip) {\n            return;\n        }\n        var clip = this._clip;\n        if (forwardToLast) {\n            clip.onframe(1);\n        }\n        this._abortedCallback();\n    };\n    Animator.prototype.delay = function (time) {\n        this._delay = time;\n        return this;\n    };\n    Animator.prototype.during = function (cb) {\n        if (cb) {\n            if (!this._onframeCbs) {\n                this._onframeCbs = [];\n            }\n            this._onframeCbs.push(cb);\n        }\n        return this;\n    };\n    Animator.prototype.done = function (cb) {\n        if (cb) {\n            if (!this._doneCbs) {\n                this._doneCbs = [];\n            }\n            this._doneCbs.push(cb);\n        }\n        return this;\n    };\n    Animator.prototype.aborted = function (cb) {\n        if (cb) {\n            if (!this._abortedCbs) {\n                this._abortedCbs = [];\n            }\n            this._abortedCbs.push(cb);\n        }\n        return this;\n    };\n    Animator.prototype.getClip = function () {\n        return this._clip;\n    };\n    Animator.prototype.getTrack = function (propName) {\n        return this._tracks[propName];\n    };\n    Animator.prototype.getTracks = function () {\n        var _this = this;\n        return map(this._trackKeys, function (key) { return _this._tracks[key]; });\n    };\n    Animator.prototype.stopTracks = function (propNames, forwardToLast) {\n        if (!propNames.length || !this._clip) {\n            return true;\n        }\n        var tracks = this._tracks;\n        var tracksKeys = this._trackKeys;\n        for (var i = 0; i < propNames.length; i++) {\n            var track = tracks[propNames[i]];\n            if (track && !track.isFinished()) {\n                if (forwardToLast) {\n                    track.step(this._target, 1);\n                }\n                else if (this._started === 1) {\n                    track.step(this._target, 0);\n                }\n                track.setFinished();\n            }\n        }\n        var allAborted = true;\n        for (var i = 0; i < tracksKeys.length; i++) {\n            if (!tracks[tracksKeys[i]].isFinished()) {\n                allAborted = false;\n                break;\n            }\n        }\n        if (allAborted) {\n            this._abortedCallback();\n        }\n        return allAborted;\n    };\n    Animator.prototype.saveTo = function (target, trackKeys, firstOrLast) {\n        if (!target) {\n            return;\n        }\n        trackKeys = trackKeys || this._trackKeys;\n        for (var i = 0; i < trackKeys.length; i++) {\n            var propName = trackKeys[i];\n            var track = this._tracks[propName];\n            if (!track || track.isFinished()) {\n                continue;\n            }\n            var kfs = track.keyframes;\n            var kf = kfs[firstOrLast ? 0 : kfs.length - 1];\n            if (kf) {\n                target[propName] = cloneValue(kf.rawValue);\n            }\n        }\n    };\n    Animator.prototype.__changeFinalValue = function (finalProps, trackKeys) {\n        trackKeys = trackKeys || keys(finalProps);\n        for (var i = 0; i < trackKeys.length; i++) {\n            var propName = trackKeys[i];\n            var track = this._tracks[propName];\n            if (!track) {\n                continue;\n            }\n            var kfs = track.keyframes;\n            if (kfs.length > 1) {\n                var lastKf = kfs.pop();\n                track.addKeyframe(lastKf.time, finalProps[propName]);\n                track.prepare(this._maxTime, track.getAdditiveTrack());\n            }\n        }\n    };\n    return Animator;\n}());\nexport default Animator;\n", "import Transformable, { TRANSFORMABLE_PROPS } from './core/Transformable.js';\nimport Animator, { cloneValue } from './animation/Animator.js';\nimport BoundingRect from './core/BoundingRect.js';\nimport Eventful from './core/Eventful.js';\nimport { calculateTextPosition, parsePercent } from './contain/text.js';\nimport { guid, isObject, keys, extend, indexOf, logError, mixin, isArrayLike, isTypedArray, isGradientObject, filter, reduce } from './core/util.js';\nimport { LIGHT_LABEL_COLOR, DARK_LABEL_COLOR } from './config.js';\nimport { parse, stringify } from './tool/color.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nexport var PRESERVED_NORMAL_STATE = '__zr_normal__';\nvar PRIMARY_STATES_KEYS = TRANSFORMABLE_PROPS.concat(['ignore']);\nvar DEFAULT_ANIMATABLE_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n    obj[key] = true;\n    return obj;\n}, { ignore: false });\nvar tmpTextPosCalcRes = {};\nvar tmpBoundingRect = new BoundingRect(0, 0, 0, 0);\nvar Element = (function () {\n    function Element(props) {\n        this.id = guid();\n        this.animators = [];\n        this.currentStates = [];\n        this.states = {};\n        this._init(props);\n    }\n    Element.prototype._init = function (props) {\n        this.attr(props);\n    };\n    Element.prototype.drift = function (dx, dy, e) {\n        switch (this.draggable) {\n            case 'horizontal':\n                dy = 0;\n                break;\n            case 'vertical':\n                dx = 0;\n                break;\n        }\n        var m = this.transform;\n        if (!m) {\n            m = this.transform = [1, 0, 0, 1, 0, 0];\n        }\n        m[4] += dx;\n        m[5] += dy;\n        this.decomposeTransform();\n        this.markRedraw();\n    };\n    Element.prototype.beforeUpdate = function () { };\n    Element.prototype.afterUpdate = function () { };\n    Element.prototype.update = function () {\n        this.updateTransform();\n        if (this.__dirty) {\n            this.updateInnerText();\n        }\n    };\n    Element.prototype.updateInnerText = function (forceUpdate) {\n        var textEl = this._textContent;\n        if (textEl && (!textEl.ignore || forceUpdate)) {\n            if (!this.textConfig) {\n                this.textConfig = {};\n            }\n            var textConfig = this.textConfig;\n            var isLocal = textConfig.local;\n            var innerTransformable = textEl.innerTransformable;\n            var textAlign = void 0;\n            var textVerticalAlign = void 0;\n            var textStyleChanged = false;\n            innerTransformable.parent = isLocal ? this : null;\n            var innerOrigin = false;\n            innerTransformable.copyTransform(textEl);\n            if (textConfig.position != null) {\n                var layoutRect = tmpBoundingRect;\n                if (textConfig.layoutRect) {\n                    layoutRect.copy(textConfig.layoutRect);\n                }\n                else {\n                    layoutRect.copy(this.getBoundingRect());\n                }\n                if (!isLocal) {\n                    layoutRect.applyTransform(this.transform);\n                }\n                if (this.calculateTextPosition) {\n                    this.calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n                }\n                else {\n                    calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n                }\n                innerTransformable.x = tmpTextPosCalcRes.x;\n                innerTransformable.y = tmpTextPosCalcRes.y;\n                textAlign = tmpTextPosCalcRes.align;\n                textVerticalAlign = tmpTextPosCalcRes.verticalAlign;\n                var textOrigin = textConfig.origin;\n                if (textOrigin && textConfig.rotation != null) {\n                    var relOriginX = void 0;\n                    var relOriginY = void 0;\n                    if (textOrigin === 'center') {\n                        relOriginX = layoutRect.width * 0.5;\n                        relOriginY = layoutRect.height * 0.5;\n                    }\n                    else {\n                        relOriginX = parsePercent(textOrigin[0], layoutRect.width);\n                        relOriginY = parsePercent(textOrigin[1], layoutRect.height);\n                    }\n                    innerOrigin = true;\n                    innerTransformable.originX = -innerTransformable.x + relOriginX + (isLocal ? 0 : layoutRect.x);\n                    innerTransformable.originY = -innerTransformable.y + relOriginY + (isLocal ? 0 : layoutRect.y);\n                }\n            }\n            if (textConfig.rotation != null) {\n                innerTransformable.rotation = textConfig.rotation;\n            }\n            var textOffset = textConfig.offset;\n            if (textOffset) {\n                innerTransformable.x += textOffset[0];\n                innerTransformable.y += textOffset[1];\n                if (!innerOrigin) {\n                    innerTransformable.originX = -textOffset[0];\n                    innerTransformable.originY = -textOffset[1];\n                }\n            }\n            var isInside = textConfig.inside == null\n                ? (typeof textConfig.position === 'string' && textConfig.position.indexOf('inside') >= 0)\n                : textConfig.inside;\n            var innerTextDefaultStyle = this._innerTextDefaultStyle || (this._innerTextDefaultStyle = {});\n            var textFill = void 0;\n            var textStroke = void 0;\n            var autoStroke = void 0;\n            if (isInside && this.canBeInsideText()) {\n                textFill = textConfig.insideFill;\n                textStroke = textConfig.insideStroke;\n                if (textFill == null || textFill === 'auto') {\n                    textFill = this.getInsideTextFill();\n                }\n                if (textStroke == null || textStroke === 'auto') {\n                    textStroke = this.getInsideTextStroke(textFill);\n                    autoStroke = true;\n                }\n            }\n            else {\n                textFill = textConfig.outsideFill;\n                textStroke = textConfig.outsideStroke;\n                if (textFill == null || textFill === 'auto') {\n                    textFill = this.getOutsideFill();\n                }\n                if (textStroke == null || textStroke === 'auto') {\n                    textStroke = this.getOutsideStroke(textFill);\n                    autoStroke = true;\n                }\n            }\n            textFill = textFill || '#000';\n            if (textFill !== innerTextDefaultStyle.fill\n                || textStroke !== innerTextDefaultStyle.stroke\n                || autoStroke !== innerTextDefaultStyle.autoStroke\n                || textAlign !== innerTextDefaultStyle.align\n                || textVerticalAlign !== innerTextDefaultStyle.verticalAlign) {\n                textStyleChanged = true;\n                innerTextDefaultStyle.fill = textFill;\n                innerTextDefaultStyle.stroke = textStroke;\n                innerTextDefaultStyle.autoStroke = autoStroke;\n                innerTextDefaultStyle.align = textAlign;\n                innerTextDefaultStyle.verticalAlign = textVerticalAlign;\n                textEl.setDefaultTextStyle(innerTextDefaultStyle);\n            }\n            textEl.__dirty |= REDRAW_BIT;\n            if (textStyleChanged) {\n                textEl.dirtyStyle(true);\n            }\n        }\n    };\n    Element.prototype.canBeInsideText = function () {\n        return true;\n    };\n    Element.prototype.getInsideTextFill = function () {\n        return '#fff';\n    };\n    Element.prototype.getInsideTextStroke = function (textFill) {\n        return '#000';\n    };\n    Element.prototype.getOutsideFill = function () {\n        return this.__zr && this.__zr.isDarkMode() ? LIGHT_LABEL_COLOR : DARK_LABEL_COLOR;\n    };\n    Element.prototype.getOutsideStroke = function (textFill) {\n        var backgroundColor = this.__zr && this.__zr.getBackgroundColor();\n        var colorArr = typeof backgroundColor === 'string' && parse(backgroundColor);\n        if (!colorArr) {\n            colorArr = [255, 255, 255, 1];\n        }\n        var alpha = colorArr[3];\n        var isDark = this.__zr.isDarkMode();\n        for (var i = 0; i < 3; i++) {\n            colorArr[i] = colorArr[i] * alpha + (isDark ? 0 : 255) * (1 - alpha);\n        }\n        colorArr[3] = 1;\n        return stringify(colorArr, 'rgba');\n    };\n    Element.prototype.traverse = function (cb, context) { };\n    Element.prototype.attrKV = function (key, value) {\n        if (key === 'textConfig') {\n            this.setTextConfig(value);\n        }\n        else if (key === 'textContent') {\n            this.setTextContent(value);\n        }\n        else if (key === 'clipPath') {\n            this.setClipPath(value);\n        }\n        else if (key === 'extra') {\n            this.extra = this.extra || {};\n            extend(this.extra, value);\n        }\n        else {\n            this[key] = value;\n        }\n    };\n    Element.prototype.hide = function () {\n        this.ignore = true;\n        this.markRedraw();\n    };\n    Element.prototype.show = function () {\n        this.ignore = false;\n        this.markRedraw();\n    };\n    Element.prototype.attr = function (keyOrObj, value) {\n        if (typeof keyOrObj === 'string') {\n            this.attrKV(keyOrObj, value);\n        }\n        else if (isObject(keyOrObj)) {\n            var obj = keyOrObj;\n            var keysArr = keys(obj);\n            for (var i = 0; i < keysArr.length; i++) {\n                var key = keysArr[i];\n                this.attrKV(key, keyOrObj[key]);\n            }\n        }\n        this.markRedraw();\n        return this;\n    };\n    Element.prototype.saveCurrentToNormalState = function (toState) {\n        this._innerSaveToNormal(toState);\n        var normalState = this._normalState;\n        for (var i = 0; i < this.animators.length; i++) {\n            var animator = this.animators[i];\n            var fromStateTransition = animator.__fromStateTransition;\n            if (animator.getLoop() || fromStateTransition && fromStateTransition !== PRESERVED_NORMAL_STATE) {\n                continue;\n            }\n            var targetName = animator.targetName;\n            var target = targetName\n                ? normalState[targetName] : normalState;\n            animator.saveTo(target);\n        }\n    };\n    Element.prototype._innerSaveToNormal = function (toState) {\n        var normalState = this._normalState;\n        if (!normalState) {\n            normalState = this._normalState = {};\n        }\n        if (toState.textConfig && !normalState.textConfig) {\n            normalState.textConfig = this.textConfig;\n        }\n        this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n    };\n    Element.prototype._savePrimaryToNormal = function (toState, normalState, primaryKeys) {\n        for (var i = 0; i < primaryKeys.length; i++) {\n            var key = primaryKeys[i];\n            if (toState[key] != null && !(key in normalState)) {\n                normalState[key] = this[key];\n            }\n        }\n    };\n    Element.prototype.hasState = function () {\n        return this.currentStates.length > 0;\n    };\n    Element.prototype.getState = function (name) {\n        return this.states[name];\n    };\n    Element.prototype.ensureState = function (name) {\n        var states = this.states;\n        if (!states[name]) {\n            states[name] = {};\n        }\n        return states[name];\n    };\n    Element.prototype.clearStates = function (noAnimation) {\n        this.useState(PRESERVED_NORMAL_STATE, false, noAnimation);\n    };\n    Element.prototype.useState = function (stateName, keepCurrentStates, noAnimation, forceUseHoverLayer) {\n        var toNormalState = stateName === PRESERVED_NORMAL_STATE;\n        var hasStates = this.hasState();\n        if (!hasStates && toNormalState) {\n            return;\n        }\n        var currentStates = this.currentStates;\n        var animationCfg = this.stateTransition;\n        if (indexOf(currentStates, stateName) >= 0 && (keepCurrentStates || currentStates.length === 1)) {\n            return;\n        }\n        var state;\n        if (this.stateProxy && !toNormalState) {\n            state = this.stateProxy(stateName);\n        }\n        if (!state) {\n            state = (this.states && this.states[stateName]);\n        }\n        if (!state && !toNormalState) {\n            logError(\"State \" + stateName + \" not exists.\");\n            return;\n        }\n        if (!toNormalState) {\n            this.saveCurrentToNormalState(state);\n        }\n        var useHoverLayer = !!((state && state.hoverLayer) || forceUseHoverLayer);\n        if (useHoverLayer) {\n            this._toggleHoverLayerFlag(true);\n        }\n        this._applyStateObj(stateName, state, this._normalState, keepCurrentStates, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n        var textContent = this._textContent;\n        var textGuide = this._textGuide;\n        if (textContent) {\n            textContent.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n        }\n        if (textGuide) {\n            textGuide.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n        }\n        if (toNormalState) {\n            this.currentStates = [];\n            this._normalState = {};\n        }\n        else {\n            if (!keepCurrentStates) {\n                this.currentStates = [stateName];\n            }\n            else {\n                this.currentStates.push(stateName);\n            }\n        }\n        this._updateAnimationTargets();\n        this.markRedraw();\n        if (!useHoverLayer && this.__inHover) {\n            this._toggleHoverLayerFlag(false);\n            this.__dirty &= ~REDRAW_BIT;\n        }\n        return state;\n    };\n    Element.prototype.useStates = function (states, noAnimation, forceUseHoverLayer) {\n        if (!states.length) {\n            this.clearStates();\n        }\n        else {\n            var stateObjects = [];\n            var currentStates = this.currentStates;\n            var len = states.length;\n            var notChange = len === currentStates.length;\n            if (notChange) {\n                for (var i = 0; i < len; i++) {\n                    if (states[i] !== currentStates[i]) {\n                        notChange = false;\n                        break;\n                    }\n                }\n            }\n            if (notChange) {\n                return;\n            }\n            for (var i = 0; i < len; i++) {\n                var stateName = states[i];\n                var stateObj = void 0;\n                if (this.stateProxy) {\n                    stateObj = this.stateProxy(stateName, states);\n                }\n                if (!stateObj) {\n                    stateObj = this.states[stateName];\n                }\n                if (stateObj) {\n                    stateObjects.push(stateObj);\n                }\n            }\n            var lastStateObj = stateObjects[len - 1];\n            var useHoverLayer = !!((lastStateObj && lastStateObj.hoverLayer) || forceUseHoverLayer);\n            if (useHoverLayer) {\n                this._toggleHoverLayerFlag(true);\n            }\n            var mergedState = this._mergeStates(stateObjects);\n            var animationCfg = this.stateTransition;\n            this.saveCurrentToNormalState(mergedState);\n            this._applyStateObj(states.join(','), mergedState, this._normalState, false, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n            var textContent = this._textContent;\n            var textGuide = this._textGuide;\n            if (textContent) {\n                textContent.useStates(states, noAnimation, useHoverLayer);\n            }\n            if (textGuide) {\n                textGuide.useStates(states, noAnimation, useHoverLayer);\n            }\n            this._updateAnimationTargets();\n            this.currentStates = states.slice();\n            this.markRedraw();\n            if (!useHoverLayer && this.__inHover) {\n                this._toggleHoverLayerFlag(false);\n                this.__dirty &= ~REDRAW_BIT;\n            }\n        }\n    };\n    Element.prototype.isSilent = function () {\n        var isSilent = this.silent;\n        var ancestor = this.parent;\n        while (!isSilent && ancestor) {\n            if (ancestor.silent) {\n                isSilent = true;\n                break;\n            }\n            ancestor = ancestor.parent;\n        }\n        return isSilent;\n    };\n    Element.prototype._updateAnimationTargets = function () {\n        for (var i = 0; i < this.animators.length; i++) {\n            var animator = this.animators[i];\n            if (animator.targetName) {\n                animator.changeTarget(this[animator.targetName]);\n            }\n        }\n    };\n    Element.prototype.removeState = function (state) {\n        var idx = indexOf(this.currentStates, state);\n        if (idx >= 0) {\n            var currentStates = this.currentStates.slice();\n            currentStates.splice(idx, 1);\n            this.useStates(currentStates);\n        }\n    };\n    Element.prototype.replaceState = function (oldState, newState, forceAdd) {\n        var currentStates = this.currentStates.slice();\n        var idx = indexOf(currentStates, oldState);\n        var newStateExists = indexOf(currentStates, newState) >= 0;\n        if (idx >= 0) {\n            if (!newStateExists) {\n                currentStates[idx] = newState;\n            }\n            else {\n                currentStates.splice(idx, 1);\n            }\n        }\n        else if (forceAdd && !newStateExists) {\n            currentStates.push(newState);\n        }\n        this.useStates(currentStates);\n    };\n    Element.prototype.toggleState = function (state, enable) {\n        if (enable) {\n            this.useState(state, true);\n        }\n        else {\n            this.removeState(state);\n        }\n    };\n    Element.prototype._mergeStates = function (states) {\n        var mergedState = {};\n        var mergedTextConfig;\n        for (var i = 0; i < states.length; i++) {\n            var state = states[i];\n            extend(mergedState, state);\n            if (state.textConfig) {\n                mergedTextConfig = mergedTextConfig || {};\n                extend(mergedTextConfig, state.textConfig);\n            }\n        }\n        if (mergedTextConfig) {\n            mergedState.textConfig = mergedTextConfig;\n        }\n        return mergedState;\n    };\n    Element.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n        var needsRestoreToNormal = !(state && keepCurrentStates);\n        if (state && state.textConfig) {\n            this.textConfig = extend({}, keepCurrentStates ? this.textConfig : normalState.textConfig);\n            extend(this.textConfig, state.textConfig);\n        }\n        else if (needsRestoreToNormal) {\n            if (normalState.textConfig) {\n                this.textConfig = normalState.textConfig;\n            }\n        }\n        var transitionTarget = {};\n        var hasTransition = false;\n        for (var i = 0; i < PRIMARY_STATES_KEYS.length; i++) {\n            var key = PRIMARY_STATES_KEYS[i];\n            var propNeedsTransition = transition && DEFAULT_ANIMATABLE_MAP[key];\n            if (state && state[key] != null) {\n                if (propNeedsTransition) {\n                    hasTransition = true;\n                    transitionTarget[key] = state[key];\n                }\n                else {\n                    this[key] = state[key];\n                }\n            }\n            else if (needsRestoreToNormal) {\n                if (normalState[key] != null) {\n                    if (propNeedsTransition) {\n                        hasTransition = true;\n                        transitionTarget[key] = normalState[key];\n                    }\n                    else {\n                        this[key] = normalState[key];\n                    }\n                }\n            }\n        }\n        if (!transition) {\n            for (var i = 0; i < this.animators.length; i++) {\n                var animator = this.animators[i];\n                var targetName = animator.targetName;\n                if (!animator.getLoop()) {\n                    animator.__changeFinalValue(targetName\n                        ? (state || normalState)[targetName]\n                        : (state || normalState));\n                }\n            }\n        }\n        if (hasTransition) {\n            this._transitionState(stateName, transitionTarget, animationCfg);\n        }\n    };\n    Element.prototype._attachComponent = function (componentEl) {\n        if (componentEl.__zr && !componentEl.__hostTarget) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new Error('Text element has been added to zrender.');\n            }\n            return;\n        }\n        if (componentEl === this) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new Error('Recursive component attachment.');\n            }\n            return;\n        }\n        var zr = this.__zr;\n        if (zr) {\n            componentEl.addSelfToZr(zr);\n        }\n        componentEl.__zr = zr;\n        componentEl.__hostTarget = this;\n    };\n    Element.prototype._detachComponent = function (componentEl) {\n        if (componentEl.__zr) {\n            componentEl.removeSelfFromZr(componentEl.__zr);\n        }\n        componentEl.__zr = null;\n        componentEl.__hostTarget = null;\n    };\n    Element.prototype.getClipPath = function () {\n        return this._clipPath;\n    };\n    Element.prototype.setClipPath = function (clipPath) {\n        if (this._clipPath && this._clipPath !== clipPath) {\n            this.removeClipPath();\n        }\n        this._attachComponent(clipPath);\n        this._clipPath = clipPath;\n        this.markRedraw();\n    };\n    Element.prototype.removeClipPath = function () {\n        var clipPath = this._clipPath;\n        if (clipPath) {\n            this._detachComponent(clipPath);\n            this._clipPath = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.getTextContent = function () {\n        return this._textContent;\n    };\n    Element.prototype.setTextContent = function (textEl) {\n        var previousTextContent = this._textContent;\n        if (previousTextContent === textEl) {\n            return;\n        }\n        if (previousTextContent && previousTextContent !== textEl) {\n            this.removeTextContent();\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (textEl.__zr && !textEl.__hostTarget) {\n                throw new Error('Text element has been added to zrender.');\n            }\n        }\n        textEl.innerTransformable = new Transformable();\n        this._attachComponent(textEl);\n        this._textContent = textEl;\n        this.markRedraw();\n    };\n    Element.prototype.setTextConfig = function (cfg) {\n        if (!this.textConfig) {\n            this.textConfig = {};\n        }\n        extend(this.textConfig, cfg);\n        this.markRedraw();\n    };\n    Element.prototype.removeTextConfig = function () {\n        this.textConfig = null;\n        this.markRedraw();\n    };\n    Element.prototype.removeTextContent = function () {\n        var textEl = this._textContent;\n        if (textEl) {\n            textEl.innerTransformable = null;\n            this._detachComponent(textEl);\n            this._textContent = null;\n            this._innerTextDefaultStyle = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.getTextGuideLine = function () {\n        return this._textGuide;\n    };\n    Element.prototype.setTextGuideLine = function (guideLine) {\n        if (this._textGuide && this._textGuide !== guideLine) {\n            this.removeTextGuideLine();\n        }\n        this._attachComponent(guideLine);\n        this._textGuide = guideLine;\n        this.markRedraw();\n    };\n    Element.prototype.removeTextGuideLine = function () {\n        var textGuide = this._textGuide;\n        if (textGuide) {\n            this._detachComponent(textGuide);\n            this._textGuide = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.markRedraw = function () {\n        this.__dirty |= REDRAW_BIT;\n        var zr = this.__zr;\n        if (zr) {\n            if (this.__inHover) {\n                zr.refreshHover();\n            }\n            else {\n                zr.refresh();\n            }\n        }\n        if (this.__hostTarget) {\n            this.__hostTarget.markRedraw();\n        }\n    };\n    Element.prototype.dirty = function () {\n        this.markRedraw();\n    };\n    Element.prototype._toggleHoverLayerFlag = function (inHover) {\n        this.__inHover = inHover;\n        var textContent = this._textContent;\n        var textGuide = this._textGuide;\n        if (textContent) {\n            textContent.__inHover = inHover;\n        }\n        if (textGuide) {\n            textGuide.__inHover = inHover;\n        }\n    };\n    Element.prototype.addSelfToZr = function (zr) {\n        if (this.__zr === zr) {\n            return;\n        }\n        this.__zr = zr;\n        var animators = this.animators;\n        if (animators) {\n            for (var i = 0; i < animators.length; i++) {\n                zr.animation.addAnimator(animators[i]);\n            }\n        }\n        if (this._clipPath) {\n            this._clipPath.addSelfToZr(zr);\n        }\n        if (this._textContent) {\n            this._textContent.addSelfToZr(zr);\n        }\n        if (this._textGuide) {\n            this._textGuide.addSelfToZr(zr);\n        }\n    };\n    Element.prototype.removeSelfFromZr = function (zr) {\n        if (!this.__zr) {\n            return;\n        }\n        this.__zr = null;\n        var animators = this.animators;\n        if (animators) {\n            for (var i = 0; i < animators.length; i++) {\n                zr.animation.removeAnimator(animators[i]);\n            }\n        }\n        if (this._clipPath) {\n            this._clipPath.removeSelfFromZr(zr);\n        }\n        if (this._textContent) {\n            this._textContent.removeSelfFromZr(zr);\n        }\n        if (this._textGuide) {\n            this._textGuide.removeSelfFromZr(zr);\n        }\n    };\n    Element.prototype.animate = function (key, loop, allowDiscreteAnimation) {\n        var target = key ? this[key] : this;\n        if (process.env.NODE_ENV !== 'production') {\n            if (!target) {\n                logError('Property \"'\n                    + key\n                    + '\" is not existed in element '\n                    + this.id);\n                return;\n            }\n        }\n        var animator = new Animator(target, loop, allowDiscreteAnimation);\n        key && (animator.targetName = key);\n        this.addAnimator(animator, key);\n        return animator;\n    };\n    Element.prototype.addAnimator = function (animator, key) {\n        var zr = this.__zr;\n        var el = this;\n        animator.during(function () {\n            el.updateDuringAnimation(key);\n        }).done(function () {\n            var animators = el.animators;\n            var idx = indexOf(animators, animator);\n            if (idx >= 0) {\n                animators.splice(idx, 1);\n            }\n        });\n        this.animators.push(animator);\n        if (zr) {\n            zr.animation.addAnimator(animator);\n        }\n        zr && zr.wakeUp();\n    };\n    Element.prototype.updateDuringAnimation = function (key) {\n        this.markRedraw();\n    };\n    Element.prototype.stopAnimation = function (scope, forwardToLast) {\n        var animators = this.animators;\n        var len = animators.length;\n        var leftAnimators = [];\n        for (var i = 0; i < len; i++) {\n            var animator = animators[i];\n            if (!scope || scope === animator.scope) {\n                animator.stop(forwardToLast);\n            }\n            else {\n                leftAnimators.push(animator);\n            }\n        }\n        this.animators = leftAnimators;\n        return this;\n    };\n    Element.prototype.animateTo = function (target, cfg, animationProps) {\n        animateTo(this, target, cfg, animationProps);\n    };\n    Element.prototype.animateFrom = function (target, cfg, animationProps) {\n        animateTo(this, target, cfg, animationProps, true);\n    };\n    Element.prototype._transitionState = function (stateName, target, cfg, animationProps) {\n        var animators = animateTo(this, target, cfg, animationProps);\n        for (var i = 0; i < animators.length; i++) {\n            animators[i].__fromStateTransition = stateName;\n        }\n    };\n    Element.prototype.getBoundingRect = function () {\n        return null;\n    };\n    Element.prototype.getPaintRect = function () {\n        return null;\n    };\n    Element.initDefaultProps = (function () {\n        var elProto = Element.prototype;\n        elProto.type = 'element';\n        elProto.name = '';\n        elProto.ignore =\n            elProto.silent =\n                elProto.isGroup =\n                    elProto.draggable =\n                        elProto.dragging =\n                            elProto.ignoreClip =\n                                elProto.__inHover = false;\n        elProto.__dirty = REDRAW_BIT;\n        var logs = {};\n        function logDeprecatedError(key, xKey, yKey) {\n            if (!logs[key + xKey + yKey]) {\n                console.warn(\"DEPRECATED: '\" + key + \"' has been deprecated. use '\" + xKey + \"', '\" + yKey + \"' instead\");\n                logs[key + xKey + yKey] = true;\n            }\n        }\n        function createLegacyProperty(key, privateKey, xKey, yKey) {\n            Object.defineProperty(elProto, key, {\n                get: function () {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logDeprecatedError(key, xKey, yKey);\n                    }\n                    if (!this[privateKey]) {\n                        var pos = this[privateKey] = [];\n                        enhanceArray(this, pos);\n                    }\n                    return this[privateKey];\n                },\n                set: function (pos) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logDeprecatedError(key, xKey, yKey);\n                    }\n                    this[xKey] = pos[0];\n                    this[yKey] = pos[1];\n                    this[privateKey] = pos;\n                    enhanceArray(this, pos);\n                }\n            });\n            function enhanceArray(self, pos) {\n                Object.defineProperty(pos, 0, {\n                    get: function () {\n                        return self[xKey];\n                    },\n                    set: function (val) {\n                        self[xKey] = val;\n                    }\n                });\n                Object.defineProperty(pos, 1, {\n                    get: function () {\n                        return self[yKey];\n                    },\n                    set: function (val) {\n                        self[yKey] = val;\n                    }\n                });\n            }\n        }\n        if (Object.defineProperty) {\n            createLegacyProperty('position', '_legacyPos', 'x', 'y');\n            createLegacyProperty('scale', '_legacyScale', 'scaleX', 'scaleY');\n            createLegacyProperty('origin', '_legacyOrigin', 'originX', 'originY');\n        }\n    })();\n    return Element;\n}());\nmixin(Element, Eventful);\nmixin(Element, Transformable);\nfunction animateTo(animatable, target, cfg, animationProps, reverse) {\n    cfg = cfg || {};\n    var animators = [];\n    animateToShallow(animatable, '', animatable, target, cfg, animationProps, animators, reverse);\n    var finishCount = animators.length;\n    var doneHappened = false;\n    var cfgDone = cfg.done;\n    var cfgAborted = cfg.aborted;\n    var doneCb = function () {\n        doneHappened = true;\n        finishCount--;\n        if (finishCount <= 0) {\n            doneHappened\n                ? (cfgDone && cfgDone())\n                : (cfgAborted && cfgAborted());\n        }\n    };\n    var abortedCb = function () {\n        finishCount--;\n        if (finishCount <= 0) {\n            doneHappened\n                ? (cfgDone && cfgDone())\n                : (cfgAborted && cfgAborted());\n        }\n    };\n    if (!finishCount) {\n        cfgDone && cfgDone();\n    }\n    if (animators.length > 0 && cfg.during) {\n        animators[0].during(function (target, percent) {\n            cfg.during(percent);\n        });\n    }\n    for (var i = 0; i < animators.length; i++) {\n        var animator = animators[i];\n        if (doneCb) {\n            animator.done(doneCb);\n        }\n        if (abortedCb) {\n            animator.aborted(abortedCb);\n        }\n        if (cfg.force) {\n            animator.duration(cfg.duration);\n        }\n        animator.start(cfg.easing);\n    }\n    return animators;\n}\nfunction copyArrShallow(source, target, len) {\n    for (var i = 0; i < len; i++) {\n        source[i] = target[i];\n    }\n}\nfunction is2DArray(value) {\n    return isArrayLike(value[0]);\n}\nfunction copyValue(target, source, key) {\n    if (isArrayLike(source[key])) {\n        if (!isArrayLike(target[key])) {\n            target[key] = [];\n        }\n        if (isTypedArray(source[key])) {\n            var len = source[key].length;\n            if (target[key].length !== len) {\n                target[key] = new (source[key].constructor)(len);\n                copyArrShallow(target[key], source[key], len);\n            }\n        }\n        else {\n            var sourceArr = source[key];\n            var targetArr = target[key];\n            var len0 = sourceArr.length;\n            if (is2DArray(sourceArr)) {\n                var len1 = sourceArr[0].length;\n                for (var i = 0; i < len0; i++) {\n                    if (!targetArr[i]) {\n                        targetArr[i] = Array.prototype.slice.call(sourceArr[i]);\n                    }\n                    else {\n                        copyArrShallow(targetArr[i], sourceArr[i], len1);\n                    }\n                }\n            }\n            else {\n                copyArrShallow(targetArr, sourceArr, len0);\n            }\n            targetArr.length = sourceArr.length;\n        }\n    }\n    else {\n        target[key] = source[key];\n    }\n}\nfunction isValueSame(val1, val2) {\n    return val1 === val2\n        || isArrayLike(val1) && isArrayLike(val2) && is1DArraySame(val1, val2);\n}\nfunction is1DArraySame(arr0, arr1) {\n    var len = arr0.length;\n    if (len !== arr1.length) {\n        return false;\n    }\n    for (var i = 0; i < len; i++) {\n        if (arr0[i] !== arr1[i]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction animateToShallow(animatable, topKey, animateObj, target, cfg, animationProps, animators, reverse) {\n    var targetKeys = keys(target);\n    var duration = cfg.duration;\n    var delay = cfg.delay;\n    var additive = cfg.additive;\n    var setToFinal = cfg.setToFinal;\n    var animateAll = !isObject(animationProps);\n    var existsAnimators = animatable.animators;\n    var animationKeys = [];\n    for (var k = 0; k < targetKeys.length; k++) {\n        var innerKey = targetKeys[k];\n        var targetVal = target[innerKey];\n        if (targetVal != null && animateObj[innerKey] != null\n            && (animateAll || animationProps[innerKey])) {\n            if (isObject(targetVal)\n                && !isArrayLike(targetVal)\n                && !isGradientObject(targetVal)) {\n                if (topKey) {\n                    if (!reverse) {\n                        animateObj[innerKey] = targetVal;\n                        animatable.updateDuringAnimation(topKey);\n                    }\n                    continue;\n                }\n                animateToShallow(animatable, innerKey, animateObj[innerKey], targetVal, cfg, animationProps && animationProps[innerKey], animators, reverse);\n            }\n            else {\n                animationKeys.push(innerKey);\n            }\n        }\n        else if (!reverse) {\n            animateObj[innerKey] = targetVal;\n            animatable.updateDuringAnimation(topKey);\n            animationKeys.push(innerKey);\n        }\n    }\n    var keyLen = animationKeys.length;\n    if (!additive && keyLen) {\n        for (var i = 0; i < existsAnimators.length; i++) {\n            var animator = existsAnimators[i];\n            if (animator.targetName === topKey) {\n                var allAborted = animator.stopTracks(animationKeys);\n                if (allAborted) {\n                    var idx = indexOf(existsAnimators, animator);\n                    existsAnimators.splice(idx, 1);\n                }\n            }\n        }\n    }\n    if (!cfg.force) {\n        animationKeys = filter(animationKeys, function (key) { return !isValueSame(target[key], animateObj[key]); });\n        keyLen = animationKeys.length;\n    }\n    if (keyLen > 0\n        || (cfg.force && !animators.length)) {\n        var revertedSource = void 0;\n        var reversedTarget = void 0;\n        var sourceClone = void 0;\n        if (reverse) {\n            reversedTarget = {};\n            if (setToFinal) {\n                revertedSource = {};\n            }\n            for (var i = 0; i < keyLen; i++) {\n                var innerKey = animationKeys[i];\n                reversedTarget[innerKey] = animateObj[innerKey];\n                if (setToFinal) {\n                    revertedSource[innerKey] = target[innerKey];\n                }\n                else {\n                    animateObj[innerKey] = target[innerKey];\n                }\n            }\n        }\n        else if (setToFinal) {\n            sourceClone = {};\n            for (var i = 0; i < keyLen; i++) {\n                var innerKey = animationKeys[i];\n                sourceClone[innerKey] = cloneValue(animateObj[innerKey]);\n                copyValue(animateObj, target, innerKey);\n            }\n        }\n        var animator = new Animator(animateObj, false, false, additive ? filter(existsAnimators, function (animator) { return animator.targetName === topKey; }) : null);\n        animator.targetName = topKey;\n        if (cfg.scope) {\n            animator.scope = cfg.scope;\n        }\n        if (setToFinal && revertedSource) {\n            animator.whenWithKeys(0, revertedSource, animationKeys);\n        }\n        if (sourceClone) {\n            animator.whenWithKeys(0, sourceClone, animationKeys);\n        }\n        animator.whenWithKeys(duration == null ? 500 : duration, reverse ? reversedTarget : target, animationKeys).delay(delay || 0);\n        animatable.addAnimator(animator, topKey);\n        animators.push(animator);\n    }\n}\nexport default Element;\n", "import { __extends } from \"tslib\";\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { keys, extend, createObject } from '../core/util.js';\nimport { REDRAW_BIT, STYLE_CHANGED_BIT } from './constants.js';\nvar STYLE_MAGIC_KEY = '__zr_style_' + Math.round((Math.random() * 10));\nexport var DEFAULT_COMMON_STYLE = {\n    shadowBlur: 0,\n    shadowOffsetX: 0,\n    shadowOffsetY: 0,\n    shadowColor: '#000',\n    opacity: 1,\n    blend: 'source-over'\n};\nexport var DEFAULT_COMMON_ANIMATION_PROPS = {\n    style: {\n        shadowBlur: true,\n        shadowOffsetX: true,\n        shadowOffsetY: true,\n        shadowColor: true,\n        opacity: true\n    }\n};\nDEFAULT_COMMON_STYLE[STYLE_MAGIC_KEY] = true;\nvar PRIMARY_STATES_KEYS = ['z', 'z2', 'invisible'];\nvar PRIMARY_STATES_KEYS_IN_HOVER_LAYER = ['invisible'];\nvar Displayable = (function (_super) {\n    __extends(Displayable, _super);\n    function Displayable(props) {\n        return _super.call(this, props) || this;\n    }\n    Displayable.prototype._init = function (props) {\n        var keysArr = keys(props);\n        for (var i = 0; i < keysArr.length; i++) {\n            var key = keysArr[i];\n            if (key === 'style') {\n                this.useStyle(props[key]);\n            }\n            else {\n                _super.prototype.attrKV.call(this, key, props[key]);\n            }\n        }\n        if (!this.style) {\n            this.useStyle({});\n        }\n    };\n    Displayable.prototype.beforeBrush = function () { };\n    Displayable.prototype.afterBrush = function () { };\n    Displayable.prototype.innerBeforeBrush = function () { };\n    Displayable.prototype.innerAfterBrush = function () { };\n    Displayable.prototype.shouldBePainted = function (viewWidth, viewHeight, considerClipPath, considerAncestors) {\n        var m = this.transform;\n        if (this.ignore\n            || this.invisible\n            || this.style.opacity === 0\n            || (this.culling\n                && isDisplayableCulled(this, viewWidth, viewHeight))\n            || (m && !m[0] && !m[3])) {\n            return false;\n        }\n        if (considerClipPath && this.__clipPaths) {\n            for (var i = 0; i < this.__clipPaths.length; ++i) {\n                if (this.__clipPaths[i].isZeroArea()) {\n                    return false;\n                }\n            }\n        }\n        if (considerAncestors && this.parent) {\n            var parent_1 = this.parent;\n            while (parent_1) {\n                if (parent_1.ignore) {\n                    return false;\n                }\n                parent_1 = parent_1.parent;\n            }\n        }\n        return true;\n    };\n    Displayable.prototype.contain = function (x, y) {\n        return this.rectContain(x, y);\n    };\n    Displayable.prototype.traverse = function (cb, context) {\n        cb.call(context, this);\n    };\n    Displayable.prototype.rectContain = function (x, y) {\n        var coord = this.transformCoordToLocal(x, y);\n        var rect = this.getBoundingRect();\n        return rect.contain(coord[0], coord[1]);\n    };\n    Displayable.prototype.getPaintRect = function () {\n        var rect = this._paintRect;\n        if (!this._paintRect || this.__dirty) {\n            var transform = this.transform;\n            var elRect = this.getBoundingRect();\n            var style = this.style;\n            var shadowSize = style.shadowBlur || 0;\n            var shadowOffsetX = style.shadowOffsetX || 0;\n            var shadowOffsetY = style.shadowOffsetY || 0;\n            rect = this._paintRect || (this._paintRect = new BoundingRect(0, 0, 0, 0));\n            if (transform) {\n                BoundingRect.applyTransform(rect, elRect, transform);\n            }\n            else {\n                rect.copy(elRect);\n            }\n            if (shadowSize || shadowOffsetX || shadowOffsetY) {\n                rect.width += shadowSize * 2 + Math.abs(shadowOffsetX);\n                rect.height += shadowSize * 2 + Math.abs(shadowOffsetY);\n                rect.x = Math.min(rect.x, rect.x + shadowOffsetX - shadowSize);\n                rect.y = Math.min(rect.y, rect.y + shadowOffsetY - shadowSize);\n            }\n            var tolerance = this.dirtyRectTolerance;\n            if (!rect.isZero()) {\n                rect.x = Math.floor(rect.x - tolerance);\n                rect.y = Math.floor(rect.y - tolerance);\n                rect.width = Math.ceil(rect.width + 1 + tolerance * 2);\n                rect.height = Math.ceil(rect.height + 1 + tolerance * 2);\n            }\n        }\n        return rect;\n    };\n    Displayable.prototype.setPrevPaintRect = function (paintRect) {\n        if (paintRect) {\n            this._prevPaintRect = this._prevPaintRect || new BoundingRect(0, 0, 0, 0);\n            this._prevPaintRect.copy(paintRect);\n        }\n        else {\n            this._prevPaintRect = null;\n        }\n    };\n    Displayable.prototype.getPrevPaintRect = function () {\n        return this._prevPaintRect;\n    };\n    Displayable.prototype.animateStyle = function (loop) {\n        return this.animate('style', loop);\n    };\n    Displayable.prototype.updateDuringAnimation = function (targetKey) {\n        if (targetKey === 'style') {\n            this.dirtyStyle();\n        }\n        else {\n            this.markRedraw();\n        }\n    };\n    Displayable.prototype.attrKV = function (key, value) {\n        if (key !== 'style') {\n            _super.prototype.attrKV.call(this, key, value);\n        }\n        else {\n            if (!this.style) {\n                this.useStyle(value);\n            }\n            else {\n                this.setStyle(value);\n            }\n        }\n    };\n    Displayable.prototype.setStyle = function (keyOrObj, value) {\n        if (typeof keyOrObj === 'string') {\n            this.style[keyOrObj] = value;\n        }\n        else {\n            extend(this.style, keyOrObj);\n        }\n        this.dirtyStyle();\n        return this;\n    };\n    Displayable.prototype.dirtyStyle = function (notRedraw) {\n        if (!notRedraw) {\n            this.markRedraw();\n        }\n        this.__dirty |= STYLE_CHANGED_BIT;\n        if (this._rect) {\n            this._rect = null;\n        }\n    };\n    Displayable.prototype.dirty = function () {\n        this.dirtyStyle();\n    };\n    Displayable.prototype.styleChanged = function () {\n        return !!(this.__dirty & STYLE_CHANGED_BIT);\n    };\n    Displayable.prototype.styleUpdated = function () {\n        this.__dirty &= ~STYLE_CHANGED_BIT;\n    };\n    Displayable.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_COMMON_STYLE, obj);\n    };\n    Displayable.prototype.useStyle = function (obj) {\n        if (!obj[STYLE_MAGIC_KEY]) {\n            obj = this.createStyle(obj);\n        }\n        if (this.__inHover) {\n            this.__hoverStyle = obj;\n        }\n        else {\n            this.style = obj;\n        }\n        this.dirtyStyle();\n    };\n    Displayable.prototype.isStyleObject = function (obj) {\n        return obj[STYLE_MAGIC_KEY];\n    };\n    Displayable.prototype._innerSaveToNormal = function (toState) {\n        _super.prototype._innerSaveToNormal.call(this, toState);\n        var normalState = this._normalState;\n        if (toState.style && !normalState.style) {\n            normalState.style = this._mergeStyle(this.createStyle(), this.style);\n        }\n        this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n    };\n    Displayable.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n        _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n        var needsRestoreToNormal = !(state && keepCurrentStates);\n        var targetStyle;\n        if (state && state.style) {\n            if (transition) {\n                if (keepCurrentStates) {\n                    targetStyle = state.style;\n                }\n                else {\n                    targetStyle = this._mergeStyle(this.createStyle(), normalState.style);\n                    this._mergeStyle(targetStyle, state.style);\n                }\n            }\n            else {\n                targetStyle = this._mergeStyle(this.createStyle(), keepCurrentStates ? this.style : normalState.style);\n                this._mergeStyle(targetStyle, state.style);\n            }\n        }\n        else if (needsRestoreToNormal) {\n            targetStyle = normalState.style;\n        }\n        if (targetStyle) {\n            if (transition) {\n                var sourceStyle = this.style;\n                this.style = this.createStyle(needsRestoreToNormal ? {} : sourceStyle);\n                if (needsRestoreToNormal) {\n                    var changedKeys = keys(sourceStyle);\n                    for (var i = 0; i < changedKeys.length; i++) {\n                        var key = changedKeys[i];\n                        if (key in targetStyle) {\n                            targetStyle[key] = targetStyle[key];\n                            this.style[key] = sourceStyle[key];\n                        }\n                    }\n                }\n                var targetKeys = keys(targetStyle);\n                for (var i = 0; i < targetKeys.length; i++) {\n                    var key = targetKeys[i];\n                    this.style[key] = this.style[key];\n                }\n                this._transitionState(stateName, {\n                    style: targetStyle\n                }, animationCfg, this.getAnimationStyleProps());\n            }\n            else {\n                this.useStyle(targetStyle);\n            }\n        }\n        var statesKeys = this.__inHover ? PRIMARY_STATES_KEYS_IN_HOVER_LAYER : PRIMARY_STATES_KEYS;\n        for (var i = 0; i < statesKeys.length; i++) {\n            var key = statesKeys[i];\n            if (state && state[key] != null) {\n                this[key] = state[key];\n            }\n            else if (needsRestoreToNormal) {\n                if (normalState[key] != null) {\n                    this[key] = normalState[key];\n                }\n            }\n        }\n    };\n    Displayable.prototype._mergeStates = function (states) {\n        var mergedState = _super.prototype._mergeStates.call(this, states);\n        var mergedStyle;\n        for (var i = 0; i < states.length; i++) {\n            var state = states[i];\n            if (state.style) {\n                mergedStyle = mergedStyle || {};\n                this._mergeStyle(mergedStyle, state.style);\n            }\n        }\n        if (mergedStyle) {\n            mergedState.style = mergedStyle;\n        }\n        return mergedState;\n    };\n    Displayable.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n        extend(targetStyle, sourceStyle);\n        return targetStyle;\n    };\n    Displayable.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_COMMON_ANIMATION_PROPS;\n    };\n    Displayable.initDefaultProps = (function () {\n        var dispProto = Displayable.prototype;\n        dispProto.type = 'displayable';\n        dispProto.invisible = false;\n        dispProto.z = 0;\n        dispProto.z2 = 0;\n        dispProto.zlevel = 0;\n        dispProto.culling = false;\n        dispProto.cursor = 'pointer';\n        dispProto.rectHover = false;\n        dispProto.incremental = false;\n        dispProto._rect = null;\n        dispProto.dirtyRectTolerance = 0;\n        dispProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT;\n    })();\n    return Displayable;\n}(Element));\nvar tmpRect = new BoundingRect(0, 0, 0, 0);\nvar viewRect = new BoundingRect(0, 0, 0, 0);\nfunction isDisplayableCulled(el, width, height) {\n    tmpRect.copy(el.getBoundingRect());\n    if (el.transform) {\n        tmpRect.applyTransform(el.transform);\n    }\n    viewRect.width = width;\n    viewRect.height = height;\n    return !tmpRect.intersect(viewRect);\n}\nexport default Displayable;\n", "import * as vec2 from './vector.js';\nimport * as curve from './curve.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI2 = Math.PI * 2;\nvar start = vec2.create();\nvar end = vec2.create();\nvar extremity = vec2.create();\nexport function fromPoints(points, min, max) {\n    if (points.length === 0) {\n        return;\n    }\n    var p = points[0];\n    var left = p[0];\n    var right = p[0];\n    var top = p[1];\n    var bottom = p[1];\n    for (var i = 1; i < points.length; i++) {\n        p = points[i];\n        left = mathMin(left, p[0]);\n        right = mathMax(right, p[0]);\n        top = mathMin(top, p[1]);\n        bottom = mathMax(bottom, p[1]);\n    }\n    min[0] = left;\n    min[1] = top;\n    max[0] = right;\n    max[1] = bottom;\n}\nexport function fromLine(x0, y0, x1, y1, min, max) {\n    min[0] = mathMin(x0, x1);\n    min[1] = mathMin(y0, y1);\n    max[0] = mathMax(x0, x1);\n    max[1] = mathMax(y0, y1);\n}\nvar xDim = [];\nvar yDim = [];\nexport function fromCubic(x0, y0, x1, y1, x2, y2, x3, y3, min, max) {\n    var cubicExtrema = curve.cubicExtrema;\n    var cubicAt = curve.cubicAt;\n    var n = cubicExtrema(x0, x1, x2, x3, xDim);\n    min[0] = Infinity;\n    min[1] = Infinity;\n    max[0] = -Infinity;\n    max[1] = -Infinity;\n    for (var i = 0; i < n; i++) {\n        var x = cubicAt(x0, x1, x2, x3, xDim[i]);\n        min[0] = mathMin(x, min[0]);\n        max[0] = mathMax(x, max[0]);\n    }\n    n = cubicExtrema(y0, y1, y2, y3, yDim);\n    for (var i = 0; i < n; i++) {\n        var y = cubicAt(y0, y1, y2, y3, yDim[i]);\n        min[1] = mathMin(y, min[1]);\n        max[1] = mathMax(y, max[1]);\n    }\n    min[0] = mathMin(x0, min[0]);\n    max[0] = mathMax(x0, max[0]);\n    min[0] = mathMin(x3, min[0]);\n    max[0] = mathMax(x3, max[0]);\n    min[1] = mathMin(y0, min[1]);\n    max[1] = mathMax(y0, max[1]);\n    min[1] = mathMin(y3, min[1]);\n    max[1] = mathMax(y3, max[1]);\n}\nexport function fromQuadratic(x0, y0, x1, y1, x2, y2, min, max) {\n    var quadraticExtremum = curve.quadraticExtremum;\n    var quadraticAt = curve.quadraticAt;\n    var tx = mathMax(mathMin(quadraticExtremum(x0, x1, x2), 1), 0);\n    var ty = mathMax(mathMin(quadraticExtremum(y0, y1, y2), 1), 0);\n    var x = quadraticAt(x0, x1, x2, tx);\n    var y = quadraticAt(y0, y1, y2, ty);\n    min[0] = mathMin(x0, x2, x);\n    min[1] = mathMin(y0, y2, y);\n    max[0] = mathMax(x0, x2, x);\n    max[1] = mathMax(y0, y2, y);\n}\nexport function fromArc(x, y, rx, ry, startAngle, endAngle, anticlockwise, min, max) {\n    var vec2Min = vec2.min;\n    var vec2Max = vec2.max;\n    var diff = Math.abs(startAngle - endAngle);\n    if (diff % PI2 < 1e-4 && diff > 1e-4) {\n        min[0] = x - rx;\n        min[1] = y - ry;\n        max[0] = x + rx;\n        max[1] = y + ry;\n        return;\n    }\n    start[0] = mathCos(startAngle) * rx + x;\n    start[1] = mathSin(startAngle) * ry + y;\n    end[0] = mathCos(endAngle) * rx + x;\n    end[1] = mathSin(endAngle) * ry + y;\n    vec2Min(min, start, end);\n    vec2Max(max, start, end);\n    startAngle = startAngle % (PI2);\n    if (startAngle < 0) {\n        startAngle = startAngle + PI2;\n    }\n    endAngle = endAngle % (PI2);\n    if (endAngle < 0) {\n        endAngle = endAngle + PI2;\n    }\n    if (startAngle > endAngle && !anticlockwise) {\n        endAngle += PI2;\n    }\n    else if (startAngle < endAngle && anticlockwise) {\n        startAngle += PI2;\n    }\n    if (anticlockwise) {\n        var tmp = endAngle;\n        endAngle = startAngle;\n        startAngle = tmp;\n    }\n    for (var angle = 0; angle < endAngle; angle += Math.PI / 2) {\n        if (angle > startAngle) {\n            extremity[0] = mathCos(angle) * rx + x;\n            extremity[1] = mathSin(angle) * ry + y;\n            vec2Min(min, extremity, min);\n            vec2Max(max, extremity, max);\n        }\n    }\n}\n", "import * as vec2 from './vector.js';\nimport BoundingRect from './BoundingRect.js';\nimport { devicePixelRatio as dpr } from '../config.js';\nimport { fromLine, fromCubic, fromQuadratic, fromArc } from './bbox.js';\nimport { cubicLength, cubicSubdivide, quadraticLength, quadraticSubdivide } from './curve.js';\nvar CMD = {\n    M: 1,\n    L: 2,\n    C: 3,\n    Q: 4,\n    A: 5,\n    Z: 6,\n    R: 7\n};\nvar tmpOutX = [];\nvar tmpOutY = [];\nvar min = [];\nvar max = [];\nvar min2 = [];\nvar max2 = [];\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathCos = Math.cos;\nvar mathSin = Math.sin;\nvar mathAbs = Math.abs;\nvar PI = Math.PI;\nvar PI2 = PI * 2;\nvar hasTypedArray = typeof Float32Array !== 'undefined';\nvar tmpAngles = [];\nfunction modPI2(radian) {\n    var n = Math.round(radian / PI * 1e8) / 1e8;\n    return (n % 2) * PI;\n}\nexport function normalizeArcAngles(angles, anticlockwise) {\n    var newStartAngle = modPI2(angles[0]);\n    if (newStartAngle < 0) {\n        newStartAngle += PI2;\n    }\n    var delta = newStartAngle - angles[0];\n    var newEndAngle = angles[1];\n    newEndAngle += delta;\n    if (!anticlockwise && newEndAngle - newStartAngle >= PI2) {\n        newEndAngle = newStartAngle + PI2;\n    }\n    else if (anticlockwise && newStartAngle - newEndAngle >= PI2) {\n        newEndAngle = newStartAngle - PI2;\n    }\n    else if (!anticlockwise && newStartAngle > newEndAngle) {\n        newEndAngle = newStartAngle + (PI2 - modPI2(newStartAngle - newEndAngle));\n    }\n    else if (anticlockwise && newStartAngle < newEndAngle) {\n        newEndAngle = newStartAngle - (PI2 - modPI2(newEndAngle - newStartAngle));\n    }\n    angles[0] = newStartAngle;\n    angles[1] = newEndAngle;\n}\nvar PathProxy = (function () {\n    function PathProxy(notSaveData) {\n        this.dpr = 1;\n        this._xi = 0;\n        this._yi = 0;\n        this._x0 = 0;\n        this._y0 = 0;\n        this._len = 0;\n        if (notSaveData) {\n            this._saveData = false;\n        }\n        if (this._saveData) {\n            this.data = [];\n        }\n    }\n    PathProxy.prototype.increaseVersion = function () {\n        this._version++;\n    };\n    PathProxy.prototype.getVersion = function () {\n        return this._version;\n    };\n    PathProxy.prototype.setScale = function (sx, sy, segmentIgnoreThreshold) {\n        segmentIgnoreThreshold = segmentIgnoreThreshold || 0;\n        if (segmentIgnoreThreshold > 0) {\n            this._ux = mathAbs(segmentIgnoreThreshold / dpr / sx) || 0;\n            this._uy = mathAbs(segmentIgnoreThreshold / dpr / sy) || 0;\n        }\n    };\n    PathProxy.prototype.setDPR = function (dpr) {\n        this.dpr = dpr;\n    };\n    PathProxy.prototype.setContext = function (ctx) {\n        this._ctx = ctx;\n    };\n    PathProxy.prototype.getContext = function () {\n        return this._ctx;\n    };\n    PathProxy.prototype.beginPath = function () {\n        this._ctx && this._ctx.beginPath();\n        this.reset();\n        return this;\n    };\n    PathProxy.prototype.reset = function () {\n        if (this._saveData) {\n            this._len = 0;\n        }\n        if (this._pathSegLen) {\n            this._pathSegLen = null;\n            this._pathLen = 0;\n        }\n        this._version++;\n    };\n    PathProxy.prototype.moveTo = function (x, y) {\n        this._drawPendingPt();\n        this.addData(CMD.M, x, y);\n        this._ctx && this._ctx.moveTo(x, y);\n        this._x0 = x;\n        this._y0 = y;\n        this._xi = x;\n        this._yi = y;\n        return this;\n    };\n    PathProxy.prototype.lineTo = function (x, y) {\n        var dx = mathAbs(x - this._xi);\n        var dy = mathAbs(y - this._yi);\n        var exceedUnit = dx > this._ux || dy > this._uy;\n        this.addData(CMD.L, x, y);\n        if (this._ctx && exceedUnit) {\n            this._ctx.lineTo(x, y);\n        }\n        if (exceedUnit) {\n            this._xi = x;\n            this._yi = y;\n            this._pendingPtDist = 0;\n        }\n        else {\n            var d2 = dx * dx + dy * dy;\n            if (d2 > this._pendingPtDist) {\n                this._pendingPtX = x;\n                this._pendingPtY = y;\n                this._pendingPtDist = d2;\n            }\n        }\n        return this;\n    };\n    PathProxy.prototype.bezierCurveTo = function (x1, y1, x2, y2, x3, y3) {\n        this._drawPendingPt();\n        this.addData(CMD.C, x1, y1, x2, y2, x3, y3);\n        if (this._ctx) {\n            this._ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n        }\n        this._xi = x3;\n        this._yi = y3;\n        return this;\n    };\n    PathProxy.prototype.quadraticCurveTo = function (x1, y1, x2, y2) {\n        this._drawPendingPt();\n        this.addData(CMD.Q, x1, y1, x2, y2);\n        if (this._ctx) {\n            this._ctx.quadraticCurveTo(x1, y1, x2, y2);\n        }\n        this._xi = x2;\n        this._yi = y2;\n        return this;\n    };\n    PathProxy.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n        this._drawPendingPt();\n        tmpAngles[0] = startAngle;\n        tmpAngles[1] = endAngle;\n        normalizeArcAngles(tmpAngles, anticlockwise);\n        startAngle = tmpAngles[0];\n        endAngle = tmpAngles[1];\n        var delta = endAngle - startAngle;\n        this.addData(CMD.A, cx, cy, r, r, startAngle, delta, 0, anticlockwise ? 0 : 1);\n        this._ctx && this._ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n        this._xi = mathCos(endAngle) * r + cx;\n        this._yi = mathSin(endAngle) * r + cy;\n        return this;\n    };\n    PathProxy.prototype.arcTo = function (x1, y1, x2, y2, radius) {\n        this._drawPendingPt();\n        if (this._ctx) {\n            this._ctx.arcTo(x1, y1, x2, y2, radius);\n        }\n        return this;\n    };\n    PathProxy.prototype.rect = function (x, y, w, h) {\n        this._drawPendingPt();\n        this._ctx && this._ctx.rect(x, y, w, h);\n        this.addData(CMD.R, x, y, w, h);\n        return this;\n    };\n    PathProxy.prototype.closePath = function () {\n        this._drawPendingPt();\n        this.addData(CMD.Z);\n        var ctx = this._ctx;\n        var x0 = this._x0;\n        var y0 = this._y0;\n        if (ctx) {\n            ctx.closePath();\n        }\n        this._xi = x0;\n        this._yi = y0;\n        return this;\n    };\n    PathProxy.prototype.fill = function (ctx) {\n        ctx && ctx.fill();\n        this.toStatic();\n    };\n    PathProxy.prototype.stroke = function (ctx) {\n        ctx && ctx.stroke();\n        this.toStatic();\n    };\n    PathProxy.prototype.len = function () {\n        return this._len;\n    };\n    PathProxy.prototype.setData = function (data) {\n        var len = data.length;\n        if (!(this.data && this.data.length === len) && hasTypedArray) {\n            this.data = new Float32Array(len);\n        }\n        for (var i = 0; i < len; i++) {\n            this.data[i] = data[i];\n        }\n        this._len = len;\n    };\n    PathProxy.prototype.appendPath = function (path) {\n        if (!(path instanceof Array)) {\n            path = [path];\n        }\n        var len = path.length;\n        var appendSize = 0;\n        var offset = this._len;\n        for (var i = 0; i < len; i++) {\n            appendSize += path[i].len();\n        }\n        if (hasTypedArray && (this.data instanceof Float32Array)) {\n            this.data = new Float32Array(offset + appendSize);\n        }\n        for (var i = 0; i < len; i++) {\n            var appendPathData = path[i].data;\n            for (var k = 0; k < appendPathData.length; k++) {\n                this.data[offset++] = appendPathData[k];\n            }\n        }\n        this._len = offset;\n    };\n    PathProxy.prototype.addData = function (cmd, a, b, c, d, e, f, g, h) {\n        if (!this._saveData) {\n            return;\n        }\n        var data = this.data;\n        if (this._len + arguments.length > data.length) {\n            this._expandData();\n            data = this.data;\n        }\n        for (var i = 0; i < arguments.length; i++) {\n            data[this._len++] = arguments[i];\n        }\n    };\n    PathProxy.prototype._drawPendingPt = function () {\n        if (this._pendingPtDist > 0) {\n            this._ctx && this._ctx.lineTo(this._pendingPtX, this._pendingPtY);\n            this._pendingPtDist = 0;\n        }\n    };\n    PathProxy.prototype._expandData = function () {\n        if (!(this.data instanceof Array)) {\n            var newData = [];\n            for (var i = 0; i < this._len; i++) {\n                newData[i] = this.data[i];\n            }\n            this.data = newData;\n        }\n    };\n    PathProxy.prototype.toStatic = function () {\n        if (!this._saveData) {\n            return;\n        }\n        this._drawPendingPt();\n        var data = this.data;\n        if (data instanceof Array) {\n            data.length = this._len;\n            if (hasTypedArray && this._len > 11) {\n                this.data = new Float32Array(data);\n            }\n        }\n    };\n    PathProxy.prototype.getBoundingRect = function () {\n        min[0] = min[1] = min2[0] = min2[1] = Number.MAX_VALUE;\n        max[0] = max[1] = max2[0] = max2[1] = -Number.MAX_VALUE;\n        var data = this.data;\n        var xi = 0;\n        var yi = 0;\n        var x0 = 0;\n        var y0 = 0;\n        var i;\n        for (i = 0; i < this._len;) {\n            var cmd = data[i++];\n            var isFirst = i === 1;\n            if (isFirst) {\n                xi = data[i];\n                yi = data[i + 1];\n                x0 = xi;\n                y0 = yi;\n            }\n            switch (cmd) {\n                case CMD.M:\n                    xi = x0 = data[i++];\n                    yi = y0 = data[i++];\n                    min2[0] = x0;\n                    min2[1] = y0;\n                    max2[0] = x0;\n                    max2[1] = y0;\n                    break;\n                case CMD.L:\n                    fromLine(xi, yi, data[i], data[i + 1], min2, max2);\n                    xi = data[i++];\n                    yi = data[i++];\n                    break;\n                case CMD.C:\n                    fromCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], min2, max2);\n                    xi = data[i++];\n                    yi = data[i++];\n                    break;\n                case CMD.Q:\n                    fromQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], min2, max2);\n                    xi = data[i++];\n                    yi = data[i++];\n                    break;\n                case CMD.A:\n                    var cx = data[i++];\n                    var cy = data[i++];\n                    var rx = data[i++];\n                    var ry = data[i++];\n                    var startAngle = data[i++];\n                    var endAngle = data[i++] + startAngle;\n                    i += 1;\n                    var anticlockwise = !data[i++];\n                    if (isFirst) {\n                        x0 = mathCos(startAngle) * rx + cx;\n                        y0 = mathSin(startAngle) * ry + cy;\n                    }\n                    fromArc(cx, cy, rx, ry, startAngle, endAngle, anticlockwise, min2, max2);\n                    xi = mathCos(endAngle) * rx + cx;\n                    yi = mathSin(endAngle) * ry + cy;\n                    break;\n                case CMD.R:\n                    x0 = xi = data[i++];\n                    y0 = yi = data[i++];\n                    var width = data[i++];\n                    var height = data[i++];\n                    fromLine(x0, y0, x0 + width, y0 + height, min2, max2);\n                    break;\n                case CMD.Z:\n                    xi = x0;\n                    yi = y0;\n                    break;\n            }\n            vec2.min(min, min, min2);\n            vec2.max(max, max, max2);\n        }\n        if (i === 0) {\n            min[0] = min[1] = max[0] = max[1] = 0;\n        }\n        return new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    };\n    PathProxy.prototype._calculateLength = function () {\n        var data = this.data;\n        var len = this._len;\n        var ux = this._ux;\n        var uy = this._uy;\n        var xi = 0;\n        var yi = 0;\n        var x0 = 0;\n        var y0 = 0;\n        if (!this._pathSegLen) {\n            this._pathSegLen = [];\n        }\n        var pathSegLen = this._pathSegLen;\n        var pathTotalLen = 0;\n        var segCount = 0;\n        for (var i = 0; i < len;) {\n            var cmd = data[i++];\n            var isFirst = i === 1;\n            if (isFirst) {\n                xi = data[i];\n                yi = data[i + 1];\n                x0 = xi;\n                y0 = yi;\n            }\n            var l = -1;\n            switch (cmd) {\n                case CMD.M:\n                    xi = x0 = data[i++];\n                    yi = y0 = data[i++];\n                    break;\n                case CMD.L: {\n                    var x2 = data[i++];\n                    var y2 = data[i++];\n                    var dx = x2 - xi;\n                    var dy = y2 - yi;\n                    if (mathAbs(dx) > ux || mathAbs(dy) > uy || i === len - 1) {\n                        l = Math.sqrt(dx * dx + dy * dy);\n                        xi = x2;\n                        yi = y2;\n                    }\n                    break;\n                }\n                case CMD.C: {\n                    var x1 = data[i++];\n                    var y1 = data[i++];\n                    var x2 = data[i++];\n                    var y2 = data[i++];\n                    var x3 = data[i++];\n                    var y3 = data[i++];\n                    l = cubicLength(xi, yi, x1, y1, x2, y2, x3, y3, 10);\n                    xi = x3;\n                    yi = y3;\n                    break;\n                }\n                case CMD.Q: {\n                    var x1 = data[i++];\n                    var y1 = data[i++];\n                    var x2 = data[i++];\n                    var y2 = data[i++];\n                    l = quadraticLength(xi, yi, x1, y1, x2, y2, 10);\n                    xi = x2;\n                    yi = y2;\n                    break;\n                }\n                case CMD.A:\n                    var cx = data[i++];\n                    var cy = data[i++];\n                    var rx = data[i++];\n                    var ry = data[i++];\n                    var startAngle = data[i++];\n                    var delta = data[i++];\n                    var endAngle = delta + startAngle;\n                    i += 1;\n                    if (isFirst) {\n                        x0 = mathCos(startAngle) * rx + cx;\n                        y0 = mathSin(startAngle) * ry + cy;\n                    }\n                    l = mathMax(rx, ry) * mathMin(PI2, Math.abs(delta));\n                    xi = mathCos(endAngle) * rx + cx;\n                    yi = mathSin(endAngle) * ry + cy;\n                    break;\n                case CMD.R: {\n                    x0 = xi = data[i++];\n                    y0 = yi = data[i++];\n                    var width = data[i++];\n                    var height = data[i++];\n                    l = width * 2 + height * 2;\n                    break;\n                }\n                case CMD.Z: {\n                    var dx = x0 - xi;\n                    var dy = y0 - yi;\n                    l = Math.sqrt(dx * dx + dy * dy);\n                    xi = x0;\n                    yi = y0;\n                    break;\n                }\n            }\n            if (l >= 0) {\n                pathSegLen[segCount++] = l;\n                pathTotalLen += l;\n            }\n        }\n        this._pathLen = pathTotalLen;\n        return pathTotalLen;\n    };\n    PathProxy.prototype.rebuildPath = function (ctx, percent) {\n        var d = this.data;\n        var ux = this._ux;\n        var uy = this._uy;\n        var len = this._len;\n        var x0;\n        var y0;\n        var xi;\n        var yi;\n        var x;\n        var y;\n        var drawPart = percent < 1;\n        var pathSegLen;\n        var pathTotalLen;\n        var accumLength = 0;\n        var segCount = 0;\n        var displayedLength;\n        var pendingPtDist = 0;\n        var pendingPtX;\n        var pendingPtY;\n        if (drawPart) {\n            if (!this._pathSegLen) {\n                this._calculateLength();\n            }\n            pathSegLen = this._pathSegLen;\n            pathTotalLen = this._pathLen;\n            displayedLength = percent * pathTotalLen;\n            if (!displayedLength) {\n                return;\n            }\n        }\n        lo: for (var i = 0; i < len;) {\n            var cmd = d[i++];\n            var isFirst = i === 1;\n            if (isFirst) {\n                xi = d[i];\n                yi = d[i + 1];\n                x0 = xi;\n                y0 = yi;\n            }\n            if (cmd !== CMD.L && pendingPtDist > 0) {\n                ctx.lineTo(pendingPtX, pendingPtY);\n                pendingPtDist = 0;\n            }\n            switch (cmd) {\n                case CMD.M:\n                    x0 = xi = d[i++];\n                    y0 = yi = d[i++];\n                    ctx.moveTo(xi, yi);\n                    break;\n                case CMD.L: {\n                    x = d[i++];\n                    y = d[i++];\n                    var dx = mathAbs(x - xi);\n                    var dy = mathAbs(y - yi);\n                    if (dx > ux || dy > uy) {\n                        if (drawPart) {\n                            var l = pathSegLen[segCount++];\n                            if (accumLength + l > displayedLength) {\n                                var t = (displayedLength - accumLength) / l;\n                                ctx.lineTo(xi * (1 - t) + x * t, yi * (1 - t) + y * t);\n                                break lo;\n                            }\n                            accumLength += l;\n                        }\n                        ctx.lineTo(x, y);\n                        xi = x;\n                        yi = y;\n                        pendingPtDist = 0;\n                    }\n                    else {\n                        var d2 = dx * dx + dy * dy;\n                        if (d2 > pendingPtDist) {\n                            pendingPtX = x;\n                            pendingPtY = y;\n                            pendingPtDist = d2;\n                        }\n                    }\n                    break;\n                }\n                case CMD.C: {\n                    var x1 = d[i++];\n                    var y1 = d[i++];\n                    var x2 = d[i++];\n                    var y2 = d[i++];\n                    var x3 = d[i++];\n                    var y3 = d[i++];\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var t = (displayedLength - accumLength) / l;\n                            cubicSubdivide(xi, x1, x2, x3, t, tmpOutX);\n                            cubicSubdivide(yi, y1, y2, y3, t, tmpOutY);\n                            ctx.bezierCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2], tmpOutX[3], tmpOutY[3]);\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n                    xi = x3;\n                    yi = y3;\n                    break;\n                }\n                case CMD.Q: {\n                    var x1 = d[i++];\n                    var y1 = d[i++];\n                    var x2 = d[i++];\n                    var y2 = d[i++];\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var t = (displayedLength - accumLength) / l;\n                            quadraticSubdivide(xi, x1, x2, t, tmpOutX);\n                            quadraticSubdivide(yi, y1, y2, t, tmpOutY);\n                            ctx.quadraticCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2]);\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.quadraticCurveTo(x1, y1, x2, y2);\n                    xi = x2;\n                    yi = y2;\n                    break;\n                }\n                case CMD.A:\n                    var cx = d[i++];\n                    var cy = d[i++];\n                    var rx = d[i++];\n                    var ry = d[i++];\n                    var startAngle = d[i++];\n                    var delta = d[i++];\n                    var psi = d[i++];\n                    var anticlockwise = !d[i++];\n                    var r = (rx > ry) ? rx : ry;\n                    var isEllipse = mathAbs(rx - ry) > 1e-3;\n                    var endAngle = startAngle + delta;\n                    var breakBuild = false;\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            endAngle = startAngle + delta * (displayedLength - accumLength) / l;\n                            breakBuild = true;\n                        }\n                        accumLength += l;\n                    }\n                    if (isEllipse && ctx.ellipse) {\n                        ctx.ellipse(cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise);\n                    }\n                    else {\n                        ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n                    }\n                    if (breakBuild) {\n                        break lo;\n                    }\n                    if (isFirst) {\n                        x0 = mathCos(startAngle) * rx + cx;\n                        y0 = mathSin(startAngle) * ry + cy;\n                    }\n                    xi = mathCos(endAngle) * rx + cx;\n                    yi = mathSin(endAngle) * ry + cy;\n                    break;\n                case CMD.R:\n                    x0 = xi = d[i];\n                    y0 = yi = d[i + 1];\n                    x = d[i++];\n                    y = d[i++];\n                    var width = d[i++];\n                    var height = d[i++];\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var d_1 = displayedLength - accumLength;\n                            ctx.moveTo(x, y);\n                            ctx.lineTo(x + mathMin(d_1, width), y);\n                            d_1 -= width;\n                            if (d_1 > 0) {\n                                ctx.lineTo(x + width, y + mathMin(d_1, height));\n                            }\n                            d_1 -= height;\n                            if (d_1 > 0) {\n                                ctx.lineTo(x + mathMax(width - d_1, 0), y + height);\n                            }\n                            d_1 -= width;\n                            if (d_1 > 0) {\n                                ctx.lineTo(x, y + mathMax(height - d_1, 0));\n                            }\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.rect(x, y, width, height);\n                    break;\n                case CMD.Z:\n                    if (drawPart) {\n                        var l = pathSegLen[segCount++];\n                        if (accumLength + l > displayedLength) {\n                            var t = (displayedLength - accumLength) / l;\n                            ctx.lineTo(xi * (1 - t) + x0 * t, yi * (1 - t) + y0 * t);\n                            break lo;\n                        }\n                        accumLength += l;\n                    }\n                    ctx.closePath();\n                    xi = x0;\n                    yi = y0;\n            }\n        }\n    };\n    PathProxy.prototype.clone = function () {\n        var newProxy = new PathProxy();\n        var data = this.data;\n        newProxy.data = data.slice ? data.slice()\n            : Array.prototype.slice.call(data);\n        newProxy._len = this._len;\n        return newProxy;\n    };\n    PathProxy.CMD = CMD;\n    PathProxy.initDefaultProps = (function () {\n        var proto = PathProxy.prototype;\n        proto._saveData = true;\n        proto._ux = 0;\n        proto._uy = 0;\n        proto._pendingPtDist = 0;\n        proto._version = 0;\n    })();\n    return PathProxy;\n}());\nexport default PathProxy;\n", "export function containStroke(x0, y0, x1, y1, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    var _a = 0;\n    var _b = x0;\n    if ((y > y0 + _l && y > y1 + _l)\n        || (y < y0 - _l && y < y1 - _l)\n        || (x > x0 + _l && x > x1 + _l)\n        || (x < x0 - _l && x < x1 - _l)) {\n        return false;\n    }\n    if (x0 !== x1) {\n        _a = (y0 - y1) / (x0 - x1);\n        _b = (x0 * y1 - x1 * y0) / (x0 - x1);\n    }\n    else {\n        return Math.abs(x - x0) <= _l / 2;\n    }\n    var tmp = _a * x - y + _b;\n    var _s = tmp * tmp / (_a * _a + 1);\n    return _s <= _l / 2 * _l / 2;\n}\n", "import * as curve from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, x3, y3, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    if ((y > y0 + _l && y > y1 + _l && y > y2 + _l && y > y3 + _l)\n        || (y < y0 - _l && y < y1 - _l && y < y2 - _l && y < y3 - _l)\n        || (x > x0 + _l && x > x1 + _l && x > x2 + _l && x > x3 + _l)\n        || (x < x0 - _l && x < x1 - _l && x < x2 - _l && x < x3 - _l)) {\n        return false;\n    }\n    var d = curve.cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, null);\n    return d <= _l / 2;\n}\n", "import { quadraticProjectPoint } from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    if ((y > y0 + _l && y > y1 + _l && y > y2 + _l)\n        || (y < y0 - _l && y < y1 - _l && y < y2 - _l)\n        || (x > x0 + _l && x > x1 + _l && x > x2 + _l)\n        || (x < x0 - _l && x < x1 - _l && x < x2 - _l)) {\n        return false;\n    }\n    var d = quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, null);\n    return d <= _l / 2;\n}\n", "var PI2 = Math.PI * 2;\nexport function normalizeRadian(angle) {\n    angle %= PI2;\n    if (angle < 0) {\n        angle += PI2;\n    }\n    return angle;\n}\n", "import { normalizeRadian } from './util.js';\nvar PI2 = Math.PI * 2;\nexport function containStroke(cx, cy, r, startAngle, endAngle, anticlockwise, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    x -= cx;\n    y -= cy;\n    var d = Math.sqrt(x * x + y * y);\n    if ((d - _l > r) || (d + _l < r)) {\n        return false;\n    }\n    if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n        return true;\n    }\n    if (anticlockwise) {\n        var tmp = startAngle;\n        startAngle = normalizeRadian(endAngle);\n        endAngle = normalizeRadian(tmp);\n    }\n    else {\n        startAngle = normalizeRadian(startAngle);\n        endAngle = normalizeRadian(endAngle);\n    }\n    if (startAngle > endAngle) {\n        endAngle += PI2;\n    }\n    var angle = Math.atan2(y, x);\n    if (angle < 0) {\n        angle += PI2;\n    }\n    return (angle >= startAngle && angle <= endAngle)\n        || (angle + PI2 >= startAngle && angle + PI2 <= endAngle);\n}\n", "export default function windingLine(x0, y0, x1, y1, x, y) {\n    if ((y > y0 && y > y1) || (y < y0 && y < y1)) {\n        return 0;\n    }\n    if (y1 === y0) {\n        return 0;\n    }\n    var t = (y - y0) / (y1 - y0);\n    var dir = y1 < y0 ? 1 : -1;\n    if (t === 1 || t === 0) {\n        dir = y1 < y0 ? 0.5 : -0.5;\n    }\n    var x_ = t * (x1 - x0) + x0;\n    return x_ === x ? Infinity : x_ > x ? dir : 0;\n}\n", "import PathProxy from '../core/PathProxy.js';\nimport * as line from './line.js';\nimport * as cubic from './cubic.js';\nimport * as quadratic from './quadratic.js';\nimport * as arc from './arc.js';\nimport * as curve from '../core/curve.js';\nimport windingLine from './windingLine.js';\nvar CMD = PathProxy.CMD;\nvar PI2 = Math.PI * 2;\nvar EPSILON = 1e-4;\nfunction isAroundEqual(a, b) {\n    return Math.abs(a - b) < EPSILON;\n}\nvar roots = [-1, -1, -1];\nvar extrema = [-1, -1];\nfunction swapExtrema() {\n    var tmp = extrema[0];\n    extrema[0] = extrema[1];\n    extrema[1] = tmp;\n}\nfunction windingCubic(x0, y0, x1, y1, x2, y2, x3, y3, x, y) {\n    if ((y > y0 && y > y1 && y > y2 && y > y3)\n        || (y < y0 && y < y1 && y < y2 && y < y3)) {\n        return 0;\n    }\n    var nRoots = curve.cubicRootAt(y0, y1, y2, y3, y, roots);\n    if (nRoots === 0) {\n        return 0;\n    }\n    else {\n        var w = 0;\n        var nExtrema = -1;\n        var y0_ = void 0;\n        var y1_ = void 0;\n        for (var i = 0; i < nRoots; i++) {\n            var t = roots[i];\n            var unit = (t === 0 || t === 1) ? 0.5 : 1;\n            var x_ = curve.cubicAt(x0, x1, x2, x3, t);\n            if (x_ < x) {\n                continue;\n            }\n            if (nExtrema < 0) {\n                nExtrema = curve.cubicExtrema(y0, y1, y2, y3, extrema);\n                if (extrema[1] < extrema[0] && nExtrema > 1) {\n                    swapExtrema();\n                }\n                y0_ = curve.cubicAt(y0, y1, y2, y3, extrema[0]);\n                if (nExtrema > 1) {\n                    y1_ = curve.cubicAt(y0, y1, y2, y3, extrema[1]);\n                }\n            }\n            if (nExtrema === 2) {\n                if (t < extrema[0]) {\n                    w += y0_ < y0 ? unit : -unit;\n                }\n                else if (t < extrema[1]) {\n                    w += y1_ < y0_ ? unit : -unit;\n                }\n                else {\n                    w += y3 < y1_ ? unit : -unit;\n                }\n            }\n            else {\n                if (t < extrema[0]) {\n                    w += y0_ < y0 ? unit : -unit;\n                }\n                else {\n                    w += y3 < y0_ ? unit : -unit;\n                }\n            }\n        }\n        return w;\n    }\n}\nfunction windingQuadratic(x0, y0, x1, y1, x2, y2, x, y) {\n    if ((y > y0 && y > y1 && y > y2)\n        || (y < y0 && y < y1 && y < y2)) {\n        return 0;\n    }\n    var nRoots = curve.quadraticRootAt(y0, y1, y2, y, roots);\n    if (nRoots === 0) {\n        return 0;\n    }\n    else {\n        var t = curve.quadraticExtremum(y0, y1, y2);\n        if (t >= 0 && t <= 1) {\n            var w = 0;\n            var y_ = curve.quadraticAt(y0, y1, y2, t);\n            for (var i = 0; i < nRoots; i++) {\n                var unit = (roots[i] === 0 || roots[i] === 1) ? 0.5 : 1;\n                var x_ = curve.quadraticAt(x0, x1, x2, roots[i]);\n                if (x_ < x) {\n                    continue;\n                }\n                if (roots[i] < t) {\n                    w += y_ < y0 ? unit : -unit;\n                }\n                else {\n                    w += y2 < y_ ? unit : -unit;\n                }\n            }\n            return w;\n        }\n        else {\n            var unit = (roots[0] === 0 || roots[0] === 1) ? 0.5 : 1;\n            var x_ = curve.quadraticAt(x0, x1, x2, roots[0]);\n            if (x_ < x) {\n                return 0;\n            }\n            return y2 < y0 ? unit : -unit;\n        }\n    }\n}\nfunction windingArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y) {\n    y -= cy;\n    if (y > r || y < -r) {\n        return 0;\n    }\n    var tmp = Math.sqrt(r * r - y * y);\n    roots[0] = -tmp;\n    roots[1] = tmp;\n    var dTheta = Math.abs(startAngle - endAngle);\n    if (dTheta < 1e-4) {\n        return 0;\n    }\n    if (dTheta >= PI2 - 1e-4) {\n        startAngle = 0;\n        endAngle = PI2;\n        var dir = anticlockwise ? 1 : -1;\n        if (x >= roots[0] + cx && x <= roots[1] + cx) {\n            return dir;\n        }\n        else {\n            return 0;\n        }\n    }\n    if (startAngle > endAngle) {\n        var tmp_1 = startAngle;\n        startAngle = endAngle;\n        endAngle = tmp_1;\n    }\n    if (startAngle < 0) {\n        startAngle += PI2;\n        endAngle += PI2;\n    }\n    var w = 0;\n    for (var i = 0; i < 2; i++) {\n        var x_ = roots[i];\n        if (x_ + cx > x) {\n            var angle = Math.atan2(y, x_);\n            var dir = anticlockwise ? 1 : -1;\n            if (angle < 0) {\n                angle = PI2 + angle;\n            }\n            if ((angle >= startAngle && angle <= endAngle)\n                || (angle + PI2 >= startAngle && angle + PI2 <= endAngle)) {\n                if (angle > Math.PI / 2 && angle < Math.PI * 1.5) {\n                    dir = -dir;\n                }\n                w += dir;\n            }\n        }\n    }\n    return w;\n}\nfunction containPath(path, lineWidth, isStroke, x, y) {\n    var data = path.data;\n    var len = path.len();\n    var w = 0;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    var x1;\n    var y1;\n    for (var i = 0; i < len;) {\n        var cmd = data[i++];\n        var isFirst = i === 1;\n        if (cmd === CMD.M && i > 1) {\n            if (!isStroke) {\n                w += windingLine(xi, yi, x0, y0, x, y);\n            }\n        }\n        if (isFirst) {\n            xi = data[i];\n            yi = data[i + 1];\n            x0 = xi;\n            y0 = yi;\n        }\n        switch (cmd) {\n            case CMD.M:\n                x0 = data[i++];\n                y0 = data[i++];\n                xi = x0;\n                yi = y0;\n                break;\n            case CMD.L:\n                if (isStroke) {\n                    if (line.containStroke(xi, yi, data[i], data[i + 1], lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingLine(xi, yi, data[i], data[i + 1], x, y) || 0;\n                }\n                xi = data[i++];\n                yi = data[i++];\n                break;\n            case CMD.C:\n                if (isStroke) {\n                    if (cubic.containStroke(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n                }\n                xi = data[i++];\n                yi = data[i++];\n                break;\n            case CMD.Q:\n                if (isStroke) {\n                    if (quadratic.containStroke(xi, yi, data[i++], data[i++], data[i], data[i + 1], lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y) || 0;\n                }\n                xi = data[i++];\n                yi = data[i++];\n                break;\n            case CMD.A:\n                var cx = data[i++];\n                var cy = data[i++];\n                var rx = data[i++];\n                var ry = data[i++];\n                var theta = data[i++];\n                var dTheta = data[i++];\n                i += 1;\n                var anticlockwise = !!(1 - data[i++]);\n                x1 = Math.cos(theta) * rx + cx;\n                y1 = Math.sin(theta) * ry + cy;\n                if (!isFirst) {\n                    w += windingLine(xi, yi, x1, y1, x, y);\n                }\n                else {\n                    x0 = x1;\n                    y0 = y1;\n                }\n                var _x = (x - cx) * ry / rx + cx;\n                if (isStroke) {\n                    if (arc.containStroke(cx, cy, ry, theta, theta + dTheta, anticlockwise, lineWidth, _x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y);\n                }\n                xi = Math.cos(theta + dTheta) * rx + cx;\n                yi = Math.sin(theta + dTheta) * ry + cy;\n                break;\n            case CMD.R:\n                x0 = xi = data[i++];\n                y0 = yi = data[i++];\n                var width = data[i++];\n                var height = data[i++];\n                x1 = x0 + width;\n                y1 = y0 + height;\n                if (isStroke) {\n                    if (line.containStroke(x0, y0, x1, y0, lineWidth, x, y)\n                        || line.containStroke(x1, y0, x1, y1, lineWidth, x, y)\n                        || line.containStroke(x1, y1, x0, y1, lineWidth, x, y)\n                        || line.containStroke(x0, y1, x0, y0, lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingLine(x1, y0, x1, y1, x, y);\n                    w += windingLine(x0, y1, x0, y0, x, y);\n                }\n                break;\n            case CMD.Z:\n                if (isStroke) {\n                    if (line.containStroke(xi, yi, x0, y0, lineWidth, x, y)) {\n                        return true;\n                    }\n                }\n                else {\n                    w += windingLine(xi, yi, x0, y0, x, y);\n                }\n                xi = x0;\n                yi = y0;\n                break;\n        }\n    }\n    if (!isStroke && !isAroundEqual(yi, y0)) {\n        w += windingLine(xi, yi, x0, y0, x, y) || 0;\n    }\n    return w !== 0;\n}\nexport function contain(pathProxy, x, y) {\n    return containPath(pathProxy, 0, false, x, y);\n}\nexport function containStroke(pathProxy, lineWidth, x, y) {\n    return containPath(pathProxy, lineWidth, true, x, y);\n}\n", "import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport * as pathContain from '../contain/path.js';\nimport { defaults, keys, extend, clone, isString, createObject } from '../core/util.js';\nimport { lum } from '../tool/color.js';\nimport { DARK_LABEL_COLOR, LIGHT_LABEL_COLOR, DARK_MODE_THRESHOLD, LIGHTER_LABEL_COLOR } from '../config.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT, STYLE_CHANGED_BIT } from './constants.js';\nimport { TRANSFORMABLE_PROPS } from '../core/Transformable.js';\nexport var DEFAULT_PATH_STYLE = defaults({\n    fill: '#000',\n    stroke: null,\n    strokePercent: 1,\n    fillOpacity: 1,\n    strokeOpacity: 1,\n    lineDashOffset: 0,\n    lineWidth: 1,\n    lineCap: 'butt',\n    miterLimit: 10,\n    strokeNoScale: false,\n    strokeFirst: false\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_PATH_ANIMATION_PROPS = {\n    style: defaults({\n        fill: true,\n        stroke: true,\n        strokePercent: true,\n        fillOpacity: true,\n        strokeOpacity: true,\n        lineDashOffset: true,\n        lineWidth: true,\n        miterLimit: true\n    }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar pathCopyParams = TRANSFORMABLE_PROPS.concat(['invisible',\n    'culling', 'z', 'z2', 'zlevel', 'parent'\n]);\nvar Path = (function (_super) {\n    __extends(Path, _super);\n    function Path(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Path.prototype.update = function () {\n        var _this = this;\n        _super.prototype.update.call(this);\n        var style = this.style;\n        if (style.decal) {\n            var decalEl = this._decalEl = this._decalEl || new Path();\n            if (decalEl.buildPath === Path.prototype.buildPath) {\n                decalEl.buildPath = function (ctx) {\n                    _this.buildPath(ctx, _this.shape);\n                };\n            }\n            decalEl.silent = true;\n            var decalElStyle = decalEl.style;\n            for (var key in style) {\n                if (decalElStyle[key] !== style[key]) {\n                    decalElStyle[key] = style[key];\n                }\n            }\n            decalElStyle.fill = style.fill ? style.decal : null;\n            decalElStyle.decal = null;\n            decalElStyle.shadowColor = null;\n            style.strokeFirst && (decalElStyle.stroke = null);\n            for (var i = 0; i < pathCopyParams.length; ++i) {\n                decalEl[pathCopyParams[i]] = this[pathCopyParams[i]];\n            }\n            decalEl.__dirty |= REDRAW_BIT;\n        }\n        else if (this._decalEl) {\n            this._decalEl = null;\n        }\n    };\n    Path.prototype.getDecalElement = function () {\n        return this._decalEl;\n    };\n    Path.prototype._init = function (props) {\n        var keysArr = keys(props);\n        this.shape = this.getDefaultShape();\n        var defaultStyle = this.getDefaultStyle();\n        if (defaultStyle) {\n            this.useStyle(defaultStyle);\n        }\n        for (var i = 0; i < keysArr.length; i++) {\n            var key = keysArr[i];\n            var value = props[key];\n            if (key === 'style') {\n                if (!this.style) {\n                    this.useStyle(value);\n                }\n                else {\n                    extend(this.style, value);\n                }\n            }\n            else if (key === 'shape') {\n                extend(this.shape, value);\n            }\n            else {\n                _super.prototype.attrKV.call(this, key, value);\n            }\n        }\n        if (!this.style) {\n            this.useStyle({});\n        }\n    };\n    Path.prototype.getDefaultStyle = function () {\n        return null;\n    };\n    Path.prototype.getDefaultShape = function () {\n        return {};\n    };\n    Path.prototype.canBeInsideText = function () {\n        return this.hasFill();\n    };\n    Path.prototype.getInsideTextFill = function () {\n        var pathFill = this.style.fill;\n        if (pathFill !== 'none') {\n            if (isString(pathFill)) {\n                var fillLum = lum(pathFill, 0);\n                if (fillLum > 0.5) {\n                    return DARK_LABEL_COLOR;\n                }\n                else if (fillLum > 0.2) {\n                    return LIGHTER_LABEL_COLOR;\n                }\n                return LIGHT_LABEL_COLOR;\n            }\n            else if (pathFill) {\n                return LIGHT_LABEL_COLOR;\n            }\n        }\n        return DARK_LABEL_COLOR;\n    };\n    Path.prototype.getInsideTextStroke = function (textFill) {\n        var pathFill = this.style.fill;\n        if (isString(pathFill)) {\n            var zr = this.__zr;\n            var isDarkMode = !!(zr && zr.isDarkMode());\n            var isDarkLabel = lum(textFill, 0) < DARK_MODE_THRESHOLD;\n            if (isDarkMode === isDarkLabel) {\n                return pathFill;\n            }\n        }\n    };\n    Path.prototype.buildPath = function (ctx, shapeCfg, inBatch) { };\n    Path.prototype.pathUpdated = function () {\n        this.__dirty &= ~SHAPE_CHANGED_BIT;\n    };\n    Path.prototype.getUpdatedPathProxy = function (inBatch) {\n        !this.path && this.createPathProxy();\n        this.path.beginPath();\n        this.buildPath(this.path, this.shape, inBatch);\n        return this.path;\n    };\n    Path.prototype.createPathProxy = function () {\n        this.path = new PathProxy(false);\n    };\n    Path.prototype.hasStroke = function () {\n        var style = this.style;\n        var stroke = style.stroke;\n        return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n    };\n    Path.prototype.hasFill = function () {\n        var style = this.style;\n        var fill = style.fill;\n        return fill != null && fill !== 'none';\n    };\n    Path.prototype.getBoundingRect = function () {\n        var rect = this._rect;\n        var style = this.style;\n        var needsUpdateRect = !rect;\n        if (needsUpdateRect) {\n            var firstInvoke = false;\n            if (!this.path) {\n                firstInvoke = true;\n                this.createPathProxy();\n            }\n            var path = this.path;\n            if (firstInvoke || (this.__dirty & SHAPE_CHANGED_BIT)) {\n                path.beginPath();\n                this.buildPath(path, this.shape, false);\n                this.pathUpdated();\n            }\n            rect = path.getBoundingRect();\n        }\n        this._rect = rect;\n        if (this.hasStroke() && this.path && this.path.len() > 0) {\n            var rectStroke = this._rectStroke || (this._rectStroke = rect.clone());\n            if (this.__dirty || needsUpdateRect) {\n                rectStroke.copy(rect);\n                var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n                var w = style.lineWidth;\n                if (!this.hasFill()) {\n                    var strokeContainThreshold = this.strokeContainThreshold;\n                    w = Math.max(w, strokeContainThreshold == null ? 4 : strokeContainThreshold);\n                }\n                if (lineScale > 1e-10) {\n                    rectStroke.width += w / lineScale;\n                    rectStroke.height += w / lineScale;\n                    rectStroke.x -= w / lineScale / 2;\n                    rectStroke.y -= w / lineScale / 2;\n                }\n            }\n            return rectStroke;\n        }\n        return rect;\n    };\n    Path.prototype.contain = function (x, y) {\n        var localPos = this.transformCoordToLocal(x, y);\n        var rect = this.getBoundingRect();\n        var style = this.style;\n        x = localPos[0];\n        y = localPos[1];\n        if (rect.contain(x, y)) {\n            var pathProxy = this.path;\n            if (this.hasStroke()) {\n                var lineWidth = style.lineWidth;\n                var lineScale = style.strokeNoScale ? this.getLineScale() : 1;\n                if (lineScale > 1e-10) {\n                    if (!this.hasFill()) {\n                        lineWidth = Math.max(lineWidth, this.strokeContainThreshold);\n                    }\n                    if (pathContain.containStroke(pathProxy, lineWidth / lineScale, x, y)) {\n                        return true;\n                    }\n                }\n            }\n            if (this.hasFill()) {\n                return pathContain.contain(pathProxy, x, y);\n            }\n        }\n        return false;\n    };\n    Path.prototype.dirtyShape = function () {\n        this.__dirty |= SHAPE_CHANGED_BIT;\n        if (this._rect) {\n            this._rect = null;\n        }\n        if (this._decalEl) {\n            this._decalEl.dirtyShape();\n        }\n        this.markRedraw();\n    };\n    Path.prototype.dirty = function () {\n        this.dirtyStyle();\n        this.dirtyShape();\n    };\n    Path.prototype.animateShape = function (loop) {\n        return this.animate('shape', loop);\n    };\n    Path.prototype.updateDuringAnimation = function (targetKey) {\n        if (targetKey === 'style') {\n            this.dirtyStyle();\n        }\n        else if (targetKey === 'shape') {\n            this.dirtyShape();\n        }\n        else {\n            this.markRedraw();\n        }\n    };\n    Path.prototype.attrKV = function (key, value) {\n        if (key === 'shape') {\n            this.setShape(value);\n        }\n        else {\n            _super.prototype.attrKV.call(this, key, value);\n        }\n    };\n    Path.prototype.setShape = function (keyOrObj, value) {\n        var shape = this.shape;\n        if (!shape) {\n            shape = this.shape = {};\n        }\n        if (typeof keyOrObj === 'string') {\n            shape[keyOrObj] = value;\n        }\n        else {\n            extend(shape, keyOrObj);\n        }\n        this.dirtyShape();\n        return this;\n    };\n    Path.prototype.shapeChanged = function () {\n        return !!(this.__dirty & SHAPE_CHANGED_BIT);\n    };\n    Path.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_PATH_STYLE, obj);\n    };\n    Path.prototype._innerSaveToNormal = function (toState) {\n        _super.prototype._innerSaveToNormal.call(this, toState);\n        var normalState = this._normalState;\n        if (toState.shape && !normalState.shape) {\n            normalState.shape = extend({}, this.shape);\n        }\n    };\n    Path.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n        _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n        var needsRestoreToNormal = !(state && keepCurrentStates);\n        var targetShape;\n        if (state && state.shape) {\n            if (transition) {\n                if (keepCurrentStates) {\n                    targetShape = state.shape;\n                }\n                else {\n                    targetShape = extend({}, normalState.shape);\n                    extend(targetShape, state.shape);\n                }\n            }\n            else {\n                targetShape = extend({}, keepCurrentStates ? this.shape : normalState.shape);\n                extend(targetShape, state.shape);\n            }\n        }\n        else if (needsRestoreToNormal) {\n            targetShape = normalState.shape;\n        }\n        if (targetShape) {\n            if (transition) {\n                this.shape = extend({}, this.shape);\n                var targetShapePrimaryProps = {};\n                var shapeKeys = keys(targetShape);\n                for (var i = 0; i < shapeKeys.length; i++) {\n                    var key = shapeKeys[i];\n                    if (typeof targetShape[key] === 'object') {\n                        this.shape[key] = targetShape[key];\n                    }\n                    else {\n                        targetShapePrimaryProps[key] = targetShape[key];\n                    }\n                }\n                this._transitionState(stateName, {\n                    shape: targetShapePrimaryProps\n                }, animationCfg);\n            }\n            else {\n                this.shape = targetShape;\n                this.dirtyShape();\n            }\n        }\n    };\n    Path.prototype._mergeStates = function (states) {\n        var mergedState = _super.prototype._mergeStates.call(this, states);\n        var mergedShape;\n        for (var i = 0; i < states.length; i++) {\n            var state = states[i];\n            if (state.shape) {\n                mergedShape = mergedShape || {};\n                this._mergeStyle(mergedShape, state.shape);\n            }\n        }\n        if (mergedShape) {\n            mergedState.shape = mergedShape;\n        }\n        return mergedState;\n    };\n    Path.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_PATH_ANIMATION_PROPS;\n    };\n    Path.prototype.isZeroArea = function () {\n        return false;\n    };\n    Path.extend = function (defaultProps) {\n        var Sub = (function (_super) {\n            __extends(Sub, _super);\n            function Sub(opts) {\n                var _this = _super.call(this, opts) || this;\n                defaultProps.init && defaultProps.init.call(_this, opts);\n                return _this;\n            }\n            Sub.prototype.getDefaultStyle = function () {\n                return clone(defaultProps.style);\n            };\n            Sub.prototype.getDefaultShape = function () {\n                return clone(defaultProps.shape);\n            };\n            return Sub;\n        }(Path));\n        for (var key in defaultProps) {\n            if (typeof defaultProps[key] === 'function') {\n                Sub.prototype[key] = defaultProps[key];\n            }\n        }\n        return Sub;\n    };\n    Path.initDefaultProps = (function () {\n        var pathProto = Path.prototype;\n        pathProto.type = 'path';\n        pathProto.strokeContainThreshold = 5;\n        pathProto.segmentIgnoreThreshold = 0;\n        pathProto.subPixelOptimize = false;\n        pathProto.autoBatch = false;\n        pathProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT | SHAPE_CHANGED_BIT;\n    })();\n    return Path;\n}(Displayable));\nexport default Path;\n", "import { __extends } from \"tslib\";\nimport Displayable from './Displayable.js';\nimport { getBoundingRect } from '../contain/text.js';\nimport { DEFAULT_PATH_STYLE } from './Path.js';\nimport { createObject, defaults } from '../core/util.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nexport var DEFAULT_TSPAN_STYLE = defaults({\n    strokeFirst: true,\n    font: DEFAULT_FONT,\n    x: 0,\n    y: 0,\n    textAlign: 'left',\n    textBaseline: 'top',\n    miterLimit: 2\n}, DEFAULT_PATH_STYLE);\nvar TSpan = (function (_super) {\n    __extends(TSpan, _super);\n    function TSpan() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TSpan.prototype.hasStroke = function () {\n        var style = this.style;\n        var stroke = style.stroke;\n        return stroke != null && stroke !== 'none' && style.lineWidth > 0;\n    };\n    TSpan.prototype.hasFill = function () {\n        var style = this.style;\n        var fill = style.fill;\n        return fill != null && fill !== 'none';\n    };\n    TSpan.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_TSPAN_STYLE, obj);\n    };\n    TSpan.prototype.setBoundingRect = function (rect) {\n        this._rect = rect;\n    };\n    TSpan.prototype.getBoundingRect = function () {\n        var style = this.style;\n        if (!this._rect) {\n            var text = style.text;\n            text != null ? (text += '') : (text = '');\n            var rect = getBoundingRect(text, style.font, style.textAlign, style.textBaseline);\n            rect.x += style.x || 0;\n            rect.y += style.y || 0;\n            if (this.hasStroke()) {\n                var w = style.lineWidth;\n                rect.x -= w / 2;\n                rect.y -= w / 2;\n                rect.width += w;\n                rect.height += w;\n            }\n            this._rect = rect;\n        }\n        return this._rect;\n    };\n    TSpan.initDefaultProps = (function () {\n        var tspanProto = TSpan.prototype;\n        tspanProto.dirtyRectTolerance = 10;\n    })();\n    return TSpan;\n}(Displayable));\nTSpan.prototype.type = 'tspan';\nexport default TSpan;\n", "import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { defaults, createObject } from '../core/util.js';\nexport var DEFAULT_IMAGE_STYLE = defaults({\n    x: 0,\n    y: 0\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_IMAGE_ANIMATION_PROPS = {\n    style: defaults({\n        x: true,\n        y: true,\n        width: true,\n        height: true,\n        sx: true,\n        sy: true,\n        sWidth: true,\n        sHeight: true\n    }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nfunction isImageLike(source) {\n    return !!(source\n        && typeof source !== 'string'\n        && source.width && source.height);\n}\nvar ZRImage = (function (_super) {\n    __extends(ZRImage, _super);\n    function ZRImage() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ZRImage.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_IMAGE_STYLE, obj);\n    };\n    ZRImage.prototype._getSize = function (dim) {\n        var style = this.style;\n        var size = style[dim];\n        if (size != null) {\n            return size;\n        }\n        var imageSource = isImageLike(style.image)\n            ? style.image : this.__image;\n        if (!imageSource) {\n            return 0;\n        }\n        var otherDim = dim === 'width' ? 'height' : 'width';\n        var otherDimSize = style[otherDim];\n        if (otherDimSize == null) {\n            return imageSource[dim];\n        }\n        else {\n            return imageSource[dim] / imageSource[otherDim] * otherDimSize;\n        }\n    };\n    ZRImage.prototype.getWidth = function () {\n        return this._getSize('width');\n    };\n    ZRImage.prototype.getHeight = function () {\n        return this._getSize('height');\n    };\n    ZRImage.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_IMAGE_ANIMATION_PROPS;\n    };\n    ZRImage.prototype.getBoundingRect = function () {\n        var style = this.style;\n        if (!this._rect) {\n            this._rect = new BoundingRect(style.x || 0, style.y || 0, this.getWidth(), this.getHeight());\n        }\n        return this._rect;\n    };\n    return ZRImage;\n}(Displayable));\nZRImage.prototype.type = 'image';\nexport default ZRImage;\n", "export function buildPath(ctx, shape) {\n    var x = shape.x;\n    var y = shape.y;\n    var width = shape.width;\n    var height = shape.height;\n    var r = shape.r;\n    var r1;\n    var r2;\n    var r3;\n    var r4;\n    if (width < 0) {\n        x = x + width;\n        width = -width;\n    }\n    if (height < 0) {\n        y = y + height;\n        height = -height;\n    }\n    if (typeof r === 'number') {\n        r1 = r2 = r3 = r4 = r;\n    }\n    else if (r instanceof Array) {\n        if (r.length === 1) {\n            r1 = r2 = r3 = r4 = r[0];\n        }\n        else if (r.length === 2) {\n            r1 = r3 = r[0];\n            r2 = r4 = r[1];\n        }\n        else if (r.length === 3) {\n            r1 = r[0];\n            r2 = r4 = r[1];\n            r3 = r[2];\n        }\n        else {\n            r1 = r[0];\n            r2 = r[1];\n            r3 = r[2];\n            r4 = r[3];\n        }\n    }\n    else {\n        r1 = r2 = r3 = r4 = 0;\n    }\n    var total;\n    if (r1 + r2 > width) {\n        total = r1 + r2;\n        r1 *= width / total;\n        r2 *= width / total;\n    }\n    if (r3 + r4 > width) {\n        total = r3 + r4;\n        r3 *= width / total;\n        r4 *= width / total;\n    }\n    if (r2 + r3 > height) {\n        total = r2 + r3;\n        r2 *= height / total;\n        r3 *= height / total;\n    }\n    if (r1 + r4 > height) {\n        total = r1 + r4;\n        r1 *= height / total;\n        r4 *= height / total;\n    }\n    ctx.moveTo(x + r1, y);\n    ctx.lineTo(x + width - r2, y);\n    r2 !== 0 && ctx.arc(x + width - r2, y + r2, r2, -Math.PI / 2, 0);\n    ctx.lineTo(x + width, y + height - r3);\n    r3 !== 0 && ctx.arc(x + width - r3, y + height - r3, r3, 0, Math.PI / 2);\n    ctx.lineTo(x + r4, y + height);\n    r4 !== 0 && ctx.arc(x + r4, y + height - r4, r4, Math.PI / 2, Math.PI);\n    ctx.lineTo(x, y + r1);\n    r1 !== 0 && ctx.arc(x + r1, y + r1, r1, Math.PI, Math.PI * 1.5);\n}\n", "var round = Math.round;\nexport function subPixelOptimizeLine(outputShape, inputShape, style) {\n    if (!inputShape) {\n        return;\n    }\n    var x1 = inputShape.x1;\n    var x2 = inputShape.x2;\n    var y1 = inputShape.y1;\n    var y2 = inputShape.y2;\n    outputShape.x1 = x1;\n    outputShape.x2 = x2;\n    outputShape.y1 = y1;\n    outputShape.y2 = y2;\n    var lineWidth = style && style.lineWidth;\n    if (!lineWidth) {\n        return outputShape;\n    }\n    if (round(x1 * 2) === round(x2 * 2)) {\n        outputShape.x1 = outputShape.x2 = subPixelOptimize(x1, lineWidth, true);\n    }\n    if (round(y1 * 2) === round(y2 * 2)) {\n        outputShape.y1 = outputShape.y2 = subPixelOptimize(y1, lineWidth, true);\n    }\n    return outputShape;\n}\nexport function subPixelOptimizeRect(outputShape, inputShape, style) {\n    if (!inputShape) {\n        return;\n    }\n    var originX = inputShape.x;\n    var originY = inputShape.y;\n    var originWidth = inputShape.width;\n    var originHeight = inputShape.height;\n    outputShape.x = originX;\n    outputShape.y = originY;\n    outputShape.width = originWidth;\n    outputShape.height = originHeight;\n    var lineWidth = style && style.lineWidth;\n    if (!lineWidth) {\n        return outputShape;\n    }\n    outputShape.x = subPixelOptimize(originX, lineWidth, true);\n    outputShape.y = subPixelOptimize(originY, lineWidth, true);\n    outputShape.width = Math.max(subPixelOptimize(originX + originWidth, lineWidth, false) - outputShape.x, originWidth === 0 ? 0 : 1);\n    outputShape.height = Math.max(subPixelOptimize(originY + originHeight, lineWidth, false) - outputShape.y, originHeight === 0 ? 0 : 1);\n    return outputShape;\n}\nexport function subPixelOptimize(position, lineWidth, positiveOrNegative) {\n    if (!lineWidth) {\n        return position;\n    }\n    var doubledPosition = round(position * 2);\n    return (doubledPosition + round(lineWidth)) % 2 === 0\n        ? doubledPosition / 2\n        : (doubledPosition + (positiveOrNegative ? 1 : -1)) / 2;\n}\n", "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as roundRectHelper from '../helper/roundRect.js';\nimport { subPixelOptimizeRect } from '../helper/subPixelOptimize.js';\nvar RectShape = (function () {\n    function RectShape() {\n        this.x = 0;\n        this.y = 0;\n        this.width = 0;\n        this.height = 0;\n    }\n    return RectShape;\n}());\nexport { RectShape };\nvar subPixelOptimizeOutputShape = {};\nvar Rect = (function (_super) {\n    __extends(Rect, _super);\n    function Rect(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Rect.prototype.getDefaultShape = function () {\n        return new RectShape();\n    };\n    Rect.prototype.buildPath = function (ctx, shape) {\n        var x;\n        var y;\n        var width;\n        var height;\n        if (this.subPixelOptimize) {\n            var optimizedShape = subPixelOptimizeRect(subPixelOptimizeOutputShape, shape, this.style);\n            x = optimizedShape.x;\n            y = optimizedShape.y;\n            width = optimizedShape.width;\n            height = optimizedShape.height;\n            optimizedShape.r = shape.r;\n            shape = optimizedShape;\n        }\n        else {\n            x = shape.x;\n            y = shape.y;\n            width = shape.width;\n            height = shape.height;\n        }\n        if (!shape.r) {\n            ctx.rect(x, y, width, height);\n        }\n        else {\n            roundRectHelper.buildPath(ctx, shape);\n        }\n    };\n    Rect.prototype.isZeroArea = function () {\n        return !this.shape.width || !this.shape.height;\n    };\n    return Rect;\n}(Path));\nRect.prototype.type = 'rect';\nexport default Rect;\n", "import { __extends } from \"tslib\";\nimport { parseRichText, parsePlainText } from './helper/parseText.js';\nimport TSpan from './TSpan.js';\nimport { retrieve2, each, normalizeCssArray, trim, retrieve3, extend, keys, defaults } from '../core/util.js';\nimport { adjustTextX, adjustTextY } from '../contain/text.js';\nimport ZRImage from './Image.js';\nimport Rect from './shape/Rect.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Displayable, { DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_SIZE } from '../core/platform.js';\nvar DEFAULT_RICH_TEXT_COLOR = {\n    fill: '#000'\n};\nvar DEFAULT_STROKE_LINE_WIDTH = 2;\nexport var DEFAULT_TEXT_ANIMATION_PROPS = {\n    style: defaults({\n        fill: true,\n        stroke: true,\n        fillOpacity: true,\n        strokeOpacity: true,\n        lineWidth: true,\n        fontSize: true,\n        lineHeight: true,\n        width: true,\n        height: true,\n        textShadowColor: true,\n        textShadowBlur: true,\n        textShadowOffsetX: true,\n        textShadowOffsetY: true,\n        backgroundColor: true,\n        padding: true,\n        borderColor: true,\n        borderWidth: true,\n        borderRadius: true\n    }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar ZRText = (function (_super) {\n    __extends(ZRText, _super);\n    function ZRText(opts) {\n        var _this = _super.call(this) || this;\n        _this.type = 'text';\n        _this._children = [];\n        _this._defaultStyle = DEFAULT_RICH_TEXT_COLOR;\n        _this.attr(opts);\n        return _this;\n    }\n    ZRText.prototype.childrenRef = function () {\n        return this._children;\n    };\n    ZRText.prototype.update = function () {\n        _super.prototype.update.call(this);\n        if (this.styleChanged()) {\n            this._updateSubTexts();\n        }\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            child.zlevel = this.zlevel;\n            child.z = this.z;\n            child.z2 = this.z2;\n            child.culling = this.culling;\n            child.cursor = this.cursor;\n            child.invisible = this.invisible;\n        }\n    };\n    ZRText.prototype.updateTransform = function () {\n        var innerTransformable = this.innerTransformable;\n        if (innerTransformable) {\n            innerTransformable.updateTransform();\n            if (innerTransformable.transform) {\n                this.transform = innerTransformable.transform;\n            }\n        }\n        else {\n            _super.prototype.updateTransform.call(this);\n        }\n    };\n    ZRText.prototype.getLocalTransform = function (m) {\n        var innerTransformable = this.innerTransformable;\n        return innerTransformable\n            ? innerTransformable.getLocalTransform(m)\n            : _super.prototype.getLocalTransform.call(this, m);\n    };\n    ZRText.prototype.getComputedTransform = function () {\n        if (this.__hostTarget) {\n            this.__hostTarget.getComputedTransform();\n            this.__hostTarget.updateInnerText(true);\n        }\n        return _super.prototype.getComputedTransform.call(this);\n    };\n    ZRText.prototype._updateSubTexts = function () {\n        this._childCursor = 0;\n        normalizeTextStyle(this.style);\n        this.style.rich\n            ? this._updateRichTexts()\n            : this._updatePlainTexts();\n        this._children.length = this._childCursor;\n        this.styleUpdated();\n    };\n    ZRText.prototype.addSelfToZr = function (zr) {\n        _super.prototype.addSelfToZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            this._children[i].__zr = zr;\n        }\n    };\n    ZRText.prototype.removeSelfFromZr = function (zr) {\n        _super.prototype.removeSelfFromZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            this._children[i].__zr = null;\n        }\n    };\n    ZRText.prototype.getBoundingRect = function () {\n        if (this.styleChanged()) {\n            this._updateSubTexts();\n        }\n        if (!this._rect) {\n            var tmpRect = new BoundingRect(0, 0, 0, 0);\n            var children = this._children;\n            var tmpMat = [];\n            var rect = null;\n            for (var i = 0; i < children.length; i++) {\n                var child = children[i];\n                var childRect = child.getBoundingRect();\n                var transform = child.getLocalTransform(tmpMat);\n                if (transform) {\n                    tmpRect.copy(childRect);\n                    tmpRect.applyTransform(transform);\n                    rect = rect || tmpRect.clone();\n                    rect.union(tmpRect);\n                }\n                else {\n                    rect = rect || childRect.clone();\n                    rect.union(childRect);\n                }\n            }\n            this._rect = rect || tmpRect;\n        }\n        return this._rect;\n    };\n    ZRText.prototype.setDefaultTextStyle = function (defaultTextStyle) {\n        this._defaultStyle = defaultTextStyle || DEFAULT_RICH_TEXT_COLOR;\n    };\n    ZRText.prototype.setTextContent = function (textContent) {\n        if (process.env.NODE_ENV !== 'production') {\n            throw new Error('Can\\'t attach text on another text');\n        }\n    };\n    ZRText.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n        if (!sourceStyle) {\n            return targetStyle;\n        }\n        var sourceRich = sourceStyle.rich;\n        var targetRich = targetStyle.rich || (sourceRich && {});\n        extend(targetStyle, sourceStyle);\n        if (sourceRich && targetRich) {\n            this._mergeRich(targetRich, sourceRich);\n            targetStyle.rich = targetRich;\n        }\n        else if (targetRich) {\n            targetStyle.rich = targetRich;\n        }\n        return targetStyle;\n    };\n    ZRText.prototype._mergeRich = function (targetRich, sourceRich) {\n        var richNames = keys(sourceRich);\n        for (var i = 0; i < richNames.length; i++) {\n            var richName = richNames[i];\n            targetRich[richName] = targetRich[richName] || {};\n            extend(targetRich[richName], sourceRich[richName]);\n        }\n    };\n    ZRText.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_TEXT_ANIMATION_PROPS;\n    };\n    ZRText.prototype._getOrCreateChild = function (Ctor) {\n        var child = this._children[this._childCursor];\n        if (!child || !(child instanceof Ctor)) {\n            child = new Ctor();\n        }\n        this._children[this._childCursor++] = child;\n        child.__zr = this.__zr;\n        child.parent = this;\n        return child;\n    };\n    ZRText.prototype._updatePlainTexts = function () {\n        var style = this.style;\n        var textFont = style.font || DEFAULT_FONT;\n        var textPadding = style.padding;\n        var text = getStyleText(style);\n        var contentBlock = parsePlainText(text, style);\n        var needDrawBg = needDrawBackground(style);\n        var bgColorDrawn = !!(style.backgroundColor);\n        var outerHeight = contentBlock.outerHeight;\n        var outerWidth = contentBlock.outerWidth;\n        var contentWidth = contentBlock.contentWidth;\n        var textLines = contentBlock.lines;\n        var lineHeight = contentBlock.lineHeight;\n        var defaultStyle = this._defaultStyle;\n        this.isTruncated = !!contentBlock.isTruncated;\n        var baseX = style.x || 0;\n        var baseY = style.y || 0;\n        var textAlign = style.align || defaultStyle.align || 'left';\n        var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign || 'top';\n        var textX = baseX;\n        var textY = adjustTextY(baseY, contentBlock.contentHeight, verticalAlign);\n        if (needDrawBg || textPadding) {\n            var boxX = adjustTextX(baseX, outerWidth, textAlign);\n            var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n            needDrawBg && this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n        }\n        textY += lineHeight / 2;\n        if (textPadding) {\n            textX = getTextXForPadding(baseX, textAlign, textPadding);\n            if (verticalAlign === 'top') {\n                textY += textPadding[0];\n            }\n            else if (verticalAlign === 'bottom') {\n                textY -= textPadding[2];\n            }\n        }\n        var defaultLineWidth = 0;\n        var useDefaultFill = false;\n        var textFill = getFill('fill' in style\n            ? style.fill\n            : (useDefaultFill = true, defaultStyle.fill));\n        var textStroke = getStroke('stroke' in style\n            ? style.stroke\n            : (!bgColorDrawn\n                && (!defaultStyle.autoStroke || useDefaultFill))\n                ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke)\n                : null);\n        var hasShadow = style.textShadowBlur > 0;\n        var fixedBoundingRect = style.width != null\n            && (style.overflow === 'truncate' || style.overflow === 'break' || style.overflow === 'breakAll');\n        var calculatedLineHeight = contentBlock.calculatedLineHeight;\n        for (var i = 0; i < textLines.length; i++) {\n            var el = this._getOrCreateChild(TSpan);\n            var subElStyle = el.createStyle();\n            el.useStyle(subElStyle);\n            subElStyle.text = textLines[i];\n            subElStyle.x = textX;\n            subElStyle.y = textY;\n            if (textAlign) {\n                subElStyle.textAlign = textAlign;\n            }\n            subElStyle.textBaseline = 'middle';\n            subElStyle.opacity = style.opacity;\n            subElStyle.strokeFirst = true;\n            if (hasShadow) {\n                subElStyle.shadowBlur = style.textShadowBlur || 0;\n                subElStyle.shadowColor = style.textShadowColor || 'transparent';\n                subElStyle.shadowOffsetX = style.textShadowOffsetX || 0;\n                subElStyle.shadowOffsetY = style.textShadowOffsetY || 0;\n            }\n            subElStyle.stroke = textStroke;\n            subElStyle.fill = textFill;\n            if (textStroke) {\n                subElStyle.lineWidth = style.lineWidth || defaultLineWidth;\n                subElStyle.lineDash = style.lineDash;\n                subElStyle.lineDashOffset = style.lineDashOffset || 0;\n            }\n            subElStyle.font = textFont;\n            setSeparateFont(subElStyle, style);\n            textY += lineHeight;\n            if (fixedBoundingRect) {\n                el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, contentWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, calculatedLineHeight, subElStyle.textBaseline), contentWidth, calculatedLineHeight));\n            }\n        }\n    };\n    ZRText.prototype._updateRichTexts = function () {\n        var style = this.style;\n        var text = getStyleText(style);\n        var contentBlock = parseRichText(text, style);\n        var contentWidth = contentBlock.width;\n        var outerWidth = contentBlock.outerWidth;\n        var outerHeight = contentBlock.outerHeight;\n        var textPadding = style.padding;\n        var baseX = style.x || 0;\n        var baseY = style.y || 0;\n        var defaultStyle = this._defaultStyle;\n        var textAlign = style.align || defaultStyle.align;\n        var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign;\n        this.isTruncated = !!contentBlock.isTruncated;\n        var boxX = adjustTextX(baseX, outerWidth, textAlign);\n        var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n        var xLeft = boxX;\n        var lineTop = boxY;\n        if (textPadding) {\n            xLeft += textPadding[3];\n            lineTop += textPadding[0];\n        }\n        var xRight = xLeft + contentWidth;\n        if (needDrawBackground(style)) {\n            this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n        }\n        var bgColorDrawn = !!(style.backgroundColor);\n        for (var i = 0; i < contentBlock.lines.length; i++) {\n            var line = contentBlock.lines[i];\n            var tokens = line.tokens;\n            var tokenCount = tokens.length;\n            var lineHeight = line.lineHeight;\n            var remainedWidth = line.width;\n            var leftIndex = 0;\n            var lineXLeft = xLeft;\n            var lineXRight = xRight;\n            var rightIndex = tokenCount - 1;\n            var token = void 0;\n            while (leftIndex < tokenCount\n                && (token = tokens[leftIndex], !token.align || token.align === 'left')) {\n                this._placeToken(token, style, lineHeight, lineTop, lineXLeft, 'left', bgColorDrawn);\n                remainedWidth -= token.width;\n                lineXLeft += token.width;\n                leftIndex++;\n            }\n            while (rightIndex >= 0\n                && (token = tokens[rightIndex], token.align === 'right')) {\n                this._placeToken(token, style, lineHeight, lineTop, lineXRight, 'right', bgColorDrawn);\n                remainedWidth -= token.width;\n                lineXRight -= token.width;\n                rightIndex--;\n            }\n            lineXLeft += (contentWidth - (lineXLeft - xLeft) - (xRight - lineXRight) - remainedWidth) / 2;\n            while (leftIndex <= rightIndex) {\n                token = tokens[leftIndex];\n                this._placeToken(token, style, lineHeight, lineTop, lineXLeft + token.width / 2, 'center', bgColorDrawn);\n                lineXLeft += token.width;\n                leftIndex++;\n            }\n            lineTop += lineHeight;\n        }\n    };\n    ZRText.prototype._placeToken = function (token, style, lineHeight, lineTop, x, textAlign, parentBgColorDrawn) {\n        var tokenStyle = style.rich[token.styleName] || {};\n        tokenStyle.text = token.text;\n        var verticalAlign = token.verticalAlign;\n        var y = lineTop + lineHeight / 2;\n        if (verticalAlign === 'top') {\n            y = lineTop + token.height / 2;\n        }\n        else if (verticalAlign === 'bottom') {\n            y = lineTop + lineHeight - token.height / 2;\n        }\n        var needDrawBg = !token.isLineHolder && needDrawBackground(tokenStyle);\n        needDrawBg && this._renderBackground(tokenStyle, style, textAlign === 'right'\n            ? x - token.width\n            : textAlign === 'center'\n                ? x - token.width / 2\n                : x, y - token.height / 2, token.width, token.height);\n        var bgColorDrawn = !!tokenStyle.backgroundColor;\n        var textPadding = token.textPadding;\n        if (textPadding) {\n            x = getTextXForPadding(x, textAlign, textPadding);\n            y -= token.height / 2 - textPadding[0] - token.innerHeight / 2;\n        }\n        var el = this._getOrCreateChild(TSpan);\n        var subElStyle = el.createStyle();\n        el.useStyle(subElStyle);\n        var defaultStyle = this._defaultStyle;\n        var useDefaultFill = false;\n        var defaultLineWidth = 0;\n        var textFill = getFill('fill' in tokenStyle ? tokenStyle.fill\n            : 'fill' in style ? style.fill\n                : (useDefaultFill = true, defaultStyle.fill));\n        var textStroke = getStroke('stroke' in tokenStyle ? tokenStyle.stroke\n            : 'stroke' in style ? style.stroke\n                : (!bgColorDrawn\n                    && !parentBgColorDrawn\n                    && (!defaultStyle.autoStroke || useDefaultFill)) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke)\n                    : null);\n        var hasShadow = tokenStyle.textShadowBlur > 0\n            || style.textShadowBlur > 0;\n        subElStyle.text = token.text;\n        subElStyle.x = x;\n        subElStyle.y = y;\n        if (hasShadow) {\n            subElStyle.shadowBlur = tokenStyle.textShadowBlur || style.textShadowBlur || 0;\n            subElStyle.shadowColor = tokenStyle.textShadowColor || style.textShadowColor || 'transparent';\n            subElStyle.shadowOffsetX = tokenStyle.textShadowOffsetX || style.textShadowOffsetX || 0;\n            subElStyle.shadowOffsetY = tokenStyle.textShadowOffsetY || style.textShadowOffsetY || 0;\n        }\n        subElStyle.textAlign = textAlign;\n        subElStyle.textBaseline = 'middle';\n        subElStyle.font = token.font || DEFAULT_FONT;\n        subElStyle.opacity = retrieve3(tokenStyle.opacity, style.opacity, 1);\n        setSeparateFont(subElStyle, tokenStyle);\n        if (textStroke) {\n            subElStyle.lineWidth = retrieve3(tokenStyle.lineWidth, style.lineWidth, defaultLineWidth);\n            subElStyle.lineDash = retrieve2(tokenStyle.lineDash, style.lineDash);\n            subElStyle.lineDashOffset = style.lineDashOffset || 0;\n            subElStyle.stroke = textStroke;\n        }\n        if (textFill) {\n            subElStyle.fill = textFill;\n        }\n        var textWidth = token.contentWidth;\n        var textHeight = token.contentHeight;\n        el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, textWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, textHeight, subElStyle.textBaseline), textWidth, textHeight));\n    };\n    ZRText.prototype._renderBackground = function (style, topStyle, x, y, width, height) {\n        var textBackgroundColor = style.backgroundColor;\n        var textBorderWidth = style.borderWidth;\n        var textBorderColor = style.borderColor;\n        var isImageBg = textBackgroundColor && textBackgroundColor.image;\n        var isPlainOrGradientBg = textBackgroundColor && !isImageBg;\n        var textBorderRadius = style.borderRadius;\n        var self = this;\n        var rectEl;\n        var imgEl;\n        if (isPlainOrGradientBg || style.lineHeight || (textBorderWidth && textBorderColor)) {\n            rectEl = this._getOrCreateChild(Rect);\n            rectEl.useStyle(rectEl.createStyle());\n            rectEl.style.fill = null;\n            var rectShape = rectEl.shape;\n            rectShape.x = x;\n            rectShape.y = y;\n            rectShape.width = width;\n            rectShape.height = height;\n            rectShape.r = textBorderRadius;\n            rectEl.dirtyShape();\n        }\n        if (isPlainOrGradientBg) {\n            var rectStyle = rectEl.style;\n            rectStyle.fill = textBackgroundColor || null;\n            rectStyle.fillOpacity = retrieve2(style.fillOpacity, 1);\n        }\n        else if (isImageBg) {\n            imgEl = this._getOrCreateChild(ZRImage);\n            imgEl.onload = function () {\n                self.dirtyStyle();\n            };\n            var imgStyle = imgEl.style;\n            imgStyle.image = textBackgroundColor.image;\n            imgStyle.x = x;\n            imgStyle.y = y;\n            imgStyle.width = width;\n            imgStyle.height = height;\n        }\n        if (textBorderWidth && textBorderColor) {\n            var rectStyle = rectEl.style;\n            rectStyle.lineWidth = textBorderWidth;\n            rectStyle.stroke = textBorderColor;\n            rectStyle.strokeOpacity = retrieve2(style.strokeOpacity, 1);\n            rectStyle.lineDash = style.borderDash;\n            rectStyle.lineDashOffset = style.borderDashOffset || 0;\n            rectEl.strokeContainThreshold = 0;\n            if (rectEl.hasFill() && rectEl.hasStroke()) {\n                rectStyle.strokeFirst = true;\n                rectStyle.lineWidth *= 2;\n            }\n        }\n        var commonStyle = (rectEl || imgEl).style;\n        commonStyle.shadowBlur = style.shadowBlur || 0;\n        commonStyle.shadowColor = style.shadowColor || 'transparent';\n        commonStyle.shadowOffsetX = style.shadowOffsetX || 0;\n        commonStyle.shadowOffsetY = style.shadowOffsetY || 0;\n        commonStyle.opacity = retrieve3(style.opacity, topStyle.opacity, 1);\n    };\n    ZRText.makeFont = function (style) {\n        var font = '';\n        if (hasSeparateFont(style)) {\n            font = [\n                style.fontStyle,\n                style.fontWeight,\n                parseFontSize(style.fontSize),\n                style.fontFamily || 'sans-serif'\n            ].join(' ');\n        }\n        return font && trim(font) || style.textFont || style.font;\n    };\n    return ZRText;\n}(Displayable));\nvar VALID_TEXT_ALIGN = { left: true, right: 1, center: 1 };\nvar VALID_TEXT_VERTICAL_ALIGN = { top: 1, bottom: 1, middle: 1 };\nvar FONT_PARTS = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily'];\nexport function parseFontSize(fontSize) {\n    if (typeof fontSize === 'string'\n        && (fontSize.indexOf('px') !== -1\n            || fontSize.indexOf('rem') !== -1\n            || fontSize.indexOf('em') !== -1)) {\n        return fontSize;\n    }\n    else if (!isNaN(+fontSize)) {\n        return fontSize + 'px';\n    }\n    else {\n        return DEFAULT_FONT_SIZE + 'px';\n    }\n}\nfunction setSeparateFont(targetStyle, sourceStyle) {\n    for (var i = 0; i < FONT_PARTS.length; i++) {\n        var fontProp = FONT_PARTS[i];\n        var val = sourceStyle[fontProp];\n        if (val != null) {\n            targetStyle[fontProp] = val;\n        }\n    }\n}\nexport function hasSeparateFont(style) {\n    return style.fontSize != null || style.fontFamily || style.fontWeight;\n}\nexport function normalizeTextStyle(style) {\n    normalizeStyle(style);\n    each(style.rich, normalizeStyle);\n    return style;\n}\nfunction normalizeStyle(style) {\n    if (style) {\n        style.font = ZRText.makeFont(style);\n        var textAlign = style.align;\n        textAlign === 'middle' && (textAlign = 'center');\n        style.align = (textAlign == null || VALID_TEXT_ALIGN[textAlign]) ? textAlign : 'left';\n        var verticalAlign = style.verticalAlign;\n        verticalAlign === 'center' && (verticalAlign = 'middle');\n        style.verticalAlign = (verticalAlign == null || VALID_TEXT_VERTICAL_ALIGN[verticalAlign]) ? verticalAlign : 'top';\n        var textPadding = style.padding;\n        if (textPadding) {\n            style.padding = normalizeCssArray(style.padding);\n        }\n    }\n}\nfunction getStroke(stroke, lineWidth) {\n    return (stroke == null || lineWidth <= 0 || stroke === 'transparent' || stroke === 'none')\n        ? null\n        : (stroke.image || stroke.colorStops)\n            ? '#000'\n            : stroke;\n}\nfunction getFill(fill) {\n    return (fill == null || fill === 'none')\n        ? null\n        : (fill.image || fill.colorStops)\n            ? '#000'\n            : fill;\n}\nfunction getTextXForPadding(x, textAlign, textPadding) {\n    return textAlign === 'right'\n        ? (x - textPadding[1])\n        : textAlign === 'center'\n            ? (x + textPadding[3] / 2 - textPadding[1] / 2)\n            : (x + textPadding[3]);\n}\nfunction getStyleText(style) {\n    var text = style.text;\n    text != null && (text += '');\n    return text;\n}\nfunction needDrawBackground(style) {\n    return !!(style.backgroundColor\n        || style.lineHeight\n        || (style.borderWidth && style.borderColor));\n}\nexport default ZRText;\n", "import { __extends } from \"tslib\";\nimport * as zrUtil from '../core/util.js';\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nvar Group = (function (_super) {\n    __extends(Group, _super);\n    function Group(opts) {\n        var _this = _super.call(this) || this;\n        _this.isGroup = true;\n        _this._children = [];\n        _this.attr(opts);\n        return _this;\n    }\n    Group.prototype.childrenRef = function () {\n        return this._children;\n    };\n    Group.prototype.children = function () {\n        return this._children.slice();\n    };\n    Group.prototype.childAt = function (idx) {\n        return this._children[idx];\n    };\n    Group.prototype.childOfName = function (name) {\n        var children = this._children;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i].name === name) {\n                return children[i];\n            }\n        }\n    };\n    Group.prototype.childCount = function () {\n        return this._children.length;\n    };\n    Group.prototype.add = function (child) {\n        if (child) {\n            if (child !== this && child.parent !== this) {\n                this._children.push(child);\n                this._doAdd(child);\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (child.__hostTarget) {\n                    throw 'This elemenet has been used as an attachment';\n                }\n            }\n        }\n        return this;\n    };\n    Group.prototype.addBefore = function (child, nextSibling) {\n        if (child && child !== this && child.parent !== this\n            && nextSibling && nextSibling.parent === this) {\n            var children = this._children;\n            var idx = children.indexOf(nextSibling);\n            if (idx >= 0) {\n                children.splice(idx, 0, child);\n                this._doAdd(child);\n            }\n        }\n        return this;\n    };\n    Group.prototype.replace = function (oldChild, newChild) {\n        var idx = zrUtil.indexOf(this._children, oldChild);\n        if (idx >= 0) {\n            this.replaceAt(newChild, idx);\n        }\n        return this;\n    };\n    Group.prototype.replaceAt = function (child, index) {\n        var children = this._children;\n        var old = children[index];\n        if (child && child !== this && child.parent !== this && child !== old) {\n            children[index] = child;\n            old.parent = null;\n            var zr = this.__zr;\n            if (zr) {\n                old.removeSelfFromZr(zr);\n            }\n            this._doAdd(child);\n        }\n        return this;\n    };\n    Group.prototype._doAdd = function (child) {\n        if (child.parent) {\n            child.parent.remove(child);\n        }\n        child.parent = this;\n        var zr = this.__zr;\n        if (zr && zr !== child.__zr) {\n            child.addSelfToZr(zr);\n        }\n        zr && zr.refresh();\n    };\n    Group.prototype.remove = function (child) {\n        var zr = this.__zr;\n        var children = this._children;\n        var idx = zrUtil.indexOf(children, child);\n        if (idx < 0) {\n            return this;\n        }\n        children.splice(idx, 1);\n        child.parent = null;\n        if (zr) {\n            child.removeSelfFromZr(zr);\n        }\n        zr && zr.refresh();\n        return this;\n    };\n    Group.prototype.removeAll = function () {\n        var children = this._children;\n        var zr = this.__zr;\n        for (var i = 0; i < children.length; i++) {\n            var child = children[i];\n            if (zr) {\n                child.removeSelfFromZr(zr);\n            }\n            child.parent = null;\n        }\n        children.length = 0;\n        return this;\n    };\n    Group.prototype.eachChild = function (cb, context) {\n        var children = this._children;\n        for (var i = 0; i < children.length; i++) {\n            var child = children[i];\n            cb.call(context, child, i);\n        }\n        return this;\n    };\n    Group.prototype.traverse = function (cb, context) {\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            var stopped = cb.call(context, child);\n            if (child.isGroup && !stopped) {\n                child.traverse(cb, context);\n            }\n        }\n        return this;\n    };\n    Group.prototype.addSelfToZr = function (zr) {\n        _super.prototype.addSelfToZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            child.addSelfToZr(zr);\n        }\n    };\n    Group.prototype.removeSelfFromZr = function (zr) {\n        _super.prototype.removeSelfFromZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            child.removeSelfFromZr(zr);\n        }\n    };\n    Group.prototype.getBoundingRect = function (includeChildren) {\n        var tmpRect = new BoundingRect(0, 0, 0, 0);\n        var children = includeChildren || this._children;\n        var tmpMat = [];\n        var rect = null;\n        for (var i = 0; i < children.length; i++) {\n            var child = children[i];\n            if (child.ignore || child.invisible) {\n                continue;\n            }\n            var childRect = child.getBoundingRect();\n            var transform = child.getLocalTransform(tmpMat);\n            if (transform) {\n                BoundingRect.applyTransform(tmpRect, childRect, transform);\n                rect = rect || tmpRect.clone();\n                rect.union(tmpRect);\n            }\n            else {\n                rect = rect || childRect.clone();\n                rect.union(childRect);\n            }\n        }\n        return rect || tmpRect;\n    };\n    return Group;\n}(Element));\nGroup.prototype.type = 'group';\nexport default Group;\n", "import { __extends } from \"tslib\";\nimport Path from './Path.js';\nvar CompoundPath = (function (_super) {\n    __extends(CompoundPath, _super);\n    function CompoundPath() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = 'compound';\n        return _this;\n    }\n    CompoundPath.prototype._updatePathDirty = function () {\n        var paths = this.shape.paths;\n        var dirtyPath = this.shapeChanged();\n        for (var i = 0; i < paths.length; i++) {\n            dirtyPath = dirtyPath || paths[i].shapeChanged();\n        }\n        if (dirtyPath) {\n            this.dirtyShape();\n        }\n    };\n    CompoundPath.prototype.beforeBrush = function () {\n        this._updatePathDirty();\n        var paths = this.shape.paths || [];\n        var scale = this.getGlobalScale();\n        for (var i = 0; i < paths.length; i++) {\n            if (!paths[i].path) {\n                paths[i].createPathProxy();\n            }\n            paths[i].path.setScale(scale[0], scale[1], paths[i].segmentIgnoreThreshold);\n        }\n    };\n    CompoundPath.prototype.buildPath = function (ctx, shape) {\n        var paths = shape.paths || [];\n        for (var i = 0; i < paths.length; i++) {\n            paths[i].buildPath(ctx, paths[i].shape, true);\n        }\n    };\n    CompoundPath.prototype.afterBrush = function () {\n        var paths = this.shape.paths || [];\n        for (var i = 0; i < paths.length; i++) {\n            paths[i].pathUpdated();\n        }\n    };\n    CompoundPath.prototype.getBoundingRect = function () {\n        this._updatePathDirty.call(this);\n        return Path.prototype.getBoundingRect.call(this);\n    };\n    return CompoundPath;\n}(Path));\nexport default CompoundPath;\n", "var LN2 = Math.log(2);\nfunction determinant(rows, rank, rowStart, rowMask, colMask, detCache) {\n    var cacheKey = rowMask + '-' + colMask;\n    var fullRank = rows.length;\n    if (detCache.hasOwnProperty(cacheKey)) {\n        return detCache[cacheKey];\n    }\n    if (rank === 1) {\n        var colStart = Math.round(Math.log(((1 << fullRank) - 1) & ~colMask) / LN2);\n        return rows[rowStart][colStart];\n    }\n    var subRowMask = rowMask | (1 << rowStart);\n    var subRowStart = rowStart + 1;\n    while (rowMask & (1 << subRowStart)) {\n        subRowStart++;\n    }\n    var sum = 0;\n    for (var j = 0, colLocalIdx = 0; j < fullRank; j++) {\n        var colTag = 1 << j;\n        if (!(colTag & colMask)) {\n            sum += (colLocalIdx % 2 ? -1 : 1) * rows[rowStart][j]\n                * determinant(rows, rank - 1, subRowStart, subRowMask, colMask | colTag, detCache);\n            colLocalIdx++;\n        }\n    }\n    detCache[cacheKey] = sum;\n    return sum;\n}\nexport function buildTransformer(src, dest) {\n    var mA = [\n        [src[0], src[1], 1, 0, 0, 0, -dest[0] * src[0], -dest[0] * src[1]],\n        [0, 0, 0, src[0], src[1], 1, -dest[1] * src[0], -dest[1] * src[1]],\n        [src[2], src[3], 1, 0, 0, 0, -dest[2] * src[2], -dest[2] * src[3]],\n        [0, 0, 0, src[2], src[3], 1, -dest[3] * src[2], -dest[3] * src[3]],\n        [src[4], src[5], 1, 0, 0, 0, -dest[4] * src[4], -dest[4] * src[5]],\n        [0, 0, 0, src[4], src[5], 1, -dest[5] * src[4], -dest[5] * src[5]],\n        [src[6], src[7], 1, 0, 0, 0, -dest[6] * src[6], -dest[6] * src[7]],\n        [0, 0, 0, src[6], src[7], 1, -dest[7] * src[6], -dest[7] * src[7]]\n    ];\n    var detCache = {};\n    var det = determinant(mA, 8, 0, 0, 0, detCache);\n    if (det === 0) {\n        return;\n    }\n    var vh = [];\n    for (var i = 0; i < 8; i++) {\n        for (var j = 0; j < 8; j++) {\n            vh[j] == null && (vh[j] = 0);\n            vh[j] += ((i + j) % 2 ? -1 : 1)\n                * determinant(mA, 7, i === 0 ? 1 : 0, 1 << i, 1 << j, detCache)\n                / det * dest[i];\n        }\n    }\n    return function (out, srcPointX, srcPointY) {\n        var pk = srcPointX * vh[6] + srcPointY * vh[7] + 1;\n        out[0] = (srcPointX * vh[0] + srcPointY * vh[1] + vh[2]) / pk;\n        out[1] = (srcPointX * vh[3] + srcPointY * vh[4] + vh[5]) / pk;\n    };\n}\n", "import env from './env.js';\nimport { buildTransformer } from './fourPointsTransform.js';\nvar EVENT_SAVED_PROP = '___zrEVENTSAVED';\nvar _calcOut = [];\nexport function transformLocalCoord(out, elFrom, elTarget, inX, inY) {\n    return transformCoordWithViewport(_calcOut, elFrom, inX, inY, true)\n        && transformCoordWithViewport(out, elTarget, _calcOut[0], _calcOut[1]);\n}\nexport function transformCoordWithViewport(out, el, inX, inY, inverse) {\n    if (el.getBoundingClientRect && env.domSupported && !isCanvasEl(el)) {\n        var saved = el[EVENT_SAVED_PROP] || (el[EVENT_SAVED_PROP] = {});\n        var markers = prepareCoordMarkers(el, saved);\n        var transformer = preparePointerTransformer(markers, saved, inverse);\n        if (transformer) {\n            transformer(out, inX, inY);\n            return true;\n        }\n    }\n    return false;\n}\nfunction prepareCoordMarkers(el, saved) {\n    var markers = saved.markers;\n    if (markers) {\n        return markers;\n    }\n    markers = saved.markers = [];\n    var propLR = ['left', 'right'];\n    var propTB = ['top', 'bottom'];\n    for (var i = 0; i < 4; i++) {\n        var marker = document.createElement('div');\n        var stl = marker.style;\n        var idxLR = i % 2;\n        var idxTB = (i >> 1) % 2;\n        stl.cssText = [\n            'position: absolute',\n            'visibility: hidden',\n            'padding: 0',\n            'margin: 0',\n            'border-width: 0',\n            'user-select: none',\n            'width:0',\n            'height:0',\n            propLR[idxLR] + ':0',\n            propTB[idxTB] + ':0',\n            propLR[1 - idxLR] + ':auto',\n            propTB[1 - idxTB] + ':auto',\n            ''\n        ].join('!important;');\n        el.appendChild(marker);\n        markers.push(marker);\n    }\n    return markers;\n}\nfunction preparePointerTransformer(markers, saved, inverse) {\n    var transformerName = inverse ? 'invTrans' : 'trans';\n    var transformer = saved[transformerName];\n    var oldSrcCoords = saved.srcCoords;\n    var srcCoords = [];\n    var destCoords = [];\n    var oldCoordTheSame = true;\n    for (var i = 0; i < 4; i++) {\n        var rect = markers[i].getBoundingClientRect();\n        var ii = 2 * i;\n        var x = rect.left;\n        var y = rect.top;\n        srcCoords.push(x, y);\n        oldCoordTheSame = oldCoordTheSame && oldSrcCoords && x === oldSrcCoords[ii] && y === oldSrcCoords[ii + 1];\n        destCoords.push(markers[i].offsetLeft, markers[i].offsetTop);\n    }\n    return (oldCoordTheSame && transformer)\n        ? transformer\n        : (saved.srcCoords = srcCoords,\n            saved[transformerName] = inverse\n                ? buildTransformer(destCoords, srcCoords)\n                : buildTransformer(srcCoords, destCoords));\n}\nexport function isCanvasEl(el) {\n    return el.nodeName.toUpperCase() === 'CANVAS';\n}\nvar replaceReg = /([&<>\"'])/g;\nvar replaceMap = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    '\\'': '&#39;'\n};\nexport function encodeHTML(source) {\n    return source == null\n        ? ''\n        : (source + '').replace(replaceReg, function (str, c) {\n            return replaceMap[c];\n        });\n}\n"], "mappings": ";;;;;AAAO,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,eAAe,oBAAoB,QAAQ;AACtD,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,qBAAqB;AACzB,SAAS,gBAAgB,QAAQ;AAC7B,MAAIA,OAAM,CAAC;AACX,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAOA;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,OAAO,aAAa,IAAI,EAAE;AACrC,QAAI,QAAQ,OAAO,WAAW,CAAC,IAAI,UAAU;AAC7C,IAAAA,KAAI,IAAI,IAAI;AAAA,EAChB;AACA,SAAOA;AACX;AACO,IAAI,yBAAyB,gBAAgB,kBAAkB;AAC/D,IAAI,cAAc;AAAA,EACrB,cAAc,WAAY;AACtB,WAAO,OAAO,aAAa,eACpB,SAAS,cAAc,QAAQ;AAAA,EAC1C;AAAA,EACA,aAAc,2BAAY;AACtB,QAAI;AACJ,QAAI;AACJ,WAAO,SAAU,MAAM,MAAM;AACzB,UAAI,CAAC,MAAM;AACP,YAAI,SAAS,YAAY,aAAa;AACtC,eAAO,UAAU,OAAO,WAAW,IAAI;AAAA,MAC3C;AACA,UAAI,MAAM;AACN,YAAI,gBAAgB,MAAM;AACtB,wBAAc,KAAK,OAAO,QAAQ;AAAA,QACtC;AACA,eAAO,KAAK,YAAY,IAAI;AAAA,MAChC,OACK;AACD,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf,YAAI,MAAM,qBAAqB,KAAK,IAAI;AACxC,YAAI,WAAW,OAAO,CAAC,IAAI,CAAC,KAAK;AACjC,YAAI,QAAQ;AACZ,YAAI,KAAK,QAAQ,MAAM,KAAK,GAAG;AAC3B,kBAAQ,WAAW,KAAK;AAAA,QAC5B,OACK;AACD,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,gBAAI,eAAe,uBAAuB,KAAK,CAAC,CAAC;AACjD,qBAAS,gBAAgB,OAAO,WAAY,eAAe;AAAA,UAC/D;AAAA,QACJ;AACA,eAAO,EAAE,MAAa;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ,EAAG;AAAA,EACH,WAAW,SAAU,KAAK,QAAQ,SAAS;AACvC,QAAI,QAAQ,IAAI,MAAM;AACtB,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,UAAM,MAAM;AACZ,WAAO;AAAA,EACX;AACJ;AACO,SAAS,eAAe,iBAAiB;AAC5C,WAAS,OAAO,aAAa;AACzB,QAAI,gBAAgB,GAAG,GAAG;AACtB,kBAAY,GAAG,IAAI,gBAAgB,GAAG;AAAA,IAC1C;AAAA,EACJ;AACJ;;;ACvEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,IAAI,iBAAiB,OAAO;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,GAAG,SAAU,KAAK,KAAK;AACnB,MAAI,aAAa,MAAM,GAAG,IAAI;AAC9B,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAI,cAAc,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,GAAG,SAAU,KAAK,KAAK;AACnB,MAAI,aAAa,MAAM,QAAQ,IAAI;AACnC,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAI,cAAc,OAAO,UAAU;AACnC,IAAI,aAAa,MAAM;AACvB,IAAI,gBAAgB,WAAW;AAC/B,IAAI,eAAe,WAAW;AAC9B,IAAI,cAAc,WAAW;AAC7B,IAAI,YAAY,WAAW;AAC3B,IAAI,gBAAe,WAAY;AAAE,GAAE;AACnC,IAAI,gBAAgB,eAAe,aAAa,YAAY;AAC5D,IAAI,WAAW;AACf,IAAI,UAAU;AACP,SAAS,OAAO;AACnB,SAAO;AACX;AACO,SAAS,WAAW;AACvB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,MAAI,OAAO,YAAY,aAAa;AAChC,YAAQ,MAAM,MAAM,SAAS,IAAI;AAAA,EACrC;AACJ;AACO,SAAS,MAAM,QAAQ;AAC1B,MAAI,UAAU,QAAQ,OAAO,WAAW,UAAU;AAC9C,WAAO;AAAA,EACX;AACA,MAAI,SAAS;AACb,MAAI,UAAU,YAAY,KAAK,MAAM;AACrC,MAAI,YAAY,kBAAkB;AAC9B,QAAI,CAAC,YAAY,MAAM,GAAG;AACtB,eAAS,CAAC;AACV,eAAS,IAAI,GAAGC,OAAM,OAAO,QAAQ,IAAIA,MAAK,KAAK;AAC/C,eAAO,CAAC,IAAI,MAAM,OAAO,CAAC,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ,WACS,YAAY,OAAO,GAAG;AAC3B,QAAI,CAAC,YAAY,MAAM,GAAG;AACtB,UAAI,OAAO,OAAO;AAClB,UAAI,KAAK,MAAM;AACX,iBAAS,KAAK,KAAK,MAAM;AAAA,MAC7B,OACK;AACD,iBAAS,IAAI,KAAK,OAAO,MAAM;AAC/B,iBAAS,IAAI,GAAGA,OAAM,OAAO,QAAQ,IAAIA,MAAK,KAAK;AAC/C,iBAAO,CAAC,IAAI,OAAO,CAAC;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,WACS,CAAC,eAAe,OAAO,KAAK,CAAC,YAAY,MAAM,KAAK,CAAC,MAAM,MAAM,GAAG;AACzE,aAAS,CAAC;AACV,aAAS,OAAO,QAAQ;AACpB,UAAI,OAAO,eAAe,GAAG,KAAK,QAAQ,UAAU;AAChD,eAAO,GAAG,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,MAAM,QAAQ,QAAQ,WAAW;AAC7C,MAAI,CAAC,SAAS,MAAM,KAAK,CAAC,SAAS,MAAM,GAAG;AACxC,WAAO,YAAY,MAAM,MAAM,IAAI;AAAA,EACvC;AACA,WAAS,OAAO,QAAQ;AACpB,QAAI,OAAO,eAAe,GAAG,KAAK,QAAQ,UAAU;AAChD,UAAI,aAAa,OAAO,GAAG;AAC3B,UAAI,aAAa,OAAO,GAAG;AAC3B,UAAI,SAAS,UAAU,KAChB,SAAS,UAAU,KACnB,CAAC,QAAQ,UAAU,KACnB,CAAC,QAAQ,UAAU,KACnB,CAAC,MAAM,UAAU,KACjB,CAAC,MAAM,UAAU,KACjB,CAAC,gBAAgB,UAAU,KAC3B,CAAC,gBAAgB,UAAU,KAC3B,CAAC,YAAY,UAAU,KACvB,CAAC,YAAY,UAAU,GAAG;AAC7B,cAAM,YAAY,YAAY,SAAS;AAAA,MAC3C,WACS,aAAa,EAAE,OAAO,SAAS;AACpC,eAAO,GAAG,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,SAAS,kBAAkB,WAAW;AAClD,MAAI,SAAS,iBAAiB,CAAC;AAC/B,WAAS,IAAI,GAAGA,OAAM,iBAAiB,QAAQ,IAAIA,MAAK,KAAK;AACzD,aAAS,MAAM,QAAQ,iBAAiB,CAAC,GAAG,SAAS;AAAA,EACzD;AACA,SAAO;AACX;AACO,SAAS,OAAO,QAAQ,QAAQ;AACnC,MAAI,OAAO,QAAQ;AACf,WAAO,OAAO,QAAQ,MAAM;AAAA,EAChC,OACK;AACD,aAAS,OAAO,QAAQ;AACpB,UAAI,OAAO,eAAe,GAAG,KAAK,QAAQ,UAAU;AAChD,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAC5B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,SAAS,QAAQ,QAAQ,SAAS;AAC9C,MAAI,UAAU,KAAK,MAAM;AACzB,WAAS,IAAI,GAAGA,OAAM,QAAQ,QAAQ,IAAIA,MAAK,KAAK;AAChD,QAAI,MAAM,QAAQ,CAAC;AACnB,QAAK,UAAU,OAAO,GAAG,KAAK,OAAO,OAAO,GAAG,KAAK,MAAO;AACvD,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC5B;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAI,eAAe,YAAY;AAC/B,SAAS,QAAQ,OAAO,OAAO;AAClC,MAAI,OAAO;AACP,QAAI,MAAM,SAAS;AACf,aAAO,MAAM,QAAQ,KAAK;AAAA,IAC9B;AACA,aAAS,IAAI,GAAGA,OAAM,MAAM,QAAQ,IAAIA,MAAK,KAAK;AAC9C,UAAI,MAAM,CAAC,MAAM,OAAO;AACpB,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,SAAS,OAAO,WAAW;AACvC,MAAI,iBAAiB,MAAM;AAC3B,WAAS,IAAI;AAAA,EAAE;AACf,IAAE,YAAY,UAAU;AACxB,QAAM,YAAY,IAAI,EAAE;AACxB,WAAS,QAAQ,gBAAgB;AAC7B,QAAI,eAAe,eAAe,IAAI,GAAG;AACrC,YAAM,UAAU,IAAI,IAAI,eAAe,IAAI;AAAA,IAC/C;AAAA,EACJ;AACA,QAAM,UAAU,cAAc;AAC9B,QAAM,aAAa;AACvB;AACO,SAAS,MAAM,QAAQ,QAAQ,UAAU;AAC5C,WAAS,eAAe,SAAS,OAAO,YAAY;AACpD,WAAS,eAAe,SAAS,OAAO,YAAY;AACpD,MAAI,OAAO,qBAAqB;AAC5B,QAAI,UAAU,OAAO,oBAAoB,MAAM;AAC/C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,QAAQ,eAAe;AACvB,YAAK,WAAW,OAAO,GAAG,KAAK,OAAO,OAAO,GAAG,KAAK,MAAO;AACxD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,OACK;AACD,aAAS,QAAQ,QAAQ,QAAQ;AAAA,EACrC;AACJ;AACO,SAAS,YAAY,MAAM;AAC9B,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO;AAAA,EACX;AACA,SAAO,OAAO,KAAK,WAAW;AAClC;AACO,SAAS,KAAK,KAAK,IAAI,SAAS;AACnC,MAAI,EAAE,OAAO,KAAK;AACd;AAAA,EACJ;AACA,MAAI,IAAI,WAAW,IAAI,YAAY,eAAe;AAC9C,QAAI,QAAQ,IAAI,OAAO;AAAA,EAC3B,WACS,IAAI,WAAW,CAAC,IAAI,QAAQ;AACjC,aAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC5C,SAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,IACnC;AAAA,EACJ,OACK;AACD,aAAS,OAAO,KAAK;AACjB,UAAI,IAAI,eAAe,GAAG,GAAG;AACzB,WAAG,KAAK,SAAS,IAAI,GAAG,GAAG,KAAK,GAAG;AAAA,MACvC;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,SAAS,IAAI,KAAK,IAAI,SAAS;AAClC,MAAI,CAAC,KAAK;AACN,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,CAAC,IAAI;AACL,WAAO,MAAM,GAAG;AAAA,EACpB;AACA,MAAI,IAAI,OAAO,IAAI,QAAQ,WAAW;AAClC,WAAO,IAAI,IAAI,IAAI,OAAO;AAAA,EAC9B,OACK;AACD,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC5C,aAAO,KAAK,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAChD;AACA,WAAO;AAAA,EACX;AACJ;AACO,SAAS,OAAO,KAAK,IAAI,MAAM,SAAS;AAC3C,MAAI,EAAE,OAAO,KAAK;AACd;AAAA,EACJ;AACA,WAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC5C,WAAO,GAAG,KAAK,SAAS,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,EAChD;AACA,SAAO;AACX;AACO,SAAS,OAAO,KAAK,IAAI,SAAS;AACrC,MAAI,CAAC,KAAK;AACN,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,CAAC,IAAI;AACL,WAAO,MAAM,GAAG;AAAA,EACpB;AACA,MAAI,IAAI,UAAU,IAAI,WAAW,cAAc;AAC3C,WAAO,IAAI,OAAO,IAAI,OAAO;AAAA,EACjC,OACK;AACD,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC5C,UAAI,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AAClC,eAAO,KAAK,IAAI,CAAC,CAAC;AAAA,MACtB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACO,SAAS,KAAK,KAAK,IAAI,SAAS;AACnC,MAAI,EAAE,OAAO,KAAK;AACd;AAAA,EACJ;AACA,WAAS,IAAI,GAAGA,OAAM,IAAI,QAAQ,IAAIA,MAAK,KAAK;AAC5C,QAAI,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AAClC,aAAO,IAAI,CAAC;AAAA,IAChB;AAAA,EACJ;AACJ;AACO,SAAS,KAAK,KAAK;AACtB,MAAI,CAAC,KAAK;AACN,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,OAAO,MAAM;AACb,WAAO,OAAO,KAAK,GAAG;AAAA,EAC1B;AACA,MAAI,UAAU,CAAC;AACf,WAAS,OAAO,KAAK;AACjB,QAAI,IAAI,eAAe,GAAG,GAAG;AACzB,cAAQ,KAAK,GAAG;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,aAAa,MAAM,SAAS;AACjC,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC/B;AACA,SAAO,WAAY;AACf,WAAO,KAAK,MAAM,SAAS,KAAK,OAAO,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,EACvE;AACJ;AACO,IAAI,OAAQ,iBAAiB,WAAW,cAAc,IAAI,IAC3D,cAAc,KAAK,KAAK,cAAc,IAAI,IAC1C;AACN,SAAS,MAAM,MAAM;AACjB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC/B;AACA,SAAO,WAAY;AACf,WAAO,KAAK,MAAM,MAAM,KAAK,OAAO,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,EACpE;AACJ;AAEO,SAAS,QAAQ,OAAO;AAC3B,MAAI,MAAM,SAAS;AACf,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC9B;AACA,SAAO,YAAY,KAAK,KAAK,MAAM;AACvC;AACO,SAAS,WAAW,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC5B;AACO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AACO,SAAS,aAAa,OAAO;AAChC,SAAO,YAAY,KAAK,KAAK,MAAM;AACvC;AACO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AACO,SAAS,SAAS,OAAO;AAC5B,MAAI,OAAO,OAAO;AAClB,SAAO,SAAS,cAAe,CAAC,CAAC,SAAS,SAAS;AACvD;AACO,SAAS,gBAAgB,OAAO;AACnC,SAAO,CAAC,CAAC,eAAe,YAAY,KAAK,KAAK,CAAC;AACnD;AACO,SAAS,aAAa,OAAO;AAChC,SAAO,CAAC,CAAC,YAAY,YAAY,KAAK,KAAK,CAAC;AAChD;AACO,SAAS,MAAM,OAAO;AACzB,SAAO,OAAO,UAAU,YACjB,OAAO,MAAM,aAAa,YAC1B,OAAO,MAAM,kBAAkB;AAC1C;AACO,SAAS,iBAAiB,OAAO;AACpC,SAAO,MAAM,cAAc;AAC/B;AACO,SAAS,qBAAqB,OAAO;AACxC,SAAO,MAAM,SAAS;AAC1B;AACO,SAAS,SAAS,OAAO;AAC5B,SAAO,YAAY,KAAK,KAAK,MAAM;AACvC;AACO,SAAS,MAAM,OAAO;AACzB,SAAO,UAAU;AACrB;AACO,SAAS,WAAW;AACvB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,WAAS,IAAI,GAAGC,OAAM,KAAK,QAAQ,IAAIA,MAAK,KAAK;AAC7C,QAAI,KAAK,CAAC,KAAK,MAAM;AACjB,aAAO,KAAK,CAAC;AAAA,IACjB;AAAA,EACJ;AACJ;AACO,SAAS,UAAU,QAAQ,QAAQ;AACtC,SAAO,UAAU,OACX,SACA;AACV;AACO,SAAS,UAAU,QAAQ,QAAQ,QAAQ;AAC9C,SAAO,UAAU,OACX,SACA,UAAU,OACN,SACA;AACd;AACO,SAAS,MAAM,KAAK;AACvB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC/B;AACA,SAAO,YAAY,MAAM,KAAK,IAAI;AACtC;AACO,SAAS,kBAAkB,KAAK;AACnC,MAAI,OAAQ,QAAS,UAAU;AAC3B,WAAO,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC9B;AACA,MAAIA,OAAM,IAAI;AACd,MAAIA,SAAQ,GAAG;AACX,WAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,EAC1C,WACSA,SAAQ,GAAG;AAChB,WAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,EAC1C;AACA,SAAO;AACX;AACO,SAAS,OAAO,WAAW,SAAS;AACvC,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,MAAM,OAAO;AAAA,EAC3B;AACJ;AACO,SAAS,KAAK,KAAK;AACtB,MAAI,OAAO,MAAM;AACb,WAAO;AAAA,EACX,WACS,OAAO,IAAI,SAAS,YAAY;AACrC,WAAO,IAAI,KAAK;AAAA,EACpB,OACK;AACD,WAAO,IAAI,QAAQ,sCAAsC,EAAE;AAAA,EAC/D;AACJ;AACA,IAAI,eAAe;AACZ,SAAS,eAAe,KAAK;AAChC,MAAI,YAAY,IAAI;AACxB;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,YAAY;AAC3B;AACA,IAAI,cAAe,WAAY;AAC3B,WAASC,eAAc;AACnB,SAAK,OAAO,CAAC;AAAA,EACjB;AACA,EAAAA,aAAY,UAAU,QAAQ,IAAI,SAAU,KAAK;AAC7C,QAAI,UAAU,KAAK,IAAI,GAAG;AAC1B,QAAI,SAAS;AACT,aAAO,KAAK,KAAK,GAAG;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,MAAM,SAAU,KAAK;AACvC,WAAO,KAAK,KAAK,eAAe,GAAG;AAAA,EACvC;AACA,EAAAA,aAAY,UAAU,MAAM,SAAU,KAAK;AACvC,WAAO,KAAK,KAAK,GAAG;AAAA,EACxB;AACA,EAAAA,aAAY,UAAU,MAAM,SAAU,KAAK,OAAO;AAC9C,SAAK,KAAK,GAAG,IAAI;AACjB,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,OAAO,WAAY;AACrC,WAAO,KAAK,KAAK,IAAI;AAAA,EACzB;AACA,EAAAA,aAAY,UAAU,UAAU,SAAU,UAAU;AAChD,QAAI,OAAO,KAAK;AAChB,aAAS,OAAO,MAAM;AAClB,UAAI,KAAK,eAAe,GAAG,GAAG;AAC1B,iBAAS,KAAK,GAAG,GAAG,GAAG;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AACA,SAAOA;AACX,EAAE;AACF,IAAI,uBAAuB,OAAO,QAAQ;AAC1C,SAAS,iBAAiB;AACtB,SAAQ,uBAAuB,oBAAI,IAAI,IAAI,IAAI,YAAY;AAC/D;AACA,IAAI,UAAW,WAAY;AACvB,WAASC,SAAQ,KAAK;AAClB,QAAI,QAAQ,QAAQ,GAAG;AACvB,SAAK,OAAO,eAAe;AAC3B,QAAI,UAAU;AACd,IAAC,eAAeA,WACV,IAAI,KAAK,KAAK,IACb,OAAO,KAAK,KAAK,KAAK;AAC7B,aAAS,MAAM,OAAO,KAAK;AACvB,cAAQ,QAAQ,IAAI,OAAO,GAAG,IAAI,QAAQ,IAAI,KAAK,KAAK;AAAA,IAC5D;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,SAAS,SAAU,KAAK;AACtC,WAAO,KAAK,KAAK,IAAI,GAAG;AAAA,EAC5B;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,KAAK;AACnC,WAAO,KAAK,KAAK,IAAI,GAAG;AAAA,EAC5B;AACA,EAAAA,SAAQ,UAAU,MAAM,SAAU,KAAK,OAAO;AAC1C,SAAK,KAAK,IAAI,KAAK,KAAK;AACxB,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,OAAO,SAAU,IAAI,SAAS;AAC5C,SAAK,KAAK,QAAQ,SAAU,OAAO,KAAK;AACpC,SAAG,KAAK,SAAS,OAAO,GAAG;AAAA,IAC/B,CAAC;AAAA,EACL;AACA,EAAAA,SAAQ,UAAU,OAAO,WAAY;AACjC,QAAIC,QAAO,KAAK,KAAK,KAAK;AAC1B,WAAO,uBACD,MAAM,KAAKA,KAAI,IACfA;AAAA,EACV;AACA,EAAAD,SAAQ,UAAU,YAAY,SAAU,KAAK;AACzC,SAAK,KAAK,QAAQ,EAAE,GAAG;AAAA,EAC3B;AACA,SAAOA;AACX,EAAE;AAEK,SAAS,cAAc,KAAK;AAC/B,SAAO,IAAI,QAAQ,GAAG;AAC1B;AACO,SAAS,YAAY,GAAG,GAAG;AAC9B,MAAI,WAAW,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM;AACpD,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,aAAS,CAAC,IAAI,EAAE,CAAC;AAAA,EACrB;AACA,MAAI,SAAS,EAAE;AACf,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,aAAS,IAAI,MAAM,IAAI,EAAE,CAAC;AAAA,EAC9B;AACA,SAAO;AACX;AACO,SAAS,aAAa,OAAO,YAAY;AAC5C,MAAI;AACJ,MAAI,OAAO,QAAQ;AACf,UAAM,OAAO,OAAO,KAAK;AAAA,EAC7B,OACK;AACD,QAAI,YAAY,WAAY;AAAA,IAAE;AAC9B,cAAU,YAAY;AACtB,UAAM,IAAI,UAAU;AAAA,EACxB;AACA,MAAI,YAAY;AACZ,WAAO,KAAK,UAAU;AAAA,EAC1B;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,KAAK;AACnC,MAAI,WAAW,IAAI;AACnB,WAAS,mBAAmB;AAC5B,WAAS,aAAa;AACtB,WAAS,0BAA0B;AACnC,WAAS,uBAAuB,IAAI;AACxC;AACO,SAAS,OAAO,KAAK,MAAM;AAC9B,SAAO,IAAI,eAAe,IAAI;AAClC;AACO,SAAS,OAAO;AAAE;AAClB,IAAI,mBAAmB,MAAM,KAAK;;;AC7hBzC,IAAI,UAAW,2BAAY;AACvB,WAASE,WAAU;AACf,SAAK,UAAU;AACf,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAClB;AACA,SAAOA;AACX,EAAE;AACF,IAAI,MAAO,2BAAY;AACnB,WAASC,OAAM;AACX,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,kBAAkB,OAAO,WAAW;AAAA,EAC7C;AACA,SAAOA;AACX,EAAE;AACF,IAAI,MAAM,IAAI,IAAI;AAClB,IAAI,OAAO,OAAO,YAAY,OAAO,GAAG,sBAAsB,YAAY;AACtE,MAAI,MAAM;AACV,MAAI,uBAAuB;AAC/B,WACS,OAAO,aAAa,eAAe,OAAO,SAAS,aAAa;AACrE,MAAI,SAAS;AACjB,WACS,CAAC,IAAI,mBAAmB,UAAU,QAAQ;AAC/C,MAAI,OAAO;AACX,MAAI,eAAe;AACvB,OACK;AACD,SAAO,UAAU,WAAW,GAAG;AACnC;AACA,SAAS,OAAO,IAAIC,MAAK;AACrB,MAAI,UAAUA,KAAI;AAClB,MAAI,UAAU,GAAG,MAAM,mBAAmB;AAC1C,MAAI,KAAK,GAAG,MAAM,gBAAgB,KAC3B,GAAG,MAAM,2BAA2B;AAC3C,MAAI,OAAO,GAAG,MAAM,iBAAiB;AACrC,MAAI,SAAU,kBAAmB,KAAK,EAAE;AACxC,MAAI,SAAS;AACT,YAAQ,UAAU;AAClB,YAAQ,UAAU,QAAQ,CAAC;AAAA,EAC/B;AACA,MAAI,IAAI;AACJ,YAAQ,KAAK;AACb,YAAQ,UAAU,GAAG,CAAC;AAAA,EAC1B;AACA,MAAI,MAAM;AACN,YAAQ,OAAO;AACf,YAAQ,UAAU,KAAK,CAAC;AACxB,YAAQ,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,EAC/C;AACA,MAAI,QAAQ;AACR,YAAQ,SAAS;AAAA,EACrB;AACA,EAAAA,KAAI,eAAe,OAAO,YAAY;AACtC,EAAAA,KAAI,uBAAuB,kBAAkB,UAAU,CAAC,QAAQ,MAAM,CAAC,QAAQ;AAC/E,EAAAA,KAAI,yBAAyB,mBAAmB,WACxC,QAAQ,QAAS,QAAQ,MAAM,CAAC,QAAQ,WAAW;AAC3D,EAAAA,KAAI,eAAe,OAAO,aAAa;AACvC,MAAI,QAAQ,SAAS,gBAAgB;AACrC,EAAAA,KAAI,wBAAyB,QAAQ,MAAM,gBAAgB,SACpD,QAAQ,QACN,qBAAqB,UAAY,SAAS,IAAI,gBAAgB,KAChE,oBAAoB,UACpB,EAAE,iBAAiB;AAC1B,EAAAA,KAAI,qBAAqBA,KAAI,wBACrB,QAAQ,MAAM,CAAC,QAAQ,WAAW;AAC9C;AACA,IAAO,cAAQ;;;AC9Ef;AAAA;AAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,SAAS,SAAS;AACrB,SAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC5B;AACO,SAAS,SAAS,KAAK;AAC1B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACX;AACO,SAAS,KAAK,KAAK,GAAG;AACzB,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACX;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC7B,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAC/C,MAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAC/C,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACX;AACO,SAAS,UAAU,KAAK,GAAG,GAAG;AACjC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACX;AACO,SAAS,OAAO,KAAK,GAAG,KAAK,OAAO;AACvC,MAAI,UAAU,QAAQ;AAAE,YAAQ,CAAC,GAAG,CAAC;AAAA,EAAG;AACxC,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;AACzB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;AACzB,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,CAAC,KAAK,MAAM,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC;AAChE,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,CAAC,KAAK,MAAM,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC;AAChE,SAAO;AACX;AACO,SAAS,MAAM,KAAK,GAAG,GAAG;AAC7B,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACX;AACO,SAAS,OAAO,KAAK,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,KAAK,KAAK,KAAK;AACzB,MAAI,CAAC,KAAK;AACN,WAAO;AAAA,EACX;AACA,QAAM,IAAM;AACZ,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,KAAK,KAAK,MAAM,KAAK,OAAO;AACjC,MAAI,CAAC,KAAK,KAAK,MAAM,KAAK,OAAO;AACjC,SAAO;AACX;AACO,SAASA,OAAM,GAAG;AACrB,MAAI,IAAI,OAAO;AACf,OAAK,GAAG,CAAC;AACT,SAAO;AACX;;;AClGA;AAAA;AAAA;AAAA;AAAA,eAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAAC;AAAA,EAAA;AAAA;AAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAO,SAASF,QAAO,GAAG,GAAG;AACzB,MAAI,KAAK,MAAM;AACX,QAAI;AAAA,EACR;AACA,MAAI,KAAK,MAAM;AACX,QAAI;AAAA,EACR;AACA,SAAO,CAAC,GAAG,CAAC;AAChB;AACO,SAASD,MAAK,KAAK,GAAG;AACzB,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACX;AACO,SAASD,OAAM,GAAG;AACrB,SAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACtB;AACO,SAAS,IAAI,KAAK,GAAG,GAAG;AAC3B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACX;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACX;AACO,SAAS,YAAY,KAAK,IAAI,IAAI,GAAG;AACxC,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI;AACzB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI;AACzB,SAAO;AACX;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACX;AACO,SAAS,IAAI,GAAG;AACnB,SAAO,KAAK,KAAK,UAAU,CAAC,CAAC;AACjC;AACO,IAAI,SAAS;AACb,SAAS,UAAU,GAAG;AACzB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnC;AACO,IAAI,eAAe;AACnB,SAASG,KAAI,KAAK,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACX;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACrB,SAAO;AACX;AACO,SAAS,IAAI,IAAI,IAAI;AACxB,SAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AACvC;AACO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACX;AACO,SAAS,UAAU,KAAK,GAAG;AAC9B,MAAI,IAAI,IAAI,CAAC;AACb,MAAI,MAAM,GAAG;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACb,OACK;AACD,QAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,QAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAAA,EACpB;AACA,SAAO;AACX;AACO,SAAS,SAAS,IAAI,IAAI;AAC7B,SAAO,KAAK,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MACzC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE;AAC3C;AACO,IAAI,OAAO;AACX,SAAS,eAAe,IAAI,IAAI;AACnC,UAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAC/B,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AACzC;AACO,IAAI,aAAa;AACjB,SAAS,OAAO,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,SAAO;AACX;AACO,SAAS,KAAK,KAAK,IAAI,IAAI,GAAG;AACjC,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC;AAClC,MAAI,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC;AAClC,SAAO;AACX;AACO,SAAS,eAAe,KAAK,GAAG,GAAG;AACtC,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,SAAO;AACX;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,SAAO;AACX;AACO,SAAS,IAAI,KAAK,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9B,SAAO;AACX;;;AC/GA;AAAA;AAAA;AAAA;AAAA,cAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAI,QAAS,2BAAY;AACrB,WAASC,OAAM,KAAK;AAChB,SAAK,QAAQ;AAAA,EACjB;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,aAAc,WAAY;AAC1B,WAASC,cAAa;AAClB,SAAK,OAAO;AAAA,EAChB;AACA,EAAAA,YAAW,UAAU,SAAS,SAAU,KAAK;AACzC,QAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,SAAK,YAAY,KAAK;AACtB,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,UAAU,cAAc,SAAU,OAAO;AAChD,QAAI,CAAC,KAAK,MAAM;AACZ,WAAK,OAAO,KAAK,OAAO;AAAA,IAC5B,OACK;AACD,WAAK,KAAK,OAAO;AACjB,YAAM,OAAO,KAAK;AAClB,YAAM,OAAO;AACb,WAAK,OAAO;AAAA,IAChB;AACA,SAAK;AAAA,EACT;AACA,EAAAA,YAAW,UAAU,SAAS,SAAU,OAAO;AAC3C,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,MAAM;AACjB,QAAI,MAAM;AACN,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,OAAO;AAAA,IAChB;AACA,QAAI,MAAM;AACN,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,OAAO;AAAA,IAChB;AACA,UAAM,OAAO,MAAM,OAAO;AAC1B,SAAK;AAAA,EACT;AACA,EAAAA,YAAW,UAAU,MAAM,WAAY;AACnC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,YAAW,UAAU,QAAQ,WAAY;AACrC,SAAK,OAAO,KAAK,OAAO;AACxB,SAAK,OAAO;AAAA,EAChB;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,MAAO,WAAY;AACnB,WAASC,KAAI,SAAS;AAClB,SAAK,QAAQ,IAAI,WAAW;AAC5B,SAAK,WAAW;AAChB,SAAK,OAAO,CAAC;AACb,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,KAAI,UAAU,MAAM,SAAU,KAAK,OAAO;AACtC,QAAI,OAAO,KAAK;AAChB,QAAIC,OAAM,KAAK;AACf,QAAI,UAAU;AACd,QAAIA,KAAI,GAAG,KAAK,MAAM;AAClB,UAAIC,OAAM,KAAK,IAAI;AACnB,UAAI,QAAQ,KAAK;AACjB,UAAIA,QAAO,KAAK,YAAYA,OAAM,GAAG;AACjC,YAAI,iBAAiB,KAAK;AAC1B,aAAK,OAAO,cAAc;AAC1B,eAAOD,KAAI,eAAe,GAAG;AAC7B,kBAAU,eAAe;AACzB,aAAK,oBAAoB;AAAA,MAC7B;AACA,UAAI,OAAO;AACP,cAAM,QAAQ;AAAA,MAClB,OACK;AACD,gBAAQ,IAAI,MAAM,KAAK;AAAA,MAC3B;AACA,YAAM,MAAM;AACZ,WAAK,YAAY,KAAK;AACtB,MAAAA,KAAI,GAAG,IAAI;AAAA,IACf;AACA,WAAO;AAAA,EACX;AACA,EAAAD,KAAI,UAAU,MAAM,SAAU,KAAK;AAC/B,QAAI,QAAQ,KAAK,KAAK,GAAG;AACzB,QAAI,OAAO,KAAK;AAChB,QAAI,SAAS,MAAM;AACf,UAAI,UAAU,KAAK,MAAM;AACrB,aAAK,OAAO,KAAK;AACjB,aAAK,YAAY,KAAK;AAAA,MAC1B;AACA,aAAO,MAAM;AAAA,IACjB;AAAA,EACJ;AACA,EAAAA,KAAI,UAAU,QAAQ,WAAY;AAC9B,SAAK,MAAM,MAAM;AACjB,SAAK,OAAO,CAAC;AAAA,EACjB;AACA,EAAAA,KAAI,UAAU,MAAM,WAAY;AAC5B,WAAO,KAAK,MAAM,IAAI;AAAA,EAC1B;AACA,SAAOA;AACX,EAAE;AACF,IAAO,cAAQ;;;AD3Gf,IAAI,iBAAiB;AAAA,EACjB,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3D,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAC3D,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5D,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACxD,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,EAAG,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1D,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EACtD,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzD,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAC7D,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAAG,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EACzD,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnE,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EACpD,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EAAG,YAAY,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EACvD,iBAAiB,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAAG,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACjE,aAAa,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,EAAG,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1D,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC/D,kBAAkB,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EACjE,cAAc,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAAG,WAAW,CAAC,KAAK,GAAG,GAAG,CAAC;AAAA,EACzD,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnE,iBAAiB,CAAC,IAAI,IAAI,KAAK,CAAC;AAAA,EAAG,iBAAiB,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,EAClE,iBAAiB,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,EAAG,iBAAiB,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAClE,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAAG,YAAY,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAC5D,eAAe,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAAG,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC7D,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC7D,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/D,eAAe,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EAAG,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC3D,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChE,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EACvD,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,EAClD,eAAe,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAAG,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3D,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5D,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC;AAAA,EACvD,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACvD,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAClE,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAAG,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChE,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChE,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,wBAAwB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1E,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChE,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/D,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,iBAAiB,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EACpE,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACvE,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,kBAAkB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzE,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,EACxD,aAAa,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EAAG,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzD,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAAG,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAAA,EACpD,oBAAoB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EACnE,gBAAgB,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAAG,gBAAgB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACpE,kBAAkB,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAAG,mBAAmB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACzE,qBAAqB,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAAG,mBAAmB,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC1E,mBAAmB,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAAG,gBAAgB,CAAC,IAAI,IAAI,KAAK,CAAC;AAAA,EACrE,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/D,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,eAAe,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChE,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EAAG,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACpD,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EACxD,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,IAAI,GAAG,CAAC;AAAA,EACvD,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAChE,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACnE,iBAAiB,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACpE,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EACzD,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACrD,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC3D,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACrD,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAAG,eAAe,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAC9D,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,EAC5D,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,EAAG,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3D,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACvD,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,IAAI,KAAK,CAAC;AAAA,EAC5D,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,aAAa,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC/D,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,eAAe,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAC1D,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACxD,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,EAAG,WAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACtD,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EACzD,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACxD,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAAG,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EAC5D,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,EAAG,eAAe,CAAC,KAAK,KAAK,IAAI,CAAC;AAC/D;AACA,SAAS,aAAa,GAAG;AACrB,MAAI,KAAK,MAAM,CAAC;AAChB,SAAO,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM;AACvC;AACA,SAAS,cAAc,GAAG;AACtB,MAAI,KAAK,MAAM,CAAC;AAChB,SAAO,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM;AACvC;AACA,SAAS,cAAc,GAAG;AACtB,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACnC;AACA,SAAS,YAAY,KAAK;AACtB,MAAI,MAAM;AACV,MAAI,IAAI,UAAU,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK;AAClD,WAAO,aAAa,WAAW,GAAG,IAAI,MAAM,GAAG;AAAA,EACnD;AACA,SAAO,aAAa,SAAS,KAAK,EAAE,CAAC;AACzC;AACA,SAAS,cAAc,KAAK;AACxB,MAAI,MAAM;AACV,MAAI,IAAI,UAAU,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK;AAClD,WAAO,cAAc,WAAW,GAAG,IAAI,GAAG;AAAA,EAC9C;AACA,SAAO,cAAc,WAAW,GAAG,CAAC;AACxC;AACA,SAAS,YAAY,IAAI,IAAI,GAAG;AAC5B,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT,WACS,IAAI,GAAG;AACZ,SAAK;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,MAAM,KAAK,MAAM,IAAI;AAAA,EAChC;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO;AAAA,EACX;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK;AAAA,EAC1C;AACA,SAAO;AACX;AACA,SAAS,WAAW,GAAG,GAAG,GAAG;AACzB,SAAO,KAAK,IAAI,KAAK;AACzB;AACA,SAAS,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG;AAC9B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACX;AACA,SAAS,SAAS,KAAK,GAAG;AACtB,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACX;AACA,IAAI,aAAa,IAAI,YAAI,EAAE;AAC3B,IAAI,iBAAiB;AACrB,SAAS,WAAW,UAAU,SAAS;AACnC,MAAI,gBAAgB;AAChB,aAAS,gBAAgB,OAAO;AAAA,EACpC;AACA,mBAAiB,WAAW,IAAI,UAAU,kBAAmB,QAAQ,MAAM,CAAE;AACjF;AACO,SAAS,MAAM,UAAU,SAAS;AACrC,MAAI,CAAC,UAAU;AACX;AAAA,EACJ;AACA,YAAU,WAAW,CAAC;AACtB,MAAI,SAAS,WAAW,IAAI,QAAQ;AACpC,MAAI,QAAQ;AACR,WAAO,SAAS,SAAS,MAAM;AAAA,EACnC;AACA,aAAW,WAAW;AACtB,MAAI,MAAM,SAAS,QAAQ,MAAM,EAAE,EAAE,YAAY;AACjD,MAAI,OAAO,gBAAgB;AACvB,aAAS,SAAS,eAAe,GAAG,CAAC;AACrC,eAAW,UAAU,OAAO;AAC5B,WAAO;AAAA,EACX;AACA,MAAI,SAAS,IAAI;AACjB,MAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACvB,QAAI,WAAW,KAAK,WAAW,GAAG;AAC9B,UAAI,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACrC,UAAI,EAAE,MAAM,KAAK,MAAM,OAAQ;AAC3B,gBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,MACJ;AACA,cAAQ,UAAW,KAAK,SAAU,KAAO,KAAK,SAAU,GAAK,KAAK,OAAU,KAAK,QAAS,GAAK,KAAK,MAAS,KAAK,OAAQ,GAAI,WAAW,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAM,CAAC;AACjL,iBAAW,UAAU,OAAO;AAC5B,aAAO;AAAA,IACX,WACS,WAAW,KAAK,WAAW,GAAG;AACnC,UAAI,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACrC,UAAI,EAAE,MAAM,KAAK,MAAM,WAAW;AAC9B,gBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,MACJ;AACA,cAAQ,UAAU,KAAK,aAAa,KAAK,KAAK,UAAW,GAAG,KAAK,KAAM,WAAW,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,MAAO,CAAC;AAC3H,iBAAW,UAAU,OAAO;AAC5B,aAAO;AAAA,IACX;AACA;AAAA,EACJ;AACA,MAAI,KAAK,IAAI,QAAQ,GAAG;AACxB,MAAI,KAAK,IAAI,QAAQ,GAAG;AACxB,MAAI,OAAO,MAAM,KAAK,MAAM,QAAQ;AAChC,QAAI,QAAQ,IAAI,OAAO,GAAG,EAAE;AAC5B,QAAI,SAAS,IAAI,OAAO,KAAK,GAAG,MAAM,KAAK,EAAE,EAAE,MAAM,GAAG;AACxD,QAAI,QAAQ;AACZ,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,YAAI,OAAO,WAAW,GAAG;AACrB,iBAAO,OAAO,WAAW,IACnB,QAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IACtD,QAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,QACrC;AACA,gBAAQ,cAAc,OAAO,IAAI,CAAC;AAAA,MACtC,KAAK;AACD,YAAI,OAAO,UAAU,GAAG;AACpB,kBAAQ,SAAS,YAAY,OAAO,CAAC,CAAC,GAAG,YAAY,OAAO,CAAC,CAAC,GAAG,YAAY,OAAO,CAAC,CAAC,GAAG,OAAO,WAAW,IAAI,QAAQ,cAAc,OAAO,CAAC,CAAC,CAAC;AAC/I,qBAAW,UAAU,OAAO;AAC5B,iBAAO;AAAA,QACX,OACK;AACD,kBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,OAAO,WAAW,GAAG;AACrB,kBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,QACJ;AACA,eAAO,CAAC,IAAI,cAAc,OAAO,CAAC,CAAC;AACnC,kBAAU,QAAQ,OAAO;AACzB,mBAAW,UAAU,OAAO;AAC5B,eAAO;AAAA,MACX,KAAK;AACD,YAAI,OAAO,WAAW,GAAG;AACrB,kBAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AAAA,QACJ;AACA,kBAAU,QAAQ,OAAO;AACzB,mBAAW,UAAU,OAAO;AAC5B,eAAO;AAAA,MACX;AACI;AAAA,IACR;AAAA,EACJ;AACA,UAAQ,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3B;AACJ;AACA,SAAS,UAAU,MAAM,MAAM;AAC3B,MAAI,KAAO,WAAW,KAAK,CAAC,CAAC,IAAI,MAAO,OAAO,MAAO;AACtD,MAAI,IAAI,cAAc,KAAK,CAAC,CAAC;AAC7B,MAAI,IAAI,cAAc,KAAK,CAAC,CAAC;AAC7B,MAAI,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC9C,MAAI,KAAK,IAAI,IAAI;AACjB,SAAO,QAAQ,CAAC;AAChB,UAAQ,MAAM,aAAa,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,aAAa,YAAY,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,aAAa,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACnK,MAAI,KAAK,WAAW,GAAG;AACnB,SAAK,CAAC,IAAI,KAAK,CAAC;AAAA,EACpB;AACA,SAAO;AACX;AACA,SAAS,UAAU,MAAM;AACrB,MAAI,CAAC,MAAM;AACP;AAAA,EACJ;AACA,MAAI,IAAI,KAAK,CAAC,IAAI;AAClB,MAAI,IAAI,KAAK,CAAC,IAAI;AAClB,MAAI,IAAI,KAAK,CAAC,IAAI;AAClB,MAAI,OAAO,KAAK,IAAI,GAAG,GAAG,CAAC;AAC3B,MAAI,OAAO,KAAK,IAAI,GAAG,GAAG,CAAC;AAC3B,MAAI,QAAQ,OAAO;AACnB,MAAI,KAAK,OAAO,QAAQ;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,GAAG;AACb,QAAI;AACJ,QAAI;AAAA,EACR,OACK;AACD,QAAI,IAAI,KAAK;AACT,UAAI,SAAS,OAAO;AAAA,IACxB,OACK;AACD,UAAI,SAAS,IAAI,OAAO;AAAA,IAC5B;AACA,QAAI,WAAY,OAAO,KAAK,IAAM,QAAQ,KAAM;AAChD,QAAI,WAAY,OAAO,KAAK,IAAM,QAAQ,KAAM;AAChD,QAAI,WAAY,OAAO,KAAK,IAAM,QAAQ,KAAM;AAChD,QAAI,MAAM,MAAM;AACZ,UAAI,SAAS;AAAA,IACjB,WACS,MAAM,MAAM;AACjB,UAAK,IAAI,IAAK,SAAS;AAAA,IAC3B,WACS,MAAM,MAAM;AACjB,UAAK,IAAI,IAAK,SAAS;AAAA,IAC3B;AACA,QAAI,IAAI,GAAG;AACP,WAAK;AAAA,IACT;AACA,QAAI,IAAI,GAAG;AACP,WAAK;AAAA,IACT;AAAA,EACJ;AACA,MAAI,OAAO,CAAC,IAAI,KAAK,GAAG,CAAC;AACzB,MAAI,KAAK,CAAC,KAAK,MAAM;AACjB,SAAK,KAAK,KAAK,CAAC,CAAC;AAAA,EACrB;AACA,SAAO;AACX;AACO,SAAS,KAAK,OAAO,OAAO;AAC/B,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,UAAU;AACV,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAI,QAAQ,GAAG;AACX,iBAAS,CAAC,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS;AAAA,MAC9C,OACK;AACD,iBAAS,CAAC,KAAM,MAAM,SAAS,CAAC,KAAK,QAAQ,SAAS,CAAC,IAAK;AAAA,MAChE;AACA,UAAI,SAAS,CAAC,IAAI,KAAK;AACnB,iBAAS,CAAC,IAAI;AAAA,MAClB,WACS,SAAS,CAAC,IAAI,GAAG;AACtB,iBAAS,CAAC,IAAI;AAAA,MAClB;AAAA,IACJ;AACA,WAAO,UAAU,UAAU,SAAS,WAAW,IAAI,SAAS,KAAK;AAAA,EACrE;AACJ;AACO,SAAS,MAAM,OAAO;AACzB,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,UAAU;AACV,aAAS,KAAK,OAAO,SAAS,CAAC,KAAK,OAAO,SAAS,CAAC,KAAK,KAAM,CAAC,SAAS,CAAC,GAAI,SAAS,EAAE,EAAE,MAAM,CAAC;AAAA,EACvG;AACJ;AACO,SAAS,SAAS,iBAAiB,QAAQ,KAAK;AACnD,MAAI,EAAE,UAAU,OAAO,WAChB,EAAE,mBAAmB,KAAK,mBAAmB,IAAI;AACpD;AAAA,EACJ;AACA,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ,mBAAmB,OAAO,SAAS;AAC/C,MAAI,YAAY,KAAK,MAAM,KAAK;AAChC,MAAI,aAAa,KAAK,KAAK,KAAK;AAChC,MAAI,YAAY,OAAO,SAAS;AAChC,MAAI,aAAa,OAAO,UAAU;AAClC,MAAI,KAAK,QAAQ;AACjB,MAAI,CAAC,IAAI,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,MAAI,CAAC,IAAI,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,MAAI,CAAC,IAAI,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,MAAI,CAAC,IAAI,cAAc,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAClE,SAAO;AACX;AACO,IAAI,iBAAiB;AACrB,SAASG,MAAK,iBAAiB,QAAQ,YAAY;AACtD,MAAI,EAAE,UAAU,OAAO,WAChB,EAAE,mBAAmB,KAAK,mBAAmB,IAAI;AACpD;AAAA,EACJ;AACA,MAAI,QAAQ,mBAAmB,OAAO,SAAS;AAC/C,MAAI,YAAY,KAAK,MAAM,KAAK;AAChC,MAAI,aAAa,KAAK,KAAK,KAAK;AAChC,MAAI,YAAY,MAAM,OAAO,SAAS,CAAC;AACvC,MAAI,aAAa,MAAM,OAAO,UAAU,CAAC;AACzC,MAAI,KAAK,QAAQ;AACjB,MAAI,QAAQ,UAAU;AAAA,IAClB,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAAA,IACxD,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAAA,IACxD,aAAa,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAAA,IACxD,cAAc,WAAW,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAAA,EAC7D,GAAG,MAAM;AACT,SAAO,aACD;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IACE;AACV;AACO,IAAI,aAAaA;AACjB,SAAS,UAAU,OAAO,GAAG,GAAG,GAAG;AACtC,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,OAAO;AACP,eAAW,UAAU,QAAQ;AAC7B,SAAK,SAAS,SAAS,CAAC,IAAI,cAAc,CAAC;AAC3C,SAAK,SAAS,SAAS,CAAC,IAAI,cAAc,CAAC;AAC3C,SAAK,SAAS,SAAS,CAAC,IAAI,cAAc,CAAC;AAC3C,WAAO,UAAU,UAAU,QAAQ,GAAG,MAAM;AAAA,EAChD;AACJ;AACO,SAAS,YAAY,OAAO,OAAO;AACtC,MAAI,WAAW,MAAM,KAAK;AAC1B,MAAI,YAAY,SAAS,MAAM;AAC3B,aAAS,CAAC,IAAI,cAAc,KAAK;AACjC,WAAO,UAAU,UAAU,MAAM;AAAA,EACrC;AACJ;AACO,SAAS,UAAU,UAAU,MAAM;AACtC,MAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;AAC/B;AAAA,EACJ;AACA,MAAI,WAAW,SAAS,CAAC,IAAI,MAAM,SAAS,CAAC,IAAI,MAAM,SAAS,CAAC;AACjE,MAAI,SAAS,UAAU,SAAS,UAAU,SAAS,QAAQ;AACvD,gBAAY,MAAM,SAAS,CAAC;AAAA,EAChC;AACA,SAAO,OAAO,MAAM,WAAW;AACnC;AACO,SAAS,IAAI,OAAO,eAAe;AACtC,MAAI,MAAM,MAAM,KAAK;AACrB,SAAO,OACA,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,OACzD,IAAI,IAAI,CAAC,KAAK,gBACnB;AACV;AACO,SAAS,SAAS;AACrB,SAAO,UAAU;AAAA,IACb,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,IAC9B,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,IAC9B,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,EAClC,GAAG,KAAK;AACZ;AACA,IAAI,mBAAmB,IAAI,YAAI,GAAG;AAC3B,SAAS,UAAU,OAAO;AAC7B,MAAI,SAAS,KAAK,GAAG;AACjB,QAAI,cAAc,iBAAiB,IAAI,KAAK;AAC5C,QAAI,CAAC,aAAa;AACd,oBAAc,KAAK,OAAO,IAAI;AAC9B,uBAAiB,IAAI,OAAO,WAAW;AAAA,IAC3C;AACA,WAAO;AAAA,EACX,WACS,iBAAiB,KAAK,GAAG;AAC9B,QAAI,MAAM,OAAO,CAAC,GAAG,KAAK;AAC1B,QAAI,aAAa,IAAI,MAAM,YAAY,SAAU,MAAM;AAAE,aAAQ;AAAA,QAC7D,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK,KAAK,OAAO,IAAI;AAAA,MAChC;AAAA,IAAI,CAAC;AACL,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;AE5ZA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEO,SAAS,UAAU,GAAG,GAAG;AAC5B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;;;AC3BA,IAAI,mBAAmB,IAAI,YAAI,EAAE;AAC1B,SAAS,eAAe,eAAe;AAC1C,MAAI,OAAO,kBAAkB,UAAU;AACnC,QAAI,eAAe,iBAAiB,IAAI,aAAa;AACrD,WAAO,gBAAgB,aAAa;AAAA,EACxC,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACO,SAAS,oBAAoB,eAAe,OAAO,QAAQ,QAAQ,WAAW;AACjF,MAAI,CAAC,eAAe;AAChB,WAAO;AAAA,EACX,WACS,OAAO,kBAAkB,UAAU;AACxC,QAAK,SAAS,MAAM,iBAAiB,iBAAkB,CAAC,QAAQ;AAC5D,aAAO;AAAA,IACX;AACA,QAAI,eAAe,iBAAiB,IAAI,aAAa;AACrD,QAAI,cAAc,EAAE,QAAgB,IAAI,QAAQ,UAAqB;AACrE,QAAI,cAAc;AACd,cAAQ,aAAa;AACrB,OAAC,aAAa,KAAK,KAAK,aAAa,QAAQ,KAAK,WAAW;AAAA,IACjE,OACK;AACD,cAAQ,YAAY,UAAU,eAAe,aAAa,WAAW;AACrE,YAAM,eAAe;AACrB,uBAAiB,IAAI,eAAe,MAAM,iBAAiB;AAAA,QACvD;AAAA,QACA,SAAS,CAAC,WAAW;AAAA,MACzB,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,cAAc;AACnB,MAAI,eAAe,KAAK;AACxB,OAAK,SAAS,KAAK,UAAU,KAAK,iBAAiB;AACnD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,QAAQ,KAAK;AAClD,QAAI,cAAc,aAAa,QAAQ,CAAC;AACxC,QAAI,KAAK,YAAY;AACrB,UAAM,GAAG,MAAM,YAAY,SAAS;AACpC,gBAAY,OAAO,MAAM;AAAA,EAC7B;AACA,eAAa,QAAQ,SAAS;AAClC;AACO,SAAS,aAAa,OAAO;AAChC,SAAO,SAAS,MAAM,SAAS,MAAM;AACzC;;;ACrDA,IAAI,QAAS,WAAY;AACrB,WAASC,OAAM,GAAG,GAAG;AACjB,SAAK,IAAI,KAAK;AACd,SAAK,IAAI,KAAK;AAAA,EAClB;AACA,EAAAA,OAAM,UAAU,OAAO,SAAU,OAAO;AACpC,SAAK,IAAI,MAAM;AACf,SAAK,IAAI,MAAM;AACf,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,QAAQ,WAAY;AAChC,WAAO,IAAIA,OAAM,KAAK,GAAG,KAAK,CAAC;AAAA,EACnC;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,GAAG,GAAG;AAClC,SAAK,IAAI;AACT,SAAK,IAAI;AACT,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,QAAQ,SAAU,OAAO;AACrC,WAAO,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EAClD;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACnC,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,QAAQ,SAAU,QAAQ;AACtC,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACd;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,OAAO,QAAQ;AACnD,SAAK,KAAK,MAAM,IAAI;AACpB,SAAK,KAAK,MAAM,IAAI;AAAA,EACxB;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACnC,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACnC,WAAO,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;AAAA,EAC7C;AACA,EAAAA,OAAM,UAAU,MAAM,WAAY;AAC9B,WAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EACtD;AACA,EAAAA,OAAM,UAAU,YAAY,WAAY;AACpC,WAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,EAC3C;AACA,EAAAA,OAAM,UAAU,YAAY,WAAY;AACpC,QAAIC,OAAM,KAAK,IAAI;AACnB,SAAK,KAAKA;AACV,SAAK,KAAKA;AACV,WAAO;AAAA,EACX;AACA,EAAAD,OAAM,UAAU,WAAW,SAAU,OAAO;AACxC,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,WAAO,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAAA,EACtC;AACA,EAAAA,OAAM,UAAU,iBAAiB,SAAU,OAAO;AAC9C,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,QAAI,KAAK,KAAK,IAAI,MAAM;AACxB,WAAO,KAAK,KAAK,KAAK;AAAA,EAC1B;AACA,EAAAA,OAAM,UAAU,SAAS,WAAY;AACjC,SAAK,IAAI,CAAC,KAAK;AACf,SAAK,IAAI,CAAC,KAAK;AACf,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,YAAY,SAAU,GAAG;AACrC,QAAI,CAAC,GAAG;AACJ;AAAA,IACJ;AACA,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,SAAK,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,SAAK,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,UAAU,SAAU,KAAK;AACrC,QAAI,CAAC,IAAI,KAAK;AACd,QAAI,CAAC,IAAI,KAAK;AACd,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,YAAY,SAAU,OAAO;AACzC,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,MAAM,CAAC;AAAA,EACpB;AACA,EAAAA,OAAM,MAAM,SAAU,GAAG,GAAG,GAAG;AAC3B,MAAE,IAAI;AACN,MAAE,IAAI;AAAA,EACV;AACA,EAAAA,OAAM,OAAO,SAAU,GAAG,IAAI;AAC1B,MAAE,IAAI,GAAG;AACT,MAAE,IAAI,GAAG;AAAA,EACb;AACA,EAAAA,OAAM,MAAM,SAAU,GAAG;AACrB,WAAO,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAAA,EAC1C;AACA,EAAAA,OAAM,YAAY,SAAU,GAAG;AAC3B,WAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,EAC/B;AACA,EAAAA,OAAM,MAAM,SAAU,IAAI,IAAI;AAC1B,WAAO,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAAA,EACnC;AACA,EAAAA,OAAM,MAAM,SAAU,KAAK,IAAI,IAAI;AAC/B,QAAI,IAAI,GAAG,IAAI,GAAG;AAClB,QAAI,IAAI,GAAG,IAAI,GAAG;AAAA,EACtB;AACA,EAAAA,OAAM,MAAM,SAAU,KAAK,IAAI,IAAI;AAC/B,QAAI,IAAI,GAAG,IAAI,GAAG;AAClB,QAAI,IAAI,GAAG,IAAI,GAAG;AAAA,EACtB;AACA,EAAAA,OAAM,QAAQ,SAAU,KAAK,IAAI,QAAQ;AACrC,QAAI,IAAI,GAAG,IAAI;AACf,QAAI,IAAI,GAAG,IAAI;AAAA,EACnB;AACA,EAAAA,OAAM,cAAc,SAAU,KAAK,IAAI,IAAI,QAAQ;AAC/C,QAAI,IAAI,GAAG,IAAI,GAAG,IAAI;AACtB,QAAI,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,EAC1B;AACA,EAAAA,OAAM,OAAO,SAAU,KAAK,IAAI,IAAI,GAAG;AACnC,QAAI,OAAO,IAAI;AACf,QAAI,IAAI,OAAO,GAAG,IAAI,IAAI,GAAG;AAC7B,QAAI,IAAI,OAAO,GAAG,IAAI,IAAI,GAAG;AAAA,EACjC;AACA,SAAOA;AACX,EAAE;AACF,IAAO,gBAAQ;;;AC9Hf,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,KAAK,IAAI,cAAM;AACnB,IAAI,QAAQ,IAAI,cAAM;AACtB,IAAI,QAAQ,IAAI,cAAM;AACtB,IAAI,eAAgB,WAAY;AAC5B,WAASE,cAAa,GAAG,GAAG,OAAO,QAAQ;AACvC,QAAI,QAAQ,GAAG;AACX,UAAI,IAAI;AACR,cAAQ,CAAC;AAAA,IACb;AACA,QAAI,SAAS,GAAG;AACZ,UAAI,IAAI;AACR,eAAS,CAAC;AAAA,IACd;AACA,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AACA,EAAAA,cAAa,UAAU,QAAQ,SAAU,OAAO;AAC5C,QAAI,IAAI,QAAQ,MAAM,GAAG,KAAK,CAAC;AAC/B,QAAI,IAAI,QAAQ,MAAM,GAAG,KAAK,CAAC;AAC/B,QAAI,SAAS,KAAK,CAAC,KAAK,SAAS,KAAK,KAAK,GAAG;AAC1C,WAAK,QAAQ,QAAQ,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,IAAI;AAAA,IACvE,OACK;AACD,WAAK,QAAQ,MAAM;AAAA,IACvB;AACA,QAAI,SAAS,KAAK,CAAC,KAAK,SAAS,KAAK,MAAM,GAAG;AAC3C,WAAK,SAAS,QAAQ,MAAM,IAAI,MAAM,QAAQ,KAAK,IAAI,KAAK,MAAM,IAAI;AAAA,IAC1E,OACK;AACD,WAAK,SAAS,MAAM;AAAA,IACxB;AACA,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACb;AACA,EAAAA,cAAa,UAAU,iBAAiB,SAAU,GAAG;AACjD,IAAAA,cAAa,eAAe,MAAM,MAAM,CAAC;AAAA,EAC7C;AACA,EAAAA,cAAa,UAAU,qBAAqB,SAAU,GAAG;AACrD,QAAI,IAAI;AACR,QAAI,KAAK,EAAE,QAAQ,EAAE;AACrB,QAAI,KAAK,EAAE,SAAS,EAAE;AACtB,QAAI,IAAW,OAAO;AACtB,IAAO,UAAU,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AACnC,IAAO,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AAC3B,IAAO,UAAU,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACjC,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,UAAU,YAAY,SAAU,GAAG,KAAK;AACjD,QAAI,CAAC,GAAG;AACJ,aAAO;AAAA,IACX;AACA,QAAI,EAAE,aAAaA,gBAAe;AAC9B,UAAIA,cAAa,OAAO,CAAC;AAAA,IAC7B;AACA,QAAI,IAAI;AACR,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,MAAM,EAAE;AACZ,QAAI,MAAM,EAAE,IAAI,EAAE;AAClB,QAAI,UAAU,EAAE,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM;AAC7D,QAAI,KAAK;AACL,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,MAAM,GAAG;AAC3B,UAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AACxB,UAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AACxB,UAAI,MAAM,OAAO,MAAM,KAAK;AACxB,YAAI,KAAK,MAAM;AACX,iBAAO;AACP,cAAI,KAAK,IAAI;AACT,0BAAM,IAAI,OAAO,CAAC,IAAI,CAAC;AAAA,UAC3B,OACK;AACD,0BAAM,IAAI,OAAO,IAAI,CAAC;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ,OACK;AACD,YAAI,KAAK,MAAM;AACX,iBAAO;AACP,cAAI,KAAK,IAAI;AACT,0BAAM,IAAI,OAAO,IAAI,CAAC;AAAA,UAC1B,OACK;AACD,0BAAM,IAAI,OAAO,CAAC,IAAI,CAAC;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,MAAM,OAAO,MAAM,KAAK;AACxB,YAAI,KAAK,MAAM;AACX,iBAAO;AACP,cAAI,KAAK,IAAI;AACT,0BAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAAA,UAC3B,OACK;AACD,0BAAM,IAAI,OAAO,GAAG,EAAE;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ,OACK;AACD,YAAI,KAAK,MAAM;AACX,iBAAO;AACP,cAAI,KAAK,IAAI;AACT,0BAAM,IAAI,OAAO,GAAG,EAAE;AAAA,UAC1B,OACK;AACD,0BAAM,IAAI,OAAO,GAAG,CAAC,EAAE;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,KAAK;AACL,oBAAM,KAAK,KAAK,UAAU,QAAQ,KAAK;AAAA,IAC3C;AACA,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,UAAU,UAAU,SAAU,GAAG,GAAG;AAC7C,QAAI,OAAO;AACX,WAAO,KAAK,KAAK,KACV,KAAM,KAAK,IAAI,KAAK,SACpB,KAAK,KAAK,KACV,KAAM,KAAK,IAAI,KAAK;AAAA,EAC/B;AACA,EAAAA,cAAa,UAAU,QAAQ,WAAY;AACvC,WAAO,IAAIA,cAAa,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACnE;AACA,EAAAA,cAAa,UAAU,OAAO,SAAU,OAAO;AAC3C,IAAAA,cAAa,KAAK,MAAM,KAAK;AAAA,EACjC;AACA,EAAAA,cAAa,UAAU,QAAQ,WAAY;AACvC,WAAO;AAAA,MACH,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ;AACA,EAAAA,cAAa,UAAU,WAAW,WAAY;AAC1C,WAAO,SAAS,KAAK,CAAC,KACf,SAAS,KAAK,CAAC,KACf,SAAS,KAAK,KAAK,KACnB,SAAS,KAAK,MAAM;AAAA,EAC/B;AACA,EAAAA,cAAa,UAAU,SAAS,WAAY;AACxC,WAAO,KAAK,UAAU,KAAK,KAAK,WAAW;AAAA,EAC/C;AACA,EAAAA,cAAa,SAAS,SAAU,MAAM;AAClC,WAAO,IAAIA,cAAa,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACnE;AACA,EAAAA,cAAa,OAAO,SAAU,QAAQ,QAAQ;AAC1C,WAAO,IAAI,OAAO;AAClB,WAAO,IAAI,OAAO;AAClB,WAAO,QAAQ,OAAO;AACtB,WAAO,SAAS,OAAO;AAAA,EAC3B;AACA,EAAAA,cAAa,iBAAiB,SAAU,QAAQ,QAAQ,GAAG;AACvD,QAAI,CAAC,GAAG;AACJ,UAAI,WAAW,QAAQ;AACnB,QAAAA,cAAa,KAAK,QAAQ,MAAM;AAAA,MACpC;AACA;AAAA,IACJ;AACA,QAAI,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,IAAI,OAAO;AAC5D,UAAI,KAAK,EAAE,CAAC;AACZ,UAAI,KAAK,EAAE,CAAC;AACZ,UAAI,KAAK,EAAE,CAAC;AACZ,UAAI,KAAK,EAAE,CAAC;AACZ,aAAO,IAAI,OAAO,IAAI,KAAK;AAC3B,aAAO,IAAI,OAAO,IAAI,KAAK;AAC3B,aAAO,QAAQ,OAAO,QAAQ;AAC9B,aAAO,SAAS,OAAO,SAAS;AAChC,UAAI,OAAO,QAAQ,GAAG;AAClB,eAAO,KAAK,OAAO;AACnB,eAAO,QAAQ,CAAC,OAAO;AAAA,MAC3B;AACA,UAAI,OAAO,SAAS,GAAG;AACnB,eAAO,KAAK,OAAO;AACnB,eAAO,SAAS,CAAC,OAAO;AAAA,MAC5B;AACA;AAAA,IACJ;AACA,OAAG,IAAI,GAAG,IAAI,OAAO;AACrB,OAAG,IAAI,GAAG,IAAI,OAAO;AACrB,OAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO;AAChC,OAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO;AAChC,OAAG,UAAU,CAAC;AACd,OAAG,UAAU,CAAC;AACd,OAAG,UAAU,CAAC;AACd,OAAG,UAAU,CAAC;AACd,WAAO,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,WAAO,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,QAAI,OAAO,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,QAAI,OAAO,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,WAAO,QAAQ,OAAO,OAAO;AAC7B,WAAO,SAAS,OAAO,OAAO;AAAA,EAClC;AACA,SAAOA;AACX,EAAE;AACF,IAAO,uBAAQ;;;ACnNf,IAAI,iBAAiB,CAAC;AACf,SAAS,SAAS,MAAM,MAAM;AACjC,SAAO,QAAQ;AACf,MAAI,cAAc,eAAe,IAAI;AACrC,MAAI,CAAC,aAAa;AACd,kBAAc,eAAe,IAAI,IAAI,IAAI,YAAI,GAAG;AAAA,EACpD;AACA,MAAI,QAAQ,YAAY,IAAI,IAAI;AAChC,MAAI,SAAS,MAAM;AACf,YAAQ,YAAY,YAAY,MAAM,IAAI,EAAE;AAC5C,gBAAY,IAAI,MAAM,KAAK;AAAA,EAC/B;AACA,SAAO;AACX;AACO,SAAS,qBAAqB,MAAM,MAAM,WAAW,cAAc;AACtE,MAAI,QAAQ,SAAS,MAAM,IAAI;AAC/B,MAAI,SAAS,cAAc,IAAI;AAC/B,MAAI,IAAI,YAAY,GAAG,OAAO,SAAS;AACvC,MAAI,IAAI,YAAY,GAAG,QAAQ,YAAY;AAC3C,MAAI,OAAO,IAAI,qBAAa,GAAG,GAAG,OAAO,MAAM;AAC/C,SAAO;AACX;AACO,SAAS,gBAAgB,MAAM,MAAM,WAAW,cAAc;AACjE,MAAI,cAAc,QAAQ,MAAM,IAAI,MAAM,IAAI;AAC9C,MAAIC,OAAM,UAAU;AACpB,MAAIA,SAAQ,GAAG;AACX,WAAO,qBAAqB,UAAU,CAAC,GAAG,MAAM,WAAW,YAAY;AAAA,EAC3E,OACK;AACD,QAAI,aAAa,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,OAAO,qBAAqB,UAAU,CAAC,GAAG,MAAM,WAAW,YAAY;AAC3E,YAAM,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,MAAM,IAAI;AAAA,IAC3D;AACA,WAAO;AAAA,EACX;AACJ;AACO,SAAS,YAAY,GAAG,OAAO,WAAW;AAC7C,MAAI,cAAc,SAAS;AACvB,SAAK;AAAA,EACT,WACS,cAAc,UAAU;AAC7B,SAAK,QAAQ;AAAA,EACjB;AACA,SAAO;AACX;AACO,SAAS,YAAY,GAAG,QAAQ,eAAe;AAClD,MAAI,kBAAkB,UAAU;AAC5B,SAAK,SAAS;AAAA,EAClB,WACS,kBAAkB,UAAU;AACjC,SAAK;AAAA,EACT;AACA,SAAO;AACX;AACO,SAAS,cAAc,MAAM;AAChC,SAAO,SAAS,KAAK,IAAI;AAC7B;AAIO,SAAS,aAAa,OAAO,UAAU;AAC1C,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,MAAM,YAAY,GAAG,KAAK,GAAG;AAC7B,aAAO,WAAW,KAAK,IAAI,MAAM;AAAA,IACrC;AACA,WAAO,WAAW,KAAK;AAAA,EAC3B;AACA,SAAO;AACX;AACO,SAAS,sBAAsB,KAAK,MAAM,MAAM;AACnD,MAAI,eAAe,KAAK,YAAY;AACpC,MAAIC,YAAW,KAAK,YAAY,OAAO,KAAK,WAAW;AACvD,MAAI,SAAS,KAAK;AAClB,MAAI,QAAQ,KAAK;AACjB,MAAI,aAAa,SAAS;AAC1B,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,YAAY;AAChB,MAAI,oBAAoB;AACxB,MAAI,wBAAwB,OAAO;AAC/B,SAAK,aAAa,aAAa,CAAC,GAAG,KAAK,KAAK;AAC7C,SAAK,aAAa,aAAa,CAAC,GAAG,KAAK,MAAM;AAC9C,gBAAY;AACZ,wBAAoB;AAAA,EACxB,OACK;AACD,YAAQ,cAAc;AAAA,MAClB,KAAK;AACD,aAAKA;AACL,aAAK;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAKA,YAAW;AAChB,aAAK;AACL,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAK,QAAQ;AACb,aAAKA;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAK,QAAQ;AACb,aAAK,SAASA;AACd,oBAAY;AACZ;AAAA,MACJ,KAAK;AACD,aAAK,QAAQ;AACb,aAAK;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAKA;AACL,aAAK;AACL,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAK,QAAQA;AACb,aAAK;AACL,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAK,QAAQ;AACb,aAAKA;AACL,oBAAY;AACZ;AAAA,MACJ,KAAK;AACD,aAAK,QAAQ;AACb,aAAK,SAASA;AACd,oBAAY;AACZ,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAKA;AACL,aAAKA;AACL;AAAA,MACJ,KAAK;AACD,aAAK,QAAQA;AACb,aAAKA;AACL,oBAAY;AACZ;AAAA,MACJ,KAAK;AACD,aAAKA;AACL,aAAK,SAASA;AACd,4BAAoB;AACpB;AAAA,MACJ,KAAK;AACD,aAAK,QAAQA;AACb,aAAK,SAASA;AACd,oBAAY;AACZ,4BAAoB;AACpB;AAAA,IACR;AAAA,EACJ;AACA,QAAM,OAAO,CAAC;AACd,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,gBAAgB;AACpB,SAAO;AACX;;;ACtKA,IAAI,YAAY;AACT,SAAS,aAAa,MAAM,gBAAgB,MAAM,UAAU,SAAS;AACxE,MAAI,MAAM,CAAC;AACX,gBAAc,KAAK,MAAM,gBAAgB,MAAM,UAAU,OAAO;AAChE,SAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK,MAAM,gBAAgB,MAAM,UAAU,SAAS;AACvE,MAAI,CAAC,gBAAgB;AACjB,QAAI,OAAO;AACX,QAAI,cAAc;AAClB;AAAA,EACJ;AACA,MAAI,aAAa,OAAO,IAAI,MAAM,IAAI;AACtC,YAAU,uBAAuB,gBAAgB,MAAM,UAAU,OAAO;AACxE,MAAI,cAAc;AAClB,MAAI,cAAc,CAAC;AACnB,WAAS,IAAI,GAAGC,OAAM,UAAU,QAAQ,IAAIA,MAAK,KAAK;AAClD,uBAAmB,aAAa,UAAU,CAAC,GAAG,OAAO;AACrD,cAAU,CAAC,IAAI,YAAY;AAC3B,kBAAc,eAAe,YAAY;AAAA,EAC7C;AACA,MAAI,OAAO,UAAU,KAAK,IAAI;AAC9B,MAAI,cAAc;AACtB;AACA,SAAS,uBAAuB,gBAAgB,MAAM,UAAU,SAAS;AACrE,YAAU,WAAW,CAAC;AACtB,MAAI,eAAe,OAAO,CAAC,GAAG,OAAO;AACrC,eAAa,OAAO;AACpB,aAAW,UAAU,UAAU,KAAK;AACpC,eAAa,gBAAgB,UAAU,QAAQ,eAAe,CAAC;AAC/D,MAAI,UAAU,aAAa,UAAU,UAAU,QAAQ,SAAS,CAAC;AACjE,eAAa,cAAc,SAAS,KAAK,IAAI;AAC7C,MAAI,eAAe,aAAa,eAAe,SAAS,KAAK,IAAI;AACjE,eAAa,cAAc,UAAU,QAAQ,aAAa,EAAE;AAC5D,MAAI,eAAe,iBAAiB,KAAK,IAAI,GAAG,iBAAiB,CAAC;AAClE,WAAS,IAAI,GAAG,IAAI,WAAW,gBAAgB,cAAc,KAAK;AAC9D,oBAAgB;AAAA,EACpB;AACA,MAAI,gBAAgB,SAAS,UAAU,IAAI;AAC3C,MAAI,gBAAgB,cAAc;AAC9B,eAAW;AACX,oBAAgB;AAAA,EACpB;AACA,iBAAe,iBAAiB;AAChC,eAAa,WAAW;AACxB,eAAa,gBAAgB;AAC7B,eAAa,eAAe;AAC5B,eAAa,iBAAiB;AAC9B,SAAO;AACX;AACA,SAAS,mBAAmB,KAAK,UAAU,SAAS;AAChD,MAAI,iBAAiB,QAAQ;AAC7B,MAAI,OAAO,QAAQ;AACnB,MAAI,eAAe,QAAQ;AAC3B,MAAI,CAAC,gBAAgB;AACjB,QAAI,WAAW;AACf,QAAI,cAAc;AAClB;AAAA,EACJ;AACA,MAAI,YAAY,SAAS,UAAU,IAAI;AACvC,MAAI,aAAa,gBAAgB;AAC7B,QAAI,WAAW;AACf,QAAI,cAAc;AAClB;AAAA,EACJ;AACA,WAAS,IAAI,KAAI,KAAK;AAClB,QAAI,aAAa,gBAAgB,KAAK,QAAQ,eAAe;AACzD,kBAAY,QAAQ;AACpB;AAAA,IACJ;AACA,QAAI,YAAY,MAAM,IAChB,eAAe,UAAU,cAAc,QAAQ,cAAc,QAAQ,WAAW,IAChF,YAAY,IACR,KAAK,MAAM,SAAS,SAAS,eAAe,SAAS,IACrD;AACV,eAAW,SAAS,OAAO,GAAG,SAAS;AACvC,gBAAY,SAAS,UAAU,IAAI;AAAA,EACvC;AACA,MAAI,aAAa,IAAI;AACjB,eAAW,QAAQ;AAAA,EACvB;AACA,MAAI,WAAW;AACf,MAAI,cAAc;AACtB;AACA,SAAS,eAAe,MAAM,cAAc,cAAc,aAAa;AACnE,MAAI,QAAQ;AACZ,MAAI,IAAI;AACR,WAASA,OAAM,KAAK,QAAQ,IAAIA,QAAO,QAAQ,cAAc,KAAK;AAC9D,QAAI,WAAW,KAAK,WAAW,CAAC;AAChC,aAAU,KAAK,YAAY,YAAY,MAAO,eAAe;AAAA,EACjE;AACA,SAAO;AACX;AACO,SAAS,eAAe,MAAM,OAAO;AACxC,UAAQ,SAAS,QAAQ;AACzB,MAAI,WAAW,MAAM;AACrB,MAAI,UAAU,MAAM;AACpB,MAAI,OAAO,MAAM;AACjB,MAAI,WAAW,aAAa;AAC5B,MAAI,uBAAuB,cAAc,IAAI;AAC7C,MAAI,aAAa,UAAU,MAAM,YAAY,oBAAoB;AACjE,MAAI,eAAe,CAAC,CAAE,MAAM;AAC5B,MAAI,uBAAuB,MAAM,iBAAiB;AAClD,MAAI,cAAc;AAClB,MAAI,QAAQ,MAAM;AAClB,MAAI;AACJ,MAAI,SAAS,SAAS,aAAa,WAAW,aAAa,aAAa;AACpE,YAAQ,OAAO,SAAS,MAAM,MAAM,MAAM,OAAO,aAAa,YAAY,CAAC,EAAE,QAAQ,CAAC;AAAA,EAC1F,OACK;AACD,YAAQ,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC;AAAA,EACvC;AACA,MAAI,gBAAgB,MAAM,SAAS;AACnC,MAAI,SAAS,UAAU,MAAM,QAAQ,aAAa;AAClD,MAAI,gBAAgB,UAAU,sBAAsB;AAChD,QAAI,YAAY,KAAK,MAAM,SAAS,UAAU;AAC9C,kBAAc,eAAgB,MAAM,SAAS;AAC7C,YAAQ,MAAM,MAAM,GAAG,SAAS;AAAA,EACpC;AACA,MAAI,QAAQ,YAAY,SAAS,MAAM;AACnC,QAAI,UAAU,uBAAuB,OAAO,MAAM,MAAM,UAAU;AAAA,MAC9D,SAAS,MAAM;AAAA,MACf,aAAa,MAAM;AAAA,IACvB,CAAC;AACD,QAAI,YAAY,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,yBAAmB,WAAW,MAAM,CAAC,GAAG,OAAO;AAC/C,YAAM,CAAC,IAAI,UAAU;AACrB,oBAAc,eAAe,UAAU;AAAA,IAC3C;AAAA,EACJ;AACA,MAAI,cAAc;AAClB,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,mBAAe,KAAK,IAAI,SAAS,MAAM,CAAC,GAAG,IAAI,GAAG,YAAY;AAAA,EAClE;AACA,MAAI,SAAS,MAAM;AACf,YAAQ;AAAA,EACZ;AACA,MAAI,aAAa;AACjB,MAAI,SAAS;AACT,mBAAe,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACrC,kBAAc,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACpC,aAAS,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACnC;AACA,MAAI,cAAc;AACd,iBAAa;AAAA,EACjB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,gBAAiB,2BAAY;AAC7B,WAASC,iBAAgB;AAAA,EACzB;AACA,SAAOA;AACX,EAAE;AACF,IAAI,eAAgB,2BAAY;AAC5B,WAASC,cAAa,QAAQ;AAC1B,SAAK,SAAS,CAAC;AACf,QAAI,QAAQ;AACR,WAAK,SAAS;AAAA,IAClB;AAAA,EACJ;AACA,SAAOA;AACX,EAAE;AACF,IAAI,uBAAwB,2BAAY;AACpC,WAASC,wBAAuB;AAC5B,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,QAAQ,CAAC;AACd,SAAK,cAAc;AAAA,EACvB;AACA,SAAOA;AACX,EAAE;AAEK,SAAS,cAAc,MAAM,OAAO;AACvC,MAAI,eAAe,IAAI,qBAAqB;AAC5C,UAAQ,SAAS,QAAQ;AACzB,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,MAAM;AACtB,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,aAAa,WAAW,aAAa,eAAe,YAAY,OAC1E,EAAE,OAAO,UAAU,YAAY,GAAG,UAAU,aAAa,WAAW,IACpE;AACN,MAAI,YAAY,UAAU,YAAY;AACtC,MAAI;AACJ,UAAQ,SAAS,UAAU,KAAK,IAAI,MAAM,MAAM;AAC5C,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,WAAW;AAC1B,iBAAW,cAAc,KAAK,UAAU,WAAW,YAAY,GAAG,OAAO,QAAQ;AAAA,IACrF;AACA,eAAW,cAAc,OAAO,CAAC,GAAG,OAAO,UAAU,OAAO,CAAC,CAAC;AAC9D,gBAAY,UAAU;AAAA,EAC1B;AACA,MAAI,YAAY,KAAK,QAAQ;AACzB,eAAW,cAAc,KAAK,UAAU,WAAW,KAAK,MAAM,GAAG,OAAO,QAAQ;AAAA,EACpF;AACA,MAAI,cAAc,CAAC;AACnB,MAAI,mBAAmB;AACvB,MAAI,kBAAkB;AACtB,MAAI,aAAa,MAAM;AACvB,MAAI,WAAW,aAAa;AAC5B,MAAI,eAAe,MAAM,iBAAiB;AAC1C,MAAI,iBAAiB,CAAC;AACtB,WAAS,WAAWC,OAAMC,YAAWC,aAAY;AAC7C,IAAAF,MAAK,QAAQC;AACb,IAAAD,MAAK,aAAaE;AAClB,wBAAoBA;AACpB,sBAAkB,KAAK,IAAI,iBAAiBD,UAAS;AAAA,EACzD;AACA,QAAO,UAAS,IAAI,GAAG,IAAI,aAAa,MAAM,QAAQ,KAAK;AACvD,QAAI,OAAO,aAAa,MAAM,CAAC;AAC/B,QAAI,aAAa;AACjB,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AACzC,UAAI,QAAQ,KAAK,OAAO,CAAC;AACzB,UAAI,aAAa,MAAM,aAAa,MAAM,KAAK,MAAM,SAAS,KAAK,CAAC;AACpE,UAAI,cAAc,MAAM,cAAc,WAAW;AACjD,UAAI,WAAW,cAAc,YAAY,CAAC,IAAI,YAAY,CAAC,IAAI;AAC/D,UAAI,OAAO,MAAM,OAAO,WAAW,QAAQ,MAAM;AACjD,YAAM,gBAAgB,cAAc,IAAI;AACxC,UAAI,cAAc,UAAU,WAAW,QAAQ,MAAM,aAAa;AAClE,YAAM,cAAc;AACpB,sBAAgB,eAAe,YAAY,CAAC,IAAI,YAAY,CAAC;AAC7D,YAAM,SAAS;AACf,YAAM,aAAa,UAAU,WAAW,YAAY,MAAM,YAAY,WAAW;AACjF,YAAM,QAAQ,cAAc,WAAW,SAAS,MAAM;AACtD,YAAM,gBAAgB,cAAc,WAAW,iBAAiB;AAChE,UAAI,gBAAgB,aAAa,QAAQ,mBAAmB,MAAM,aAAa,WAAW;AACtF,YAAI,iBAAiB,aAAa,MAAM;AACxC,YAAI,IAAI,GAAG;AACP,eAAK,SAAS,KAAK,OAAO,MAAM,GAAG,CAAC;AACpC,qBAAW,MAAM,WAAW,UAAU;AACtC,uBAAa,QAAQ,aAAa,MAAM,MAAM,GAAG,IAAI,CAAC;AAAA,QAC1D,OACK;AACD,uBAAa,QAAQ,aAAa,MAAM,MAAM,GAAG,CAAC;AAAA,QACtD;AACA,qBAAa,cAAc,aAAa,eAAgB,aAAa,MAAM,SAAS;AACpF,cAAM;AAAA,MACV;AACA,UAAI,kBAAkB,WAAW;AACjC,UAAI,yBAAyB,mBAAmB,QAAQ,oBAAoB;AAC5E,UAAI,OAAO,oBAAoB,YAAY,gBAAgB,OAAO,gBAAgB,SAAS,CAAC,MAAM,KAAK;AACnG,cAAM,eAAe;AACrB,oBAAY,KAAK,KAAK;AACtB,cAAM,eAAe,SAAS,MAAM,MAAM,IAAI;AAAA,MAClD,OACK;AACD,YAAI,wBAAwB;AACxB,cAAI,sBAAsB,WAAW;AACrC,cAAI,QAAQ,uBAAuB,oBAAoB;AACvD,cAAI,OAAO;AACP,oBAAoB,eAAe,KAAK;AACxC,gBAAgB,aAAa,KAAK,GAAG;AACjC,oBAAM,QAAQ,KAAK,IAAI,MAAM,OAAO,MAAM,QAAQ,cAAc,MAAM,MAAM;AAAA,YAChF;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,mBAAmB,YAAY,YAAY,OACzC,WAAW,YAAY;AAC7B,YAAI,oBAAoB,QAAQ,mBAAmB,MAAM,OAAO;AAC5D,cAAI,CAAC,0BAA0B,mBAAmB,UAAU;AACxD,kBAAM,OAAO;AACb,kBAAM,QAAQ,MAAM,eAAe;AAAA,UACvC,OACK;AACD,0BAAc,gBAAgB,MAAM,MAAM,mBAAmB,UAAU,MAAM,MAAM,UAAU,EAAE,SAAS,MAAM,gBAAgB,CAAC;AAC/H,kBAAM,OAAO,eAAe;AAC5B,yBAAa,cAAc,aAAa,eAAe,eAAe;AACtE,kBAAM,QAAQ,MAAM,eAAe,SAAS,MAAM,MAAM,IAAI;AAAA,UAChE;AAAA,QACJ,OACK;AACD,gBAAM,eAAe,SAAS,MAAM,MAAM,IAAI;AAAA,QAClD;AAAA,MACJ;AACA,YAAM,SAAS;AACf,mBAAa,MAAM;AACnB,qBAAe,aAAa,KAAK,IAAI,YAAY,MAAM,UAAU;AAAA,IACrE;AACA,eAAW,MAAM,WAAW,UAAU;AAAA,EAC1C;AACA,eAAa,aAAa,aAAa,QAAQ,UAAU,UAAU,eAAe;AAClF,eAAa,cAAc,aAAa,SAAS,UAAU,WAAW,gBAAgB;AACtF,eAAa,gBAAgB;AAC7B,eAAa,eAAe;AAC5B,MAAI,YAAY;AACZ,iBAAa,cAAc,WAAW,CAAC,IAAI,WAAW,CAAC;AACvD,iBAAa,eAAe,WAAW,CAAC,IAAI,WAAW,CAAC;AAAA,EAC5D;AACA,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,QAAI,QAAQ,YAAY,CAAC;AACzB,QAAI,eAAe,MAAM;AACzB,UAAM,QAAQ,SAAS,cAAc,EAAE,IAAI,MAAM,aAAa;AAAA,EAClE;AACA,SAAO;AACX;AACA,SAAS,WAAW,OAAO,KAAK,OAAO,UAAU,WAAW;AACxD,MAAI,aAAa,QAAQ;AACzB,MAAI,aAAa,aAAa,MAAM,KAAK,SAAS,KAAK,CAAC;AACxD,MAAI,QAAQ,MAAM;AAClB,MAAI,OAAO,WAAW,QAAQ,MAAM;AACpC,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACV,QAAI,eAAe,WAAW;AAC9B,QAAI,gBAAgB,eAAe,aAAa,CAAC,IAAI,aAAa,CAAC,IAAI;AACvE,QAAI,WAAW,SAAS,QAAQ,WAAW,UAAU,QAAQ;AACzD,UAAI,eAAe,aAAa,WAAW,OAAO,SAAS,KAAK,IAAI;AACpE,UAAI,MAAM,SAAS,GAAG;AAClB,YAAI,eAAe,SAAS,aAAa,SAAS,OAAO;AACrD,qBAAW,IAAI,MAAM,IAAI;AACzB,oBAAU;AAAA,QACd;AAAA,MACJ;AACA,eAAS,aAAa;AAAA,IAC1B,OACK;AACD,UAAI,MAAM,SAAS,KAAK,MAAM,SAAS,OAAO,SAAS,UAAU,SAAS,UAAU;AACpF,eAAS,aAAa,IAAI,aAAa;AACvC,oBAAc,IAAI;AAClB,iBAAW,IAAI;AAAA,IACnB;AAAA,EACJ,OACK;AACD,eAAW,IAAI,MAAM,IAAI;AAAA,EAC7B;AACA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,QAAI,OAAO,SAAS,CAAC;AACrB,QAAI,QAAQ,IAAI,cAAc;AAC9B,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,UAAM,eAAe,CAAC,QAAQ,CAAC;AAC/B,QAAI,OAAO,WAAW,UAAU,UAAU;AACtC,YAAM,QAAQ,WAAW;AAAA,IAC7B,OACK;AACD,YAAM,QAAQ,cACR,YAAY,CAAC,IACb,SAAS,MAAM,IAAI;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,CAAC,SAAS;AAChB,UAAI,UAAU,MAAM,MAAM,SAAS,CAAC,MAAM,MAAM,CAAC,IAAI,IAAI,aAAa,IAAI;AAC1E,UAAI,YAAY,OAAO;AACvB,MAAC,cAAc,KAAK,OAAO,CAAC,EAAE,eACvB,OAAO,CAAC,IAAI,SACX,QAAQ,CAAC,aAAa,eAAe,OAAO,KAAK,KAAK;AAAA,IAClE,OACK;AACD,YAAM,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;AAAA,IACxC;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,IAAI;AAC5B,MAAI,OAAO,GAAG,WAAW,CAAC;AAC1B,SAAO,QAAQ,MAAQ,QAAQ,OACxB,QAAQ,OAAS,QAAQ,QACzB,QAAQ,QAAU,QAAQ,QAC1B,QAAQ,QAAU,QAAQ;AACrC;AACA,IAAI,eAAe,OAAO,UAAU,MAAM,EAAE,GAAG,SAAU,KAAK,IAAI;AAC9D,MAAI,EAAE,IAAI;AACV,SAAO;AACX,GAAG,CAAC,CAAC;AACL,SAAS,gBAAgB,IAAI;AACzB,MAAI,mBAAmB,EAAE,GAAG;AACxB,QAAI,aAAa,EAAE,GAAG;AAClB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,SAAS,MAAM,MAAM,WAAW,YAAY,gBAAgB;AACjE,MAAI,QAAQ,CAAC;AACb,MAAI,cAAc,CAAC;AACnB,MAAI,OAAO;AACX,MAAI,cAAc;AAClB,MAAI,mBAAmB;AACvB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,QAAI,KAAK,KAAK,OAAO,CAAC;AACtB,QAAI,OAAO,MAAM;AACb,UAAI,aAAa;AACb,gBAAQ;AACR,sBAAc;AAAA,MAClB;AACA,YAAM,KAAK,IAAI;AACf,kBAAY,KAAK,UAAU;AAC3B,aAAO;AACP,oBAAc;AACd,yBAAmB;AACnB,mBAAa;AACb;AAAA,IACJ;AACA,QAAI,UAAU,SAAS,IAAI,IAAI;AAC/B,QAAI,SAAS,aAAa,QAAQ,CAAC,gBAAgB,EAAE;AACrD,QAAI,CAAC,MAAM,SACL,iBAAiB,aAAa,UAAU,YACxC,aAAa,UAAU,WAAW;AACpC,UAAI,CAAC,YAAY;AACb,YAAI,QAAQ;AACR,gBAAM,KAAK,WAAW;AACtB,sBAAY,KAAK,gBAAgB;AACjC,wBAAc;AACd,6BAAmB;AAAA,QACvB,OACK;AACD,gBAAM,KAAK,EAAE;AACb,sBAAY,KAAK,OAAO;AAAA,QAC5B;AAAA,MACJ,WACS,QAAQ,aAAa;AAC1B,YAAI,QAAQ;AACR,cAAI,CAAC,MAAM;AACP,mBAAO;AACP,0BAAc;AACd,+BAAmB;AACnB,yBAAa;AAAA,UACjB;AACA,gBAAM,KAAK,IAAI;AACf,sBAAY,KAAK,aAAa,gBAAgB;AAC9C,yBAAe;AACf,8BAAoB;AACpB,iBAAO;AACP,uBAAa;AAAA,QACjB,OACK;AACD,cAAI,aAAa;AACb,oBAAQ;AACR,0BAAc;AACd,+BAAmB;AAAA,UACvB;AACA,gBAAM,KAAK,IAAI;AACf,sBAAY,KAAK,UAAU;AAC3B,iBAAO;AACP,uBAAa;AAAA,QACjB;AAAA,MACJ;AACA;AAAA,IACJ;AACA,kBAAc;AACd,QAAI,QAAQ;AACR,qBAAe;AACf,0BAAoB;AAAA,IACxB,OACK;AACD,UAAI,aAAa;AACb,gBAAQ;AACR,sBAAc;AACd,2BAAmB;AAAA,MACvB;AACA,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,MAAI,CAAC,MAAM,UAAU,CAAC,MAAM;AACxB,WAAO;AACP,kBAAc;AACd,uBAAmB;AAAA,EACvB;AACA,MAAI,aAAa;AACb,YAAQ;AAAA,EACZ;AACA,MAAI,MAAM;AACN,UAAM,KAAK,IAAI;AACf,gBAAY,KAAK,UAAU;AAAA,EAC/B;AACA,MAAI,MAAM,WAAW,GAAG;AACpB,kBAAc;AAAA,EAClB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;ACjfA,IAAI,WAAY,WAAY;AACxB,WAASE,UAAS,iBAAiB;AAC/B,QAAI,iBAAiB;AACjB,WAAK,mBAAmB;AAAA,IAC5B;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,KAAK,SAAU,OAAO,OAAO,SAAS,SAAS;AAC9D,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,KAAK;AACd,QAAI,OAAO,UAAU,YAAY;AAC7B,gBAAU;AACV,gBAAU;AACV,cAAQ;AAAA,IACZ;AACA,QAAI,CAAC,WAAW,CAAC,OAAO;AACpB,aAAO;AAAA,IACX;AACA,QAAI,iBAAiB,KAAK;AAC1B,QAAI,SAAS,QAAQ,kBAAkB,eAAe,gBAAgB;AAClE,cAAQ,eAAe,eAAe,KAAK;AAAA,IAC/C;AACA,QAAI,CAAC,GAAG,KAAK,GAAG;AACZ,SAAG,KAAK,IAAI,CAAC;AAAA,IACjB;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,EAAE,QAAQ,KAAK;AACvC,UAAI,GAAG,KAAK,EAAE,CAAC,EAAE,MAAM,SAAS;AAC5B,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,OAAO;AAAA,MACP,GAAG;AAAA,MACH;AAAA,MACA,KAAM,WAAW;AAAA,MACjB,YAAY,QAAQ;AAAA,IACxB;AACA,QAAI,YAAY,GAAG,KAAK,EAAE,SAAS;AACnC,QAAI,WAAW,GAAG,KAAK,EAAE,SAAS;AAClC,IAAC,YAAY,SAAS,aAChB,GAAG,KAAK,EAAE,OAAO,WAAW,GAAG,IAAI,IACnC,GAAG,KAAK,EAAE,KAAK,IAAI;AACzB,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,WAAW;AAC/C,QAAI,KAAK,KAAK;AACd,WAAO,CAAC,MAAM,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,SAAS,EAAE;AAAA,EACnD;AACA,EAAAA,UAAS,UAAU,MAAM,SAAU,WAAW,SAAS;AACnD,QAAI,KAAK,KAAK;AACd,QAAI,CAAC,IAAI;AACL,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW;AACZ,WAAK,aAAa,CAAC;AACnB,aAAO;AAAA,IACX;AACA,QAAI,SAAS;AACT,UAAI,GAAG,SAAS,GAAG;AACf,YAAI,UAAU,CAAC;AACf,iBAAS,IAAI,GAAG,IAAI,GAAG,SAAS,EAAE,QAAQ,IAAI,GAAG,KAAK;AAClD,cAAI,GAAG,SAAS,EAAE,CAAC,EAAE,MAAM,SAAS;AAChC,oBAAQ,KAAK,GAAG,SAAS,EAAE,CAAC,CAAC;AAAA,UACjC;AAAA,QACJ;AACA,WAAG,SAAS,IAAI;AAAA,MACpB;AACA,UAAI,GAAG,SAAS,KAAK,GAAG,SAAS,EAAE,WAAW,GAAG;AAC7C,eAAO,GAAG,SAAS;AAAA,MACvB;AAAA,IACJ,OACK;AACD,aAAO,GAAG,SAAS;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,UAAU,SAAU,WAAW;AAC9C,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC/B;AACA,QAAI,CAAC,KAAK,YAAY;AAClB,aAAO;AAAA,IACX;AACA,QAAI,KAAK,KAAK,WAAW,SAAS;AAClC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,IAAI;AACJ,UAAI,SAAS,KAAK;AAClB,UAAIC,OAAM,GAAG;AACb,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,YAAI,QAAQ,GAAG,CAAC;AAChB,YAAI,kBACG,eAAe,UACf,MAAM,SAAS,QACf,CAAC,eAAe,OAAO,WAAW,MAAM,KAAK,GAAG;AACnD;AAAA,QACJ;AACA,gBAAQ,QAAQ;AAAA,UACZ,KAAK;AACD,kBAAM,EAAE,KAAK,MAAM,GAAG;AACtB;AAAA,UACJ,KAAK;AACD,kBAAM,EAAE,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC/B;AAAA,UACJ,KAAK;AACD,kBAAM,EAAE,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACxC;AAAA,UACJ;AACI,kBAAM,EAAE,MAAM,MAAM,KAAK,IAAI;AAC7B;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AACA,sBAAkB,eAAe,gBAC1B,eAAe,aAAa,SAAS;AAC5C,WAAO;AAAA,EACX;AACA,EAAAD,UAAS,UAAU,qBAAqB,SAAU,MAAM;AACpD,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC/B;AACA,QAAI,CAAC,KAAK,YAAY;AAClB,aAAO;AAAA,IACX;AACA,QAAI,KAAK,KAAK,WAAW,IAAI;AAC7B,QAAI,iBAAiB,KAAK;AAC1B,QAAI,IAAI;AACJ,UAAI,SAAS,KAAK;AAClB,UAAI,MAAM,KAAK,SAAS,CAAC;AACzB,UAAIC,OAAM,GAAG;AACb,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,YAAI,QAAQ,GAAG,CAAC;AAChB,YAAI,kBACG,eAAe,UACf,MAAM,SAAS,QACf,CAAC,eAAe,OAAO,MAAM,MAAM,KAAK,GAAG;AAC9C;AAAA,QACJ;AACA,gBAAQ,QAAQ;AAAA,UACZ,KAAK;AACD,kBAAM,EAAE,KAAK,GAAG;AAChB;AAAA,UACJ,KAAK;AACD,kBAAM,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC;AACzB;AAAA,UACJ,KAAK;AACD,kBAAM,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAClC;AAAA,UACJ;AACI,kBAAM,EAAE,MAAM,KAAK,KAAK,MAAM,GAAG,SAAS,CAAC,CAAC;AAC5C;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AACA,sBAAkB,eAAe,gBAC1B,eAAe,aAAa,IAAI;AACvC,WAAO;AAAA,EACX;AACA,SAAOD;AACX,EAAE;AACF,IAAO,mBAAQ;;;AChKf,IAAI,MAAM;AACV,IAAI,YAAI,iBAAiB;AACrB,QAAM,KAAK,IAAI,OAAO,oBACd,OAAO,UAAU,OAAO,OAAO,aAAa,OAAO,OAAO,eAC3D,GAAG,CAAC;AACf;AAEO,IAAI,mBAAmB;AACvB,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;;;ACZ1B,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;;;ACA/B,IAAI,YAAmB;AACvB,IAAI,UAAU;AACd,SAAS,gBAAgB,KAAK;AAC1B,SAAO,MAAM,WAAW,MAAM,CAAC;AACnC;AACA,IAAI,WAAW,CAAC;AAChB,IAAI,eAAe,CAAC;AACpB,IAAI,kBAAyB,OAAO;AACpC,IAAI,MAAM,KAAK;AACf,IAAI,gBAAiB,WAAY;AAC7B,WAASE,iBAAgB;AAAA,EACzB;AACA,EAAAA,eAAc,UAAU,oBAAoB,SAAU,GAAG;AACrD,WAAOA,eAAc,kBAAkB,MAAM,CAAC;AAAA,EAClD;AACA,EAAAA,eAAc,UAAU,cAAc,SAAU,KAAK;AACjD,SAAK,IAAI,IAAI,CAAC;AACd,SAAK,IAAI,IAAI,CAAC;AAAA,EAClB;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,KAAK;AAC9C,SAAK,SAAS,IAAI,CAAC;AACnB,SAAK,SAAS,IAAI,CAAC;AAAA,EACvB;AACA,EAAAA,eAAc,UAAU,UAAU,SAAU,KAAK;AAC7C,SAAK,QAAQ,IAAI,CAAC;AAClB,SAAK,QAAQ,IAAI,CAAC;AAAA,EACtB;AACA,EAAAA,eAAc,UAAU,YAAY,SAAU,KAAK;AAC/C,SAAK,UAAU,IAAI,CAAC;AACpB,SAAK,UAAU,IAAI,CAAC;AAAA,EACxB;AACA,EAAAA,eAAc,UAAU,qBAAqB,WAAY;AACrD,WAAO,gBAAgB,KAAK,QAAQ,KAC7B,gBAAgB,KAAK,CAAC,KACtB,gBAAgB,KAAK,CAAC,KACtB,gBAAgB,KAAK,SAAS,CAAC,KAC/B,gBAAgB,KAAK,SAAS,CAAC,KAC/B,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK;AAAA,EACrC;AACA,EAAAA,eAAc,UAAU,kBAAkB,WAAY;AAClD,QAAI,kBAAkB,KAAK,UAAU,KAAK,OAAO;AACjD,QAAI,qBAAqB,KAAK,mBAAmB;AACjD,QAAI,IAAI,KAAK;AACb,QAAI,EAAE,sBAAsB,kBAAkB;AAC1C,UAAI,GAAG;AACH,kBAAU,CAAC;AACX,aAAK,eAAe;AAAA,MACxB;AACA;AAAA,IACJ;AACA,QAAI,KAAY,OAAO;AACvB,QAAI,oBAAoB;AACpB,WAAK,kBAAkB,CAAC;AAAA,IAC5B,OACK;AACD,gBAAU,CAAC;AAAA,IACf;AACA,QAAI,iBAAiB;AACjB,UAAI,oBAAoB;AACpB,QAAO,IAAI,GAAG,iBAAiB,CAAC;AAAA,MACpC,OACK;AACD,QAAO,KAAK,GAAG,eAAe;AAAA,MAClC;AAAA,IACJ;AACA,SAAK,YAAY;AACjB,SAAK,yBAAyB,CAAC;AAAA,EACnC;AACA,EAAAA,eAAc,UAAU,2BAA2B,SAAU,GAAG;AAC5D,QAAI,mBAAmB,KAAK;AAC5B,QAAI,oBAAoB,QAAQ,qBAAqB,GAAG;AACpD,WAAK,eAAe,QAAQ;AAC5B,UAAI,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK;AAClC,UAAI,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK;AAClC,UAAI,OAAO,SAAS,CAAC,IAAI,QAAQ,mBAAmB,QAAQ,SAAS,CAAC,KAAK;AAC3E,UAAI,OAAO,SAAS,CAAC,IAAI,QAAQ,mBAAmB,QAAQ,SAAS,CAAC,KAAK;AAC3E,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AACR,QAAE,CAAC,KAAK;AAAA,IACZ;AACA,SAAK,eAAe,KAAK,gBAAuB,OAAO;AACvD,IAAO,OAAO,KAAK,cAAc,CAAC;AAAA,EACtC;AACA,EAAAA,eAAc,UAAU,uBAAuB,WAAY;AACvD,QAAI,gBAAgB;AACpB,QAAI,YAAY,CAAC;AACjB,WAAO,eAAe;AAClB,gBAAU,KAAK,aAAa;AAC5B,sBAAgB,cAAc;AAAA,IAClC;AACA,WAAO,gBAAgB,UAAU,IAAI,GAAG;AACpC,oBAAc,gBAAgB;AAAA,IAClC;AACA,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,eAAc,UAAU,oBAAoB,SAAU,GAAG;AACrD,QAAI,CAAC,GAAG;AACJ;AAAA,IACJ;AACA,QAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC,QAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC,QAAI,WAAW,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,QAAI,SAAS,KAAK,KAAK,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3D,SAAK,KAAK,KAAK,EAAE,IAAI,KAAK,IAAI,MAAM;AACpC,SAAK,KAAK,KAAK,EAAE;AACjB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,WAAW,CAAC;AACjB,SAAK,IAAI,CAAC,EAAE,CAAC;AACb,SAAK,IAAI,CAAC,EAAE,CAAC;AACb,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,eAAc,UAAU,qBAAqB,WAAY;AACrD,QAAI,CAAC,KAAK,WAAW;AACjB;AAAA,IACJ;AACA,QAAI,SAAS,KAAK;AAClB,QAAI,IAAI,KAAK;AACb,QAAI,UAAU,OAAO,WAAW;AAC5B,aAAO,eAAe,OAAO,gBAAuB,OAAO;AAC3D,MAAO,IAAI,cAAc,OAAO,cAAc,CAAC;AAC/C,UAAI;AAAA,IACR;AACA,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,MAAM,IAAI;AACV,sBAAgB,CAAC,IAAI;AACrB,sBAAgB,CAAC,IAAI;AACrB,MAAO,IAAI,cAAc,GAAG,eAAe;AAC3C,mBAAa,CAAC,KAAK;AACnB,mBAAa,CAAC,KAAK;AACnB,UAAI;AAAA,IACR;AACA,SAAK,kBAAkB,CAAC;AAAA,EAC5B;AACA,EAAAA,eAAc,UAAU,iBAAiB,SAAU,KAAK;AACpD,QAAI,IAAI,KAAK;AACb,UAAM,OAAO,CAAC;AACd,QAAI,CAAC,GAAG;AACJ,UAAI,CAAC,IAAI;AACT,UAAI,CAAC,IAAI;AACT,aAAO;AAAA,IACX;AACA,QAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5C,QAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5C,QAAI,EAAE,CAAC,IAAI,GAAG;AACV,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACnB;AACA,QAAI,EAAE,CAAC,IAAI,GAAG;AACV,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,wBAAwB,SAAU,GAAG,GAAG;AAC5D,QAAI,KAAK,CAAC,GAAG,CAAC;AACd,QAAI,eAAe,KAAK;AACxB,QAAI,cAAc;AACd,MAAO,eAAe,IAAI,IAAI,YAAY;AAAA,IAC9C;AACA,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,yBAAyB,SAAU,GAAG,GAAG;AAC7D,QAAI,KAAK,CAAC,GAAG,CAAC;AACd,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACX,MAAO,eAAe,IAAI,IAAI,SAAS;AAAA,IAC3C;AACA,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,eAAe,WAAY;AAC/C,QAAI,IAAI,KAAK;AACb,WAAO,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,SAAS,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,QAC/C,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IACxC;AAAA,EACV;AACA,EAAAA,eAAc,UAAU,gBAAgB,SAAU,QAAQ;AACtD,kBAAc,MAAM,MAAM;AAAA,EAC9B;AACA,EAAAA,eAAc,oBAAoB,SAAU,QAAQ,GAAG;AACnD,QAAI,KAAK,CAAC;AACV,QAAI,KAAK,OAAO,WAAW;AAC3B,QAAI,KAAK,OAAO,WAAW;AAC3B,QAAI,KAAK,OAAO;AAChB,QAAI,KAAK,OAAO;AAChB,QAAI,KAAK,OAAO;AAChB,QAAI,KAAK,OAAO;AAChB,QAAI,WAAW,OAAO,YAAY;AAClC,QAAI,IAAI,OAAO;AACf,QAAI,IAAI,OAAO;AACf,QAAI,QAAQ,OAAO,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI;AACpD,QAAI,QAAQ,OAAO,QAAQ,KAAK,IAAI,CAAC,OAAO,KAAK,IAAI;AACrD,QAAI,MAAM,MAAM,MAAM,IAAI;AACtB,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,QAAE,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,KAAK;AAC/B,QAAE,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,KAAK;AAAA,IACnC,OACK;AACD,QAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAAA,IAClB;AACA,MAAE,CAAC,IAAI;AACP,MAAE,CAAC,IAAI;AACP,MAAE,CAAC,IAAI,QAAQ;AACf,MAAE,CAAC,IAAI,QAAQ;AACf,gBAAmB,OAAO,GAAG,GAAG,QAAQ;AACxC,MAAE,CAAC,KAAK,KAAK;AACb,MAAE,CAAC,KAAK,KAAK;AACb,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,mBAAoB,WAAY;AAC1C,QAAI,QAAQA,eAAc;AAC1B,UAAM,SACF,MAAM,SACF,MAAM,mBAAmB;AACjC,UAAM,IACF,MAAM,IACF,MAAM,UACF,MAAM,UACF,MAAM,QACF,MAAM,QACF,MAAM,WACF,MAAM,UACF,MAAM,UAAU;AAAA,EACpD,EAAG;AACH,SAAOA;AACX,EAAE;AAEK,IAAI,sBAAsB;AAAA,EAC7B;AAAA,EAAK;AAAA,EAAK;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EAAS;AACnG;AACO,SAAS,cAAc,QAAQ,QAAQ;AAC1C,WAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,QAAI,WAAW,oBAAoB,CAAC;AACpC,WAAO,QAAQ,IAAI,OAAO,QAAQ;AAAA,EACtC;AACJ;AACA,IAAO,wBAAQ;;;ACnPf,IAAI,cAAc;AAAA,EACd,QAAQ,SAAU,GAAG;AACjB,WAAO;AAAA,EACX;AAAA,EACA,aAAa,SAAU,GAAG;AACtB,WAAO,IAAI;AAAA,EACf;AAAA,EACA,cAAc,SAAU,GAAG;AACvB,WAAO,KAAK,IAAI;AAAA,EACpB;AAAA,EACA,gBAAgB,SAAU,GAAG;AACzB,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,MAAM,IAAI;AAAA,IACrB;AACA,WAAO,QAAQ,EAAE,KAAK,IAAI,KAAK;AAAA,EACnC;AAAA,EACA,SAAS,SAAU,GAAG;AAClB,WAAO,IAAI,IAAI;AAAA,EACnB;AAAA,EACA,UAAU,SAAU,GAAG;AACnB,WAAO,EAAE,IAAI,IAAI,IAAI;AAAA,EACzB;AAAA,EACA,YAAY,SAAU,GAAG;AACrB,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,MAAM,IAAI,IAAI;AAAA,IACzB;AACA,WAAO,QAAQ,KAAK,KAAK,IAAI,IAAI;AAAA,EACrC;AAAA,EACA,WAAW,SAAU,GAAG;AACpB,WAAO,IAAI,IAAI,IAAI;AAAA,EACvB;AAAA,EACA,YAAY,SAAU,GAAG;AACrB,WAAO,IAAK,EAAE,IAAI,IAAI,IAAI;AAAA,EAC9B;AAAA,EACA,cAAc,SAAU,GAAG;AACvB,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,MAAM,IAAI,IAAI,IAAI;AAAA,IAC7B;AACA,WAAO,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,EAC1C;AAAA,EACA,WAAW,SAAU,GAAG;AACpB,WAAO,IAAI,IAAI,IAAI,IAAI;AAAA,EAC3B;AAAA,EACA,YAAY,SAAU,GAAG;AACrB,WAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,EACjC;AAAA,EACA,cAAc,SAAU,GAAG;AACvB,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,MAAM,IAAI,IAAI,IAAI,IAAI;AAAA,IACjC;AACA,WAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;AAAA,EAC7C;AAAA,EACA,cAAc,SAAU,GAAG;AACvB,WAAO,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AAAA,EACvC;AAAA,EACA,eAAe,SAAU,GAAG;AACxB,WAAO,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AAAA,EACnC;AAAA,EACA,iBAAiB,SAAU,GAAG;AAC1B,WAAO,OAAO,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,eAAe,SAAU,GAAG;AACxB,WAAO,MAAM,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,EAC7C;AAAA,EACA,gBAAgB,SAAU,GAAG;AACzB,WAAO,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,EAChD;AAAA,EACA,kBAAkB,SAAU,GAAG;AAC3B,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,IACrC;AACA,WAAO,OAAO,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,IAAI;AAAA,EAChD;AAAA,EACA,YAAY,SAAU,GAAG;AACrB,WAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,EAClC;AAAA,EACA,aAAa,SAAU,GAAG;AACtB,WAAO,KAAK,KAAK,IAAK,EAAE,IAAI,CAAE;AAAA,EAClC;AAAA,EACA,eAAe,SAAU,GAAG;AACxB,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI;AAAA,IAC1C;AACA,WAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI;AAAA,EAChD;AAAA,EACA,WAAW,SAAU,GAAG;AACpB,QAAI;AACJ,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,CAAC,KAAK,IAAI,GAAG;AACb,UAAI;AACJ,UAAI,IAAI;AAAA,IACZ,OACK;AACD,UAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AAAA,IACzC;AACA,WAAO,EAAE,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,EAAE,IAChC,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC;AAAA,EAC9C;AAAA,EACA,YAAY,SAAU,GAAG;AACrB,QAAI;AACJ,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,CAAC,KAAK,IAAI,GAAG;AACb,UAAI;AACJ,UAAI,IAAI;AAAA,IACZ,OACK;AACD,UAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AAAA,IACzC;AACA,WAAQ,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,IACzB,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI;AAAA,EAClD;AAAA,EACA,cAAc,SAAU,GAAG;AACvB,QAAI;AACJ,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,CAAC,KAAK,IAAI,GAAG;AACb,UAAI;AACJ,UAAI,IAAI;AAAA,IACZ,OACK;AACD,UAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AAAA,IACzC;AACA,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,QAAQ,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,EAAE,IACtC,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC;AAAA,IAC9C;AACA,WAAO,IAAI,KAAK,IAAI,GAAG,OAAO,KAAK,EAAE,IAC/B,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM;AAAA,EACxD;AAAA,EACA,QAAQ,SAAU,GAAG;AACjB,QAAI,IAAI;AACR,WAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAAA,EAClC;AAAA,EACA,SAAS,SAAU,GAAG;AAClB,QAAI,IAAI;AACR,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,EACzC;AAAA,EACA,WAAW,SAAU,GAAG;AACpB,QAAI,IAAI,UAAU;AAClB,SAAK,KAAK,KAAK,GAAG;AACd,aAAO,OAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAAA,IACzC;AACA,WAAO,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,EACrD;AAAA,EACA,UAAU,SAAU,GAAG;AACnB,WAAO,IAAI,YAAY,UAAU,IAAI,CAAC;AAAA,EAC1C;AAAA,EACA,WAAW,SAAU,GAAG;AACpB,QAAI,IAAK,IAAI,MAAO;AAChB,aAAO,SAAS,IAAI;AAAA,IACxB,WACS,IAAK,IAAI,MAAO;AACrB,aAAO,UAAU,KAAM,MAAM,QAAS,IAAI;AAAA,IAC9C,WACS,IAAK,MAAM,MAAO;AACvB,aAAO,UAAU,KAAM,OAAO,QAAS,IAAI;AAAA,IAC/C,OACK;AACD,aAAO,UAAU,KAAM,QAAQ,QAAS,IAAI;AAAA,IAChD;AAAA,EACJ;AAAA,EACA,aAAa,SAAU,GAAG;AACtB,QAAI,IAAI,KAAK;AACT,aAAO,YAAY,SAAS,IAAI,CAAC,IAAI;AAAA,IACzC;AACA,WAAO,YAAY,UAAU,IAAI,IAAI,CAAC,IAAI,MAAM;AAAA,EACpD;AACJ;AACA,IAAO,iBAAQ;;;ACjMf,IAAI,UAAU,KAAK;AACnB,IAAI,WAAW,KAAK;AACpB,IAAIC,WAAU;AACd,IAAI,kBAAkB;AACtB,IAAI,aAAa,SAAS,CAAC;AAC3B,IAAI,YAAY,IAAI;AACpB,IAAI,MAAMC,QAAS;AACnB,IAAI,MAAMA,QAAS;AACnB,IAAI,MAAMA,QAAS;AACnB,SAAS,aAAa,KAAK;AACvB,SAAO,MAAM,CAACD,YAAW,MAAMA;AACnC;AACA,SAASE,iBAAgB,KAAK;AAC1B,SAAO,MAAMF,YAAW,MAAM,CAACA;AACnC;AACO,SAAS,QAAQ,IAAI,IAAI,IAAI,IAAI,GAAG;AACvC,MAAI,OAAO,IAAI;AACf,SAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,IAAI,MACpC,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO;AACvC;AACO,SAAS,kBAAkB,IAAI,IAAI,IAAI,IAAI,GAAG;AACjD,MAAI,OAAO,IAAI;AACf,SAAO,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,KAAK,QAC9C,KAAK,MAAM,IAAI;AAC1B;AACO,SAAS,YAAY,IAAI,IAAI,IAAI,IAAI,KAAKG,QAAO;AACpD,MAAI,IAAI,KAAK,KAAK,KAAK,MAAM;AAC7B,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI;AAC3B,MAAI,IAAI,KAAK,KAAK;AAClB,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI;AACR,MAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;AACpC,QAAI,aAAa,CAAC,GAAG;AACjB,MAAAA,OAAM,CAAC,IAAI;AAAA,IACf,OACK;AACD,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ,OACK;AACD,QAAI,OAAO,IAAI,IAAI,IAAI,IAAI;AAC3B,QAAI,aAAa,IAAI,GAAG;AACpB,UAAI,IAAI,IAAI;AACZ,UAAI,KAAK,CAAC,IAAI,IAAI;AAClB,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ,WACS,OAAO,GAAG;AACf,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI;AACjC,UAAI,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI;AACjC,UAAI,KAAK,GAAG;AACR,aAAK,CAAC,QAAQ,CAAC,IAAI,SAAS;AAAA,MAChC,OACK;AACD,aAAK,QAAQ,IAAI,SAAS;AAAA,MAC9B;AACA,UAAI,KAAK,GAAG;AACR,aAAK,CAAC,QAAQ,CAAC,IAAI,SAAS;AAAA,MAChC,OACK;AACD,aAAK,QAAQ,IAAI,SAAS;AAAA,MAC9B;AACA,UAAI,MAAM,CAAC,KAAK,KAAK,QAAQ,IAAI;AACjC,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ,OACK;AACD,UAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,SAAS,IAAI,IAAI,CAAC;AACzD,UAAI,QAAQ,KAAK,KAAK,CAAC,IAAI;AAC3B,UAAI,QAAQ,SAAS,CAAC;AACtB,UAAI,MAAM,KAAK,IAAI,KAAK;AACxB,UAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,QAAQ,IAAI;AACvC,UAAI,MAAM,CAAC,IAAI,SAAS,MAAM,aAAa,KAAK,IAAI,KAAK,OAAO,IAAI;AACpE,UAAI,MAAM,CAAC,IAAI,SAAS,MAAM,aAAa,KAAK,IAAI,KAAK,OAAO,IAAI;AACpE,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,aAAa,IAAI,IAAI,IAAI,IAAIC,UAAS;AAClD,MAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI;AAC/B,MAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACvC,MAAI,IAAI,IAAI,KAAK,IAAI;AACrB,MAAI,IAAI;AACR,MAAI,aAAa,CAAC,GAAG;AACjB,QAAIF,iBAAgB,CAAC,GAAG;AACpB,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAE,SAAQ,GAAG,IAAI;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ,OACK;AACD,QAAI,OAAO,IAAI,IAAI,IAAI,IAAI;AAC3B,QAAI,aAAa,IAAI,GAAG;AACpB,MAAAA,SAAQ,CAAC,IAAI,CAAC,KAAK,IAAI;AAAA,IAC3B,WACS,OAAO,GAAG;AACf,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,SAAQ,GAAG,IAAI;AAAA,MACnB;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,SAAQ,GAAG,IAAI;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,eAAe,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK;AACnD,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,QAAQ,MAAM,OAAO,IAAI;AAC7B,MAAI,QAAQ,MAAM,OAAO,IAAI;AAC7B,MAAI,SAAS,OAAO,QAAQ,IAAI;AAChC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACb;AACO,SAAS,kBAAkB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK;AACzE,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,WAAS,KAAK,GAAG,KAAK,GAAG,MAAM,MAAM;AACjC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AACnC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AACnC,SAAK,WAAa,KAAK,GAAG;AAC1B,QAAI,KAAK,GAAG;AACR,UAAI;AACJ,UAAI;AAAA,IACR;AAAA,EACJ;AACA,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,QAAI,WAAW,iBAAiB;AAC5B;AAAA,IACJ;AACA,WAAO,IAAI;AACX,WAAO,IAAI;AACX,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,SAAK,WAAa,KAAK,GAAG;AAC1B,QAAI,QAAQ,KAAK,KAAK,GAAG;AACrB,UAAI;AACJ,UAAI;AAAA,IACR,OACK;AACD,UAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,UAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,WAAK,WAAa,KAAK,GAAG;AAC1B,UAAI,QAAQ,KAAK,KAAK,GAAG;AACrB,YAAI;AACJ,YAAI;AAAA,MACR,OACK;AACD,oBAAY;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,KAAK;AACL,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AAClC,QAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AAAA,EACtC;AACA,SAAO,SAAS,CAAC;AACrB;AACO,SAAS,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW;AACnE,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,OAAO,IAAI;AACf,WAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACjC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AACb,SAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAChC,SAAK;AACL,SAAK;AAAA,EACT;AACA,SAAO;AACX;AACO,SAAS,YAAY,IAAI,IAAI,IAAI,GAAG;AACvC,MAAI,OAAO,IAAI;AACf,SAAO,QAAQ,OAAO,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AACrD;AACO,SAAS,sBAAsB,IAAI,IAAI,IAAI,GAAG;AACjD,SAAO,MAAM,IAAI,MAAM,KAAK,MAAM,KAAK,KAAK;AAChD;AACO,SAAS,gBAAgB,IAAI,IAAI,IAAI,KAAKD,QAAO;AACpD,MAAI,IAAI,KAAK,IAAI,KAAK;AACtB,MAAI,IAAI,KAAK,KAAK;AAClB,MAAI,IAAI,KAAK;AACb,MAAI,IAAI;AACR,MAAI,aAAa,CAAC,GAAG;AACjB,QAAID,iBAAgB,CAAC,GAAG;AACpB,UAAI,KAAK,CAAC,IAAI;AACd,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAC,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ,OACK;AACD,QAAI,OAAO,IAAI,IAAI,IAAI,IAAI;AAC3B,QAAI,aAAa,IAAI,GAAG;AACpB,UAAI,KAAK,CAAC,KAAK,IAAI;AACnB,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ,WACS,OAAO,GAAG;AACf,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,CAAC,IAAI,aAAa,IAAI;AAChC,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACpB,QAAAA,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,IAAI,IAAI,IAAI;AAC1C,MAAI,UAAU,KAAK,KAAK,IAAI;AAC5B,MAAI,YAAY,GAAG;AACf,WAAO;AAAA,EACX,OACK;AACD,YAAQ,KAAK,MAAM;AAAA,EACvB;AACJ;AACO,SAAS,mBAAmB,IAAI,IAAI,IAAI,GAAG,KAAK;AACnD,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,MAAI,QAAQ,MAAM,OAAO,IAAI;AAC7B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACb;AACO,SAAS,sBAAsB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK;AACrE,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,IAAI;AACR,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,WAAS,KAAK,GAAG,KAAK,GAAG,MAAM,MAAM;AACjC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,EAAE;AACnC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,EAAE;AACnC,QAAI,KAAK,WAAa,KAAK,GAAG;AAC9B,QAAI,KAAK,GAAG;AACR,UAAI;AACJ,UAAI;AAAA,IACR;AAAA,EACJ;AACA,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,QAAI,WAAW,iBAAiB;AAC5B;AAAA,IACJ;AACA,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AACf,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,QAAI,KAAK,WAAa,KAAK,GAAG;AAC9B,QAAI,QAAQ,KAAK,KAAK,GAAG;AACrB,UAAI;AACJ,UAAI;AAAA,IACR,OACK;AACD,UAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,UAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,IAAI;AACrC,UAAI,KAAK,WAAa,KAAK,GAAG;AAC9B,UAAI,QAAQ,KAAK,KAAK,GAAG;AACrB,YAAI;AACJ,YAAI;AAAA,MACR,OACK;AACD,oBAAY;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,KAAK;AACL,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AAClC,QAAI,CAAC,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AAAA,EACtC;AACA,SAAO,SAAS,CAAC;AACrB;AACO,SAAS,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW;AAC/D,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,OAAO,IAAI;AACf,WAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACjC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC;AACjC,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AACb,SAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAChC,SAAK;AACL,SAAK;AAAA,EACT;AACA,SAAO;AACX;;;ACtVA,IAAI,SAAS;AACN,SAAS,sBAAsB,gBAAgB;AAClD,MAAI,QAAQ,kBAAkB,OAAO,KAAK,cAAc;AACxD,MAAI,OAAO;AACP,QAAI,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG;AAC/B,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC;AACzB,QAAI,MAAM,MAAM,MAAM,MAAM,GAAG,GAAG;AAC9B;AAAA,IACJ;AACA,QAAI,UAAU,CAAC;AACf,WAAO,SAAU,GAAG;AAChB,aAAO,KAAK,IACN,IAAI,KAAK,IACT,IACA,YAAY,GAAG,KAAK,KAAK,GAAG,GAAG,OAAO,KAAK,QAAQ,GAAG,KAAK,KAAK,GAAG,QAAQ,CAAC,CAAC;AAAA,IACvF;AAAA,EACJ;AACJ;;;ACnBA,IAAI,OAAQ,WAAY;AACpB,WAASE,MAAK,MAAM;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,QAAQ,KAAK,QAAQ;AAC1B,SAAK,SAAS,KAAK,SAAS;AAC5B,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,UAAU,KAAK,UAAU,KAAK,MAAM;AAAA,EAC7C;AACA,EAAAA,MAAK,UAAU,OAAO,SAAU,YAAY,WAAW;AACnD,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,aAAa,aAAa,KAAK;AACpC,WAAK,UAAU;AAAA,IACnB;AACA,QAAI,KAAK,SAAS;AACd,WAAK,eAAe;AACpB;AAAA,IACJ;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,cAAc,aAAa,KAAK,aAAa,KAAK;AACtD,QAAI,UAAU,cAAc;AAC5B,QAAI,UAAU,GAAG;AACb,gBAAU;AAAA,IACd;AACA,cAAU,KAAK,IAAI,SAAS,CAAC;AAC7B,QAAI,aAAa,KAAK;AACtB,QAAI,WAAW,aAAa,WAAW,OAAO,IAAI;AAClD,SAAK,QAAQ,QAAQ;AACrB,QAAI,YAAY,GAAG;AACf,UAAI,KAAK,MAAM;AACX,YAAI,YAAY,cAAc;AAC9B,aAAK,aAAa,aAAa;AAC/B,aAAK,cAAc;AACnB,aAAK,UAAU;AAAA,MACnB,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,UAAU,QAAQ,WAAY;AAC/B,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,MAAK,UAAU,SAAS,WAAY;AAChC,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,MAAK,UAAU,YAAY,SAAU,QAAQ;AACzC,SAAK,SAAS;AACd,SAAK,aAAa,WAAW,MAAM,IAC7B,SACA,eAAY,MAAM,KAAK,sBAAsB,MAAM;AAAA,EAC7D;AACA,SAAOA;AACX,EAAE;AACF,IAAO,eAAQ;;;AC5Df,IAAI,YAAY,KAAK;AACd,SAAS,eAAe,OAAO;AAClC,MAAI;AACJ,MAAI,CAAC,SAAS,UAAU,eAAe;AACnC,YAAQ;AAAA,EACZ,WACS,OAAO,UAAU,YAAY,MAAM,QAAQ,MAAM,IAAI,IAAI;AAC9D,QAAI,MAAM,MAAM,KAAK;AACrB,QAAI,KAAK;AACL,cAAQ,SAAS,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI;AACxD,gBAAU,IAAI,CAAC;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,SAAS,WAAW,OAAO,IAAI;AAAA,EACnC;AACJ;AACA,IAAIC,WAAU;AACP,SAASC,cAAa,WAAW;AACpC,SAAO,YAAYD,YAAW,YAAY,CAACA;AAC/C;AACO,SAAS,OAAO,WAAW;AAC9B,SAAO,UAAU,YAAY,GAAG,IAAI;AACxC;AACO,SAAS,OAAO,WAAW;AAC9B,SAAO,UAAU,YAAY,GAAG,IAAI;AACxC;AAIO,SAAS,aAAa,GAAG;AAC5B,SAAO,YACD,OAAO,EAAE,CAAC,CAAC,IAAI,MACf,OAAO,EAAE,CAAC,CAAC,IAAI,MACf,OAAO,EAAE,CAAC,CAAC,IAAI,MACf,OAAO,EAAE,CAAC,CAAC,IAAI,MACf,OAAO,EAAE,CAAC,CAAC,IAAI,MACf,OAAO,EAAE,CAAC,CAAC,IACX;AACV;AACO,IAAI,uBAAuB;AAAA,EAC9B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AACZ;AACO,SAASE,aAAY,GAAG,YAAY,cAAc;AACrD,MAAI,iBAAiB,OAAO;AACxB,SAAK,aAAa;AAAA,EACtB,WACS,iBAAiB,UAAU;AAChC,SAAK,aAAa;AAAA,EACtB;AACA,SAAO;AACX;AACO,SAAS,UAAU,OAAO;AAC7B,SAAO,UACC,MAAM,cAAc,MAAM,iBAAiB,MAAM;AAC7D;AACO,SAAS,aAAa,aAAa;AACtC,MAAI,QAAQ,YAAY;AACxB,MAAI,cAAc,YAAY,eAAe;AAC7C,SAAO;AAAA,IACH,MAAM;AAAA,KACL,MAAM,cAAc,GAAG,QAAQ,CAAC;AAAA,KAChC,MAAM,iBAAiB,GAAG,QAAQ,CAAC;AAAA,KACnC,MAAM,iBAAiB,GAAG,QAAQ,CAAC;AAAA,IACpC,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,EACjB,EAAE,KAAK,GAAG;AACd;AAWO,SAAS,eAAe,KAAK;AAChC,SAAO,OAAQ,CAAC,CAAC,IAAI;AACzB;AACO,SAAS,aAAa,KAAK;AAC9B,SAAO,OAAQ,CAAC,CAAC,IAAI;AACzB;AACO,SAAS,UAAU,KAAK;AAC3B,SAAO,eAAe,GAAG,KAAK,aAAa,GAAG;AAClD;AACO,SAAS,iBAAiB,KAAK;AAClC,SAAO,IAAI,SAAS;AACxB;AACO,SAAS,iBAAiB,KAAK;AAClC,SAAO,IAAI,SAAS;AACxB;AACO,SAAS,WAAW,KAAK;AAC5B,SAAO,QAAQ,IAAI,SAAS,YACrB,IAAI,SAAS;AACxB;AACO,SAAS,SAAS,IAAI;AACzB,SAAO,UAAU,KAAK;AAC1B;AACO,SAAS,iBAAiB,IAAI;AACjC,MAAIC,SAAQ,GAAG,eAAe;AAC9B,MAAI,OAAO,KAAK,IAAIA,OAAM,CAAC,GAAGA,OAAM,CAAC,CAAC;AACtC,SAAO,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC;AAC/D;AACO,SAAS,sBAAsB,WAAW;AAC7C,MAAI,IAAI,UAAU,KAAK;AACvB,MAAI,IAAI,UAAU,KAAK;AACvB,MAAI,YAAY,UAAU,YAAY,KAAK;AAC3C,MAAI,SAAS,UAAU,UAAU,QAAQ,CAAC;AAC1C,MAAI,SAAS,UAAU,UAAU,QAAQ,CAAC;AAC1C,MAAI,QAAQ,UAAU,SAAS;AAC/B,MAAI,QAAQ,UAAU,SAAS;AAC/B,MAAI,MAAM,CAAC;AACX,MAAI,KAAK,GAAG;AACR,QAAI,KAAK,eAAe,IAAI,QAAQ,IAAI,KAAK;AAAA,EACjD;AACA,MAAI,UAAU;AACV,QAAI,KAAK,YAAY,WAAW,GAAG;AAAA,EACvC;AACA,MAAI,WAAW,KAAK,WAAW,GAAG;AAC9B,QAAI,KAAK,WAAW,SAAS,MAAM,SAAS,GAAG;AAAA,EACnD;AACA,MAAI,SAAS,OAAO;AAChB,QAAI,KAAK,UAAU,UAAU,QAAQ,gBAAgB,IAAI,UAAU,UAAU,QAAQ,gBAAgB,IAAI,MAAM;AAAA,EACnH;AACA,SAAO,IAAI,KAAK,GAAG;AACvB;AACO,IAAI,eAAgB,WAAY;AACnC,MAAI,YAAI,mBAAmB,WAAW,OAAO,IAAI,GAAG;AAChD,WAAO,SAAU,KAAK;AAClB,aAAO,OAAO,KAAK,SAAS,mBAAmB,GAAG,CAAC,CAAC;AAAA,IACxD;AAAA,EACJ;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO,SAAU,KAAK;AAClB,aAAO,OAAO,KAAK,GAAG,EAAE,SAAS,QAAQ;AAAA,IAC7C;AAAA,EACJ;AACA,SAAO,SAAU,KAAK;AAClB,QAAI,MAAuC;AACvC,eAAS,6DAA8D;AAAA,IAC3E;AACA,WAAO;AAAA,EACX;AACJ,EAAG;;;ACjJH,IAAI,aAAa,MAAM,UAAU;AACjC,SAAS,kBAAkB,IAAI,IAAI,SAAS;AACxC,UAAQ,KAAK,MAAM,UAAU;AACjC;AACA,SAAS,mBAAmB,KAAK,IAAI,IAAI,SAAS;AAC9C,MAAIC,OAAM,GAAG;AACb,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,QAAI,CAAC,IAAI,kBAAkB,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EACpD;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,KAAK,IAAI,IAAI,SAAS;AAC9C,MAAIA,OAAM,GAAG;AACb,MAAIC,QAAOD,QAAO,GAAG,CAAC,EAAE;AACxB,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,QAAI,CAAC,IAAI,CAAC,GAAG;AACT,UAAI,CAAC,IAAI,CAAC;AAAA,IACd;AACA,aAAS,IAAI,GAAG,IAAIC,OAAM,KAAK;AAC3B,UAAI,CAAC,EAAE,CAAC,IAAI,kBAAkB,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO;AAAA,IAC7D;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,KAAK,IAAI,IAAI,MAAM;AACnC,MAAID,OAAM,GAAG;AACb,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,QAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI;AAAA,EAC7B;AACA,SAAO;AACX;AACA,SAAS,WAAW,KAAK,IAAI,IAAI,MAAM;AACnC,MAAIA,OAAM,GAAG;AACb,MAAIC,QAAOD,QAAO,GAAG,CAAC,EAAE;AACxB,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,QAAI,CAAC,IAAI,CAAC,GAAG;AACT,UAAI,CAAC,IAAI,CAAC;AAAA,IACd;AACA,aAAS,IAAI,GAAG,IAAIC,OAAM,KAAK;AAC3B,UAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI;AAAA,IACtC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,MAAM,MAAM;AAChC,MAAI,OAAO,KAAK;AAChB,MAAI,OAAO,KAAK;AAChB,MAAI,aAAa,OAAO,OAAO,OAAO;AACtC,MAAI,aAAa,KAAK,IAAI,MAAM,IAAI;AACpC,MAAI,OAAO,WAAW,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,QAAQ,EAAE;AAC1E,WAAS,IAAI,YAAY,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG,KAAK;AACpD,eAAW,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK,MAAM,MAAM;AAAA,IAC5B,CAAC;AAAA,EACL;AACJ;AACA,SAAS,UAAU,MAAM,MAAM,QAAQ;AACnC,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,MAAM;AAC1B;AAAA,EACJ;AACA,MAAI,UAAU,KAAK;AACnB,MAAI,UAAU,KAAK;AACnB,MAAI,YAAY,SAAS;AACrB,QAAI,mBAAmB,UAAU;AACjC,QAAI,kBAAkB;AAClB,WAAK,SAAS;AAAA,IAClB,OACK;AACD,eAAS,IAAI,SAAS,IAAI,SAAS,KAAK;AACpC,aAAK,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MAC/D;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE;AAC9B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,QAAI,WAAW,GAAG;AACd,UAAI,MAAM,KAAK,CAAC,CAAC,GAAG;AAChB,aAAK,CAAC,IAAI,KAAK,CAAC;AAAA,MACpB;AAAA,IACJ,OACK;AACD,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,YAAI,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG;AACnB,eAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,SAAS,WAAW,OAAO;AAC9B,MAAI,YAAY,KAAK,GAAG;AACpB,QAAID,OAAM,MAAM;AAChB,QAAI,YAAY,MAAM,CAAC,CAAC,GAAG;AACvB,UAAI,MAAM,CAAC;AACX,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,YAAI,KAAK,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,MACtC;AACA,aAAO;AAAA,IACX;AACA,WAAO,WAAW,KAAK,KAAK;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,YAAY,MAAM;AACvB,OAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AACjC,OAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AACjC,OAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,CAAC,KAAK;AACjC,OAAK,CAAC,IAAI,KAAK,CAAC,KAAK,OAAO,IAAI,KAAK,CAAC;AACtC,SAAO,UAAU,KAAK,KAAK,GAAG,IAAI;AACtC;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,YAAY,SAAS,MAAM,CAAC,CAAC,IAAI,IAAI;AAChD;AACA,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,6BAA6B;AACjC,IAAI,6BAA6B;AACjC,IAAI,oBAAoB;AACxB,SAAS,oBAAoB,SAAS;AAClC,SAAO,YAAY,8BAA8B,YAAY;AACjE;AACA,SAAS,iBAAiB,SAAS;AAC/B,SAAO,YAAY,uBAAuB,YAAY;AAC1D;AACA,IAAI,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;AACzB,IAAI,QAAS,WAAY;AACrB,WAASE,OAAM,UAAU;AACrB,SAAK,YAAY,CAAC;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,OAAM,UAAU,aAAa,WAAY;AACrC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,SAAK,YAAY;AACjB,QAAI,KAAK,gBAAgB;AACrB,WAAK,eAAe,YAAY;AAAA,IACpC;AAAA,EACJ;AACA,EAAAA,OAAM,UAAU,eAAe,WAAY;AACvC,WAAO,KAAK,UAAU,UAAU;AAAA,EACpC;AACA,EAAAA,OAAM,UAAU,mBAAmB,WAAY;AAC3C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,MAAM,UAAU,QAAQ;AAC5D,SAAK,aAAa;AAClB,QAAI,YAAY,KAAK;AACrB,QAAIF,OAAM,UAAU;AACpB,QAAI,WAAW;AACf,QAAI,UAAU;AACd,QAAI,QAAQ;AACZ,QAAI,YAAY,QAAQ,GAAG;AACvB,UAAI,WAAW,cAAc,QAAQ;AACrC,gBAAU;AACV,UAAI,aAAa,KAAK,CAAC,SAAS,SAAS,CAAC,CAAC,KACpC,aAAa,KAAK,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG;AAChD,mBAAW;AAAA,MACf;AAAA,IACJ,OACK;AACD,UAAI,SAAS,QAAQ,KAAK,CAAC,MAAM,QAAQ,GAAG;AACxC,kBAAU;AAAA,MACd,WACS,SAAS,QAAQ,GAAG;AACzB,YAAI,CAAC,MAAM,CAAC,QAAQ,GAAG;AACnB,oBAAU;AAAA,QACd,OACK;AACD,cAAI,aAAmB,MAAM,QAAQ;AACrC,cAAI,YAAY;AACZ,oBAAQ;AACR,sBAAU;AAAA,UACd;AAAA,QACJ;AAAA,MACJ,WACS,iBAAiB,QAAQ,GAAG;AACjC,YAAI,iBAAiB,OAAO,CAAC,GAAG,KAAK;AACrC,uBAAe,aAAa,IAAI,SAAS,YAAY,SAAU,WAAW;AAAE,iBAAQ;AAAA,YAChF,QAAQ,UAAU;AAAA,YAClB,OAAa,MAAM,UAAU,KAAK;AAAA,UACtC;AAAA,QAAI,CAAC;AACL,YAAI,iBAAiB,QAAQ,GAAG;AAC5B,oBAAU;AAAA,QACd,WACS,iBAAiB,QAAQ,GAAG;AACjC,oBAAU;AAAA,QACd;AACA,gBAAQ;AAAA,MACZ;AAAA,IACJ;AACA,QAAIA,SAAQ,GAAG;AACX,WAAK,UAAU;AAAA,IACnB,WACS,YAAY,KAAK,WAAW,YAAY,mBAAmB;AAChE,iBAAW;AAAA,IACf;AACA,SAAK,WAAW,KAAK,YAAY;AACjC,QAAI,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACb;AACA,QAAI,QAAQ;AACR,SAAG,SAAS;AACZ,SAAG,aAAa,WAAW,MAAM,IAC3B,SACA,eAAY,MAAM,KAAK,sBAAsB,MAAM;AAAA,IAC7D;AACA,cAAU,KAAK,EAAE;AACjB,WAAO;AAAA,EACX;AACA,EAAAE,OAAM,UAAU,UAAU,SAAU,SAAS,eAAe;AACxD,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,YAAY;AACjB,UAAI,KAAK,SAAU,GAAG,GAAG;AACrB,eAAO,EAAE,OAAO,EAAE;AAAA,MACtB,CAAC;AAAA,IACL;AACA,QAAI,UAAU,KAAK;AACnB,QAAI,SAAS,IAAI;AACjB,QAAI,SAAS,IAAI,SAAS,CAAC;AAC3B,QAAI,aAAa,KAAK;AACtB,QAAI,QAAQ,iBAAiB,OAAO;AACpC,QAAIC,cAAa,oBAAoB,OAAO;AAC5C,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,UAAI,KAAK,IAAI,CAAC;AACd,UAAI,QAAQ,GAAG;AACf,UAAI,YAAY,OAAO;AACvB,SAAG,UAAU,GAAG,OAAO;AACvB,UAAI,CAAC,YAAY;AACb,YAAI,SAAS,MAAM,SAAS,GAAG;AAC3B,oBAAU,OAAO,WAAW,OAAO;AAAA,QACvC,WACSA,aAAY;AACjB,yBAAe,MAAM,YAAY,UAAU,UAAU;AAAA,QACzD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,cACE,YAAY,8BACZ,iBACA,KAAK,aAAa,KAClB,cAAc,aAAa,KAC3B,YAAY,cAAc,WAC1B,CAAC,cAAc,WAAW;AAC7B,WAAK,iBAAiB;AACtB,UAAI,aAAa,IAAI,CAAC,EAAE;AACxB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAI,YAAY,mBAAmB;AAC/B,cAAI,CAAC,EAAE,gBAAgB,IAAI,CAAC,EAAE,QAAQ;AAAA,QAC1C,WACS,YAAY,kBAAkB;AACnC,cAAI,CAAC,EAAE,gBACH,WAAW,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,EAAE;AAAA,QACnD,WACS,iBAAiB,OAAO,GAAG;AAChC,cAAI,CAAC,EAAE,gBAAgB,YAAY,sBAC7B,WAAW,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,EAAE,IAC3C,WAAW,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,EAAE;AAAA,QACrD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,EAAAD,OAAM,UAAU,OAAO,SAAU,QAAQ,SAAS;AAC9C,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,QAAI,KAAK,kBAAkB,KAAK,eAAe,WAAW;AACtD,WAAK,iBAAiB;AAAA,IAC1B;AACA,QAAI,aAAa,KAAK,kBAAkB;AACxC,QAAI,WAAW,aAAa,kBAAkB;AAC9C,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY,KAAK;AACrB,QAAI,SAAS,UAAU;AACvB,QAAI,WAAW,KAAK;AACpB,QAAI,eAAe,YAAY;AAC/B,QAAI;AACJ,QAAI,YAAY,KAAK;AACrB,QAAIE,WAAU,KAAK;AACnB,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,GAAG;AACd,cAAQ,YAAY,UAAU,CAAC;AAAA,IACnC,OACK;AACD,UAAI,UAAU,GAAG;AACb,mBAAW;AAAA,MACf,WACS,UAAU,KAAK,UAAU;AAC9B,YAAIC,SAAQD,SAAQ,YAAY,GAAG,SAAS,CAAC;AAC7C,aAAK,WAAWC,QAAO,YAAY,GAAG,YAAY;AAC9C,cAAI,UAAU,QAAQ,EAAE,WAAW,SAAS;AACxC;AAAA,UACJ;AAAA,QACJ;AACA,mBAAWD,SAAQ,UAAU,SAAS,CAAC;AAAA,MAC3C,OACK;AACD,aAAK,WAAW,WAAW,WAAW,QAAQ,YAAY;AACtD,cAAI,UAAU,QAAQ,EAAE,UAAU,SAAS;AACvC;AAAA,UACJ;AAAA,QACJ;AACA,mBAAWA,SAAQ,WAAW,GAAG,SAAS,CAAC;AAAA,MAC/C;AACA,kBAAY,UAAU,WAAW,CAAC;AAClC,cAAQ,UAAU,QAAQ;AAAA,IAC9B;AACA,QAAI,EAAE,SAAS,YAAY;AACvB;AAAA,IACJ;AACA,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,QAAI,WAAY,UAAU,UAAU,MAAM;AAC1C,QAAI,IAAI,aAAa,IAAI,IAAIA,UAAS,UAAU,MAAM,WAAW,UAAU,CAAC;AAC5E,QAAI,UAAU,YAAY;AACtB,UAAI,UAAU,WAAW,CAAC;AAAA,IAC9B;AACA,QAAI,YAAY,aAAa,KAAK,iBAC3B,eAAe,UAAU,OAAO,QAAQ;AAC/C,SAAK,iBAAiB,OAAO,KAAK,iBAAiB,CAAC,WAAW;AAC3D,kBAAY,KAAK,iBAAiB,CAAC;AAAA,IACvC;AACA,QAAI,KAAK,UAAU;AACf,aAAO,QAAQ,IAAI,IAAI,IAAI,MAAM,WAAW,UAAU;AAAA,IAC1D,WACS,iBAAiB,OAAO,GAAG;AAChC,kBAAY,sBACN,mBAAmB,WAAW,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC,IACrE,mBAAmB,WAAW,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC;AAAA,IAC/E,WACS,oBAAoB,OAAO,GAAG;AACnC,UAAI,MAAM,MAAM,QAAQ;AACxB,UAAI,YAAY,UAAU,QAAQ;AAClC,UAAI,qBAAqB,YAAY;AACrC,aAAO,QAAQ,IAAI;AAAA,QACf,MAAM,qBAAqB,WAAW;AAAA,QACtC,GAAG,kBAAkB,IAAI,GAAG,UAAU,GAAG,CAAC;AAAA,QAC1C,GAAG,kBAAkB,IAAI,GAAG,UAAU,GAAG,CAAC;AAAA,QAC1C,YAAY,IAAI,IAAI,YAAY,SAAU,WAAW,KAAK;AACtD,cAAI,gBAAgB,UAAU,WAAW,GAAG;AAC5C,iBAAO;AAAA,YACH,QAAQ,kBAAkB,UAAU,QAAQ,cAAc,QAAQ,CAAC;AAAA,YACnE,OAAO,YAAY,mBAAmB,CAAC,GAAG,UAAU,OAAO,cAAc,OAAO,CAAC,CAAC;AAAA,UACtF;AAAA,QACJ,CAAC;AAAA,QACD,QAAQ,UAAU;AAAA,MACtB;AACA,UAAI,oBAAoB;AACpB,eAAO,QAAQ,EAAE,KAAK,kBAAkB,IAAI,IAAI,UAAU,IAAI,CAAC;AAC/D,eAAO,QAAQ,EAAE,KAAK,kBAAkB,IAAI,IAAI,UAAU,IAAI,CAAC;AAAA,MACnE,OACK;AACD,eAAO,QAAQ,EAAE,IAAI,kBAAkB,IAAI,GAAG,UAAU,GAAG,CAAC;AAAA,MAChE;AAAA,IACJ,WACS,cAAc;AACnB,yBAAmB,WAAW,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC;AACrE,UAAI,CAAC,YAAY;AACb,eAAO,QAAQ,IAAI,YAAY,SAAS;AAAA,MAC5C;AAAA,IACJ,OACK;AACD,UAAI,QAAQ,kBAAkB,MAAM,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC;AACrE,UAAI,YAAY;AACZ,aAAK,iBAAiB;AAAA,MAC1B,OACK;AACD,eAAO,QAAQ,IAAI;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,YAAY;AACZ,WAAK,aAAa,MAAM;AAAA,IAC5B;AAAA,EACJ;AACA,EAAAF,OAAM,UAAU,eAAe,SAAU,QAAQ;AAC7C,QAAI,UAAU,KAAK;AACnB,QAAI,WAAW,KAAK;AACpB,QAAI,gBAAgB,KAAK;AACzB,QAAI,YAAY,mBAAmB;AAC/B,aAAO,QAAQ,IAAI,OAAO,QAAQ,IAAI;AAAA,IAC1C,WACS,YAAY,kBAAkB;AACnC,MAAM,MAAM,OAAO,QAAQ,GAAG,OAAO;AACrC,iBAAW,SAAS,SAAS,eAAe,CAAC;AAC7C,aAAO,QAAQ,IAAI,YAAY,OAAO;AAAA,IAC1C,WACS,YAAY,qBAAqB;AACtC,iBAAW,OAAO,QAAQ,GAAG,OAAO,QAAQ,GAAG,eAAe,CAAC;AAAA,IACnE,WACS,YAAY,qBAAqB;AACtC,iBAAW,OAAO,QAAQ,GAAG,OAAO,QAAQ,GAAG,eAAe,CAAC;AAAA,IACnE;AAAA,EACJ;AACA,SAAOA;AACX,EAAE;AACF,IAAI,WAAY,WAAY;AACxB,WAASI,UAAS,QAAQ,MAAM,wBAAwB,YAAY;AAChE,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,QAAI,QAAQ,YAAY;AACpB,eAAS,kDAAmD;AAC5D;AAAA,IACJ;AACA,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AAAA,EAC1B;AACA,EAAAA,UAAS,UAAU,aAAa,WAAY;AACxC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,UAAU,WAAW,WAAY;AACtC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,UAAU,YAAY,WAAY;AACvC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,UAAU,eAAe,SAAU,QAAQ;AAChD,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,UAAS,UAAU,OAAO,SAAU,MAAM,OAAO,QAAQ;AACrD,WAAO,KAAK,aAAa,MAAM,OAAO,KAAK,KAAK,GAAG,MAAM;AAAA,EAC7D;AACA,EAAAA,UAAS,UAAU,eAAe,SAAU,MAAM,OAAO,WAAW,QAAQ;AACxE,QAAI,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,QAAQ,OAAO,QAAQ;AAC3B,UAAI,CAAC,OAAO;AACR,gBAAQ,OAAO,QAAQ,IAAI,IAAI,MAAM,QAAQ;AAC7C,YAAI,eAAe;AACnB,YAAI,gBAAgB,KAAK,kBAAkB,QAAQ;AACnD,YAAI,eAAe;AACf,cAAI,kBAAkB,cAAc;AACpC,cAAI,cAAc,gBAAgB,gBAAgB,SAAS,CAAC;AAC5D,yBAAe,eAAe,YAAY;AAC1C,cAAI,cAAc,YAAY,oBAAoB,cAAc;AAC5D,2BAAe,YAAY,YAAY;AAAA,UAC3C;AAAA,QACJ,OACK;AACD,yBAAe,KAAK,QAAQ,QAAQ;AAAA,QACxC;AACA,YAAI,gBAAgB,MAAM;AACtB;AAAA,QACJ;AACA,YAAI,OAAO,GAAG;AACV,gBAAM,YAAY,GAAG,WAAW,YAAY,GAAG,MAAM;AAAA,QACzD;AACA,aAAK,WAAW,KAAK,QAAQ;AAAA,MACjC;AACA,YAAM,YAAY,MAAM,WAAW,MAAM,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC/D;AACA,SAAK,WAAW,KAAK,IAAI,KAAK,UAAU,IAAI;AAC5C,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,QAAQ,WAAY;AACnC,SAAK,MAAM,MAAM;AACjB,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,UAAS,UAAU,SAAS,WAAY;AACpC,SAAK,MAAM,OAAO;AAClB,SAAK,UAAU;AAAA,EACnB;AACA,EAAAA,UAAS,UAAU,WAAW,WAAY;AACtC,WAAO,CAAC,CAAC,KAAK;AAAA,EAClB;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,UAAU;AAC9C,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AACb,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU;AACV,UAAIN,OAAM,SAAS;AACnB,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,iBAAS,CAAC,EAAE,KAAK,IAAI;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ;AACA,EAAAM,UAAS,UAAU,mBAAmB,WAAY;AAC9C,SAAK,mBAAmB;AACxB,QAAI,YAAY,KAAK;AACrB,QAAI,cAAc,KAAK;AACvB,QAAI,WAAW;AACX,gBAAU,WAAW,KAAK,KAAK;AAAA,IACnC;AACA,SAAK,QAAQ;AACb,QAAI,aAAa;AACb,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,oBAAY,CAAC,EAAE,KAAK,IAAI;AAAA,MAC5B;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,qBAAqB,WAAY;AAChD,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,aAAO,WAAW,CAAC,CAAC,EAAE,YAAY;AAAA,IACtC;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,oBAAoB,SAAU,WAAW;AACxD,QAAI;AACJ,QAAI,oBAAoB,KAAK;AAC7B,QAAI,mBAAmB;AACnB,eAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAC/C,YAAI,QAAQ,kBAAkB,CAAC,EAAE,SAAS,SAAS;AACnD,YAAI,OAAO;AACP,0BAAgB;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,QAAQ,SAAU,QAAQ;AACzC,QAAI,KAAK,WAAW,GAAG;AACnB;AAAA,IACJ;AACA,SAAK,WAAW;AAChB,QAAIC,QAAO;AACX,QAAI,SAAS,CAAC;AACd,QAAI,UAAU,KAAK,YAAY;AAC/B,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC7C,UAAI,WAAW,KAAK,WAAW,CAAC;AAChC,UAAI,QAAQ,KAAK,QAAQ,QAAQ;AACjC,UAAI,gBAAgB,KAAK,kBAAkB,QAAQ;AACnD,UAAI,MAAM,MAAM;AAChB,UAAI,SAAS,IAAI;AACjB,YAAM,QAAQ,SAAS,aAAa;AACpC,UAAI,MAAM,aAAa,GAAG;AACtB,YAAI,CAAC,KAAK,kBAAkB,MAAM,UAAU;AACxC,cAAI,SAAS,IAAI,SAAS,CAAC;AAC3B,cAAI,QAAQ;AACR,YAAAA,MAAK,QAAQ,MAAM,QAAQ,IAAI,OAAO;AAAA,UAC1C;AACA,gBAAM,YAAY;AAAA,QACtB,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,UAAU,KAAK,QAAQ;AAC9B,UAAI,OAAO,IAAI,aAAK;AAAA,QAChB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,OAAO,KAAK,UAAU;AAAA,QACtB,SAAS,SAAU,SAAS;AACxB,UAAAA,MAAK,WAAW;AAChB,cAAI,oBAAoBA,MAAK;AAC7B,cAAI,mBAAmB;AACnB,gBAAI,2BAA2B;AAC/B,qBAASC,KAAI,GAAGA,KAAI,kBAAkB,QAAQA,MAAK;AAC/C,kBAAI,kBAAkBA,EAAC,EAAE,OAAO;AAC5B,2CAA2B;AAC3B;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,CAAC,0BAA0B;AAC3B,cAAAD,MAAK,qBAAqB;AAAA,YAC9B;AAAA,UACJ;AACA,mBAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACpC,mBAAOA,EAAC,EAAE,KAAKD,MAAK,SAAS,OAAO;AAAA,UACxC;AACA,cAAI,cAAcA,MAAK;AACvB,cAAI,aAAa;AACb,qBAASC,KAAI,GAAGA,KAAI,YAAY,QAAQA,MAAK;AACzC,0BAAYA,EAAC,EAAED,MAAK,SAAS,OAAO;AAAA,YACxC;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,WAAW,WAAY;AACnB,UAAAA,MAAK,cAAc;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,WAAK,QAAQ;AACb,UAAI,KAAK,WAAW;AAChB,aAAK,UAAU,QAAQ,IAAI;AAAA,MAC/B;AACA,UAAI,QAAQ;AACR,aAAK,UAAU,MAAM;AAAA,MACzB;AAAA,IACJ,OACK;AACD,WAAK,cAAc;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,EAAAD,UAAS,UAAU,OAAO,SAAU,eAAe;AAC/C,QAAI,CAAC,KAAK,OAAO;AACb;AAAA,IACJ;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,eAAe;AACf,WAAK,QAAQ,CAAC;AAAA,IAClB;AACA,SAAK,iBAAiB;AAAA,EAC1B;AACA,EAAAA,UAAS,UAAU,QAAQ,SAAU,MAAM;AACvC,SAAK,SAAS;AACd,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,SAAS,SAAU,IAAI;AACtC,QAAI,IAAI;AACJ,UAAI,CAAC,KAAK,aAAa;AACnB,aAAK,cAAc,CAAC;AAAA,MACxB;AACA,WAAK,YAAY,KAAK,EAAE;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,OAAO,SAAU,IAAI;AACpC,QAAI,IAAI;AACJ,UAAI,CAAC,KAAK,UAAU;AAChB,aAAK,WAAW,CAAC;AAAA,MACrB;AACA,WAAK,SAAS,KAAK,EAAE;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,UAAU,SAAU,IAAI;AACvC,QAAI,IAAI;AACJ,UAAI,CAAC,KAAK,aAAa;AACnB,aAAK,cAAc,CAAC;AAAA,MACxB;AACA,WAAK,YAAY,KAAK,EAAE;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,UAAS,UAAU,WAAW,SAAU,UAAU;AAC9C,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAChC;AACA,EAAAA,UAAS,UAAU,YAAY,WAAY;AACvC,QAAI,QAAQ;AACZ,WAAO,IAAI,KAAK,YAAY,SAAU,KAAK;AAAE,aAAO,MAAM,QAAQ,GAAG;AAAA,IAAG,CAAC;AAAA,EAC7E;AACA,EAAAA,UAAS,UAAU,aAAa,SAAU,WAAW,eAAe;AAChE,QAAI,CAAC,UAAU,UAAU,CAAC,KAAK,OAAO;AAClC,aAAO;AAAA,IACX;AACA,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,QAAQ,OAAO,UAAU,CAAC,CAAC;AAC/B,UAAI,SAAS,CAAC,MAAM,WAAW,GAAG;AAC9B,YAAI,eAAe;AACf,gBAAM,KAAK,KAAK,SAAS,CAAC;AAAA,QAC9B,WACS,KAAK,aAAa,GAAG;AAC1B,gBAAM,KAAK,KAAK,SAAS,CAAC;AAAA,QAC9B;AACA,cAAM,YAAY;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,WAAW,GAAG;AACrC,qBAAa;AACb;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,YAAY;AACZ,WAAK,iBAAiB;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,UAAU,SAAS,SAAU,QAAQ,WAAW,aAAa;AAClE,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,gBAAY,aAAa,KAAK;AAC9B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,QAAQ,KAAK,QAAQ,QAAQ;AACjC,UAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAC9B;AAAA,MACJ;AACA,UAAI,MAAM,MAAM;AAChB,UAAI,KAAK,IAAI,cAAc,IAAI,IAAI,SAAS,CAAC;AAC7C,UAAI,IAAI;AACJ,eAAO,QAAQ,IAAI,WAAW,GAAG,QAAQ;AAAA,MAC7C;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,UAAS,UAAU,qBAAqB,SAAU,YAAY,WAAW;AACrE,gBAAY,aAAa,KAAK,UAAU;AACxC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,QAAQ,KAAK,QAAQ,QAAQ;AACjC,UAAI,CAAC,OAAO;AACR;AAAA,MACJ;AACA,UAAI,MAAM,MAAM;AAChB,UAAI,IAAI,SAAS,GAAG;AAChB,YAAI,SAAS,IAAI,IAAI;AACrB,cAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,CAAC;AACnD,cAAM,QAAQ,KAAK,UAAU,MAAM,iBAAiB,CAAC;AAAA,MACzD;AAAA,IACJ;AAAA,EACJ;AACA,SAAOA;AACX,EAAE;AACF,IAAO,mBAAQ;;;ACvtBR,IAAI,yBAAyB;AACpC,IAAI,sBAAsB,oBAAoB,OAAO,CAAC,QAAQ,CAAC;AAC/D,IAAI,yBAAyB,OAAO,qBAAqB,SAAU,KAAK,KAAK;AACzE,MAAI,GAAG,IAAI;AACX,SAAO;AACX,GAAG,EAAE,QAAQ,MAAM,CAAC;AACpB,IAAI,oBAAoB,CAAC;AACzB,IAAI,kBAAkB,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACjD,IAAI,UAAW,WAAY;AACvB,WAASG,SAAQ,OAAO;AACpB,SAAK,KAAK,KAAK;AACf,SAAK,YAAY,CAAC;AAClB,SAAK,gBAAgB,CAAC;AACtB,SAAK,SAAS,CAAC;AACf,SAAK,MAAM,KAAK;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,QAAQ,SAAU,OAAO;AACvC,SAAK,KAAK,KAAK;AAAA,EACnB;AACA,EAAAA,SAAQ,UAAU,QAAQ,SAAU,IAAI,IAAI,GAAG;AAC3C,YAAQ,KAAK,WAAW;AAAA,MACpB,KAAK;AACD,aAAK;AACL;AAAA,MACJ,KAAK;AACD,aAAK;AACL;AAAA,IACR;AACA,QAAI,IAAI,KAAK;AACb,QAAI,CAAC,GAAG;AACJ,UAAI,KAAK,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAC1C;AACA,MAAE,CAAC,KAAK;AACR,MAAE,CAAC,KAAK;AACR,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,eAAe,WAAY;AAAA,EAAE;AAC/C,EAAAA,SAAQ,UAAU,cAAc,WAAY;AAAA,EAAE;AAC9C,EAAAA,SAAQ,UAAU,SAAS,WAAY;AACnC,SAAK,gBAAgB;AACrB,QAAI,KAAK,SAAS;AACd,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,kBAAkB,SAAU,aAAa;AACvD,QAAI,SAAS,KAAK;AAClB,QAAI,WAAW,CAAC,OAAO,UAAU,cAAc;AAC3C,UAAI,CAAC,KAAK,YAAY;AAClB,aAAK,aAAa,CAAC;AAAA,MACvB;AACA,UAAI,aAAa,KAAK;AACtB,UAAI,UAAU,WAAW;AACzB,UAAI,qBAAqB,OAAO;AAChC,UAAI,YAAY;AAChB,UAAI,oBAAoB;AACxB,UAAI,mBAAmB;AACvB,yBAAmB,SAAS,UAAU,OAAO;AAC7C,UAAI,cAAc;AAClB,yBAAmB,cAAc,MAAM;AACvC,UAAI,WAAW,YAAY,MAAM;AAC7B,YAAI,aAAa;AACjB,YAAI,WAAW,YAAY;AACvB,qBAAW,KAAK,WAAW,UAAU;AAAA,QACzC,OACK;AACD,qBAAW,KAAK,KAAK,gBAAgB,CAAC;AAAA,QAC1C;AACA,YAAI,CAAC,SAAS;AACV,qBAAW,eAAe,KAAK,SAAS;AAAA,QAC5C;AACA,YAAI,KAAK,uBAAuB;AAC5B,eAAK,sBAAsB,mBAAmB,YAAY,UAAU;AAAA,QACxE,OACK;AACD,gCAAsB,mBAAmB,YAAY,UAAU;AAAA,QACnE;AACA,2BAAmB,IAAI,kBAAkB;AACzC,2BAAmB,IAAI,kBAAkB;AACzC,oBAAY,kBAAkB;AAC9B,4BAAoB,kBAAkB;AACtC,YAAI,aAAa,WAAW;AAC5B,YAAI,cAAc,WAAW,YAAY,MAAM;AAC3C,cAAI,aAAa;AACjB,cAAI,aAAa;AACjB,cAAI,eAAe,UAAU;AACzB,yBAAa,WAAW,QAAQ;AAChC,yBAAa,WAAW,SAAS;AAAA,UACrC,OACK;AACD,yBAAa,aAAa,WAAW,CAAC,GAAG,WAAW,KAAK;AACzD,yBAAa,aAAa,WAAW,CAAC,GAAG,WAAW,MAAM;AAAA,UAC9D;AACA,wBAAc;AACd,6BAAmB,UAAU,CAAC,mBAAmB,IAAI,cAAc,UAAU,IAAI,WAAW;AAC5F,6BAAmB,UAAU,CAAC,mBAAmB,IAAI,cAAc,UAAU,IAAI,WAAW;AAAA,QAChG;AAAA,MACJ;AACA,UAAI,WAAW,YAAY,MAAM;AAC7B,2BAAmB,WAAW,WAAW;AAAA,MAC7C;AACA,UAAI,aAAa,WAAW;AAC5B,UAAI,YAAY;AACZ,2BAAmB,KAAK,WAAW,CAAC;AACpC,2BAAmB,KAAK,WAAW,CAAC;AACpC,YAAI,CAAC,aAAa;AACd,6BAAmB,UAAU,CAAC,WAAW,CAAC;AAC1C,6BAAmB,UAAU,CAAC,WAAW,CAAC;AAAA,QAC9C;AAAA,MACJ;AACA,UAAI,WAAW,WAAW,UAAU,OAC7B,OAAO,WAAW,aAAa,YAAY,WAAW,SAAS,QAAQ,QAAQ,KAAK,IACrF,WAAW;AACjB,UAAI,wBAAwB,KAAK,2BAA2B,KAAK,yBAAyB,CAAC;AAC3F,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,YAAY,KAAK,gBAAgB,GAAG;AACpC,mBAAW,WAAW;AACtB,qBAAa,WAAW;AACxB,YAAI,YAAY,QAAQ,aAAa,QAAQ;AACzC,qBAAW,KAAK,kBAAkB;AAAA,QACtC;AACA,YAAI,cAAc,QAAQ,eAAe,QAAQ;AAC7C,uBAAa,KAAK,oBAAoB,QAAQ;AAC9C,uBAAa;AAAA,QACjB;AAAA,MACJ,OACK;AACD,mBAAW,WAAW;AACtB,qBAAa,WAAW;AACxB,YAAI,YAAY,QAAQ,aAAa,QAAQ;AACzC,qBAAW,KAAK,eAAe;AAAA,QACnC;AACA,YAAI,cAAc,QAAQ,eAAe,QAAQ;AAC7C,uBAAa,KAAK,iBAAiB,QAAQ;AAC3C,uBAAa;AAAA,QACjB;AAAA,MACJ;AACA,iBAAW,YAAY;AACvB,UAAI,aAAa,sBAAsB,QAChC,eAAe,sBAAsB,UACrC,eAAe,sBAAsB,cACrC,cAAc,sBAAsB,SACpC,sBAAsB,sBAAsB,eAAe;AAC9D,2BAAmB;AACnB,8BAAsB,OAAO;AAC7B,8BAAsB,SAAS;AAC/B,8BAAsB,aAAa;AACnC,8BAAsB,QAAQ;AAC9B,8BAAsB,gBAAgB;AACtC,eAAO,oBAAoB,qBAAqB;AAAA,MACpD;AACA,aAAO,WAAW;AAClB,UAAI,kBAAkB;AAClB,eAAO,WAAW,IAAI;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,kBAAkB,WAAY;AAC5C,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,oBAAoB,WAAY;AAC9C,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,sBAAsB,SAAU,UAAU;AACxD,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC3C,WAAO,KAAK,QAAQ,KAAK,KAAK,WAAW,IAAI,oBAAoB;AAAA,EACrE;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,UAAU;AACrD,QAAI,kBAAkB,KAAK,QAAQ,KAAK,KAAK,mBAAmB;AAChE,QAAI,WAAW,OAAO,oBAAoB,YAAY,MAAM,eAAe;AAC3E,QAAI,CAAC,UAAU;AACX,iBAAW,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,IAChC;AACA,QAAI,QAAQ,SAAS,CAAC;AACtB,QAAI,SAAS,KAAK,KAAK,WAAW;AAClC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,eAAS,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI,QAAQ,IAAI;AAAA,IAClE;AACA,aAAS,CAAC,IAAI;AACd,WAAO,UAAU,UAAU,MAAM;AAAA,EACrC;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,IAAI,SAAS;AAAA,EAAE;AACtD,EAAAA,SAAQ,UAAU,SAAS,SAAU,KAAK,OAAO;AAC7C,QAAI,QAAQ,cAAc;AACtB,WAAK,cAAc,KAAK;AAAA,IAC5B,WACS,QAAQ,eAAe;AAC5B,WAAK,eAAe,KAAK;AAAA,IAC7B,WACS,QAAQ,YAAY;AACzB,WAAK,YAAY,KAAK;AAAA,IAC1B,WACS,QAAQ,SAAS;AACtB,WAAK,QAAQ,KAAK,SAAS,CAAC;AAC5B,aAAO,KAAK,OAAO,KAAK;AAAA,IAC5B,OACK;AACD,WAAK,GAAG,IAAI;AAAA,IAChB;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,OAAO,WAAY;AACjC,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,OAAO,WAAY;AACjC,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,OAAO,SAAU,UAAU,OAAO;AAChD,QAAI,OAAO,aAAa,UAAU;AAC9B,WAAK,OAAO,UAAU,KAAK;AAAA,IAC/B,WACS,SAAS,QAAQ,GAAG;AACzB,UAAI,MAAM;AACV,UAAI,UAAU,KAAK,GAAG;AACtB,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,YAAI,MAAM,QAAQ,CAAC;AACnB,aAAK,OAAO,KAAK,SAAS,GAAG,CAAC;AAAA,MAClC;AAAA,IACJ;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,2BAA2B,SAAU,SAAS;AAC5D,SAAK,mBAAmB,OAAO;AAC/B,QAAI,cAAc,KAAK;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,UAAI,sBAAsB,SAAS;AACnC,UAAI,SAAS,QAAQ,KAAK,uBAAuB,wBAAwB,wBAAwB;AAC7F;AAAA,MACJ;AACA,UAAI,aAAa,SAAS;AAC1B,UAAI,SAAS,aACP,YAAY,UAAU,IAAI;AAChC,eAAS,OAAO,MAAM;AAAA,IAC1B;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,qBAAqB,SAAU,SAAS;AACtD,QAAI,cAAc,KAAK;AACvB,QAAI,CAAC,aAAa;AACd,oBAAc,KAAK,eAAe,CAAC;AAAA,IACvC;AACA,QAAI,QAAQ,cAAc,CAAC,YAAY,YAAY;AAC/C,kBAAY,aAAa,KAAK;AAAA,IAClC;AACA,SAAK,qBAAqB,SAAS,aAAa,mBAAmB;AAAA,EACvE;AACA,EAAAA,SAAQ,UAAU,uBAAuB,SAAU,SAAS,aAAa,aAAa;AAClF,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,UAAI,MAAM,YAAY,CAAC;AACvB,UAAI,QAAQ,GAAG,KAAK,QAAQ,EAAE,OAAO,cAAc;AAC/C,oBAAY,GAAG,IAAI,KAAK,GAAG;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,WAAO,KAAK,cAAc,SAAS;AAAA,EACvC;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,MAAM;AACzC,WAAO,KAAK,OAAO,IAAI;AAAA,EAC3B;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,MAAM;AAC5C,QAAI,SAAS,KAAK;AAClB,QAAI,CAAC,OAAO,IAAI,GAAG;AACf,aAAO,IAAI,IAAI,CAAC;AAAA,IACpB;AACA,WAAO,OAAO,IAAI;AAAA,EACtB;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,aAAa;AACnD,SAAK,SAAS,wBAAwB,OAAO,WAAW;AAAA,EAC5D;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,WAAW,mBAAmB,aAAa,oBAAoB;AAClG,QAAI,gBAAgB,cAAc;AAClC,QAAI,YAAY,KAAK,SAAS;AAC9B,QAAI,CAAC,aAAa,eAAe;AAC7B;AAAA,IACJ;AACA,QAAI,gBAAgB,KAAK;AACzB,QAAI,eAAe,KAAK;AACxB,QAAI,QAAQ,eAAe,SAAS,KAAK,MAAM,qBAAqB,cAAc,WAAW,IAAI;AAC7F;AAAA,IACJ;AACA,QAAI;AACJ,QAAI,KAAK,cAAc,CAAC,eAAe;AACnC,cAAQ,KAAK,WAAW,SAAS;AAAA,IACrC;AACA,QAAI,CAAC,OAAO;AACR,cAAS,KAAK,UAAU,KAAK,OAAO,SAAS;AAAA,IACjD;AACA,QAAI,CAAC,SAAS,CAAC,eAAe;AAC1B,eAAS,WAAW,YAAY,cAAc;AAC9C;AAAA,IACJ;AACA,QAAI,CAAC,eAAe;AAChB,WAAK,yBAAyB,KAAK;AAAA,IACvC;AACA,QAAI,gBAAgB,CAAC,EAAG,SAAS,MAAM,cAAe;AACtD,QAAI,eAAe;AACf,WAAK,sBAAsB,IAAI;AAAA,IACnC;AACA,SAAK,eAAe,WAAW,OAAO,KAAK,cAAc,mBAAmB,CAAC,eAAe,CAAC,KAAK,aAAa,gBAAgB,aAAa,WAAW,GAAG,YAAY;AACtK,QAAI,cAAc,KAAK;AACvB,QAAI,YAAY,KAAK;AACrB,QAAI,aAAa;AACb,kBAAY,SAAS,WAAW,mBAAmB,aAAa,aAAa;AAAA,IACjF;AACA,QAAI,WAAW;AACX,gBAAU,SAAS,WAAW,mBAAmB,aAAa,aAAa;AAAA,IAC/E;AACA,QAAI,eAAe;AACf,WAAK,gBAAgB,CAAC;AACtB,WAAK,eAAe,CAAC;AAAA,IACzB,OACK;AACD,UAAI,CAAC,mBAAmB;AACpB,aAAK,gBAAgB,CAAC,SAAS;AAAA,MACnC,OACK;AACD,aAAK,cAAc,KAAK,SAAS;AAAA,MACrC;AAAA,IACJ;AACA,SAAK,wBAAwB;AAC7B,SAAK,WAAW;AAChB,QAAI,CAAC,iBAAiB,KAAK,WAAW;AAClC,WAAK,sBAAsB,KAAK;AAChC,WAAK,WAAW,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,YAAY,SAAU,QAAQ,aAAa,oBAAoB;AAC7E,QAAI,CAAC,OAAO,QAAQ;AAChB,WAAK,YAAY;AAAA,IACrB,OACK;AACD,UAAI,eAAe,CAAC;AACpB,UAAI,gBAAgB,KAAK;AACzB,UAAIC,OAAM,OAAO;AACjB,UAAI,YAAYA,SAAQ,cAAc;AACtC,UAAI,WAAW;AACX,iBAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,cAAI,OAAO,CAAC,MAAM,cAAc,CAAC,GAAG;AAChC,wBAAY;AACZ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,WAAW;AACX;AAAA,MACJ;AACA,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,YAAI,YAAY,OAAO,CAAC;AACxB,YAAI,WAAW;AACf,YAAI,KAAK,YAAY;AACjB,qBAAW,KAAK,WAAW,WAAW,MAAM;AAAA,QAChD;AACA,YAAI,CAAC,UAAU;AACX,qBAAW,KAAK,OAAO,SAAS;AAAA,QACpC;AACA,YAAI,UAAU;AACV,uBAAa,KAAK,QAAQ;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,eAAe,aAAaA,OAAM,CAAC;AACvC,UAAI,gBAAgB,CAAC,EAAG,gBAAgB,aAAa,cAAe;AACpE,UAAI,eAAe;AACf,aAAK,sBAAsB,IAAI;AAAA,MACnC;AACA,UAAI,cAAc,KAAK,aAAa,YAAY;AAChD,UAAI,eAAe,KAAK;AACxB,WAAK,yBAAyB,WAAW;AACzC,WAAK,eAAe,OAAO,KAAK,GAAG,GAAG,aAAa,KAAK,cAAc,OAAO,CAAC,eAAe,CAAC,KAAK,aAAa,gBAAgB,aAAa,WAAW,GAAG,YAAY;AACvK,UAAI,cAAc,KAAK;AACvB,UAAI,YAAY,KAAK;AACrB,UAAI,aAAa;AACb,oBAAY,UAAU,QAAQ,aAAa,aAAa;AAAA,MAC5D;AACA,UAAI,WAAW;AACX,kBAAU,UAAU,QAAQ,aAAa,aAAa;AAAA,MAC1D;AACA,WAAK,wBAAwB;AAC7B,WAAK,gBAAgB,OAAO,MAAM;AAClC,WAAK,WAAW;AAChB,UAAI,CAAC,iBAAiB,KAAK,WAAW;AAClC,aAAK,sBAAsB,KAAK;AAChC,aAAK,WAAW,CAAC;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,EAAAD,SAAQ,UAAU,WAAW,WAAY;AACrC,QAAI,WAAW,KAAK;AACpB,QAAI,WAAW,KAAK;AACpB,WAAO,CAAC,YAAY,UAAU;AAC1B,UAAI,SAAS,QAAQ;AACjB,mBAAW;AACX;AAAA,MACJ;AACA,iBAAW,SAAS;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,0BAA0B,WAAY;AACpD,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,UAAI,SAAS,YAAY;AACrB,iBAAS,aAAa,KAAK,SAAS,UAAU,CAAC;AAAA,MACnD;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,OAAO;AAC7C,QAAI,MAAM,QAAQ,KAAK,eAAe,KAAK;AAC3C,QAAI,OAAO,GAAG;AACV,UAAI,gBAAgB,KAAK,cAAc,MAAM;AAC7C,oBAAc,OAAO,KAAK,CAAC;AAC3B,WAAK,UAAU,aAAa;AAAA,IAChC;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,eAAe,SAAU,UAAU,UAAU,UAAU;AACrE,QAAI,gBAAgB,KAAK,cAAc,MAAM;AAC7C,QAAI,MAAM,QAAQ,eAAe,QAAQ;AACzC,QAAI,iBAAiB,QAAQ,eAAe,QAAQ,KAAK;AACzD,QAAI,OAAO,GAAG;AACV,UAAI,CAAC,gBAAgB;AACjB,sBAAc,GAAG,IAAI;AAAA,MACzB,OACK;AACD,sBAAc,OAAO,KAAK,CAAC;AAAA,MAC/B;AAAA,IACJ,WACS,YAAY,CAAC,gBAAgB;AAClC,oBAAc,KAAK,QAAQ;AAAA,IAC/B;AACA,SAAK,UAAU,aAAa;AAAA,EAChC;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,OAAO,QAAQ;AACrD,QAAI,QAAQ;AACR,WAAK,SAAS,OAAO,IAAI;AAAA,IAC7B,OACK;AACD,WAAK,YAAY,KAAK;AAAA,IAC1B;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,eAAe,SAAU,QAAQ;AAC/C,QAAI,cAAc,CAAC;AACnB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC;AACpB,aAAO,aAAa,KAAK;AACzB,UAAI,MAAM,YAAY;AAClB,2BAAmB,oBAAoB,CAAC;AACxC,eAAO,kBAAkB,MAAM,UAAU;AAAA,MAC7C;AAAA,IACJ;AACA,QAAI,kBAAkB;AAClB,kBAAY,aAAa;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,WAAW,OAAO,aAAa,mBAAmB,YAAY,cAAc;AACrH,QAAI,uBAAuB,EAAE,SAAS;AACtC,QAAI,SAAS,MAAM,YAAY;AAC3B,WAAK,aAAa,OAAO,CAAC,GAAG,oBAAoB,KAAK,aAAa,YAAY,UAAU;AACzF,aAAO,KAAK,YAAY,MAAM,UAAU;AAAA,IAC5C,WACS,sBAAsB;AAC3B,UAAI,YAAY,YAAY;AACxB,aAAK,aAAa,YAAY;AAAA,MAClC;AAAA,IACJ;AACA,QAAI,mBAAmB,CAAC;AACxB,QAAI,gBAAgB;AACpB,aAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,UAAI,MAAM,oBAAoB,CAAC;AAC/B,UAAI,sBAAsB,cAAc,uBAAuB,GAAG;AAClE,UAAI,SAAS,MAAM,GAAG,KAAK,MAAM;AAC7B,YAAI,qBAAqB;AACrB,0BAAgB;AAChB,2BAAiB,GAAG,IAAI,MAAM,GAAG;AAAA,QACrC,OACK;AACD,eAAK,GAAG,IAAI,MAAM,GAAG;AAAA,QACzB;AAAA,MACJ,WACS,sBAAsB;AAC3B,YAAI,YAAY,GAAG,KAAK,MAAM;AAC1B,cAAI,qBAAqB;AACrB,4BAAgB;AAChB,6BAAiB,GAAG,IAAI,YAAY,GAAG;AAAA,UAC3C,OACK;AACD,iBAAK,GAAG,IAAI,YAAY,GAAG;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,YAAY;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,YAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,YAAI,aAAa,SAAS;AAC1B,YAAI,CAAC,SAAS,QAAQ,GAAG;AACrB,mBAAS,mBAAmB,cACrB,SAAS,aAAa,UAAU,IAChC,SAAS,WAAY;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,eAAe;AACf,WAAK,iBAAiB,WAAW,kBAAkB,YAAY;AAAA,IACnE;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,aAAa;AACxD,QAAI,YAAY,QAAQ,CAAC,YAAY,cAAc;AAC/C,UAAI,MAAuC;AACvC,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC7D;AACA;AAAA,IACJ;AACA,QAAI,gBAAgB,MAAM;AACtB,UAAI,MAAuC;AACvC,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACrD;AACA;AAAA,IACJ;AACA,QAAI,KAAK,KAAK;AACd,QAAI,IAAI;AACJ,kBAAY,YAAY,EAAE;AAAA,IAC9B;AACA,gBAAY,OAAO;AACnB,gBAAY,eAAe;AAAA,EAC/B;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,aAAa;AACxD,QAAI,YAAY,MAAM;AAClB,kBAAY,iBAAiB,YAAY,IAAI;AAAA,IACjD;AACA,gBAAY,OAAO;AACnB,gBAAY,eAAe;AAAA,EAC/B;AACA,EAAAA,SAAQ,UAAU,cAAc,WAAY;AACxC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,UAAU;AAChD,QAAI,KAAK,aAAa,KAAK,cAAc,UAAU;AAC/C,WAAK,eAAe;AAAA,IACxB;AACA,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC3C,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU;AACV,WAAK,iBAAiB,QAAQ;AAC9B,WAAK,YAAY;AACjB,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,iBAAiB,WAAY;AAC3C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,SAAQ,UAAU,iBAAiB,SAAU,QAAQ;AACjD,QAAI,sBAAsB,KAAK;AAC/B,QAAI,wBAAwB,QAAQ;AAChC;AAAA,IACJ;AACA,QAAI,uBAAuB,wBAAwB,QAAQ;AACvD,WAAK,kBAAkB;AAAA,IAC3B;AACA,QAAI,MAAuC;AACvC,UAAI,OAAO,QAAQ,CAAC,OAAO,cAAc;AACrC,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC7D;AAAA,IACJ;AACA,WAAO,qBAAqB,IAAI,sBAAc;AAC9C,SAAK,iBAAiB,MAAM;AAC5B,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,gBAAgB,SAAU,KAAK;AAC7C,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa,CAAC;AAAA,IACvB;AACA,WAAO,KAAK,YAAY,GAAG;AAC3B,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,mBAAmB,WAAY;AAC7C,SAAK,aAAa;AAClB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,oBAAoB,WAAY;AAC9C,QAAI,SAAS,KAAK;AAClB,QAAI,QAAQ;AACR,aAAO,qBAAqB;AAC5B,WAAK,iBAAiB,MAAM;AAC5B,WAAK,eAAe;AACpB,WAAK,yBAAyB;AAC9B,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,mBAAmB,WAAY;AAC7C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,WAAW;AACtD,QAAI,KAAK,cAAc,KAAK,eAAe,WAAW;AAClD,WAAK,oBAAoB;AAAA,IAC7B;AACA,SAAK,iBAAiB,SAAS;AAC/B,SAAK,aAAa;AAClB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,sBAAsB,WAAY;AAChD,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACX,WAAK,iBAAiB,SAAS;AAC/B,WAAK,aAAa;AAClB,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,aAAa,WAAY;AACvC,SAAK,WAAW;AAChB,QAAI,KAAK,KAAK;AACd,QAAI,IAAI;AACJ,UAAI,KAAK,WAAW;AAChB,WAAG,aAAa;AAAA,MACpB,OACK;AACD,WAAG,QAAQ;AAAA,MACf;AAAA,IACJ;AACA,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,WAAW;AAAA,IACjC;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,QAAQ,WAAY;AAClC,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,wBAAwB,SAAU,SAAS;AACzD,SAAK,YAAY;AACjB,QAAI,cAAc,KAAK;AACvB,QAAI,YAAY,KAAK;AACrB,QAAI,aAAa;AACb,kBAAY,YAAY;AAAA,IAC5B;AACA,QAAI,WAAW;AACX,gBAAU,YAAY;AAAA,IAC1B;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,IAAI;AAC1C,QAAI,KAAK,SAAS,IAAI;AAClB;AAAA,IACJ;AACA,SAAK,OAAO;AACZ,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACX,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,WAAG,UAAU,YAAY,UAAU,CAAC,CAAC;AAAA,MACzC;AAAA,IACJ;AACA,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,YAAY,EAAE;AAAA,IACjC;AACA,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,YAAY,EAAE;AAAA,IACpC;AACA,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW,YAAY,EAAE;AAAA,IAClC;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,IAAI;AAC/C,QAAI,CAAC,KAAK,MAAM;AACZ;AAAA,IACJ;AACA,SAAK,OAAO;AACZ,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW;AACX,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,WAAG,UAAU,eAAe,UAAU,CAAC,CAAC;AAAA,MAC5C;AAAA,IACJ;AACA,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,iBAAiB,EAAE;AAAA,IACtC;AACA,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,iBAAiB,EAAE;AAAA,IACzC;AACA,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW,iBAAiB,EAAE;AAAA,IACvC;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,UAAU,SAAU,KAAK,MAAM,wBAAwB;AACrE,QAAI,SAAS,MAAM,KAAK,GAAG,IAAI;AAC/B,QAAI,MAAuC;AACvC,UAAI,CAAC,QAAQ;AACT,iBAAS,eACH,MACA,iCACA,KAAK,EAAE;AACb;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,WAAW,IAAI,iBAAS,QAAQ,MAAM,sBAAsB;AAChE,YAAQ,SAAS,aAAa;AAC9B,SAAK,YAAY,UAAU,GAAG;AAC9B,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,UAAU,KAAK;AACrD,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,aAAS,OAAO,WAAY;AACxB,SAAG,sBAAsB,GAAG;AAAA,IAChC,CAAC,EAAE,KAAK,WAAY;AAChB,UAAI,YAAY,GAAG;AACnB,UAAI,MAAM,QAAQ,WAAW,QAAQ;AACrC,UAAI,OAAO,GAAG;AACV,kBAAU,OAAO,KAAK,CAAC;AAAA,MAC3B;AAAA,IACJ,CAAC;AACD,SAAK,UAAU,KAAK,QAAQ;AAC5B,QAAI,IAAI;AACJ,SAAG,UAAU,YAAY,QAAQ;AAAA,IACrC;AACA,UAAM,GAAG,OAAO;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,wBAAwB,SAAU,KAAK;AACrD,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,SAAQ,UAAU,gBAAgB,SAAU,OAAO,eAAe;AAC9D,QAAI,YAAY,KAAK;AACrB,QAAIC,OAAM,UAAU;AACpB,QAAI,gBAAgB,CAAC;AACrB,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,UAAI,WAAW,UAAU,CAAC;AAC1B,UAAI,CAAC,SAAS,UAAU,SAAS,OAAO;AACpC,iBAAS,KAAK,aAAa;AAAA,MAC/B,OACK;AACD,sBAAc,KAAK,QAAQ;AAAA,MAC/B;AAAA,IACJ;AACA,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AACA,EAAAD,SAAQ,UAAU,YAAY,SAAU,QAAQ,KAAK,gBAAgB;AACjE,cAAU,MAAM,QAAQ,KAAK,cAAc;AAAA,EAC/C;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,QAAQ,KAAK,gBAAgB;AACnE,cAAU,MAAM,QAAQ,KAAK,gBAAgB,IAAI;AAAA,EACrD;AACA,EAAAA,SAAQ,UAAU,mBAAmB,SAAU,WAAW,QAAQ,KAAK,gBAAgB;AACnF,QAAI,YAAY,UAAU,MAAM,QAAQ,KAAK,cAAc;AAC3D,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,gBAAU,CAAC,EAAE,wBAAwB;AAAA,IACzC;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,kBAAkB,WAAY;AAC5C,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,eAAe,WAAY;AACzC,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,mBAAoB,WAAY;AACpC,QAAI,UAAUA,SAAQ;AACtB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,SACJ,QAAQ,SACJ,QAAQ,UACJ,QAAQ,YACJ,QAAQ,WACJ,QAAQ,aACJ,QAAQ,YAAY;AAC5C,YAAQ,UAAU;AAClB,QAAI,OAAO,CAAC;AACZ,aAAS,mBAAmB,KAAK,MAAM,MAAM;AACzC,UAAI,CAAC,KAAK,MAAM,OAAO,IAAI,GAAG;AAC1B,gBAAQ,KAAK,kBAAkB,MAAM,iCAAiC,OAAO,SAAS,OAAO,WAAW;AACxG,aAAK,MAAM,OAAO,IAAI,IAAI;AAAA,MAC9B;AAAA,IACJ;AACA,aAAS,qBAAqB,KAAK,YAAY,MAAM,MAAM;AACvD,aAAO,eAAe,SAAS,KAAK;AAAA,QAChC,KAAK,WAAY;AACb,cAAI,MAAuC;AACvC,+BAAmB,KAAK,MAAM,IAAI;AAAA,UACtC;AACA,cAAI,CAAC,KAAK,UAAU,GAAG;AACnB,gBAAI,MAAM,KAAK,UAAU,IAAI,CAAC;AAC9B,yBAAa,MAAM,GAAG;AAAA,UAC1B;AACA,iBAAO,KAAK,UAAU;AAAA,QAC1B;AAAA,QACA,KAAK,SAAU,KAAK;AAChB,cAAI,MAAuC;AACvC,+BAAmB,KAAK,MAAM,IAAI;AAAA,UACtC;AACA,eAAK,IAAI,IAAI,IAAI,CAAC;AAClB,eAAK,IAAI,IAAI,IAAI,CAAC;AAClB,eAAK,UAAU,IAAI;AACnB,uBAAa,MAAM,GAAG;AAAA,QAC1B;AAAA,MACJ,CAAC;AACD,eAAS,aAAaE,OAAM,KAAK;AAC7B,eAAO,eAAe,KAAK,GAAG;AAAA,UAC1B,KAAK,WAAY;AACb,mBAAOA,MAAK,IAAI;AAAA,UACpB;AAAA,UACA,KAAK,SAAU,KAAK;AAChB,YAAAA,MAAK,IAAI,IAAI;AAAA,UACjB;AAAA,QACJ,CAAC;AACD,eAAO,eAAe,KAAK,GAAG;AAAA,UAC1B,KAAK,WAAY;AACb,mBAAOA,MAAK,IAAI;AAAA,UACpB;AAAA,UACA,KAAK,SAAU,KAAK;AAChB,YAAAA,MAAK,IAAI,IAAI;AAAA,UACjB;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAI,OAAO,gBAAgB;AACvB,2BAAqB,YAAY,cAAc,KAAK,GAAG;AACvD,2BAAqB,SAAS,gBAAgB,UAAU,QAAQ;AAChE,2BAAqB,UAAU,iBAAiB,WAAW,SAAS;AAAA,IACxE;AAAA,EACJ,EAAG;AACH,SAAOF;AACX,EAAE;AACF,MAAM,SAAS,gBAAQ;AACvB,MAAM,SAAS,qBAAa;AAC5B,SAAS,UAAU,YAAY,QAAQ,KAAK,gBAAgB,SAAS;AACjE,QAAM,OAAO,CAAC;AACd,MAAI,YAAY,CAAC;AACjB,mBAAiB,YAAY,IAAI,YAAY,QAAQ,KAAK,gBAAgB,WAAW,OAAO;AAC5F,MAAI,cAAc,UAAU;AAC5B,MAAI,eAAe;AACnB,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,WAAY;AACrB,mBAAe;AACf;AACA,QAAI,eAAe,GAAG;AAClB,qBACO,WAAW,QAAQ,IACnB,cAAc,WAAW;AAAA,IACpC;AAAA,EACJ;AACA,MAAI,YAAY,WAAY;AACxB;AACA,QAAI,eAAe,GAAG;AAClB,qBACO,WAAW,QAAQ,IACnB,cAAc,WAAW;AAAA,IACpC;AAAA,EACJ;AACA,MAAI,CAAC,aAAa;AACd,eAAW,QAAQ;AAAA,EACvB;AACA,MAAI,UAAU,SAAS,KAAK,IAAI,QAAQ;AACpC,cAAU,CAAC,EAAE,OAAO,SAAUG,SAAQ,SAAS;AAC3C,UAAI,OAAO,OAAO;AAAA,IACtB,CAAC;AAAA,EACL;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAI,QAAQ;AACR,eAAS,KAAK,MAAM;AAAA,IACxB;AACA,QAAI,WAAW;AACX,eAAS,QAAQ,SAAS;AAAA,IAC9B;AACA,QAAI,IAAI,OAAO;AACX,eAAS,SAAS,IAAI,QAAQ;AAAA,IAClC;AACA,aAAS,MAAM,IAAI,MAAM;AAAA,EAC7B;AACA,SAAO;AACX;AACA,SAAS,eAAe,QAAQ,QAAQF,MAAK;AACzC,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,WAAO,CAAC,IAAI,OAAO,CAAC;AAAA,EACxB;AACJ;AACA,SAAS,UAAU,OAAO;AACtB,SAAO,YAAY,MAAM,CAAC,CAAC;AAC/B;AACA,SAAS,UAAU,QAAQ,QAAQ,KAAK;AACpC,MAAI,YAAY,OAAO,GAAG,CAAC,GAAG;AAC1B,QAAI,CAAC,YAAY,OAAO,GAAG,CAAC,GAAG;AAC3B,aAAO,GAAG,IAAI,CAAC;AAAA,IACnB;AACA,QAAI,aAAa,OAAO,GAAG,CAAC,GAAG;AAC3B,UAAIA,OAAM,OAAO,GAAG,EAAE;AACtB,UAAI,OAAO,GAAG,EAAE,WAAWA,MAAK;AAC5B,eAAO,GAAG,IAAI,IAAK,OAAO,GAAG,EAAE,YAAaA,IAAG;AAC/C,uBAAe,OAAO,GAAG,GAAG,OAAO,GAAG,GAAGA,IAAG;AAAA,MAChD;AAAA,IACJ,OACK;AACD,UAAI,YAAY,OAAO,GAAG;AAC1B,UAAI,YAAY,OAAO,GAAG;AAC1B,UAAI,OAAO,UAAU;AACrB,UAAI,UAAU,SAAS,GAAG;AACtB,YAAI,OAAO,UAAU,CAAC,EAAE;AACxB,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,cAAI,CAAC,UAAU,CAAC,GAAG;AACf,sBAAU,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1D,OACK;AACD,2BAAe,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI;AAAA,UACnD;AAAA,QACJ;AAAA,MACJ,OACK;AACD,uBAAe,WAAW,WAAW,IAAI;AAAA,MAC7C;AACA,gBAAU,SAAS,UAAU;AAAA,IACjC;AAAA,EACJ,OACK;AACD,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC5B;AACJ;AACA,SAAS,YAAY,MAAM,MAAM;AAC7B,SAAO,SAAS,QACT,YAAY,IAAI,KAAK,YAAY,IAAI,KAAK,cAAc,MAAM,IAAI;AAC7E;AACA,SAAS,cAAc,MAAM,MAAM;AAC/B,MAAIA,OAAM,KAAK;AACf,MAAIA,SAAQ,KAAK,QAAQ;AACrB,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,YAAY,QAAQ,YAAY,QAAQ,KAAK,gBAAgB,WAAW,SAAS;AACvG,MAAI,aAAa,KAAK,MAAM;AAC5B,MAAI,WAAW,IAAI;AACnB,MAAI,QAAQ,IAAI;AAChB,MAAI,WAAW,IAAI;AACnB,MAAI,aAAa,IAAI;AACrB,MAAI,aAAa,CAAC,SAAS,cAAc;AACzC,MAAI,kBAAkB,WAAW;AACjC,MAAI,gBAAgB,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,QAAI,WAAW,WAAW,CAAC;AAC3B,QAAI,YAAY,OAAO,QAAQ;AAC/B,QAAI,aAAa,QAAQ,WAAW,QAAQ,KAAK,SACzC,cAAc,eAAe,QAAQ,IAAI;AAC7C,UAAI,SAAS,SAAS,KACf,CAAC,YAAY,SAAS,KACtB,CAAC,iBAAiB,SAAS,GAAG;AACjC,YAAI,QAAQ;AACR,cAAI,CAAC,SAAS;AACV,uBAAW,QAAQ,IAAI;AACvB,uBAAW,sBAAsB,MAAM;AAAA,UAC3C;AACA;AAAA,QACJ;AACA,yBAAiB,YAAY,UAAU,WAAW,QAAQ,GAAG,WAAW,KAAK,kBAAkB,eAAe,QAAQ,GAAG,WAAW,OAAO;AAAA,MAC/I,OACK;AACD,sBAAc,KAAK,QAAQ;AAAA,MAC/B;AAAA,IACJ,WACS,CAAC,SAAS;AACf,iBAAW,QAAQ,IAAI;AACvB,iBAAW,sBAAsB,MAAM;AACvC,oBAAc,KAAK,QAAQ;AAAA,IAC/B;AAAA,EACJ;AACA,MAAI,SAAS,cAAc;AAC3B,MAAI,CAAC,YAAY,QAAQ;AACrB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,UAAI,WAAW,gBAAgB,CAAC;AAChC,UAAI,SAAS,eAAe,QAAQ;AAChC,YAAI,aAAa,SAAS,WAAW,aAAa;AAClD,YAAI,YAAY;AACZ,cAAI,MAAM,QAAQ,iBAAiB,QAAQ;AAC3C,0BAAgB,OAAO,KAAK,CAAC;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,CAAC,IAAI,OAAO;AACZ,oBAAgB,OAAO,eAAe,SAAU,KAAK;AAAE,aAAO,CAAC,YAAY,OAAO,GAAG,GAAG,WAAW,GAAG,CAAC;AAAA,IAAG,CAAC;AAC3G,aAAS,cAAc;AAAA,EAC3B;AACA,MAAI,SAAS,KACL,IAAI,SAAS,CAAC,UAAU,QAAS;AACrC,QAAI,iBAAiB;AACrB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,SAAS;AACT,uBAAiB,CAAC;AAClB,UAAI,YAAY;AACZ,yBAAiB,CAAC;AAAA,MACtB;AACA,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAI,WAAW,cAAc,CAAC;AAC9B,uBAAe,QAAQ,IAAI,WAAW,QAAQ;AAC9C,YAAI,YAAY;AACZ,yBAAe,QAAQ,IAAI,OAAO,QAAQ;AAAA,QAC9C,OACK;AACD,qBAAW,QAAQ,IAAI,OAAO,QAAQ;AAAA,QAC1C;AAAA,MACJ;AAAA,IACJ,WACS,YAAY;AACjB,oBAAc,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAI,WAAW,cAAc,CAAC;AAC9B,oBAAY,QAAQ,IAAI,WAAW,WAAW,QAAQ,CAAC;AACvD,kBAAU,YAAY,QAAQ,QAAQ;AAAA,MAC1C;AAAA,IACJ;AACA,QAAI,WAAW,IAAI,iBAAS,YAAY,OAAO,OAAO,WAAW,OAAO,iBAAiB,SAAUG,WAAU;AAAE,aAAOA,UAAS,eAAe;AAAA,IAAQ,CAAC,IAAI,IAAI;AAC/J,aAAS,aAAa;AACtB,QAAI,IAAI,OAAO;AACX,eAAS,QAAQ,IAAI;AAAA,IACzB;AACA,QAAI,cAAc,gBAAgB;AAC9B,eAAS,aAAa,GAAG,gBAAgB,aAAa;AAAA,IAC1D;AACA,QAAI,aAAa;AACb,eAAS,aAAa,GAAG,aAAa,aAAa;AAAA,IACvD;AACA,aAAS,aAAa,YAAY,OAAO,MAAM,UAAU,UAAU,iBAAiB,QAAQ,aAAa,EAAE,MAAM,SAAS,CAAC;AAC3H,eAAW,YAAY,UAAU,MAAM;AACvC,cAAU,KAAK,QAAQ;AAAA,EAC3B;AACJ;AACA,IAAO,kBAAQ;;;ACphCf,IAAI,kBAAkB,gBAAgB,KAAK,MAAO,KAAK,OAAO,IAAI,EAAG;AAC9D,IAAI,uBAAuB;AAAA,EAC9B,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,aAAa;AAAA,EACb,SAAS;AAAA,EACT,OAAO;AACX;AACO,IAAI,iCAAiC;AAAA,EACxC,OAAO;AAAA,IACH,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,aAAa;AAAA,IACb,SAAS;AAAA,EACb;AACJ;AACA,qBAAqB,eAAe,IAAI;AACxC,IAAIC,uBAAsB,CAAC,KAAK,MAAM,WAAW;AACjD,IAAI,qCAAqC,CAAC,WAAW;AACrD,IAAI,cAAe,SAAU,QAAQ;AACjC,YAAUC,cAAa,MAAM;AAC7B,WAASA,aAAY,OAAO;AACxB,WAAO,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,EACvC;AACA,EAAAA,aAAY,UAAU,QAAQ,SAAU,OAAO;AAC3C,QAAI,UAAU,KAAK,KAAK;AACxB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,QAAQ,SAAS;AACjB,aAAK,SAAS,MAAM,GAAG,CAAC;AAAA,MAC5B,OACK;AACD,eAAO,UAAU,OAAO,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC;AAAA,MACtD;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,OAAO;AACb,WAAK,SAAS,CAAC,CAAC;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,aAAY,UAAU,cAAc,WAAY;AAAA,EAAE;AAClD,EAAAA,aAAY,UAAU,aAAa,WAAY;AAAA,EAAE;AACjD,EAAAA,aAAY,UAAU,mBAAmB,WAAY;AAAA,EAAE;AACvD,EAAAA,aAAY,UAAU,kBAAkB,WAAY;AAAA,EAAE;AACtD,EAAAA,aAAY,UAAU,kBAAkB,SAAU,WAAW,YAAY,kBAAkB,mBAAmB;AAC1G,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,UACF,KAAK,aACL,KAAK,MAAM,YAAY,KACtB,KAAK,WACF,oBAAoB,MAAM,WAAW,UAAU,KAClD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAI;AAC1B,aAAO;AAAA,IACX;AACA,QAAI,oBAAoB,KAAK,aAAa;AACtC,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,EAAE,GAAG;AAC9C,YAAI,KAAK,YAAY,CAAC,EAAE,WAAW,GAAG;AAClC,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,qBAAqB,KAAK,QAAQ;AAClC,UAAI,WAAW,KAAK;AACpB,aAAO,UAAU;AACb,YAAI,SAAS,QAAQ;AACjB,iBAAO;AAAA,QACX;AACA,mBAAW,SAAS;AAAA,MACxB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,UAAU,SAAU,GAAG,GAAG;AAC5C,WAAO,KAAK,YAAY,GAAG,CAAC;AAAA,EAChC;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,IAAI,SAAS;AACpD,OAAG,KAAK,SAAS,IAAI;AAAA,EACzB;AACA,EAAAA,aAAY,UAAU,cAAc,SAAU,GAAG,GAAG;AAChD,QAAI,QAAQ,KAAK,sBAAsB,GAAG,CAAC;AAC3C,QAAI,OAAO,KAAK,gBAAgB;AAChC,WAAO,KAAK,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EAC1C;AACA,EAAAA,aAAY,UAAU,eAAe,WAAY;AAC7C,QAAI,OAAO,KAAK;AAChB,QAAI,CAAC,KAAK,cAAc,KAAK,SAAS;AAClC,UAAI,YAAY,KAAK;AACrB,UAAI,SAAS,KAAK,gBAAgB;AAClC,UAAI,QAAQ,KAAK;AACjB,UAAI,aAAa,MAAM,cAAc;AACrC,UAAI,gBAAgB,MAAM,iBAAiB;AAC3C,UAAI,gBAAgB,MAAM,iBAAiB;AAC3C,aAAO,KAAK,eAAe,KAAK,aAAa,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACxE,UAAI,WAAW;AACX,6BAAa,eAAe,MAAM,QAAQ,SAAS;AAAA,MACvD,OACK;AACD,aAAK,KAAK,MAAM;AAAA,MACpB;AACA,UAAI,cAAc,iBAAiB,eAAe;AAC9C,aAAK,SAAS,aAAa,IAAI,KAAK,IAAI,aAAa;AACrD,aAAK,UAAU,aAAa,IAAI,KAAK,IAAI,aAAa;AACtD,aAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,gBAAgB,UAAU;AAC7D,aAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,gBAAgB,UAAU;AAAA,MACjE;AACA,UAAI,YAAY,KAAK;AACrB,UAAI,CAAC,KAAK,OAAO,GAAG;AAChB,aAAK,IAAI,KAAK,MAAM,KAAK,IAAI,SAAS;AACtC,aAAK,IAAI,KAAK,MAAM,KAAK,IAAI,SAAS;AACtC,aAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,IAAI,YAAY,CAAC;AACrD,aAAK,SAAS,KAAK,KAAK,KAAK,SAAS,IAAI,YAAY,CAAC;AAAA,MAC3D;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,mBAAmB,SAAU,WAAW;AAC1D,QAAI,WAAW;AACX,WAAK,iBAAiB,KAAK,kBAAkB,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACxE,WAAK,eAAe,KAAK,SAAS;AAAA,IACtC,OACK;AACD,WAAK,iBAAiB;AAAA,IAC1B;AAAA,EACJ;AACA,EAAAA,aAAY,UAAU,mBAAmB,WAAY;AACjD,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,aAAY,UAAU,eAAe,SAAU,MAAM;AACjD,WAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,EACrC;AACA,EAAAA,aAAY,UAAU,wBAAwB,SAAU,WAAW;AAC/D,QAAI,cAAc,SAAS;AACvB,WAAK,WAAW;AAAA,IACpB,OACK;AACD,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,aAAY,UAAU,SAAS,SAAU,KAAK,OAAO;AACjD,QAAI,QAAQ,SAAS;AACjB,aAAO,UAAU,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,IACjD,OACK;AACD,UAAI,CAAC,KAAK,OAAO;AACb,aAAK,SAAS,KAAK;AAAA,MACvB,OACK;AACD,aAAK,SAAS,KAAK;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,UAAU,OAAO;AACxD,QAAI,OAAO,aAAa,UAAU;AAC9B,WAAK,MAAM,QAAQ,IAAI;AAAA,IAC3B,OACK;AACD,aAAO,KAAK,OAAO,QAAQ;AAAA,IAC/B;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,aAAa,SAAU,WAAW;AACpD,QAAI,CAAC,WAAW;AACZ,WAAK,WAAW;AAAA,IACpB;AACA,SAAK,WAAW;AAChB,QAAI,KAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AACA,EAAAA,aAAY,UAAU,QAAQ,WAAY;AACtC,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,aAAY,UAAU,eAAe,WAAY;AAC7C,WAAO,CAAC,EAAE,KAAK,UAAU;AAAA,EAC7B;AACA,EAAAA,aAAY,UAAU,eAAe,WAAY;AAC7C,SAAK,WAAW,CAAC;AAAA,EACrB;AACA,EAAAA,aAAY,UAAU,cAAc,SAAU,KAAK;AAC/C,WAAO,aAAa,sBAAsB,GAAG;AAAA,EACjD;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,KAAK;AAC5C,QAAI,CAAC,IAAI,eAAe,GAAG;AACvB,YAAM,KAAK,YAAY,GAAG;AAAA,IAC9B;AACA,QAAI,KAAK,WAAW;AAChB,WAAK,eAAe;AAAA,IACxB,OACK;AACD,WAAK,QAAQ;AAAA,IACjB;AACA,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,aAAY,UAAU,gBAAgB,SAAU,KAAK;AACjD,WAAO,IAAI,eAAe;AAAA,EAC9B;AACA,EAAAA,aAAY,UAAU,qBAAqB,SAAU,SAAS;AAC1D,WAAO,UAAU,mBAAmB,KAAK,MAAM,OAAO;AACtD,QAAI,cAAc,KAAK;AACvB,QAAI,QAAQ,SAAS,CAAC,YAAY,OAAO;AACrC,kBAAY,QAAQ,KAAK,YAAY,KAAK,YAAY,GAAG,KAAK,KAAK;AAAA,IACvE;AACA,SAAK,qBAAqB,SAAS,aAAaD,oBAAmB;AAAA,EACvE;AACA,EAAAC,aAAY,UAAU,iBAAiB,SAAU,WAAW,OAAO,aAAa,mBAAmB,YAAY,cAAc;AACzH,WAAO,UAAU,eAAe,KAAK,MAAM,WAAW,OAAO,aAAa,mBAAmB,YAAY,YAAY;AACrH,QAAI,uBAAuB,EAAE,SAAS;AACtC,QAAI;AACJ,QAAI,SAAS,MAAM,OAAO;AACtB,UAAI,YAAY;AACZ,YAAI,mBAAmB;AACnB,wBAAc,MAAM;AAAA,QACxB,OACK;AACD,wBAAc,KAAK,YAAY,KAAK,YAAY,GAAG,YAAY,KAAK;AACpE,eAAK,YAAY,aAAa,MAAM,KAAK;AAAA,QAC7C;AAAA,MACJ,OACK;AACD,sBAAc,KAAK,YAAY,KAAK,YAAY,GAAG,oBAAoB,KAAK,QAAQ,YAAY,KAAK;AACrG,aAAK,YAAY,aAAa,MAAM,KAAK;AAAA,MAC7C;AAAA,IACJ,WACS,sBAAsB;AAC3B,oBAAc,YAAY;AAAA,IAC9B;AACA,QAAI,aAAa;AACb,UAAI,YAAY;AACZ,YAAI,cAAc,KAAK;AACvB,aAAK,QAAQ,KAAK,YAAY,uBAAuB,CAAC,IAAI,WAAW;AACrE,YAAI,sBAAsB;AACtB,cAAI,cAAc,KAAK,WAAW;AAClC,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,gBAAI,MAAM,YAAY,CAAC;AACvB,gBAAI,OAAO,aAAa;AACpB,0BAAY,GAAG,IAAI,YAAY,GAAG;AAClC,mBAAK,MAAM,GAAG,IAAI,YAAY,GAAG;AAAA,YACrC;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,aAAa,KAAK,WAAW;AACjC,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,cAAI,MAAM,WAAW,CAAC;AACtB,eAAK,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG;AAAA,QACpC;AACA,aAAK,iBAAiB,WAAW;AAAA,UAC7B,OAAO;AAAA,QACX,GAAG,cAAc,KAAK,uBAAuB,CAAC;AAAA,MAClD,OACK;AACD,aAAK,SAAS,WAAW;AAAA,MAC7B;AAAA,IACJ;AACA,QAAI,aAAa,KAAK,YAAY,qCAAqCD;AACvE,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,SAAS,MAAM,GAAG,KAAK,MAAM;AAC7B,aAAK,GAAG,IAAI,MAAM,GAAG;AAAA,MACzB,WACS,sBAAsB;AAC3B,YAAI,YAAY,GAAG,KAAK,MAAM;AAC1B,eAAK,GAAG,IAAI,YAAY,GAAG;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,EAAAC,aAAY,UAAU,eAAe,SAAU,QAAQ;AACnD,QAAI,cAAc,OAAO,UAAU,aAAa,KAAK,MAAM,MAAM;AACjE,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,MAAM,OAAO;AACb,sBAAc,eAAe,CAAC;AAC9B,aAAK,YAAY,aAAa,MAAM,KAAK;AAAA,MAC7C;AAAA,IACJ;AACA,QAAI,aAAa;AACb,kBAAY,QAAQ;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,cAAc,SAAU,aAAa,aAAa;AACpE,WAAO,aAAa,WAAW;AAC/B,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,yBAAyB,WAAY;AACvD,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,mBAAoB,WAAY;AACxC,QAAI,YAAYA,aAAY;AAC5B,cAAU,OAAO;AACjB,cAAU,YAAY;AACtB,cAAU,IAAI;AACd,cAAU,KAAK;AACf,cAAU,SAAS;AACnB,cAAU,UAAU;AACpB,cAAU,SAAS;AACnB,cAAU,YAAY;AACtB,cAAU,cAAc;AACxB,cAAU,QAAQ;AAClB,cAAU,qBAAqB;AAC/B,cAAU,UAAU,aAAa;AAAA,EACrC,EAAG;AACH,SAAOA;AACX,EAAE,eAAO;AACT,IAAI,UAAU,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACzC,IAAI,WAAW,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC1C,SAAS,oBAAoB,IAAI,OAAO,QAAQ;AAC5C,UAAQ,KAAK,GAAG,gBAAgB,CAAC;AACjC,MAAI,GAAG,WAAW;AACd,YAAQ,eAAe,GAAG,SAAS;AAAA,EACvC;AACA,WAAS,QAAQ;AACjB,WAAS,SAAS;AAClB,SAAO,CAAC,QAAQ,UAAU,QAAQ;AACtC;AACA,IAAO,sBAAQ;;;ACjUf,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,QAAaC,QAAO;AACxB,IAAI,MAAWA,QAAO;AACtB,IAAI,YAAiBA,QAAO;AACrB,SAAS,WAAW,QAAQC,MAAKC,MAAK;AACzC,MAAI,OAAO,WAAW,GAAG;AACrB;AAAA,EACJ;AACA,MAAI,IAAI,OAAO,CAAC;AAChB,MAAI,OAAO,EAAE,CAAC;AACd,MAAI,QAAQ,EAAE,CAAC;AACf,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,SAAS,EAAE,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,CAAC;AACZ,WAAOJ,SAAQ,MAAM,EAAE,CAAC,CAAC;AACzB,YAAQC,SAAQ,OAAO,EAAE,CAAC,CAAC;AAC3B,UAAMD,SAAQ,KAAK,EAAE,CAAC,CAAC;AACvB,aAASC,SAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjC;AACA,EAAAE,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACT,EAAAC,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACb;AACO,SAAS,SAAS,IAAI,IAAI,IAAI,IAAID,MAAKC,MAAK;AAC/C,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AACvB,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AACvB,EAAAI,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AACvB,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,EAAE;AAC3B;AACA,IAAI,OAAO,CAAC;AACZ,IAAI,OAAO,CAAC;AACL,SAAS,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAIE,MAAKC,MAAK;AAChE,MAAIC,gBAAqB;AACzB,MAAIC,WAAgB;AACpB,MAAI,IAAID,cAAa,IAAI,IAAI,IAAI,IAAI,IAAI;AACzC,EAAAF,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACT,EAAAC,KAAI,CAAC,IAAI;AACT,EAAAA,KAAI,CAAC,IAAI;AACT,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,IAAIE,SAAQ,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC;AACvC,IAAAH,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAC1B,IAAAC,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAAA,EAC9B;AACA,MAAIC,cAAa,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,IAAIC,SAAQ,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC;AACvC,IAAAH,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAC1B,IAAAC,KAAI,CAAC,IAAIH,SAAQ,GAAGG,KAAI,CAAC,CAAC;AAAA,EAC9B;AACA,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAD,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC3B,EAAAC,KAAI,CAAC,IAAIH,SAAQ,IAAIG,KAAI,CAAC,CAAC;AAC/B;AACO,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAID,MAAKC,MAAK;AAC5D,MAAIG,qBAA0B;AAC9B,MAAIC,eAAoB;AACxB,MAAI,KAAKP,SAAQD,SAAQO,mBAAkB,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;AAC7D,MAAI,KAAKN,SAAQD,SAAQO,mBAAkB,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;AAC7D,MAAI,IAAIC,aAAY,IAAI,IAAI,IAAI,EAAE;AAClC,MAAI,IAAIA,aAAY,IAAI,IAAI,IAAI,EAAE;AAClC,EAAAL,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC1B,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC1B,EAAAI,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC1B,EAAAG,KAAI,CAAC,IAAIH,SAAQ,IAAI,IAAI,CAAC;AAC9B;AACO,SAAS,QAAQ,GAAG,GAAG,IAAI,IAAI,YAAY,UAAU,eAAeE,MAAKC,MAAK;AACjF,MAAI,UAAe;AACnB,MAAI,UAAe;AACnB,MAAI,OAAO,KAAK,IAAI,aAAa,QAAQ;AACzC,MAAI,OAAO,MAAM,QAAQ,OAAO,MAAM;AAClC,IAAAD,KAAI,CAAC,IAAI,IAAI;AACb,IAAAA,KAAI,CAAC,IAAI,IAAI;AACb,IAAAC,KAAI,CAAC,IAAI,IAAI;AACb,IAAAA,KAAI,CAAC,IAAI,IAAI;AACb;AAAA,EACJ;AACA,QAAM,CAAC,IAAI,QAAQ,UAAU,IAAI,KAAK;AACtC,QAAM,CAAC,IAAI,QAAQ,UAAU,IAAI,KAAK;AACtC,MAAI,CAAC,IAAI,QAAQ,QAAQ,IAAI,KAAK;AAClC,MAAI,CAAC,IAAI,QAAQ,QAAQ,IAAI,KAAK;AAClC,UAAQD,MAAK,OAAO,GAAG;AACvB,UAAQC,MAAK,OAAO,GAAG;AACvB,eAAa,aAAc;AAC3B,MAAI,aAAa,GAAG;AAChB,iBAAa,aAAa;AAAA,EAC9B;AACA,aAAW,WAAY;AACvB,MAAI,WAAW,GAAG;AACd,eAAW,WAAW;AAAA,EAC1B;AACA,MAAI,aAAa,YAAY,CAAC,eAAe;AACzC,gBAAY;AAAA,EAChB,WACS,aAAa,YAAY,eAAe;AAC7C,kBAAc;AAAA,EAClB;AACA,MAAI,eAAe;AACf,QAAI,MAAM;AACV,eAAW;AACX,iBAAa;AAAA,EACjB;AACA,WAAS,QAAQ,GAAG,QAAQ,UAAU,SAAS,KAAK,KAAK,GAAG;AACxD,QAAI,QAAQ,YAAY;AACpB,gBAAU,CAAC,IAAI,QAAQ,KAAK,IAAI,KAAK;AACrC,gBAAU,CAAC,IAAI,QAAQ,KAAK,IAAI,KAAK;AACrC,cAAQD,MAAK,WAAWA,IAAG;AAC3B,cAAQC,MAAK,WAAWA,IAAG;AAAA,IAC/B;AAAA,EACJ;AACJ;;;ACtHA,IAAI,MAAM;AAAA,EACN,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACP;AACA,IAAI,UAAU,CAAC;AACf,IAAI,UAAU,CAAC;AACf,IAAIK,OAAM,CAAC;AACX,IAAIC,OAAM,CAAC;AACX,IAAIC,QAAO,CAAC;AACZ,IAAIC,QAAO,CAAC;AACZ,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAIC,WAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI,KAAK,KAAK;AACd,IAAIC,OAAM,KAAK;AACf,IAAI,gBAAgB,OAAO,iBAAiB;AAC5C,IAAI,YAAY,CAAC;AACjB,SAAS,OAAO,QAAQ;AACpB,MAAI,IAAI,KAAK,MAAM,SAAS,KAAK,GAAG,IAAI;AACxC,SAAQ,IAAI,IAAK;AACrB;AACO,SAAS,mBAAmB,QAAQ,eAAe;AACtD,MAAI,gBAAgB,OAAO,OAAO,CAAC,CAAC;AACpC,MAAI,gBAAgB,GAAG;AACnB,qBAAiBA;AAAA,EACrB;AACA,MAAI,QAAQ,gBAAgB,OAAO,CAAC;AACpC,MAAI,cAAc,OAAO,CAAC;AAC1B,iBAAe;AACf,MAAI,CAAC,iBAAiB,cAAc,iBAAiBA,MAAK;AACtD,kBAAc,gBAAgBA;AAAA,EAClC,WACS,iBAAiB,gBAAgB,eAAeA,MAAK;AAC1D,kBAAc,gBAAgBA;AAAA,EAClC,WACS,CAAC,iBAAiB,gBAAgB,aAAa;AACpD,kBAAc,iBAAiBA,OAAM,OAAO,gBAAgB,WAAW;AAAA,EAC3E,WACS,iBAAiB,gBAAgB,aAAa;AACnD,kBAAc,iBAAiBA,OAAM,OAAO,cAAc,aAAa;AAAA,EAC3E;AACA,SAAO,CAAC,IAAI;AACZ,SAAO,CAAC,IAAI;AAChB;AACA,IAAI,YAAa,WAAY;AACzB,WAASC,WAAU,aAAa;AAC5B,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,QAAI,aAAa;AACb,WAAK,YAAY;AAAA,IACrB;AACA,QAAI,KAAK,WAAW;AAChB,WAAK,OAAO,CAAC;AAAA,IACjB;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,kBAAkB,WAAY;AAC9C,SAAK;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,WAAU,UAAU,WAAW,SAAU,IAAI,IAAI,wBAAwB;AACrE,6BAAyB,0BAA0B;AACnD,QAAI,yBAAyB,GAAG;AAC5B,WAAK,MAAM,QAAQ,yBAAyB,mBAAM,EAAE,KAAK;AACzD,WAAK,MAAM,QAAQ,yBAAyB,mBAAM,EAAE,KAAK;AAAA,IAC7D;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,SAAS,SAAUC,MAAK;AACxC,SAAK,MAAMA;AAAA,EACf;AACA,EAAAD,WAAU,UAAU,aAAa,SAAU,KAAK;AAC5C,SAAK,OAAO;AAAA,EAChB;AACA,EAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,WAAU,UAAU,YAAY,WAAY;AACxC,SAAK,QAAQ,KAAK,KAAK,UAAU;AACjC,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,QAAI,KAAK,WAAW;AAChB,WAAK,OAAO;AAAA,IAChB;AACA,QAAI,KAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,WAAW;AAAA,IACpB;AACA,SAAK;AAAA,EACT;AACA,EAAAA,WAAU,UAAU,SAAS,SAAU,GAAG,GAAG;AACzC,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,GAAG,GAAG,CAAC;AACxB,SAAK,QAAQ,KAAK,KAAK,OAAO,GAAG,CAAC;AAClC,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,SAAS,SAAU,GAAG,GAAG;AACzC,QAAI,KAAK,QAAQ,IAAI,KAAK,GAAG;AAC7B,QAAI,KAAK,QAAQ,IAAI,KAAK,GAAG;AAC7B,QAAI,aAAa,KAAK,KAAK,OAAO,KAAK,KAAK;AAC5C,SAAK,QAAQ,IAAI,GAAG,GAAG,CAAC;AACxB,QAAI,KAAK,QAAQ,YAAY;AACzB,WAAK,KAAK,OAAO,GAAG,CAAC;AAAA,IACzB;AACA,QAAI,YAAY;AACZ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,WAAK,iBAAiB;AAAA,IAC1B,OACK;AACD,UAAI,KAAK,KAAK,KAAK,KAAK;AACxB,UAAI,KAAK,KAAK,gBAAgB;AAC1B,aAAK,cAAc;AACnB,aAAK,cAAc;AACnB,aAAK,iBAAiB;AAAA,MAC1B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,gBAAgB,SAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClE,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1C,QAAI,KAAK,MAAM;AACX,WAAK,KAAK,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IAClD;AACA,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,mBAAmB,SAAU,IAAI,IAAI,IAAI,IAAI;AAC7D,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE;AAClC,QAAI,KAAK,MAAM;AACX,WAAK,KAAK,iBAAiB,IAAI,IAAI,IAAI,EAAE;AAAA,IAC7C;AACA,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,MAAM,SAAU,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe;AAChF,SAAK,eAAe;AACpB,cAAU,CAAC,IAAI;AACf,cAAU,CAAC,IAAI;AACf,uBAAmB,WAAW,aAAa;AAC3C,iBAAa,UAAU,CAAC;AACxB,eAAW,UAAU,CAAC;AACtB,QAAI,QAAQ,WAAW;AACvB,SAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,YAAY,OAAO,GAAG,gBAAgB,IAAI,CAAC;AAC7E,SAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,YAAY,UAAU,aAAa;AACzE,SAAK,MAAMH,SAAQ,QAAQ,IAAI,IAAI;AACnC,SAAK,MAAMC,SAAQ,QAAQ,IAAI,IAAI;AACnC,WAAO;AAAA,EACX;AACA,EAAAE,WAAU,UAAU,QAAQ,SAAU,IAAI,IAAI,IAAI,IAAI,QAAQ;AAC1D,SAAK,eAAe;AACpB,QAAI,KAAK,MAAM;AACX,WAAK,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,IAC1C;AACA,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,OAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AAC7C,SAAK,eAAe;AACpB,SAAK,QAAQ,KAAK,KAAK,KAAK,GAAG,GAAG,GAAG,CAAC;AACtC,SAAK,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9B,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,YAAY,WAAY;AACxC,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,CAAC;AAClB,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACL,UAAI,UAAU;AAAA,IAClB;AACA,SAAK,MAAM;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,UAAU,OAAO,SAAU,KAAK;AACtC,WAAO,IAAI,KAAK;AAChB,SAAK,SAAS;AAAA,EAClB;AACA,EAAAA,WAAU,UAAU,SAAS,SAAU,KAAK;AACxC,WAAO,IAAI,OAAO;AAClB,SAAK,SAAS;AAAA,EAClB;AACA,EAAAA,WAAU,UAAU,MAAM,WAAY;AAClC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,WAAU,UAAU,UAAU,SAAU,MAAM;AAC1C,QAAIE,OAAM,KAAK;AACf,QAAI,EAAE,KAAK,QAAQ,KAAK,KAAK,WAAWA,SAAQ,eAAe;AAC3D,WAAK,OAAO,IAAI,aAAaA,IAAG;AAAA,IACpC;AACA,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,WAAK,KAAK,CAAC,IAAI,KAAK,CAAC;AAAA,IACzB;AACA,SAAK,OAAOA;AAAA,EAChB;AACA,EAAAF,WAAU,UAAU,aAAa,SAAU,MAAM;AAC7C,QAAI,EAAE,gBAAgB,QAAQ;AAC1B,aAAO,CAAC,IAAI;AAAA,IAChB;AACA,QAAIE,OAAM,KAAK;AACf,QAAI,aAAa;AACjB,QAAI,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,oBAAc,KAAK,CAAC,EAAE,IAAI;AAAA,IAC9B;AACA,QAAI,iBAAkB,KAAK,gBAAgB,cAAe;AACtD,WAAK,OAAO,IAAI,aAAa,SAAS,UAAU;AAAA,IACpD;AACA,aAAS,IAAI,GAAG,IAAIA,MAAK,KAAK;AAC1B,UAAI,iBAAiB,KAAK,CAAC,EAAE;AAC7B,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC5C,aAAK,KAAK,QAAQ,IAAI,eAAe,CAAC;AAAA,MAC1C;AAAA,IACJ;AACA,SAAK,OAAO;AAAA,EAChB;AACA,EAAAF,WAAU,UAAU,UAAU,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACjE,QAAI,CAAC,KAAK,WAAW;AACjB;AAAA,IACJ;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,OAAO,UAAU,SAAS,KAAK,QAAQ;AAC5C,WAAK,YAAY;AACjB,aAAO,KAAK;AAAA,IAChB;AACA,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,WAAK,KAAK,MAAM,IAAI,UAAU,CAAC;AAAA,IACnC;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,iBAAiB,WAAY;AAC7C,QAAI,KAAK,iBAAiB,GAAG;AACzB,WAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,aAAa,KAAK,WAAW;AAChE,WAAK,iBAAiB;AAAA,IAC1B;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,QAAI,EAAE,KAAK,gBAAgB,QAAQ;AAC/B,UAAI,UAAU,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,gBAAQ,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,MAC5B;AACA,WAAK,OAAO;AAAA,IAChB;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,QAAI,CAAC,KAAK,WAAW;AACjB;AAAA,IACJ;AACA,SAAK,eAAe;AACpB,QAAI,OAAO,KAAK;AAChB,QAAI,gBAAgB,OAAO;AACvB,WAAK,SAAS,KAAK;AACnB,UAAI,iBAAiB,KAAK,OAAO,IAAI;AACjC,aAAK,OAAO,IAAI,aAAa,IAAI;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,WAAU,UAAU,kBAAkB,WAAY;AAC9C,IAAAT,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAIE,MAAK,CAAC,IAAIA,MAAK,CAAC,IAAI,OAAO;AAC7C,IAAAD,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAIE,MAAK,CAAC,IAAIA,MAAK,CAAC,IAAI,CAAC,OAAO;AAC9C,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI;AACJ,SAAK,IAAI,GAAG,IAAI,KAAK,QAAO;AACxB,UAAI,MAAM,KAAK,GAAG;AAClB,UAAI,UAAU,MAAM;AACpB,UAAI,SAAS;AACT,aAAK,KAAK,CAAC;AACX,aAAK,KAAK,IAAI,CAAC;AACf,aAAK;AACL,aAAK;AAAA,MACT;AACA,cAAQ,KAAK;AAAA,QACT,KAAK,IAAI;AACL,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB,UAAAD,MAAK,CAAC,IAAI;AACV,UAAAA,MAAK,CAAC,IAAI;AACV,UAAAC,MAAK,CAAC,IAAI;AACV,UAAAA,MAAK,CAAC,IAAI;AACV;AAAA,QACJ,KAAK,IAAI;AACL,mBAAS,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAGD,OAAMC,KAAI;AACjD,eAAK,KAAK,GAAG;AACb,eAAK,KAAK,GAAG;AACb;AAAA,QACJ,KAAK,IAAI;AACL,oBAAU,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAGD,OAAMC,KAAI;AAC9F,eAAK,KAAK,GAAG;AACb,eAAK,KAAK,GAAG;AACb;AAAA,QACJ,KAAK,IAAI;AACL,wBAAc,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAGD,OAAMC,KAAI;AAC5E,eAAK,KAAK,GAAG;AACb,eAAK,KAAK,GAAG;AACb;AAAA,QACJ,KAAK,IAAI;AACL,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,aAAa,KAAK,GAAG;AACzB,cAAI,WAAW,KAAK,GAAG,IAAI;AAC3B,eAAK;AACL,cAAI,gBAAgB,CAAC,KAAK,GAAG;AAC7B,cAAI,SAAS;AACT,iBAAKG,SAAQ,UAAU,IAAI,KAAK;AAChC,iBAAKC,SAAQ,UAAU,IAAI,KAAK;AAAA,UACpC;AACA,kBAAQ,IAAI,IAAI,IAAI,IAAI,YAAY,UAAU,eAAeL,OAAMC,KAAI;AACvE,eAAKG,SAAQ,QAAQ,IAAI,KAAK;AAC9B,eAAKC,SAAQ,QAAQ,IAAI,KAAK;AAC9B;AAAA,QACJ,KAAK,IAAI;AACL,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB,cAAI,QAAQ,KAAK,GAAG;AACpB,cAAI,SAAS,KAAK,GAAG;AACrB,mBAAS,IAAI,IAAI,KAAK,OAAO,KAAK,QAAQL,OAAMC,KAAI;AACpD;AAAA,QACJ,KAAK,IAAI;AACL,eAAK;AACL,eAAK;AACL;AAAA,MACR;AACA,MAAK,IAAIH,MAAKA,MAAKE,KAAI;AACvB,MAAK,IAAID,MAAKA,MAAKE,KAAI;AAAA,IAC3B;AACA,QAAI,MAAM,GAAG;AACT,MAAAH,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAIC,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAI;AAAA,IACxC;AACA,WAAO,IAAI,qBAAaD,KAAI,CAAC,GAAGA,KAAI,CAAC,GAAGC,KAAI,CAAC,IAAID,KAAI,CAAC,GAAGC,KAAI,CAAC,IAAID,KAAI,CAAC,CAAC;AAAA,EAC5E;AACA,EAAAS,WAAU,UAAU,mBAAmB,WAAY;AAC/C,QAAI,OAAO,KAAK;AAChB,QAAIE,OAAM,KAAK;AACf,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,CAAC,KAAK,aAAa;AACnB,WAAK,cAAc,CAAC;AAAA,IACxB;AACA,QAAI,aAAa,KAAK;AACtB,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAIA,QAAM;AACtB,UAAI,MAAM,KAAK,GAAG;AAClB,UAAI,UAAU,MAAM;AACpB,UAAI,SAAS;AACT,aAAK,KAAK,CAAC;AACX,aAAK,KAAK,IAAI,CAAC;AACf,aAAK;AACL,aAAK;AAAA,MACT;AACA,UAAI,IAAI;AACR,cAAQ,KAAK;AAAA,QACT,KAAK,IAAI;AACL,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB;AAAA,QACJ,KAAK,IAAI,GAAG;AACR,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK;AACd,cAAI,KAAK,KAAK;AACd,cAAI,QAAQ,EAAE,IAAI,MAAM,QAAQ,EAAE,IAAI,MAAM,MAAMA,OAAM,GAAG;AACvD,gBAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC/B,iBAAK;AACL,iBAAK;AAAA,UACT;AACA;AAAA,QACJ;AAAA,QACA,KAAK,IAAI,GAAG;AACR,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAClD,eAAK;AACL,eAAK;AACL;AAAA,QACJ;AAAA,QACA,KAAK,IAAI,GAAG;AACR,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC9C,eAAK;AACL,eAAK;AACL;AAAA,QACJ;AAAA,QACA,KAAK,IAAI;AACL,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,aAAa,KAAK,GAAG;AACzB,cAAI,QAAQ,KAAK,GAAG;AACpB,cAAI,WAAW,QAAQ;AACvB,eAAK;AACL,cAAI,SAAS;AACT,iBAAKL,SAAQ,UAAU,IAAI,KAAK;AAChC,iBAAKC,SAAQ,UAAU,IAAI,KAAK;AAAA,UACpC;AACA,cAAIF,SAAQ,IAAI,EAAE,IAAID,SAAQI,MAAK,KAAK,IAAI,KAAK,CAAC;AAClD,eAAKF,SAAQ,QAAQ,IAAI,KAAK;AAC9B,eAAKC,SAAQ,QAAQ,IAAI,KAAK;AAC9B;AAAA,QACJ,KAAK,IAAI,GAAG;AACR,eAAK,KAAK,KAAK,GAAG;AAClB,eAAK,KAAK,KAAK,GAAG;AAClB,cAAI,QAAQ,KAAK,GAAG;AACpB,cAAI,SAAS,KAAK,GAAG;AACrB,cAAI,QAAQ,IAAI,SAAS;AACzB;AAAA,QACJ;AAAA,QACA,KAAK,IAAI,GAAG;AACR,cAAI,KAAK,KAAK;AACd,cAAI,KAAK,KAAK;AACd,cAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC/B,eAAK;AACL,eAAK;AACL;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,KAAK,GAAG;AACR,mBAAW,UAAU,IAAI;AACzB,wBAAgB;AAAA,MACpB;AAAA,IACJ;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AACA,EAAAE,WAAU,UAAU,cAAc,SAAU,KAAK,SAAS;AACtD,QAAI,IAAI,KAAK;AACb,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK;AACd,QAAIE,OAAM,KAAK;AACf,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,UAAU;AACzB,QAAI;AACJ,QAAI;AACJ,QAAI,cAAc;AAClB,QAAI,WAAW;AACf,QAAI;AACJ,QAAI,gBAAgB;AACpB,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU;AACV,UAAI,CAAC,KAAK,aAAa;AACnB,aAAK,iBAAiB;AAAA,MAC1B;AACA,mBAAa,KAAK;AAClB,qBAAe,KAAK;AACpB,wBAAkB,UAAU;AAC5B,UAAI,CAAC,iBAAiB;AAClB;AAAA,MACJ;AAAA,IACJ;AACA,OAAI,UAAS,IAAI,GAAG,IAAIA,QAAM;AAC1B,UAAI,MAAM,EAAE,GAAG;AACf,UAAI,UAAU,MAAM;AACpB,UAAI,SAAS;AACT,aAAK,EAAE,CAAC;AACR,aAAK,EAAE,IAAI,CAAC;AACZ,aAAK;AACL,aAAK;AAAA,MACT;AACA,UAAI,QAAQ,IAAI,KAAK,gBAAgB,GAAG;AACpC,YAAI,OAAO,YAAY,UAAU;AACjC,wBAAgB;AAAA,MACpB;AACA,cAAQ,KAAK;AAAA,QACT,KAAK,IAAI;AACL,eAAK,KAAK,EAAE,GAAG;AACf,eAAK,KAAK,EAAE,GAAG;AACf,cAAI,OAAO,IAAI,EAAE;AACjB;AAAA,QACJ,KAAK,IAAI,GAAG;AACR,cAAI,EAAE,GAAG;AACT,cAAI,EAAE,GAAG;AACT,cAAI,KAAK,QAAQ,IAAI,EAAE;AACvB,cAAI,KAAK,QAAQ,IAAI,EAAE;AACvB,cAAI,KAAK,MAAM,KAAK,IAAI;AACpB,gBAAI,UAAU;AACV,kBAAI,IAAI,WAAW,UAAU;AAC7B,kBAAI,cAAc,IAAI,iBAAiB;AACnC,oBAAI,KAAK,kBAAkB,eAAe;AAC1C,oBAAI,OAAO,MAAM,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,CAAC;AACrD,sBAAM;AAAA,cACV;AACA,6BAAe;AAAA,YACnB;AACA,gBAAI,OAAO,GAAG,CAAC;AACf,iBAAK;AACL,iBAAK;AACL,4BAAgB;AAAA,UACpB,OACK;AACD,gBAAI,KAAK,KAAK,KAAK,KAAK;AACxB,gBAAI,KAAK,eAAe;AACpB,2BAAa;AACb,2BAAa;AACb,8BAAgB;AAAA,YACpB;AAAA,UACJ;AACA;AAAA,QACJ;AAAA,QACA,KAAK,IAAI,GAAG;AACR,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,UAAU;AACV,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACnC,kBAAI,KAAK,kBAAkB,eAAe;AAC1C,6BAAe,IAAI,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,6BAAe,IAAI,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,kBAAI,cAAc,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AACxF,oBAAM;AAAA,YACV;AACA,2BAAe;AAAA,UACnB;AACA,cAAI,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACxC,eAAK;AACL,eAAK;AACL;AAAA,QACJ;AAAA,QACA,KAAK,IAAI,GAAG;AACR,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,UAAU;AACV,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACnC,kBAAI,KAAK,kBAAkB,eAAe;AAC1C,iCAAmB,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,iCAAmB,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,kBAAI,iBAAiB,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AACnE,oBAAM;AAAA,YACV;AACA,2BAAe;AAAA,UACnB;AACA,cAAI,iBAAiB,IAAI,IAAI,IAAI,EAAE;AACnC,eAAK;AACL,eAAK;AACL;AAAA,QACJ;AAAA,QACA,KAAK,IAAI;AACL,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,KAAK,EAAE,GAAG;AACd,cAAI,aAAa,EAAE,GAAG;AACtB,cAAI,QAAQ,EAAE,GAAG;AACjB,cAAI,MAAM,EAAE,GAAG;AACf,cAAI,gBAAgB,CAAC,EAAE,GAAG;AAC1B,cAAI,IAAK,KAAK,KAAM,KAAK;AACzB,cAAI,YAAY,QAAQ,KAAK,EAAE,IAAI;AACnC,cAAI,WAAW,aAAa;AAC5B,cAAI,aAAa;AACjB,cAAI,UAAU;AACV,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACnC,yBAAW,aAAa,SAAS,kBAAkB,eAAe;AAClE,2BAAa;AAAA,YACjB;AACA,2BAAe;AAAA,UACnB;AACA,cAAI,aAAa,IAAI,SAAS;AAC1B,gBAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,KAAK,YAAY,UAAU,aAAa;AAAA,UACxE,OACK;AACD,gBAAI,IAAI,IAAI,IAAI,GAAG,YAAY,UAAU,aAAa;AAAA,UAC1D;AACA,cAAI,YAAY;AACZ,kBAAM;AAAA,UACV;AACA,cAAI,SAAS;AACT,iBAAKL,SAAQ,UAAU,IAAI,KAAK;AAChC,iBAAKC,SAAQ,UAAU,IAAI,KAAK;AAAA,UACpC;AACA,eAAKD,SAAQ,QAAQ,IAAI,KAAK;AAC9B,eAAKC,SAAQ,QAAQ,IAAI,KAAK;AAC9B;AAAA,QACJ,KAAK,IAAI;AACL,eAAK,KAAK,EAAE,CAAC;AACb,eAAK,KAAK,EAAE,IAAI,CAAC;AACjB,cAAI,EAAE,GAAG;AACT,cAAI,EAAE,GAAG;AACT,cAAI,QAAQ,EAAE,GAAG;AACjB,cAAI,SAAS,EAAE,GAAG;AAClB,cAAI,UAAU;AACV,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACnC,kBAAI,MAAM,kBAAkB;AAC5B,kBAAI,OAAO,GAAG,CAAC;AACf,kBAAI,OAAO,IAAIH,SAAQ,KAAK,KAAK,GAAG,CAAC;AACrC,qBAAO;AACP,kBAAI,MAAM,GAAG;AACT,oBAAI,OAAO,IAAI,OAAO,IAAIA,SAAQ,KAAK,MAAM,CAAC;AAAA,cAClD;AACA,qBAAO;AACP,kBAAI,MAAM,GAAG;AACT,oBAAI,OAAO,IAAIC,SAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI,MAAM;AAAA,cACtD;AACA,qBAAO;AACP,kBAAI,MAAM,GAAG;AACT,oBAAI,OAAO,GAAG,IAAIA,SAAQ,SAAS,KAAK,CAAC,CAAC;AAAA,cAC9C;AACA,oBAAM;AAAA,YACV;AACA,2BAAe;AAAA,UACnB;AACA,cAAI,KAAK,GAAG,GAAG,OAAO,MAAM;AAC5B;AAAA,QACJ,KAAK,IAAI;AACL,cAAI,UAAU;AACV,gBAAI,IAAI,WAAW,UAAU;AAC7B,gBAAI,cAAc,IAAI,iBAAiB;AACnC,kBAAI,KAAK,kBAAkB,eAAe;AAC1C,kBAAI,OAAO,MAAM,IAAI,KAAK,KAAK,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;AACvD,oBAAM;AAAA,YACV;AACA,2BAAe;AAAA,UACnB;AACA,cAAI,UAAU;AACd,eAAK;AACL,eAAK;AAAA,MACb;AAAA,IACJ;AAAA,EACJ;AACA,EAAAI,WAAU,UAAU,QAAQ,WAAY;AACpC,QAAI,WAAW,IAAIA,WAAU;AAC7B,QAAI,OAAO,KAAK;AAChB,aAAS,OAAO,KAAK,QAAQ,KAAK,MAAM,IAClC,MAAM,UAAU,MAAM,KAAK,IAAI;AACrC,aAAS,OAAO,KAAK;AACrB,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,MAAM;AAChB,EAAAA,WAAU,mBAAoB,WAAY;AACtC,QAAI,QAAQA,WAAU;AACtB,UAAM,YAAY;AAClB,UAAM,MAAM;AACZ,UAAM,MAAM;AACZ,UAAM,iBAAiB;AACvB,UAAM,WAAW;AAAA,EACrB,EAAG;AACH,SAAOA;AACX,EAAE;AACF,IAAO,oBAAQ;;;ACxrBR,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,GAAG;AAC3D,MAAI,cAAc,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MACrB,IAAI,KAAK,MAAM,IAAI,KAAK,MACxB,IAAI,KAAK,MAAM,IAAI,KAAK,MACxB,IAAI,KAAK,MAAM,IAAI,KAAK,IAAK;AACjC,WAAO;AAAA,EACX;AACA,MAAI,OAAO,IAAI;AACX,UAAM,KAAK,OAAO,KAAK;AACvB,UAAM,KAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EACrC,OACK;AACD,WAAO,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK;AAAA,EACpC;AACA,MAAI,MAAM,KAAK,IAAI,IAAI;AACvB,MAAI,KAAK,MAAM,OAAO,KAAK,KAAK;AAChC,SAAO,MAAM,KAAK,IAAI,KAAK;AAC/B;;;ACtBO,SAASG,eAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,GAAG;AAC3E,MAAI,cAAc,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,KAAK;AACT,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MACnD,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MACtD,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MACtD,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,IAAK;AAC/D,WAAO;AAAA,EACX;AACA,MAAI,IAAU,kBAAkB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI;AAC1E,SAAO,KAAK,KAAK;AACrB;;;ACbO,SAASC,eAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,GAAG;AACnE,MAAI,cAAc,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,KAAK;AACT,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MACpC,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MACvC,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MACvC,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,IAAK;AAChD,WAAO;AAAA,EACX;AACA,MAAI,IAAI,sBAAsB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI;AAChE,SAAO,KAAK,KAAK;AACrB;;;ACdA,IAAIC,OAAM,KAAK,KAAK;AACb,SAAS,gBAAgB,OAAO;AACnC,WAASA;AACT,MAAI,QAAQ,GAAG;AACX,aAASA;AAAA,EACb;AACA,SAAO;AACX;;;ACNA,IAAIC,OAAM,KAAK,KAAK;AACb,SAASC,eAAc,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe,WAAW,GAAG,GAAG;AAC3F,MAAI,cAAc,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,KAAK;AACT,OAAK;AACL,OAAK;AACL,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,MAAK,IAAI,KAAK,KAAO,IAAI,KAAK,GAAI;AAC9B,WAAO;AAAA,EACX;AACA,MAAI,KAAK,IAAI,aAAa,QAAQ,IAAID,OAAM,MAAM;AAC9C,WAAO;AAAA,EACX;AACA,MAAI,eAAe;AACf,QAAI,MAAM;AACV,iBAAa,gBAAgB,QAAQ;AACrC,eAAW,gBAAgB,GAAG;AAAA,EAClC,OACK;AACD,iBAAa,gBAAgB,UAAU;AACvC,eAAW,gBAAgB,QAAQ;AAAA,EACvC;AACA,MAAI,aAAa,UAAU;AACvB,gBAAYA;AAAA,EAChB;AACA,MAAI,QAAQ,KAAK,MAAM,GAAG,CAAC;AAC3B,MAAI,QAAQ,GAAG;AACX,aAASA;AAAA,EACb;AACA,SAAQ,SAAS,cAAc,SAAS,YAChC,QAAQA,QAAO,cAAc,QAAQA,QAAO;AACxD;;;AClCe,SAAR,YAA6B,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACtD,MAAK,IAAI,MAAM,IAAI,MAAQ,IAAI,MAAM,IAAI,IAAK;AAC1C,WAAO;AAAA,EACX;AACA,MAAI,OAAO,IAAI;AACX,WAAO;AAAA,EACX;AACA,MAAI,KAAK,IAAI,OAAO,KAAK;AACzB,MAAI,MAAM,KAAK,KAAK,IAAI;AACxB,MAAI,MAAM,KAAK,MAAM,GAAG;AACpB,UAAM,KAAK,KAAK,MAAM;AAAA,EAC1B;AACA,MAAI,KAAK,KAAK,KAAK,MAAM;AACzB,SAAO,OAAO,IAAI,WAAW,KAAK,IAAI,MAAM;AAChD;;;ACPA,IAAIE,OAAM,kBAAU;AACpB,IAAIC,OAAM,KAAK,KAAK;AACpB,IAAIC,WAAU;AACd,SAAS,cAAc,GAAG,GAAG;AACzB,SAAO,KAAK,IAAI,IAAI,CAAC,IAAIA;AAC7B;AACA,IAAI,QAAQ,CAAC,IAAI,IAAI,EAAE;AACvB,IAAI,UAAU,CAAC,IAAI,EAAE;AACrB,SAAS,cAAc;AACnB,MAAI,MAAM,QAAQ,CAAC;AACnB,UAAQ,CAAC,IAAI,QAAQ,CAAC;AACtB,UAAQ,CAAC,IAAI;AACjB;AACA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACxD,MAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAC/B,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAK;AAC3C,WAAO;AAAA,EACX;AACA,MAAI,SAAe,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK;AACvD,MAAI,WAAW,GAAG;AACd,WAAO;AAAA,EACX,OACK;AACD,QAAI,IAAI;AACR,QAAI,WAAW;AACf,QAAI,MAAM;AACV,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,UAAI,IAAI,MAAM,CAAC;AACf,UAAI,OAAQ,MAAM,KAAK,MAAM,IAAK,MAAM;AACxC,UAAI,KAAW,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC;AACxC,UAAI,KAAK,GAAG;AACR;AAAA,MACJ;AACA,UAAI,WAAW,GAAG;AACd,mBAAiB,aAAa,IAAI,IAAI,IAAI,IAAI,OAAO;AACrD,YAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,WAAW,GAAG;AACzC,sBAAY;AAAA,QAChB;AACA,cAAY,QAAQ,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAC9C,YAAI,WAAW,GAAG;AACd,gBAAY,QAAQ,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAAA,QAClD;AAAA,MACJ;AACA,UAAI,aAAa,GAAG;AAChB,YAAI,IAAI,QAAQ,CAAC,GAAG;AAChB,eAAK,MAAM,KAAK,OAAO,CAAC;AAAA,QAC5B,WACS,IAAI,QAAQ,CAAC,GAAG;AACrB,eAAK,MAAM,MAAM,OAAO,CAAC;AAAA,QAC7B,OACK;AACD,eAAK,KAAK,MAAM,OAAO,CAAC;AAAA,QAC5B;AAAA,MACJ,OACK;AACD,YAAI,IAAI,QAAQ,CAAC,GAAG;AAChB,eAAK,MAAM,KAAK,OAAO,CAAC;AAAA,QAC5B,OACK;AACD,eAAK,KAAK,MAAM,OAAO,CAAC;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,iBAAiB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACpD,MAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MACrB,IAAI,MAAM,IAAI,MAAM,IAAI,IAAK;AACjC,WAAO;AAAA,EACX;AACA,MAAI,SAAe,gBAAgB,IAAI,IAAI,IAAI,GAAG,KAAK;AACvD,MAAI,WAAW,GAAG;AACd,WAAO;AAAA,EACX,OACK;AACD,QAAI,IAAU,kBAAkB,IAAI,IAAI,EAAE;AAC1C,QAAI,KAAK,KAAK,KAAK,GAAG;AAClB,UAAI,IAAI;AACR,UAAI,KAAW,YAAY,IAAI,IAAI,IAAI,CAAC;AACxC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAI,OAAQ,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAK,MAAM;AACtD,YAAI,KAAW,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC;AAC/C,YAAI,KAAK,GAAG;AACR;AAAA,QACJ;AACA,YAAI,MAAM,CAAC,IAAI,GAAG;AACd,eAAK,KAAK,KAAK,OAAO,CAAC;AAAA,QAC3B,OACK;AACD,eAAK,KAAK,KAAK,OAAO,CAAC;AAAA,QAC3B;AAAA,MACJ;AACA,aAAO;AAAA,IACX,OACK;AACD,UAAI,OAAQ,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAK,MAAM;AACtD,UAAI,KAAW,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC;AAC/C,UAAI,KAAK,GAAG;AACR,eAAO;AAAA,MACX;AACA,aAAO,KAAK,KAAK,OAAO,CAAC;AAAA,IAC7B;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe,GAAG,GAAG;AACtE,OAAK;AACL,MAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AACjC,QAAM,CAAC,IAAI,CAAC;AACZ,QAAM,CAAC,IAAI;AACX,MAAI,SAAS,KAAK,IAAI,aAAa,QAAQ;AAC3C,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,MAAI,UAAUD,OAAM,MAAM;AACtB,iBAAa;AACb,eAAWA;AACX,QAAI,MAAM,gBAAgB,IAAI;AAC9B,QAAI,KAAK,MAAM,CAAC,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,IAAI;AAC1C,aAAO;AAAA,IACX,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,aAAa,UAAU;AACvB,QAAI,QAAQ;AACZ,iBAAa;AACb,eAAW;AAAA,EACf;AACA,MAAI,aAAa,GAAG;AAChB,kBAAcA;AACd,gBAAYA;AAAA,EAChB;AACA,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,KAAK,MAAM,CAAC;AAChB,QAAI,KAAK,KAAK,GAAG;AACb,UAAI,QAAQ,KAAK,MAAM,GAAG,EAAE;AAC5B,UAAI,MAAM,gBAAgB,IAAI;AAC9B,UAAI,QAAQ,GAAG;AACX,gBAAQA,OAAM;AAAA,MAClB;AACA,UAAK,SAAS,cAAc,SAAS,YAC7B,QAAQA,QAAO,cAAc,QAAQA,QAAO,UAAW;AAC3D,YAAI,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK;AAC9C,gBAAM,CAAC;AAAA,QACX;AACA,aAAK;AAAA,MACT;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,MAAM,WAAW,UAAU,GAAG,GAAG;AAClD,MAAI,OAAO,KAAK;AAChB,MAAIE,OAAM,KAAK,IAAI;AACnB,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,GAAG,IAAIA,QAAM;AACtB,QAAI,MAAM,KAAK,GAAG;AAClB,QAAI,UAAU,MAAM;AACpB,QAAI,QAAQH,KAAI,KAAK,IAAI,GAAG;AACxB,UAAI,CAAC,UAAU;AACX,aAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,MACzC;AAAA,IACJ;AACA,QAAI,SAAS;AACT,WAAK,KAAK,CAAC;AACX,WAAK,KAAK,IAAI,CAAC;AACf,WAAK;AACL,WAAK;AAAA,IACT;AACA,YAAQ,KAAK;AAAA,MACT,KAAKA,KAAI;AACL,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,aAAK;AACL,aAAK;AACL;AAAA,MACJ,KAAKA,KAAI;AACL,YAAI,UAAU;AACV,cAAS,cAAc,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;AACnE,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,eAAK,YAAY,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;AAAA,QAC5D;AACA,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACJ,KAAKA,KAAI;AACL,YAAI,UAAU;AACV,cAAUI,eAAc,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;AAChH,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,eAAK,aAAa,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;AAAA,QACzG;AACA,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACJ,KAAKJ,KAAI;AACL,YAAI,UAAU;AACV,cAAcI,eAAc,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;AAC9F,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,eAAK,iBAAiB,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK;AAAA,QACvF;AACA,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACJ,KAAKJ,KAAI;AACL,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AACrB,aAAK;AACL,YAAI,gBAAgB,CAAC,EAAE,IAAI,KAAK,GAAG;AACnC,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAC5B,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAC5B,YAAI,CAAC,SAAS;AACV,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,QACzC,OACK;AACD,eAAK;AACL,eAAK;AAAA,QACT;AACA,YAAI,MAAM,IAAI,MAAM,KAAK,KAAK;AAC9B,YAAI,UAAU;AACV,cAAQI,eAAc,IAAI,IAAI,IAAI,OAAO,QAAQ,QAAQ,eAAe,WAAW,IAAI,CAAC,GAAG;AACvF,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,eAAK,WAAW,IAAI,IAAI,IAAI,OAAO,QAAQ,QAAQ,eAAe,IAAI,CAAC;AAAA,QAC3E;AACA,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC;AAAA,MACJ,KAAKJ,KAAI;AACL,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AACrB,aAAK,KAAK;AACV,aAAK,KAAK;AACV,YAAI,UAAU;AACV,cAAS,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,KAC1C,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,KAC7C,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,KAC7C,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,GAAG;AACxD,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AACrC,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,QACzC;AACA;AAAA,MACJ,KAAKA,KAAI;AACL,YAAI,UAAU;AACV,cAAS,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,GAAG,CAAC,GAAG;AACrD,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,eAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,QACzC;AACA,aAAK;AACL,aAAK;AACL;AAAA,IACR;AAAA,EACJ;AACA,MAAI,CAAC,YAAY,CAAC,cAAc,IAAI,EAAE,GAAG;AACrC,SAAK,YAAY,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,KAAK;AAAA,EAC9C;AACA,SAAO,MAAM;AACjB;AACO,SAAS,QAAQ,WAAW,GAAG,GAAG;AACrC,SAAO,YAAY,WAAW,GAAG,OAAO,GAAG,CAAC;AAChD;AACO,SAASI,eAAc,WAAW,WAAW,GAAG,GAAG;AACtD,SAAO,YAAY,WAAW,WAAW,MAAM,GAAG,CAAC;AACvD;;;ACzSO,IAAI,qBAAqB,SAAS;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,aAAa;AACjB,GAAG,oBAAoB;AAChB,IAAI,+BAA+B;AAAA,EACtC,OAAO,SAAS;AAAA,IACZ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,aAAa;AAAA,IACb,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,YAAY;AAAA,EAChB,GAAG,+BAA+B,KAAK;AAC3C;AACA,IAAI,iBAAiB,oBAAoB,OAAO;AAAA,EAAC;AAAA,EAC7C;AAAA,EAAW;AAAA,EAAK;AAAA,EAAM;AAAA,EAAU;AACpC,CAAC;AACD,IAAI,OAAQ,SAAU,QAAQ;AAC1B,YAAUC,OAAM,MAAM;AACtB,WAASA,MAAK,MAAM;AAChB,WAAO,OAAO,KAAK,MAAM,IAAI,KAAK;AAAA,EACtC;AACA,EAAAA,MAAK,UAAU,SAAS,WAAY;AAChC,QAAI,QAAQ;AACZ,WAAO,UAAU,OAAO,KAAK,IAAI;AACjC,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,OAAO;AACb,UAAI,UAAU,KAAK,WAAW,KAAK,YAAY,IAAIA,MAAK;AACxD,UAAI,QAAQ,cAAcA,MAAK,UAAU,WAAW;AAChD,gBAAQ,YAAY,SAAU,KAAK;AAC/B,gBAAM,UAAU,KAAK,MAAM,KAAK;AAAA,QACpC;AAAA,MACJ;AACA,cAAQ,SAAS;AACjB,UAAI,eAAe,QAAQ;AAC3B,eAAS,OAAO,OAAO;AACnB,YAAI,aAAa,GAAG,MAAM,MAAM,GAAG,GAAG;AAClC,uBAAa,GAAG,IAAI,MAAM,GAAG;AAAA,QACjC;AAAA,MACJ;AACA,mBAAa,OAAO,MAAM,OAAO,MAAM,QAAQ;AAC/C,mBAAa,QAAQ;AACrB,mBAAa,cAAc;AAC3B,YAAM,gBAAgB,aAAa,SAAS;AAC5C,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,EAAE,GAAG;AAC5C,gBAAQ,eAAe,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC;AAAA,MACvD;AACA,cAAQ,WAAW;AAAA,IACvB,WACS,KAAK,UAAU;AACpB,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AACzC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,MAAK,UAAU,QAAQ,SAAU,OAAO;AACpC,QAAI,UAAU,KAAK,KAAK;AACxB,SAAK,QAAQ,KAAK,gBAAgB;AAClC,QAAI,eAAe,KAAK,gBAAgB;AACxC,QAAI,cAAc;AACd,WAAK,SAAS,YAAY;AAAA,IAC9B;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,QAAQ,MAAM,GAAG;AACrB,UAAI,QAAQ,SAAS;AACjB,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS,KAAK;AAAA,QACvB,OACK;AACD,iBAAO,KAAK,OAAO,KAAK;AAAA,QAC5B;AAAA,MACJ,WACS,QAAQ,SAAS;AACtB,eAAO,KAAK,OAAO,KAAK;AAAA,MAC5B,OACK;AACD,eAAO,UAAU,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,MACjD;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,OAAO;AACb,WAAK,SAAS,CAAC,CAAC;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AACzC,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AACzC,WAAO,CAAC;AAAA,EACZ;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AACzC,WAAO,KAAK,QAAQ;AAAA,EACxB;AACA,EAAAA,MAAK,UAAU,oBAAoB,WAAY;AAC3C,QAAI,WAAW,KAAK,MAAM;AAC1B,QAAI,aAAa,QAAQ;AACrB,UAAI,SAAS,QAAQ,GAAG;AACpB,YAAI,UAAU,IAAI,UAAU,CAAC;AAC7B,YAAI,UAAU,KAAK;AACf,iBAAO;AAAA,QACX,WACS,UAAU,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,WACS,UAAU;AACf,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,UAAU,sBAAsB,SAAU,UAAU;AACrD,QAAI,WAAW,KAAK,MAAM;AAC1B,QAAI,SAAS,QAAQ,GAAG;AACpB,UAAI,KAAK,KAAK;AACd,UAAI,aAAa,CAAC,EAAE,MAAM,GAAG,WAAW;AACxC,UAAI,cAAc,IAAI,UAAU,CAAC,IAAI;AACrC,UAAI,eAAe,aAAa;AAC5B,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,YAAY,SAAU,KAAK,UAAU,SAAS;AAAA,EAAE;AAC/D,EAAAA,MAAK,UAAU,cAAc,WAAY;AACrC,SAAK,WAAW,CAAC;AAAA,EACrB;AACA,EAAAA,MAAK,UAAU,sBAAsB,SAAU,SAAS;AACpD,KAAC,KAAK,QAAQ,KAAK,gBAAgB;AACnC,SAAK,KAAK,UAAU;AACpB,SAAK,UAAU,KAAK,MAAM,KAAK,OAAO,OAAO;AAC7C,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AACzC,SAAK,OAAO,IAAI,kBAAU,KAAK;AAAA,EACnC;AACA,EAAAA,MAAK,UAAU,YAAY,WAAY;AACnC,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,MAAM;AACnB,WAAO,EAAE,UAAU,QAAQ,WAAW,UAAU,EAAE,MAAM,YAAY;AAAA,EACxE;AACA,EAAAA,MAAK,UAAU,UAAU,WAAY;AACjC,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,MAAM;AACjB,WAAO,QAAQ,QAAQ,SAAS;AAAA,EACpC;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AACzC,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,KAAK;AACjB,QAAI,kBAAkB,CAAC;AACvB,QAAI,iBAAiB;AACjB,UAAI,cAAc;AAClB,UAAI,CAAC,KAAK,MAAM;AACZ,sBAAc;AACd,aAAK,gBAAgB;AAAA,MACzB;AACA,UAAI,OAAO,KAAK;AAChB,UAAI,eAAgB,KAAK,UAAU,mBAAoB;AACnD,aAAK,UAAU;AACf,aAAK,UAAU,MAAM,KAAK,OAAO,KAAK;AACtC,aAAK,YAAY;AAAA,MACrB;AACA,aAAO,KAAK,gBAAgB;AAAA,IAChC;AACA,SAAK,QAAQ;AACb,QAAI,KAAK,UAAU,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,GAAG;AACtD,UAAI,aAAa,KAAK,gBAAgB,KAAK,cAAc,KAAK,MAAM;AACpE,UAAI,KAAK,WAAW,iBAAiB;AACjC,mBAAW,KAAK,IAAI;AACpB,YAAI,YAAY,MAAM,gBAAgB,KAAK,aAAa,IAAI;AAC5D,YAAI,IAAI,MAAM;AACd,YAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,cAAI,yBAAyB,KAAK;AAClC,cAAI,KAAK,IAAI,GAAG,0BAA0B,OAAO,IAAI,sBAAsB;AAAA,QAC/E;AACA,YAAI,YAAY,OAAO;AACnB,qBAAW,SAAS,IAAI;AACxB,qBAAW,UAAU,IAAI;AACzB,qBAAW,KAAK,IAAI,YAAY;AAChC,qBAAW,KAAK,IAAI,YAAY;AAAA,QACpC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,UAAU,UAAU,SAAU,GAAG,GAAG;AACrC,QAAI,WAAW,KAAK,sBAAsB,GAAG,CAAC;AAC9C,QAAI,OAAO,KAAK,gBAAgB;AAChC,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,CAAC;AACd,QAAI,SAAS,CAAC;AACd,QAAI,KAAK,QAAQ,GAAG,CAAC,GAAG;AACpB,UAAI,YAAY,KAAK;AACrB,UAAI,KAAK,UAAU,GAAG;AAClB,YAAI,YAAY,MAAM;AACtB,YAAI,YAAY,MAAM,gBAAgB,KAAK,aAAa,IAAI;AAC5D,YAAI,YAAY,OAAO;AACnB,cAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,wBAAY,KAAK,IAAI,WAAW,KAAK,sBAAsB;AAAA,UAC/D;AACA,cAAgBC,eAAc,WAAW,YAAY,WAAW,GAAG,CAAC,GAAG;AACnE,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,KAAK,QAAQ,GAAG;AAChB,eAAmB,QAAQ,WAAW,GAAG,CAAC;AAAA,MAC9C;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAD,MAAK,UAAU,aAAa,WAAY;AACpC,SAAK,WAAW;AAChB,QAAI,KAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACjB;AACA,QAAI,KAAK,UAAU;AACf,WAAK,SAAS,WAAW;AAAA,IAC7B;AACA,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,MAAK,UAAU,QAAQ,WAAY;AAC/B,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EACpB;AACA,EAAAA,MAAK,UAAU,eAAe,SAAU,MAAM;AAC1C,WAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,EACrC;AACA,EAAAA,MAAK,UAAU,wBAAwB,SAAU,WAAW;AACxD,QAAI,cAAc,SAAS;AACvB,WAAK,WAAW;AAAA,IACpB,WACS,cAAc,SAAS;AAC5B,WAAK,WAAW;AAAA,IACpB,OACK;AACD,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,SAAS,SAAU,KAAK,OAAO;AAC1C,QAAI,QAAQ,SAAS;AACjB,WAAK,SAAS,KAAK;AAAA,IACvB,OACK;AACD,aAAO,UAAU,OAAO,KAAK,MAAM,KAAK,KAAK;AAAA,IACjD;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,WAAW,SAAU,UAAU,OAAO;AACjD,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,OAAO;AACR,cAAQ,KAAK,QAAQ,CAAC;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,UAAU;AAC9B,YAAM,QAAQ,IAAI;AAAA,IACtB,OACK;AACD,aAAO,OAAO,QAAQ;AAAA,IAC1B;AACA,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,UAAU,eAAe,WAAY;AACtC,WAAO,CAAC,EAAE,KAAK,UAAU;AAAA,EAC7B;AACA,EAAAA,MAAK,UAAU,cAAc,SAAU,KAAK;AACxC,WAAO,aAAa,oBAAoB,GAAG;AAAA,EAC/C;AACA,EAAAA,MAAK,UAAU,qBAAqB,SAAU,SAAS;AACnD,WAAO,UAAU,mBAAmB,KAAK,MAAM,OAAO;AACtD,QAAI,cAAc,KAAK;AACvB,QAAI,QAAQ,SAAS,CAAC,YAAY,OAAO;AACrC,kBAAY,QAAQ,OAAO,CAAC,GAAG,KAAK,KAAK;AAAA,IAC7C;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,iBAAiB,SAAU,WAAW,OAAO,aAAa,mBAAmB,YAAY,cAAc;AAClH,WAAO,UAAU,eAAe,KAAK,MAAM,WAAW,OAAO,aAAa,mBAAmB,YAAY,YAAY;AACrH,QAAI,uBAAuB,EAAE,SAAS;AACtC,QAAI;AACJ,QAAI,SAAS,MAAM,OAAO;AACtB,UAAI,YAAY;AACZ,YAAI,mBAAmB;AACnB,wBAAc,MAAM;AAAA,QACxB,OACK;AACD,wBAAc,OAAO,CAAC,GAAG,YAAY,KAAK;AAC1C,iBAAO,aAAa,MAAM,KAAK;AAAA,QACnC;AAAA,MACJ,OACK;AACD,sBAAc,OAAO,CAAC,GAAG,oBAAoB,KAAK,QAAQ,YAAY,KAAK;AAC3E,eAAO,aAAa,MAAM,KAAK;AAAA,MACnC;AAAA,IACJ,WACS,sBAAsB;AAC3B,oBAAc,YAAY;AAAA,IAC9B;AACA,QAAI,aAAa;AACb,UAAI,YAAY;AACZ,aAAK,QAAQ,OAAO,CAAC,GAAG,KAAK,KAAK;AAClC,YAAI,0BAA0B,CAAC;AAC/B,YAAI,YAAY,KAAK,WAAW;AAChC,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,cAAI,MAAM,UAAU,CAAC;AACrB,cAAI,OAAO,YAAY,GAAG,MAAM,UAAU;AACtC,iBAAK,MAAM,GAAG,IAAI,YAAY,GAAG;AAAA,UACrC,OACK;AACD,oCAAwB,GAAG,IAAI,YAAY,GAAG;AAAA,UAClD;AAAA,QACJ;AACA,aAAK,iBAAiB,WAAW;AAAA,UAC7B,OAAO;AAAA,QACX,GAAG,YAAY;AAAA,MACnB,OACK;AACD,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,eAAe,SAAU,QAAQ;AAC5C,QAAI,cAAc,OAAO,UAAU,aAAa,KAAK,MAAM,MAAM;AACjE,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,MAAM,OAAO;AACb,sBAAc,eAAe,CAAC;AAC9B,aAAK,YAAY,aAAa,MAAM,KAAK;AAAA,MAC7C;AAAA,IACJ;AACA,QAAI,aAAa;AACb,kBAAY,QAAQ;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,UAAU,yBAAyB,WAAY;AAChD,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,UAAU,aAAa,WAAY;AACpC,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,SAAS,SAAU,cAAc;AAClC,QAAI,MAAO,SAAUE,SAAQ;AACzB,gBAAUC,MAAKD,OAAM;AACrB,eAASC,KAAI,MAAM;AACf,YAAI,QAAQD,QAAO,KAAK,MAAM,IAAI,KAAK;AACvC,qBAAa,QAAQ,aAAa,KAAK,KAAK,OAAO,IAAI;AACvD,eAAO;AAAA,MACX;AACA,MAAAC,KAAI,UAAU,kBAAkB,WAAY;AACxC,eAAO,MAAM,aAAa,KAAK;AAAA,MACnC;AACA,MAAAA,KAAI,UAAU,kBAAkB,WAAY;AACxC,eAAO,MAAM,aAAa,KAAK;AAAA,MACnC;AACA,aAAOA;AAAA,IACX,EAAEH,KAAI;AACN,aAAS,OAAO,cAAc;AAC1B,UAAI,OAAO,aAAa,GAAG,MAAM,YAAY;AACzC,YAAI,UAAU,GAAG,IAAI,aAAa,GAAG;AAAA,MACzC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,MAAK,mBAAoB,WAAY;AACjC,QAAI,YAAYA,MAAK;AACrB,cAAU,OAAO;AACjB,cAAU,yBAAyB;AACnC,cAAU,yBAAyB;AACnC,cAAU,mBAAmB;AAC7B,cAAU,YAAY;AACtB,cAAU,UAAU,aAAa,oBAAoB;AAAA,EACzD,EAAG;AACH,SAAOA;AACX,EAAE,mBAAW;AACb,IAAO,eAAQ;;;ACvYR,IAAI,sBAAsB,SAAS;AAAA,EACtC,aAAa;AAAA,EACb,MAAM;AAAA,EACN,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,cAAc;AAAA,EACd,YAAY;AAChB,GAAG,kBAAkB;AACrB,IAAI,QAAS,SAAU,QAAQ;AAC3B,YAAUI,QAAO,MAAM;AACvB,WAASA,SAAQ;AACb,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC/D;AACA,EAAAA,OAAM,UAAU,YAAY,WAAY;AACpC,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,MAAM;AACnB,WAAO,UAAU,QAAQ,WAAW,UAAU,MAAM,YAAY;AAAA,EACpE;AACA,EAAAA,OAAM,UAAU,UAAU,WAAY;AAClC,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,MAAM;AACjB,WAAO,QAAQ,QAAQ,SAAS;AAAA,EACpC;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,KAAK;AACzC,WAAO,aAAa,qBAAqB,GAAG;AAAA,EAChD;AACA,EAAAA,OAAM,UAAU,kBAAkB,SAAU,MAAM;AAC9C,SAAK,QAAQ;AAAA,EACjB;AACA,EAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,KAAK,OAAO;AACb,UAAI,OAAO,MAAM;AACjB,cAAQ,OAAQ,QAAQ,KAAO,OAAO;AACtC,UAAI,OAAO,gBAAgB,MAAM,MAAM,MAAM,MAAM,WAAW,MAAM,YAAY;AAChF,WAAK,KAAK,MAAM,KAAK;AACrB,WAAK,KAAK,MAAM,KAAK;AACrB,UAAI,KAAK,UAAU,GAAG;AAClB,YAAI,IAAI,MAAM;AACd,aAAK,KAAK,IAAI;AACd,aAAK,KAAK,IAAI;AACd,aAAK,SAAS;AACd,aAAK,UAAU;AAAA,MACnB;AACA,WAAK,QAAQ;AAAA,IACjB;AACA,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,OAAM,mBAAoB,WAAY;AAClC,QAAI,aAAaA,OAAM;AACvB,eAAW,qBAAqB;AAAA,EACpC,EAAG;AACH,SAAOA;AACX,EAAE,mBAAW;AACb,MAAM,UAAU,OAAO;AACvB,IAAO,gBAAQ;;;AC1DR,IAAI,sBAAsB,SAAS;AAAA,EACtC,GAAG;AAAA,EACH,GAAG;AACP,GAAG,oBAAoB;AAChB,IAAI,gCAAgC;AAAA,EACvC,OAAO,SAAS;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS;AAAA,EACb,GAAG,+BAA+B,KAAK;AAC3C;AACA,SAAS,YAAY,QAAQ;AACzB,SAAO,CAAC,EAAE,UACH,OAAO,WAAW,YAClB,OAAO,SAAS,OAAO;AAClC;AACA,IAAI,UAAW,SAAU,QAAQ;AAC7B,YAAUC,UAAS,MAAM;AACzB,WAASA,WAAU;AACf,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC/D;AACA,EAAAA,SAAQ,UAAU,cAAc,SAAU,KAAK;AAC3C,WAAO,aAAa,qBAAqB,GAAG;AAAA,EAChD;AACA,EAAAA,SAAQ,UAAU,WAAW,SAAU,KAAK;AACxC,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,MAAM,GAAG;AACpB,QAAI,QAAQ,MAAM;AACd,aAAO;AAAA,IACX;AACA,QAAI,cAAc,YAAY,MAAM,KAAK,IACnC,MAAM,QAAQ,KAAK;AACzB,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,WAAW,QAAQ,UAAU,WAAW;AAC5C,QAAI,eAAe,MAAM,QAAQ;AACjC,QAAI,gBAAgB,MAAM;AACtB,aAAO,YAAY,GAAG;AAAA,IAC1B,OACK;AACD,aAAO,YAAY,GAAG,IAAI,YAAY,QAAQ,IAAI;AAAA,IACtD;AAAA,EACJ;AACA,EAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,WAAO,KAAK,SAAS,OAAO;AAAA,EAChC;AACA,EAAAA,SAAQ,UAAU,YAAY,WAAY;AACtC,WAAO,KAAK,SAAS,QAAQ;AAAA,EACjC;AACA,EAAAA,SAAQ,UAAU,yBAAyB,WAAY;AACnD,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,UAAU,kBAAkB,WAAY;AAC5C,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,KAAK,OAAO;AACb,WAAK,QAAQ,IAAI,qBAAa,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG,KAAK,SAAS,GAAG,KAAK,UAAU,CAAC;AAAA,IAC/F;AACA,WAAO,KAAK;AAAA,EAChB;AACA,SAAOA;AACX,EAAE,mBAAW;AACb,QAAQ,UAAU,OAAO;AACzB,IAAO,gBAAQ;;;ACxER,SAAS,UAAU,KAAK,OAAO;AAClC,MAAI,IAAI,MAAM;AACd,MAAI,IAAI,MAAM;AACd,MAAI,QAAQ,MAAM;AAClB,MAAI,SAAS,MAAM;AACnB,MAAI,IAAI,MAAM;AACd,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,GAAG;AACX,QAAI,IAAI;AACR,YAAQ,CAAC;AAAA,EACb;AACA,MAAI,SAAS,GAAG;AACZ,QAAI,IAAI;AACR,aAAS,CAAC;AAAA,EACd;AACA,MAAI,OAAO,MAAM,UAAU;AACvB,SAAK,KAAK,KAAK,KAAK;AAAA,EACxB,WACS,aAAa,OAAO;AACzB,QAAI,EAAE,WAAW,GAAG;AAChB,WAAK,KAAK,KAAK,KAAK,EAAE,CAAC;AAAA,IAC3B,WACS,EAAE,WAAW,GAAG;AACrB,WAAK,KAAK,EAAE,CAAC;AACb,WAAK,KAAK,EAAE,CAAC;AAAA,IACjB,WACS,EAAE,WAAW,GAAG;AACrB,WAAK,EAAE,CAAC;AACR,WAAK,KAAK,EAAE,CAAC;AACb,WAAK,EAAE,CAAC;AAAA,IACZ,OACK;AACD,WAAK,EAAE,CAAC;AACR,WAAK,EAAE,CAAC;AACR,WAAK,EAAE,CAAC;AACR,WAAK,EAAE,CAAC;AAAA,IACZ;AAAA,EACJ,OACK;AACD,SAAK,KAAK,KAAK,KAAK;AAAA,EACxB;AACA,MAAI;AACJ,MAAI,KAAK,KAAK,OAAO;AACjB,YAAQ,KAAK;AACb,UAAM,QAAQ;AACd,UAAM,QAAQ;AAAA,EAClB;AACA,MAAI,KAAK,KAAK,OAAO;AACjB,YAAQ,KAAK;AACb,UAAM,QAAQ;AACd,UAAM,QAAQ;AAAA,EAClB;AACA,MAAI,KAAK,KAAK,QAAQ;AAClB,YAAQ,KAAK;AACb,UAAM,SAAS;AACf,UAAM,SAAS;AAAA,EACnB;AACA,MAAI,KAAK,KAAK,QAAQ;AAClB,YAAQ,KAAK;AACb,UAAM,SAAS;AACf,UAAM,SAAS;AAAA,EACnB;AACA,MAAI,OAAO,IAAI,IAAI,CAAC;AACpB,MAAI,OAAO,IAAI,QAAQ,IAAI,CAAC;AAC5B,SAAO,KAAK,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/D,MAAI,OAAO,IAAI,OAAO,IAAI,SAAS,EAAE;AACrC,SAAO,KAAK,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC;AACvE,MAAI,OAAO,IAAI,IAAI,IAAI,MAAM;AAC7B,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,EAAE;AACrE,MAAI,OAAO,GAAG,IAAI,EAAE;AACpB,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG;AAClE;;;AC1EA,IAAI,QAAQ,KAAK;AACV,SAAS,qBAAqB,aAAa,YAAY,OAAO;AACjE,MAAI,CAAC,YAAY;AACb;AAAA,EACJ;AACA,MAAI,KAAK,WAAW;AACpB,MAAI,KAAK,WAAW;AACpB,MAAI,KAAK,WAAW;AACpB,MAAI,KAAK,WAAW;AACpB,cAAY,KAAK;AACjB,cAAY,KAAK;AACjB,cAAY,KAAK;AACjB,cAAY,KAAK;AACjB,MAAI,YAAY,SAAS,MAAM;AAC/B,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,MAAI,MAAM,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG;AACjC,gBAAY,KAAK,YAAY,KAAK,iBAAiB,IAAI,WAAW,IAAI;AAAA,EAC1E;AACA,MAAI,MAAM,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG;AACjC,gBAAY,KAAK,YAAY,KAAK,iBAAiB,IAAI,WAAW,IAAI;AAAA,EAC1E;AACA,SAAO;AACX;AACO,SAAS,qBAAqB,aAAa,YAAY,OAAO;AACjE,MAAI,CAAC,YAAY;AACb;AAAA,EACJ;AACA,MAAI,UAAU,WAAW;AACzB,MAAI,UAAU,WAAW;AACzB,MAAI,cAAc,WAAW;AAC7B,MAAI,eAAe,WAAW;AAC9B,cAAY,IAAI;AAChB,cAAY,IAAI;AAChB,cAAY,QAAQ;AACpB,cAAY,SAAS;AACrB,MAAI,YAAY,SAAS,MAAM;AAC/B,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,cAAY,IAAI,iBAAiB,SAAS,WAAW,IAAI;AACzD,cAAY,IAAI,iBAAiB,SAAS,WAAW,IAAI;AACzD,cAAY,QAAQ,KAAK,IAAI,iBAAiB,UAAU,aAAa,WAAW,KAAK,IAAI,YAAY,GAAG,gBAAgB,IAAI,IAAI,CAAC;AACjI,cAAY,SAAS,KAAK,IAAI,iBAAiB,UAAU,cAAc,WAAW,KAAK,IAAI,YAAY,GAAG,iBAAiB,IAAI,IAAI,CAAC;AACpI,SAAO;AACX;AACO,SAAS,iBAAiB,UAAU,WAAW,oBAAoB;AACtE,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,MAAI,kBAAkB,MAAM,WAAW,CAAC;AACxC,UAAQ,kBAAkB,MAAM,SAAS,KAAK,MAAM,IAC9C,kBAAkB,KACjB,mBAAmB,qBAAqB,IAAI,OAAO;AAC9D;;;ACnDA,IAAI,YAAa,2BAAY;AACzB,WAASC,aAAY;AACjB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AACA,SAAOA;AACX,EAAE;AAEF,IAAI,8BAA8B,CAAC;AACnC,IAAI,OAAQ,SAAU,QAAQ;AAC1B,YAAUC,OAAM,MAAM;AACtB,WAASA,MAAK,MAAM;AAChB,WAAO,OAAO,KAAK,MAAM,IAAI,KAAK;AAAA,EACtC;AACA,EAAAA,MAAK,UAAU,kBAAkB,WAAY;AACzC,WAAO,IAAI,UAAU;AAAA,EACzB;AACA,EAAAA,MAAK,UAAU,YAAY,SAAU,KAAK,OAAO;AAC7C,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,kBAAkB;AACvB,UAAI,iBAAiB,qBAAqB,6BAA6B,OAAO,KAAK,KAAK;AACxF,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,cAAQ,eAAe;AACvB,eAAS,eAAe;AACxB,qBAAe,IAAI,MAAM;AACzB,cAAQ;AAAA,IACZ,OACK;AACD,UAAI,MAAM;AACV,UAAI,MAAM;AACV,cAAQ,MAAM;AACd,eAAS,MAAM;AAAA,IACnB;AACA,QAAI,CAAC,MAAM,GAAG;AACV,UAAI,KAAK,GAAG,GAAG,OAAO,MAAM;AAAA,IAChC,OACK;AACD,MAAgB,UAAU,KAAK,KAAK;AAAA,IACxC;AAAA,EACJ;AACA,EAAAA,MAAK,UAAU,aAAa,WAAY;AACpC,WAAO,CAAC,KAAK,MAAM,SAAS,CAAC,KAAK,MAAM;AAAA,EAC5C;AACA,SAAOA;AACX,EAAE,YAAI;AACN,KAAK,UAAU,OAAO;AACtB,IAAO,eAAQ;;;AC9Cf,IAAI,0BAA0B;AAAA,EAC1B,MAAM;AACV;AACA,IAAI,4BAA4B;AACzB,IAAI,+BAA+B;AAAA,EACtC,OAAO,SAAS;AAAA,IACZ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAClB,GAAG,+BAA+B,KAAK;AAC3C;AACA,IAAI,SAAU,SAAU,QAAQ;AAC5B,YAAUC,SAAQ,MAAM;AACxB,WAASA,QAAO,MAAM;AAClB,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,OAAO;AACb,UAAM,YAAY,CAAC;AACnB,UAAM,gBAAgB;AACtB,UAAM,KAAK,IAAI;AACf,WAAO;AAAA,EACX;AACA,EAAAA,QAAO,UAAU,cAAc,WAAY;AACvC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,QAAO,UAAU,SAAS,WAAY;AAClC,WAAO,UAAU,OAAO,KAAK,IAAI;AACjC,QAAI,KAAK,aAAa,GAAG;AACrB,WAAK,gBAAgB;AAAA,IACzB;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,QAAQ,KAAK,UAAU,CAAC;AAC5B,YAAM,SAAS,KAAK;AACpB,YAAM,IAAI,KAAK;AACf,YAAM,KAAK,KAAK;AAChB,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,KAAK;AACpB,YAAM,YAAY,KAAK;AAAA,IAC3B;AAAA,EACJ;AACA,EAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC3C,QAAI,qBAAqB,KAAK;AAC9B,QAAI,oBAAoB;AACpB,yBAAmB,gBAAgB;AACnC,UAAI,mBAAmB,WAAW;AAC9B,aAAK,YAAY,mBAAmB;AAAA,MACxC;AAAA,IACJ,OACK;AACD,aAAO,UAAU,gBAAgB,KAAK,IAAI;AAAA,IAC9C;AAAA,EACJ;AACA,EAAAA,QAAO,UAAU,oBAAoB,SAAU,GAAG;AAC9C,QAAI,qBAAqB,KAAK;AAC9B,WAAO,qBACD,mBAAmB,kBAAkB,CAAC,IACtC,OAAO,UAAU,kBAAkB,KAAK,MAAM,CAAC;AAAA,EACzD;AACA,EAAAA,QAAO,UAAU,uBAAuB,WAAY;AAChD,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,qBAAqB;AACvC,WAAK,aAAa,gBAAgB,IAAI;AAAA,IAC1C;AACA,WAAO,OAAO,UAAU,qBAAqB,KAAK,IAAI;AAAA,EAC1D;AACA,EAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC3C,SAAK,eAAe;AACpB,uBAAmB,KAAK,KAAK;AAC7B,SAAK,MAAM,OACL,KAAK,iBAAiB,IACtB,KAAK,kBAAkB;AAC7B,SAAK,UAAU,SAAS,KAAK;AAC7B,SAAK,aAAa;AAAA,EACtB;AACA,EAAAA,QAAO,UAAU,cAAc,SAAU,IAAI;AACzC,WAAO,UAAU,YAAY,KAAK,MAAM,EAAE;AAC1C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,WAAK,UAAU,CAAC,EAAE,OAAO;AAAA,IAC7B;AAAA,EACJ;AACA,EAAAA,QAAO,UAAU,mBAAmB,SAAU,IAAI;AAC9C,WAAO,UAAU,iBAAiB,KAAK,MAAM,EAAE;AAC/C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,WAAK,UAAU,CAAC,EAAE,OAAO;AAAA,IAC7B;AAAA,EACJ;AACA,EAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC3C,QAAI,KAAK,aAAa,GAAG;AACrB,WAAK,gBAAgB;AAAA,IACzB;AACA,QAAI,CAAC,KAAK,OAAO;AACb,UAAIC,WAAU,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACzC,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAI,QAAQ,SAAS,CAAC;AACtB,YAAI,YAAY,MAAM,gBAAgB;AACtC,YAAI,YAAY,MAAM,kBAAkB,MAAM;AAC9C,YAAI,WAAW;AACX,UAAAA,SAAQ,KAAK,SAAS;AACtB,UAAAA,SAAQ,eAAe,SAAS;AAChC,iBAAO,QAAQA,SAAQ,MAAM;AAC7B,eAAK,MAAMA,QAAO;AAAA,QACtB,OACK;AACD,iBAAO,QAAQ,UAAU,MAAM;AAC/B,eAAK,MAAM,SAAS;AAAA,QACxB;AAAA,MACJ;AACA,WAAK,QAAQ,QAAQA;AAAA,IACzB;AACA,WAAO,KAAK;AAAA,EAChB;AACA,EAAAD,QAAO,UAAU,sBAAsB,SAAU,kBAAkB;AAC/D,SAAK,gBAAgB,oBAAoB;AAAA,EAC7C;AACA,EAAAA,QAAO,UAAU,iBAAiB,SAAU,aAAa;AACrD,QAAI,MAAuC;AACvC,YAAM,IAAI,MAAM,mCAAoC;AAAA,IACxD;AAAA,EACJ;AACA,EAAAA,QAAO,UAAU,cAAc,SAAU,aAAa,aAAa;AAC/D,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,aAAa,YAAY;AAC7B,QAAI,aAAa,YAAY,QAAS,cAAc,CAAC;AACrD,WAAO,aAAa,WAAW;AAC/B,QAAI,cAAc,YAAY;AAC1B,WAAK,WAAW,YAAY,UAAU;AACtC,kBAAY,OAAO;AAAA,IACvB,WACS,YAAY;AACjB,kBAAY,OAAO;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,QAAO,UAAU,aAAa,SAAU,YAAY,YAAY;AAC5D,QAAI,YAAY,KAAK,UAAU;AAC/B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,WAAW,UAAU,CAAC;AAC1B,iBAAW,QAAQ,IAAI,WAAW,QAAQ,KAAK,CAAC;AAChD,aAAO,WAAW,QAAQ,GAAG,WAAW,QAAQ,CAAC;AAAA,IACrD;AAAA,EACJ;AACA,EAAAA,QAAO,UAAU,yBAAyB,WAAY;AAClD,WAAO;AAAA,EACX;AACA,EAAAA,QAAO,UAAU,oBAAoB,SAAU,MAAM;AACjD,QAAI,QAAQ,KAAK,UAAU,KAAK,YAAY;AAC5C,QAAI,CAAC,SAAS,EAAE,iBAAiB,OAAO;AACpC,cAAQ,IAAI,KAAK;AAAA,IACrB;AACA,SAAK,UAAU,KAAK,cAAc,IAAI;AACtC,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AACf,WAAO;AAAA,EACX;AACA,EAAAA,QAAO,UAAU,oBAAoB,WAAY;AAC7C,QAAI,QAAQ,KAAK;AACjB,QAAI,WAAW,MAAM,QAAQ;AAC7B,QAAI,cAAc,MAAM;AACxB,QAAI,OAAO,aAAa,KAAK;AAC7B,QAAI,eAAe,eAAe,MAAM,KAAK;AAC7C,QAAI,aAAa,mBAAmB,KAAK;AACzC,QAAI,eAAe,CAAC,CAAE,MAAM;AAC5B,QAAI,cAAc,aAAa;AAC/B,QAAI,aAAa,aAAa;AAC9B,QAAI,eAAe,aAAa;AAChC,QAAI,YAAY,aAAa;AAC7B,QAAI,aAAa,aAAa;AAC9B,QAAI,eAAe,KAAK;AACxB,SAAK,cAAc,CAAC,CAAC,aAAa;AAClC,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,YAAY,MAAM,SAAS,aAAa,SAAS;AACrD,QAAI,gBAAgB,MAAM,iBAAiB,aAAa,iBAAiB;AACzE,QAAI,QAAQ;AACZ,QAAI,QAAQ,YAAY,OAAO,aAAa,eAAe,aAAa;AACxE,QAAI,cAAc,aAAa;AAC3B,UAAI,OAAO,YAAY,OAAO,YAAY,SAAS;AACnD,UAAI,OAAO,YAAY,OAAO,aAAa,aAAa;AACxD,oBAAc,KAAK,kBAAkB,OAAO,OAAO,MAAM,MAAM,YAAY,WAAW;AAAA,IAC1F;AACA,aAAS,aAAa;AACtB,QAAI,aAAa;AACb,cAAQ,mBAAmB,OAAO,WAAW,WAAW;AACxD,UAAI,kBAAkB,OAAO;AACzB,iBAAS,YAAY,CAAC;AAAA,MAC1B,WACS,kBAAkB,UAAU;AACjC,iBAAS,YAAY,CAAC;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,mBAAmB;AACvB,QAAI,iBAAiB;AACrB,QAAI,WAAW,QAAQ,UAAU,QAC3B,MAAM,QACL,iBAAiB,MAAM,aAAa,KAAK;AAChD,QAAI,aAAa,UAAU,YAAY,QACjC,MAAM,SACL,CAAC,iBACI,CAAC,aAAa,cAAc,mBAC7B,mBAAmB,2BAA2B,aAAa,UAC5D,IAAI;AACd,QAAIE,aAAY,MAAM,iBAAiB;AACvC,QAAI,oBAAoB,MAAM,SAAS,SAC/B,MAAM,aAAa,cAAc,MAAM,aAAa,WAAW,MAAM,aAAa;AAC1F,QAAI,uBAAuB,aAAa;AACxC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAI,KAAK,KAAK,kBAAkB,aAAK;AACrC,UAAI,aAAa,GAAG,YAAY;AAChC,SAAG,SAAS,UAAU;AACtB,iBAAW,OAAO,UAAU,CAAC;AAC7B,iBAAW,IAAI;AACf,iBAAW,IAAI;AACf,UAAI,WAAW;AACX,mBAAW,YAAY;AAAA,MAC3B;AACA,iBAAW,eAAe;AAC1B,iBAAW,UAAU,MAAM;AAC3B,iBAAW,cAAc;AACzB,UAAIA,YAAW;AACX,mBAAW,aAAa,MAAM,kBAAkB;AAChD,mBAAW,cAAc,MAAM,mBAAmB;AAClD,mBAAW,gBAAgB,MAAM,qBAAqB;AACtD,mBAAW,gBAAgB,MAAM,qBAAqB;AAAA,MAC1D;AACA,iBAAW,SAAS;AACpB,iBAAW,OAAO;AAClB,UAAI,YAAY;AACZ,mBAAW,YAAY,MAAM,aAAa;AAC1C,mBAAW,WAAW,MAAM;AAC5B,mBAAW,iBAAiB,MAAM,kBAAkB;AAAA,MACxD;AACA,iBAAW,OAAO;AAClB,sBAAgB,YAAY,KAAK;AACjC,eAAS;AACT,UAAI,mBAAmB;AACnB,WAAG,gBAAgB,IAAI,qBAAa,YAAY,WAAW,GAAG,cAAc,WAAW,SAAS,GAAG,YAAY,WAAW,GAAG,sBAAsB,WAAW,YAAY,GAAG,cAAc,oBAAoB,CAAC;AAAA,MACpN;AAAA,IACJ;AAAA,EACJ;AACA,EAAAF,QAAO,UAAU,mBAAmB,WAAY;AAC5C,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,aAAa,KAAK;AAC7B,QAAI,eAAe,cAAc,MAAM,KAAK;AAC5C,QAAI,eAAe,aAAa;AAChC,QAAI,aAAa,aAAa;AAC9B,QAAI,cAAc,aAAa;AAC/B,QAAI,cAAc,MAAM;AACxB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,eAAe,KAAK;AACxB,QAAI,YAAY,MAAM,SAAS,aAAa;AAC5C,QAAI,gBAAgB,MAAM,iBAAiB,aAAa;AACxD,SAAK,cAAc,CAAC,CAAC,aAAa;AAClC,QAAI,OAAO,YAAY,OAAO,YAAY,SAAS;AACnD,QAAI,OAAO,YAAY,OAAO,aAAa,aAAa;AACxD,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI,aAAa;AACb,eAAS,YAAY,CAAC;AACtB,iBAAW,YAAY,CAAC;AAAA,IAC5B;AACA,QAAI,SAAS,QAAQ;AACrB,QAAI,mBAAmB,KAAK,GAAG;AAC3B,WAAK,kBAAkB,OAAO,OAAO,MAAM,MAAM,YAAY,WAAW;AAAA,IAC5E;AACA,QAAI,eAAe,CAAC,CAAE,MAAM;AAC5B,aAAS,IAAI,GAAG,IAAI,aAAa,MAAM,QAAQ,KAAK;AAChD,UAAI,OAAO,aAAa,MAAM,CAAC;AAC/B,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,OAAO;AACxB,UAAI,aAAa,KAAK;AACtB,UAAI,gBAAgB,KAAK;AACzB,UAAI,YAAY;AAChB,UAAI,YAAY;AAChB,UAAI,aAAa;AACjB,UAAI,aAAa,aAAa;AAC9B,UAAI,QAAQ;AACZ,aAAO,YAAY,eACX,QAAQ,OAAO,SAAS,GAAG,CAAC,MAAM,SAAS,MAAM,UAAU,SAAS;AACxE,aAAK,YAAY,OAAO,OAAO,YAAY,SAAS,WAAW,QAAQ,YAAY;AACnF,yBAAiB,MAAM;AACvB,qBAAa,MAAM;AACnB;AAAA,MACJ;AACA,aAAO,cAAc,MACb,QAAQ,OAAO,UAAU,GAAG,MAAM,UAAU,UAAU;AAC1D,aAAK,YAAY,OAAO,OAAO,YAAY,SAAS,YAAY,SAAS,YAAY;AACrF,yBAAiB,MAAM;AACvB,sBAAc,MAAM;AACpB;AAAA,MACJ;AACA,oBAAc,gBAAgB,YAAY,UAAU,SAAS,cAAc,iBAAiB;AAC5F,aAAO,aAAa,YAAY;AAC5B,gBAAQ,OAAO,SAAS;AACxB,aAAK,YAAY,OAAO,OAAO,YAAY,SAAS,YAAY,MAAM,QAAQ,GAAG,UAAU,YAAY;AACvG,qBAAa,MAAM;AACnB;AAAA,MACJ;AACA,iBAAW;AAAA,IACf;AAAA,EACJ;AACA,EAAAA,QAAO,UAAU,cAAc,SAAU,OAAO,OAAO,YAAY,SAAS,GAAG,WAAW,oBAAoB;AAC1G,QAAI,aAAa,MAAM,KAAK,MAAM,SAAS,KAAK,CAAC;AACjD,eAAW,OAAO,MAAM;AACxB,QAAI,gBAAgB,MAAM;AAC1B,QAAI,IAAI,UAAU,aAAa;AAC/B,QAAI,kBAAkB,OAAO;AACzB,UAAI,UAAU,MAAM,SAAS;AAAA,IACjC,WACS,kBAAkB,UAAU;AACjC,UAAI,UAAU,aAAa,MAAM,SAAS;AAAA,IAC9C;AACA,QAAI,aAAa,CAAC,MAAM,gBAAgB,mBAAmB,UAAU;AACrE,kBAAc,KAAK,kBAAkB,YAAY,OAAO,cAAc,UAChE,IAAI,MAAM,QACV,cAAc,WACV,IAAI,MAAM,QAAQ,IAClB,GAAG,IAAI,MAAM,SAAS,GAAG,MAAM,OAAO,MAAM,MAAM;AAC5D,QAAI,eAAe,CAAC,CAAC,WAAW;AAChC,QAAI,cAAc,MAAM;AACxB,QAAI,aAAa;AACb,UAAI,mBAAmB,GAAG,WAAW,WAAW;AAChD,WAAK,MAAM,SAAS,IAAI,YAAY,CAAC,IAAI,MAAM,cAAc;AAAA,IACjE;AACA,QAAI,KAAK,KAAK,kBAAkB,aAAK;AACrC,QAAI,aAAa,GAAG,YAAY;AAChC,OAAG,SAAS,UAAU;AACtB,QAAI,eAAe,KAAK;AACxB,QAAI,iBAAiB;AACrB,QAAI,mBAAmB;AACvB,QAAI,WAAW,QAAQ,UAAU,aAAa,WAAW,OACnD,UAAU,QAAQ,MAAM,QACnB,iBAAiB,MAAM,aAAa,KAAK;AACpD,QAAI,aAAa,UAAU,YAAY,aAAa,WAAW,SACzD,YAAY,QAAQ,MAAM,SACrB,CAAC,gBACG,CAAC,uBACA,CAAC,aAAa,cAAc,mBAAoB,mBAAmB,2BAA2B,aAAa,UAC7G,IAAI;AAClB,QAAIE,aAAY,WAAW,iBAAiB,KACrC,MAAM,iBAAiB;AAC9B,eAAW,OAAO,MAAM;AACxB,eAAW,IAAI;AACf,eAAW,IAAI;AACf,QAAIA,YAAW;AACX,iBAAW,aAAa,WAAW,kBAAkB,MAAM,kBAAkB;AAC7E,iBAAW,cAAc,WAAW,mBAAmB,MAAM,mBAAmB;AAChF,iBAAW,gBAAgB,WAAW,qBAAqB,MAAM,qBAAqB;AACtF,iBAAW,gBAAgB,WAAW,qBAAqB,MAAM,qBAAqB;AAAA,IAC1F;AACA,eAAW,YAAY;AACvB,eAAW,eAAe;AAC1B,eAAW,OAAO,MAAM,QAAQ;AAChC,eAAW,UAAU,UAAU,WAAW,SAAS,MAAM,SAAS,CAAC;AACnE,oBAAgB,YAAY,UAAU;AACtC,QAAI,YAAY;AACZ,iBAAW,YAAY,UAAU,WAAW,WAAW,MAAM,WAAW,gBAAgB;AACxF,iBAAW,WAAW,UAAU,WAAW,UAAU,MAAM,QAAQ;AACnE,iBAAW,iBAAiB,MAAM,kBAAkB;AACpD,iBAAW,SAAS;AAAA,IACxB;AACA,QAAI,UAAU;AACV,iBAAW,OAAO;AAAA,IACtB;AACA,QAAI,YAAY,MAAM;AACtB,QAAI,aAAa,MAAM;AACvB,OAAG,gBAAgB,IAAI,qBAAa,YAAY,WAAW,GAAG,WAAW,WAAW,SAAS,GAAG,YAAY,WAAW,GAAG,YAAY,WAAW,YAAY,GAAG,WAAW,UAAU,CAAC;AAAA,EAC1L;AACA,EAAAF,QAAO,UAAU,oBAAoB,SAAU,OAAO,UAAU,GAAG,GAAG,OAAO,QAAQ;AACjF,QAAI,sBAAsB,MAAM;AAChC,QAAI,kBAAkB,MAAM;AAC5B,QAAI,kBAAkB,MAAM;AAC5B,QAAI,YAAY,uBAAuB,oBAAoB;AAC3D,QAAI,sBAAsB,uBAAuB,CAAC;AAClD,QAAI,mBAAmB,MAAM;AAC7B,QAAIG,QAAO;AACX,QAAI;AACJ,QAAI;AACJ,QAAI,uBAAuB,MAAM,cAAe,mBAAmB,iBAAkB;AACjF,eAAS,KAAK,kBAAkB,YAAI;AACpC,aAAO,SAAS,OAAO,YAAY,CAAC;AACpC,aAAO,MAAM,OAAO;AACpB,UAAI,YAAY,OAAO;AACvB,gBAAU,IAAI;AACd,gBAAU,IAAI;AACd,gBAAU,QAAQ;AAClB,gBAAU,SAAS;AACnB,gBAAU,IAAI;AACd,aAAO,WAAW;AAAA,IACtB;AACA,QAAI,qBAAqB;AACrB,UAAI,YAAY,OAAO;AACvB,gBAAU,OAAO,uBAAuB;AACxC,gBAAU,cAAc,UAAU,MAAM,aAAa,CAAC;AAAA,IAC1D,WACS,WAAW;AAChB,cAAQ,KAAK,kBAAkB,aAAO;AACtC,YAAM,SAAS,WAAY;AACvB,QAAAA,MAAK,WAAW;AAAA,MACpB;AACA,UAAI,WAAW,MAAM;AACrB,eAAS,QAAQ,oBAAoB;AACrC,eAAS,IAAI;AACb,eAAS,IAAI;AACb,eAAS,QAAQ;AACjB,eAAS,SAAS;AAAA,IACtB;AACA,QAAI,mBAAmB,iBAAiB;AACpC,UAAI,YAAY,OAAO;AACvB,gBAAU,YAAY;AACtB,gBAAU,SAAS;AACnB,gBAAU,gBAAgB,UAAU,MAAM,eAAe,CAAC;AAC1D,gBAAU,WAAW,MAAM;AAC3B,gBAAU,iBAAiB,MAAM,oBAAoB;AACrD,aAAO,yBAAyB;AAChC,UAAI,OAAO,QAAQ,KAAK,OAAO,UAAU,GAAG;AACxC,kBAAU,cAAc;AACxB,kBAAU,aAAa;AAAA,MAC3B;AAAA,IACJ;AACA,QAAI,eAAe,UAAU,OAAO;AACpC,gBAAY,aAAa,MAAM,cAAc;AAC7C,gBAAY,cAAc,MAAM,eAAe;AAC/C,gBAAY,gBAAgB,MAAM,iBAAiB;AACnD,gBAAY,gBAAgB,MAAM,iBAAiB;AACnD,gBAAY,UAAU,UAAU,MAAM,SAAS,SAAS,SAAS,CAAC;AAAA,EACtE;AACA,EAAAH,QAAO,WAAW,SAAU,OAAO;AAC/B,QAAI,OAAO;AACX,QAAI,gBAAgB,KAAK,GAAG;AACxB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM;AAAA,QACN,cAAc,MAAM,QAAQ;AAAA,QAC5B,MAAM,cAAc;AAAA,MACxB,EAAE,KAAK,GAAG;AAAA,IACd;AACA,WAAO,QAAQ,KAAK,IAAI,KAAK,MAAM,YAAY,MAAM;AAAA,EACzD;AACA,SAAOA;AACX,EAAE,mBAAW;AACb,IAAI,mBAAmB,EAAE,MAAM,MAAM,OAAO,GAAG,QAAQ,EAAE;AACzD,IAAI,4BAA4B,EAAE,KAAK,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAC/D,IAAI,aAAa,CAAC,aAAa,cAAc,YAAY,YAAY;AAC9D,SAAS,cAAc,UAAU;AACpC,MAAI,OAAO,aAAa,aAChB,SAAS,QAAQ,IAAI,MAAM,MACxB,SAAS,QAAQ,KAAK,MAAM,MAC5B,SAAS,QAAQ,IAAI,MAAM,KAAK;AACvC,WAAO;AAAA,EACX,WACS,CAAC,MAAM,CAAC,QAAQ,GAAG;AACxB,WAAO,WAAW;AAAA,EACtB,OACK;AACD,WAAO,oBAAoB;AAAA,EAC/B;AACJ;AACA,SAAS,gBAAgB,aAAa,aAAa;AAC/C,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,QAAI,WAAW,WAAW,CAAC;AAC3B,QAAI,MAAM,YAAY,QAAQ;AAC9B,QAAI,OAAO,MAAM;AACb,kBAAY,QAAQ,IAAI;AAAA,IAC5B;AAAA,EACJ;AACJ;AACO,SAAS,gBAAgB,OAAO;AACnC,SAAO,MAAM,YAAY,QAAQ,MAAM,cAAc,MAAM;AAC/D;AACO,SAAS,mBAAmB,OAAO;AACtC,iBAAe,KAAK;AACpB,OAAK,MAAM,MAAM,cAAc;AAC/B,SAAO;AACX;AACA,SAAS,eAAe,OAAO;AAC3B,MAAI,OAAO;AACP,UAAM,OAAO,OAAO,SAAS,KAAK;AAClC,QAAI,YAAY,MAAM;AACtB,kBAAc,aAAa,YAAY;AACvC,UAAM,QAAS,aAAa,QAAQ,iBAAiB,SAAS,IAAK,YAAY;AAC/E,QAAI,gBAAgB,MAAM;AAC1B,sBAAkB,aAAa,gBAAgB;AAC/C,UAAM,gBAAiB,iBAAiB,QAAQ,0BAA0B,aAAa,IAAK,gBAAgB;AAC5G,QAAI,cAAc,MAAM;AACxB,QAAI,aAAa;AACb,YAAM,UAAU,kBAAkB,MAAM,OAAO;AAAA,IACnD;AAAA,EACJ;AACJ;AACA,SAAS,UAAU,QAAQ,WAAW;AAClC,SAAQ,UAAU,QAAQ,aAAa,KAAK,WAAW,iBAAiB,WAAW,SAC7E,OACC,OAAO,SAAS,OAAO,aACpB,SACA;AACd;AACA,SAAS,QAAQ,MAAM;AACnB,SAAQ,QAAQ,QAAQ,SAAS,SAC3B,OACC,KAAK,SAAS,KAAK,aAChB,SACA;AACd;AACA,SAAS,mBAAmB,GAAG,WAAW,aAAa;AACnD,SAAO,cAAc,UACd,IAAI,YAAY,CAAC,IAClB,cAAc,WACT,IAAI,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,IAC1C,IAAI,YAAY,CAAC;AAChC;AACA,SAAS,aAAa,OAAO;AACzB,MAAI,OAAO,MAAM;AACjB,UAAQ,SAAS,QAAQ;AACzB,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO;AAC/B,SAAO,CAAC,EAAE,MAAM,mBACT,MAAM,cACL,MAAM,eAAe,MAAM;AACvC;AACA,IAAO,eAAQ;;;ACliBf,IAAI,QAAS,SAAU,QAAQ;AAC3B,YAAUI,QAAO,MAAM;AACvB,WAASA,OAAM,MAAM;AACjB,QAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAM,UAAU;AAChB,UAAM,YAAY,CAAC;AACnB,UAAM,KAAK,IAAI;AACf,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,WAAO,KAAK;AAAA,EAChB;AACA,EAAAA,OAAM,UAAU,WAAW,WAAY;AACnC,WAAO,KAAK,UAAU,MAAM;AAAA,EAChC;AACA,EAAAA,OAAM,UAAU,UAAU,SAAU,KAAK;AACrC,WAAO,KAAK,UAAU,GAAG;AAAA,EAC7B;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,MAAM;AAC1C,QAAI,WAAW,KAAK;AACpB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAI,SAAS,CAAC,EAAE,SAAS,MAAM;AAC3B,eAAO,SAAS,CAAC;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,OAAM,UAAU,aAAa,WAAY;AACrC,WAAO,KAAK,UAAU;AAAA,EAC1B;AACA,EAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACnC,QAAI,OAAO;AACP,UAAI,UAAU,QAAQ,MAAM,WAAW,MAAM;AACzC,aAAK,UAAU,KAAK,KAAK;AACzB,aAAK,OAAO,KAAK;AAAA,MACrB;AACA,UAAI,MAAuC;AACvC,YAAI,MAAM,cAAc;AACpB,gBAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,YAAY,SAAU,OAAO,aAAa;AACtD,QAAI,SAAS,UAAU,QAAQ,MAAM,WAAW,QACzC,eAAe,YAAY,WAAW,MAAM;AAC/C,UAAI,WAAW,KAAK;AACpB,UAAI,MAAM,SAAS,QAAQ,WAAW;AACtC,UAAI,OAAO,GAAG;AACV,iBAAS,OAAO,KAAK,GAAG,KAAK;AAC7B,aAAK,OAAO,KAAK;AAAA,MACrB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,UAAU,SAAU,UAAU,UAAU;AACpD,QAAI,MAAa,QAAQ,KAAK,WAAW,QAAQ;AACjD,QAAI,OAAO,GAAG;AACV,WAAK,UAAU,UAAU,GAAG;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,YAAY,SAAU,OAAO,OAAO;AAChD,QAAI,WAAW,KAAK;AACpB,QAAI,MAAM,SAAS,KAAK;AACxB,QAAI,SAAS,UAAU,QAAQ,MAAM,WAAW,QAAQ,UAAU,KAAK;AACnE,eAAS,KAAK,IAAI;AAClB,UAAI,SAAS;AACb,UAAI,KAAK,KAAK;AACd,UAAI,IAAI;AACJ,YAAI,iBAAiB,EAAE;AAAA,MAC3B;AACA,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,SAAS,SAAU,OAAO;AACtC,QAAI,MAAM,QAAQ;AACd,YAAM,OAAO,OAAO,KAAK;AAAA,IAC7B;AACA,UAAM,SAAS;AACf,QAAI,KAAK,KAAK;AACd,QAAI,MAAM,OAAO,MAAM,MAAM;AACzB,YAAM,YAAY,EAAE;AAAA,IACxB;AACA,UAAM,GAAG,QAAQ;AAAA,EACrB;AACA,EAAAA,OAAM,UAAU,SAAS,SAAU,OAAO;AACtC,QAAI,KAAK,KAAK;AACd,QAAI,WAAW,KAAK;AACpB,QAAI,MAAa,QAAQ,UAAU,KAAK;AACxC,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,aAAS,OAAO,KAAK,CAAC;AACtB,UAAM,SAAS;AACf,QAAI,IAAI;AACJ,YAAM,iBAAiB,EAAE;AAAA,IAC7B;AACA,UAAM,GAAG,QAAQ;AACjB,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,YAAY,WAAY;AACpC,QAAI,WAAW,KAAK;AACpB,QAAI,KAAK,KAAK;AACd,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAI,QAAQ,SAAS,CAAC;AACtB,UAAI,IAAI;AACJ,cAAM,iBAAiB,EAAE;AAAA,MAC7B;AACA,YAAM,SAAS;AAAA,IACnB;AACA,aAAS,SAAS;AAClB,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,YAAY,SAAU,IAAI,SAAS;AAC/C,QAAI,WAAW,KAAK;AACpB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAI,QAAQ,SAAS,CAAC;AACtB,SAAG,KAAK,SAAS,OAAO,CAAC;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,WAAW,SAAU,IAAI,SAAS;AAC9C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,QAAQ,KAAK,UAAU,CAAC;AAC5B,UAAI,UAAU,GAAG,KAAK,SAAS,KAAK;AACpC,UAAI,MAAM,WAAW,CAAC,SAAS;AAC3B,cAAM,SAAS,IAAI,OAAO;AAAA,MAC9B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,EAAAA,OAAM,UAAU,cAAc,SAAU,IAAI;AACxC,WAAO,UAAU,YAAY,KAAK,MAAM,EAAE;AAC1C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,QAAQ,KAAK,UAAU,CAAC;AAC5B,YAAM,YAAY,EAAE;AAAA,IACxB;AAAA,EACJ;AACA,EAAAA,OAAM,UAAU,mBAAmB,SAAU,IAAI;AAC7C,WAAO,UAAU,iBAAiB,KAAK,MAAM,EAAE;AAC/C,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,QAAQ,KAAK,UAAU,CAAC;AAC5B,YAAM,iBAAiB,EAAE;AAAA,IAC7B;AAAA,EACJ;AACA,EAAAA,OAAM,UAAU,kBAAkB,SAAU,iBAAiB;AACzD,QAAIC,WAAU,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AACzC,QAAI,WAAW,mBAAmB,KAAK;AACvC,QAAI,SAAS,CAAC;AACd,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAI,QAAQ,SAAS,CAAC;AACtB,UAAI,MAAM,UAAU,MAAM,WAAW;AACjC;AAAA,MACJ;AACA,UAAI,YAAY,MAAM,gBAAgB;AACtC,UAAI,YAAY,MAAM,kBAAkB,MAAM;AAC9C,UAAI,WAAW;AACX,6BAAa,eAAeA,UAAS,WAAW,SAAS;AACzD,eAAO,QAAQA,SAAQ,MAAM;AAC7B,aAAK,MAAMA,QAAO;AAAA,MACtB,OACK;AACD,eAAO,QAAQ,UAAU,MAAM;AAC/B,aAAK,MAAM,SAAS;AAAA,MACxB;AAAA,IACJ;AACA,WAAO,QAAQA;AAAA,EACnB;AACA,SAAOD;AACX,EAAE,eAAO;AACT,MAAM,UAAU,OAAO;AACvB,IAAO,gBAAQ;;;AChLf,IAAI,eAAgB,SAAU,QAAQ;AAClC,YAAUE,eAAc,MAAM;AAC9B,WAASA,gBAAe;AACpB,QAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,UAAM,OAAO;AACb,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,UAAU,mBAAmB,WAAY;AAClD,QAAI,QAAQ,KAAK,MAAM;AACvB,QAAI,YAAY,KAAK,aAAa;AAClC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,kBAAY,aAAa,MAAM,CAAC,EAAE,aAAa;AAAA,IACnD;AACA,QAAI,WAAW;AACX,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AACA,EAAAA,cAAa,UAAU,cAAc,WAAY;AAC7C,SAAK,iBAAiB;AACtB,QAAI,QAAQ,KAAK,MAAM,SAAS,CAAC;AACjC,QAAIC,SAAQ,KAAK,eAAe;AAChC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,CAAC,MAAM,CAAC,EAAE,MAAM;AAChB,cAAM,CAAC,EAAE,gBAAgB;AAAA,MAC7B;AACA,YAAM,CAAC,EAAE,KAAK,SAASA,OAAM,CAAC,GAAGA,OAAM,CAAC,GAAG,MAAM,CAAC,EAAE,sBAAsB;AAAA,IAC9E;AAAA,EACJ;AACA,EAAAD,cAAa,UAAU,YAAY,SAAU,KAAK,OAAO;AACrD,QAAI,QAAQ,MAAM,SAAS,CAAC;AAC5B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,CAAC,EAAE,UAAU,KAAK,MAAM,CAAC,EAAE,OAAO,IAAI;AAAA,IAChD;AAAA,EACJ;AACA,EAAAA,cAAa,UAAU,aAAa,WAAY;AAC5C,QAAI,QAAQ,KAAK,MAAM,SAAS,CAAC;AACjC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,CAAC,EAAE,YAAY;AAAA,IACzB;AAAA,EACJ;AACA,EAAAA,cAAa,UAAU,kBAAkB,WAAY;AACjD,SAAK,iBAAiB,KAAK,IAAI;AAC/B,WAAO,aAAK,UAAU,gBAAgB,KAAK,IAAI;AAAA,EACnD;AACA,SAAOA;AACX,EAAE,YAAI;AACN,IAAO,uBAAQ;;;AChDf,IAAI,MAAM,KAAK,IAAI,CAAC;AACpB,SAAS,YAAY,MAAM,MAAM,UAAU,SAAS,SAAS,UAAU;AACnE,MAAI,WAAW,UAAU,MAAM;AAC/B,MAAI,WAAW,KAAK;AACpB,MAAI,SAAS,eAAe,QAAQ,GAAG;AACnC,WAAO,SAAS,QAAQ;AAAA,EAC5B;AACA,MAAI,SAAS,GAAG;AACZ,QAAI,WAAW,KAAK,MAAM,KAAK,KAAM,KAAK,YAAY,IAAK,CAAC,OAAO,IAAI,GAAG;AAC1E,WAAO,KAAK,QAAQ,EAAE,QAAQ;AAAA,EAClC;AACA,MAAI,aAAa,UAAW,KAAK;AACjC,MAAI,cAAc,WAAW;AAC7B,SAAO,UAAW,KAAK,aAAc;AACjC;AAAA,EACJ;AACA,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,cAAc,GAAG,IAAI,UAAU,KAAK;AAChD,QAAI,SAAS,KAAK;AAClB,QAAI,EAAE,SAAS,UAAU;AACrB,cAAQ,cAAc,IAAI,KAAK,KAAK,KAAK,QAAQ,EAAE,CAAC,IAC9C,YAAY,MAAM,OAAO,GAAG,aAAa,YAAY,UAAU,QAAQ,QAAQ;AACrF;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,QAAQ,IAAI;AACrB,SAAO;AACX;AACO,SAAS,iBAAiB,KAAK,MAAM;AACxC,MAAI,KAAK;AAAA,IACL,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACjE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACjE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACjE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACjE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACjE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACjE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,IACjE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;AAAA,EACrE;AACA,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,YAAY,IAAI,GAAG,GAAG,GAAG,GAAG,QAAQ;AAC9C,MAAI,QAAQ,GAAG;AACX;AAAA,EACJ;AACA,MAAI,KAAK,CAAC;AACV,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,SAAG,CAAC,KAAK,SAAS,GAAG,CAAC,IAAI;AAC1B,SAAG,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,KACvB,YAAY,IAAI,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,QAAQ,IAC5D,MAAM,KAAK,CAAC;AAAA,IACtB;AAAA,EACJ;AACA,SAAO,SAAU,KAAK,WAAW,WAAW;AACxC,QAAI,KAAK,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI;AACjD,QAAI,CAAC,KAAK,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAC3D,QAAI,CAAC,KAAK,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAAA,EAC/D;AACJ;;;ACxDA,IAAI,mBAAmB;AACvB,IAAI,WAAW,CAAC;AACT,SAAS,oBAAoB,KAAK,QAAQ,UAAU,KAAK,KAAK;AACjE,SAAO,2BAA2B,UAAU,QAAQ,KAAK,KAAK,IAAI,KAC3D,2BAA2B,KAAK,UAAU,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC7E;AACO,SAAS,2BAA2B,KAAK,IAAI,KAAK,KAAK,SAAS;AACnE,MAAI,GAAG,yBAAyB,YAAI,gBAAgB,CAAC,WAAW,EAAE,GAAG;AACjE,QAAI,QAAQ,GAAG,gBAAgB,MAAM,GAAG,gBAAgB,IAAI,CAAC;AAC7D,QAAI,UAAU,oBAAoB,IAAI,KAAK;AAC3C,QAAI,cAAc,0BAA0B,SAAS,OAAO,OAAO;AACnE,QAAI,aAAa;AACb,kBAAY,KAAK,KAAK,GAAG;AACzB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,IAAI,OAAO;AACpC,MAAI,UAAU,MAAM;AACpB,MAAI,SAAS;AACT,WAAO;AAAA,EACX;AACA,YAAU,MAAM,UAAU,CAAC;AAC3B,MAAI,SAAS,CAAC,QAAQ,OAAO;AAC7B,MAAI,SAAS,CAAC,OAAO,QAAQ;AAC7B,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,SAAS,SAAS,cAAc,KAAK;AACzC,QAAI,MAAM,OAAO;AACjB,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,KAAK,KAAK;AACvB,QAAI,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,KAAK,IAAI;AAAA,MAChB,OAAO,KAAK,IAAI;AAAA,MAChB,OAAO,IAAI,KAAK,IAAI;AAAA,MACpB,OAAO,IAAI,KAAK,IAAI;AAAA,MACpB;AAAA,IACJ,EAAE,KAAK,aAAa;AACpB,OAAG,YAAY,MAAM;AACrB,YAAQ,KAAK,MAAM;AAAA,EACvB;AACA,SAAO;AACX;AACA,SAAS,0BAA0B,SAAS,OAAO,SAAS;AACxD,MAAI,kBAAkB,UAAU,aAAa;AAC7C,MAAI,cAAc,MAAM,eAAe;AACvC,MAAI,eAAe,MAAM;AACzB,MAAI,YAAY,CAAC;AACjB,MAAI,aAAa,CAAC;AAClB,MAAI,kBAAkB;AACtB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,OAAO,QAAQ,CAAC,EAAE,sBAAsB;AAC5C,QAAI,KAAK,IAAI;AACb,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,cAAU,KAAK,GAAG,CAAC;AACnB,sBAAkB,mBAAmB,gBAAgB,MAAM,aAAa,EAAE,KAAK,MAAM,aAAa,KAAK,CAAC;AACxG,eAAW,KAAK,QAAQ,CAAC,EAAE,YAAY,QAAQ,CAAC,EAAE,SAAS;AAAA,EAC/D;AACA,SAAQ,mBAAmB,cACrB,eACC,MAAM,YAAY,WACjB,MAAM,eAAe,IAAI,UACnB,iBAAiB,YAAY,SAAS,IACtC,iBAAiB,WAAW,UAAU;AACxD;AACO,SAAS,WAAW,IAAI;AAC3B,SAAO,GAAG,SAAS,YAAY,MAAM;AACzC;AACA,IAAI,aAAa;AACjB,IAAI,aAAa;AAAA,EACb,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAM;AACV;AACO,SAAS,WAAW,QAAQ;AAC/B,SAAO,UAAU,OACX,MACC,SAAS,IAAI,QAAQ,YAAY,SAAU,KAAK,GAAG;AAClD,WAAO,WAAW,CAAC;AAAA,EACvB,CAAC;AACT;", "names": ["map", "len", "len", "MapPolyfill", "HashMap", "keys", "Browser", "Env", "env", "clone", "clone", "copy", "create", "mul", "scale", "lerp", "Entry", "LinkedList", "LRU", "map", "len", "lerp", "d", "b", "Point", "len", "BoundingRect", "len", "distance", "len", "RichTextToken", "RichTextLine", "RichTextContentBlock", "line", "lineWidth", "lineHeight", "Eventful", "len", "Transformable", "EPSILON", "create", "isNotAroundZero", "roots", "extrema", "Clip", "EPSILON", "isAroundZero", "adjustTextY", "scale", "len", "len2", "Track", "isGradient", "mathMin", "start", "Animator", "self", "i", "Element", "len", "self", "target", "animator", "PRIMARY_STATES_KEYS", "Displayable", "mathMin", "mathMax", "create", "min", "max", "cubicExtrema", "cubicAt", "quadraticExtremum", "quadraticAt", "min", "max", "min2", "max2", "mathMin", "mathMax", "mathCos", "mathSin", "PI2", "PathProxy", "dpr", "len", "containStroke", "containStroke", "PI2", "PI2", "containStroke", "CMD", "PI2", "EPSILON", "len", "containStroke", "Path", "containStroke", "_super", "Sub", "TSpan", "ZRImage", "RectShape", "Rect", "ZRText", "tmpRect", "<PERSON><PERSON><PERSON><PERSON>", "self", "Group", "tmpRect", "CompoundPath", "scale"]}