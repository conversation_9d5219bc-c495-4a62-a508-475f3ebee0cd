package com.ict.ycwl.clustercalculate.utlis.dbDataSourceUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class FileWriteUtil {

    /**
     * 将内容覆盖写入到指定文件
     * @param filePath 文件路径
     * @param content 要写入的内容
     * @return 是否写入成功
     */
    public static boolean writeToFile(String filePath, String content) {
        BufferedWriter writer = null;
        try {
            // 创建文件对象
            File file = new File(filePath);
            
            // 如果文件不存在，创建文件及其父目录
            if (!file.exists()) {
                file.getParentFile().mkdirs(); // 创建父目录
                file.createNewFile(); // 创建文件
            }
            
            // 使用BufferedWriter进行写入
            writer = new BufferedWriter(new FileWriter(file));
            writer.write(content);
            
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            // 确保writer被关闭
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}