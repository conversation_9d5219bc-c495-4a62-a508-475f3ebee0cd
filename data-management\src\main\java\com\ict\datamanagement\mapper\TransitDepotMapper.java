package com.ict.datamanagement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.datamanagement.domain.entity.TransitDepot;
import com.ict.datamanagement.domain.vo.carVO.CarVO;
import com.ict.datamanagement.domain.vo.transitDepotV0.TransitDepotVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface TransitDepotMapper extends BaseMapper<TransitDepot> {
    //根据id查询对象
   TransitDepot selectById(int id);

    TransitDepot selectByName(String transitDepotName);

    List<TransitDepotVO> selectList(String transitDepotName, String teamName, String status, String deliveryType, String deliveryName);

    int myDeleteById(int id);

    int updateTransitDepot(int transitDepotId, int teamId, String status, String longitude, String latitude);

    List<TransitDepot> mySelectList();

    String selectGroupById(Long transitDepotId);

    int updateStatusToZeroById(int oldTransitDepotId);

    ArrayList<String> myselectTransitDepotNameS(int teamId);

    List<TransitDepot> selectTransitByTeamId(int teamId);
}
