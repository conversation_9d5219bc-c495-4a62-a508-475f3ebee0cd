package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.util.Map;

/**
 * 时间均衡统计数据结构
 * 用于评估算法的时间均衡效果
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TimeBalanceStats {
    
    /**
     * 路线级时间方差
     * 表示同一中转站下各条路线工作时间的方差
     */
    private Double routeTimeVariance;
    
    /**
     * 中转站级时间方差
     * 表示同一班组下各个中转站总工作时间的方差
     */
    private Double depotTimeVariance;
    
    /**
     * 班组级时间方差
     * 表示各个班组总工作时间的方差
     */
    private Double teamTimeVariance;
    
    /**
     * 路线级均衡度（0-100%）
     * 100%表示完全均衡，0%表示极度不均衡
     */
    private Double routeBalanceRatio;
    
    /**
     * 中转站级均衡度（0-100%）
     */
    private Double depotBalanceRatio;
    
    /**
     * 班组级均衡度（0-100%）
     */
    private Double teamBalanceRatio;
    
    /**
     * 总体均衡度（0-100%）
     * 综合考虑各层级的均衡情况
     */
    private Double overallBalanceRatio;
    
    /**
     * 各中转站的路线时间差距（分钟）
     * Key: 中转站ID, Value: 该中转站下路线的最大时间差
     */
    private Map<Long, Double> routeTimeGapByDepot;
    
    /**
     * 各班组的中转站时间差距（分钟）
     * Key: 班组ID, Value: 该班组下中转站的最大时间差
     */
    private Map<Long, Double> depotTimeGapByTeam;
    
    /**
     * 班组间时间差距（分钟）
     */
    private Double teamTimeGap;
    
    /**
     * 检查统计数据是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return routeTimeVariance != null && routeTimeVariance >= 0 &&
               depotTimeVariance != null && depotTimeVariance >= 0 &&
               teamTimeVariance != null && teamTimeVariance >= 0;
    }
    
    /**
     * 计算均衡度评级
     * 
     * @return 均衡度评级：EXCELLENT, GOOD, FAIR, POOR
     */
    public BalanceGrade getBalanceGrade() {
        if (!isValid() || overallBalanceRatio == null) {
            return BalanceGrade.UNKNOWN;
        }
        
        if (overallBalanceRatio >= 90) {
            return BalanceGrade.EXCELLENT;
        } else if (overallBalanceRatio >= 75) {
            return BalanceGrade.GOOD;
        } else if (overallBalanceRatio >= 60) {
            return BalanceGrade.FAIR;
        } else {
            return BalanceGrade.POOR;
        }
    }
    
    /**
     * 均衡度评级枚举
     */
    public enum BalanceGrade {
        EXCELLENT("优秀"),
        GOOD("良好"), 
        FAIR("一般"),
        POOR("较差"),
        UNKNOWN("未知");
        
        private final String description;
        
        BalanceGrade(String description) {
            this.description = description;
    }
    
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 生成统计摘要文本
     * 
     * @return 统计摘要
     */
    public String generateSummary() {
        if (!isValid()) {
            return "统计数据无效";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("时间均衡统计摘要:\n");
        summary.append(String.format("  路线级方差: %.2f, 均衡度: %.1f%%\n", 
                routeTimeVariance, routeBalanceRatio != null ? routeBalanceRatio : 0));
        summary.append(String.format("  中转站级方差: %.2f, 均衡度: %.1f%%\n", 
                depotTimeVariance, depotBalanceRatio != null ? depotBalanceRatio : 0));
        summary.append(String.format("  班组级方差: %.2f, 均衡度: %.1f%%\n", 
                teamTimeVariance, teamBalanceRatio != null ? teamBalanceRatio : 0));
        summary.append(String.format("  总体均衡度: %.1f%% (%s)\n", 
                overallBalanceRatio != null ? overallBalanceRatio : 0, 
                getBalanceGrade().getDescription()));
        
        return summary.toString();
    }
} 