package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 时间均衡调整记录
 * 
 * 记录时间均衡阶段的调整操作
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeBalanceAdjustment {
    
    /**
     * 调整类型
     */
    private String adjustmentType;
    
    /**
     * 源路线ID
     */
    private Long fromRouteId;
    
    /**
     * 目标路线ID
     */
    private Long toRouteId;
    
    /**
     * 被转移的聚集区ID
     */
    private Long accumulationId;
    
    /**
     * 节省的时间（分钟）
     */
    private double timeSaved;
    
    /**
     * 调整前时间差
     */
    private double beforeTimeDifference;
    
    /**
     * 调整后时间差
     */
    private double afterTimeDifference;
    
    /**
     * 调整原因
     */
    private String reason;
    
    public static TimeBalanceAdjustment create(String type, Long fromRoute, Long toRoute, 
                                             Long accId, double timeSaved) {
        TimeBalanceAdjustment adjustment = new TimeBalanceAdjustment();
        adjustment.setAdjustmentType(type);
        adjustment.setFromRouteId(fromRoute);
        adjustment.setToRouteId(toRoute);
        adjustment.setAccumulationId(accId);
        adjustment.setTimeSaved(timeSaved);
        return adjustment;
    }
}