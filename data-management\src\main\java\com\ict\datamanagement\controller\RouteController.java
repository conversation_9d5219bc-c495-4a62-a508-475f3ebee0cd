package com.ict.datamanagement.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ict.datamanagement.domain.dto.AccumulationDto;
import com.ict.datamanagement.domain.dto.route.RouteExport;
import com.ict.datamanagement.domain.dto.route.StoreVo;
import com.ict.datamanagement.domain.entity.Route;
import com.ict.datamanagement.domain.entity.Team;
import com.ict.datamanagement.domain.entity.TransitDepot;
import com.ict.datamanagement.mapper.RouteMapper;
import com.ict.datamanagement.mapper.StoreMapper;
import com.ict.datamanagement.mapper.TeamMapper;
import com.ict.datamanagement.mapper.TransitDepotMapper;
import com.ict.datamanagement.service.PathFeignService;
import com.ict.ycwl.common.web.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;


@Api(tags = "路径管理")
@Slf4j
@RestController("/route")
public class RouteController {

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private TransitDepotMapper transitDepotMapper;

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private PathFeignService pathFeignService;

    @ApiOperation("导出路径")
    @PostMapping("/routeExport")
    public String routeExport(HttpServletResponse response) throws IOException {
        String fileName = "路径导出.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), RouteExport.class).build()) {
            //逻辑处理
            //获取所有路径
            List<Route> routes = routeMapper.selectRoutes();
            //收集所有记录
            ArrayList<RouteExport> routeExports = new ArrayList<>();
            //依次处理每条路径
            String teamNameFlag = "";
            for (Route route : routes) {
                RouteExport routeExport = new RouteExport();
                String routeName = route.getRouteName();
                String[] routeSplit = routeName.split("-");
                //填充路径标识
                String routeFlag = routeSplit[2] + "-" + routeSplit[1];
                routeExport.setRouteFlag(routeFlag);
                //填充星期
                routeExport.setWeek(routeSplit[2]);
                //填充车牌号
                routeExport.setLicensePlateNumber(routeSplit[1]);
                //填充对接点
                Long transitDepotId = route.getTransitDepotId();
                //判断所属班组
                if (transitDepotId != 0) {
                    TransitDepot transitDepot = transitDepotMapper.selectById(transitDepotId);
                    Team team = teamMapper.selectById(transitDepot.getGroupId());
                    if (team == null) {
                        continue;
                    }
                    String teamName = team.getTeamName();
                    //将该班组的结果输出
                    if (!teamName.equals(teamNameFlag) && routeExports.size() != 0) {
                        //输出到文档中
                        // 每次都要创建writeSheet 这里注意必须指定sheetNo 而且sheetName必须不一样
                        WriteSheet writeSheet = EasyExcel.writerSheet(teamNameFlag).build();
                        // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
                        excelWriter.write(routeExports, writeSheet);
                        //清空待输出文档
                        routeExports = new ArrayList<>();
                    }
                    teamNameFlag = teamName;
                    routeExport.setTransitDepot(transitDepot.getTransitDepotName());
                }
                //填充打卡点 // 查询该条记录id，在聚集区表中对应的记录
                Long routeId = route.getRouteId();
                List<AccumulationDto> accumulationDtos = storeMapper.selectAccByRouteId(routeId);
                long index = 0;
                for (AccumulationDto accumulationDto : accumulationDtos) {
                    index++;
                    Long accumulationId = accumulationDto.getAccumulationId();
                    List<StoreVo> storeVos = storeMapper.selectStoreByAcc(accumulationId);
                    for (StoreVo storeVo : storeVos) {
                        if (storeVo != null) {
                            RouteExport routeExport1 = new RouteExport();
                            BeanUtils.copyProperties(routeExport, routeExport1);
                            //设置打卡点
                            routeExport1.setAccumulation(accumulationDto.getAccumulationAddress());
                            //设置商铺
                            routeExport1.setStoreName(storeVo.getStoreName());
                            //设置商铺地址
                            routeExport1.setStoreAddress(storeVo.getStoreAddress());
                            //设置客户编码
                            routeExport1.setCustomerCode(storeVo.getCustomerCode());
                            //设置负责人
                            routeExport1.setCustomerManagerName(storeVo.getContactName());
                            //设置顺序
                            routeExport1.setDeliveryOrder(index);
                            //设置商铺新增时间

                            // 定义输入格式（带毫秒）
                            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
                            // 解析为 LocalDateTime
                            LocalDateTime dateTime = LocalDateTime.parse(storeVo.getStoreCreateTime(), inputFormatter);
                            LocalDateTime dateTime1 = LocalDateTime.parse(storeVo.getStoreUpdateTime(), inputFormatter);
                            // 定义输出格式（不带秒）
                            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                            // 格式化为目标字符串
                            String addTime = dateTime.format(outputFormatter);
                            String updateTime = dateTime1.format(outputFormatter);

                            routeExport1.setStoreCreateTime(addTime);
                            //设置商铺修改时间
                            routeExport1.setStoreUpdateTime(updateTime);
                            //设置商铺状态
                            routeExport1.setStoreStatus("1".equals(storeVo.getStoreStatus()) ? "正常" : "异常");

                            System.out.println(routeExport1);
                            //添加到结果集
                            routeExports.add(routeExport1);
                        }
                    }
                }
            }
            if(routeExports.size()!=0){
                WriteSheet writeSheet = EasyExcel.writerSheet(teamNameFlag).build();
                // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
                excelWriter.write(routeExports, writeSheet);
            }
        }
        return "导出成功";
    }

    @ApiOperation("open")
    @PostMapping("/123")
    public AjaxResult open() {
        AjaxResult feign = pathFeignService.feign();
        System.out.println(feign);
        return feign;
    }

}
