package com.ict.ycwl.clustercalculate.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LngAndLat {
    private double longitude;
    private double latitude;

    static class LatitudeComparator implements Comparator<LngAndLat> {
        @Override
        public int compare(LngAndLat p1, LngAndLat p2) {
            return Double.compare(p1.getLatitude(), p2.getLatitude());
        }
    }

    public static LngAndLat[] sortPointsByLatitude(LngAndLat[] points) {
        Arrays.sort(points, new LatitudeComparator());
        return points;
    }

    static class LongitudeComparator implements Comparator<LngAndLat> {
        @Override
        public int compare(LngAndLat p1, LngAndLat p2) {
            return Double.compare(p1.getLongitude(), p2.getLongitude());
        }
    }

    public static LngAndLat[] sortPointsByLongitude(LngAndLat[] points) {
        Arrays.sort(points, new LongitudeComparator());
        return points;
    }

    // equals 和 hashCode 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LngAndLat lngAndLat = (LngAndLat) o;
        return Double.compare(lngAndLat.longitude, longitude) == 0 &&
                Double.compare(lngAndLat.latitude, latitude) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(longitude, latitude);
    }
}
