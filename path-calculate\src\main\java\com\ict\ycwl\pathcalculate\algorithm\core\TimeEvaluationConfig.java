package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 时间评估参数配置
 * 
 * 根据实际数据结构设计的简化配置：
 * - 服务时间：直接使用 Accumulation.getDeliveryTime() 配送/卸货时间数据
 * - 行驶速度：统一参数，无市区/高速路区分
 * - 时间约束：基于实际业务需求的时间限制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-08-05
 */
@Slf4j
@Getter
@Setter
@ConfigurationProperties(prefix = "h3.time-evaluation")
public class TimeEvaluationConfig {
    
    // ===================== 时间约束参数 =====================
    
    /**
     * 最大工作时间(小时)
     * 单条路线的最大允许工作时长
     */
    private double maxWorkTimeHours = 7.0;
    
    /**
     * 可动区间(小时)  
     * 达到 (最大时间 - 可动区间) 时开始谨慎评估
     */
    private double flexibilityMarginHours = 0.5;
    
    /**
     * 最优工作时间(小时)
     * 目标的理想工作时长
     */
    private double optimalWorkTimeHours = 6.0;
    
    // ===================== 行驶参数 =====================
    
    /**
     * 统一行驶速度(km/h)
     * 简化设计，不区分市区/高速路
     */
    private double drivingSpeedKmh = 40.0;
    
    // ===================== 决策阈值 =====================
    
    /**
     * 立即停止阈值
     * 时间利用率超过此值时立即停止合并
     */
    private double immediateStopThreshold = 0.95;
    
    /**
     * 谨慎阈值  
     * 时间利用率超过此值时进入谨慎模式
     */
    private double cautionThreshold = 0.85;
    
    /**
     * 最优阈值
     * 时间利用率低于此值时积极合并
     */
    private double optimalThreshold = 0.75;
    
    // ===================== 权重参数 =====================
    
    /**
     * 时间权重
     * 在综合评估中时间因素的权重
     */
    private double timeWeight = 0.7;
    
    /**
     * 效率权重
     * 在综合评估中效率因素的权重  
     */
    private double efficiencyWeight = 0.2;
    
    /**
     * 均衡权重
     * 在综合评估中均衡因素的权重
     */
    private double balanceWeight = 0.1;
    
    // ===================== 工具方法 =====================
    
    /**
     * 获取谨慎时间阈值(小时)
     * @return 开始谨慎评估的时间点
     */
    public double getCautionTimeThreshold() {
        return maxWorkTimeHours * cautionThreshold;
    }
    
    /**
     * 获取立即停止时间阈值(小时)
     * @return 立即停止的时间点
     */
    public double getImmediateStopTimeThreshold() {
        return maxWorkTimeHours * immediateStopThreshold;
    }
    
    /**
     * 获取可动区间的实际时间阈值(小时)
     * @return 开始限制合并的时间点
     */
    public double getFlexibilityTimeThreshold() {
        return maxWorkTimeHours - flexibilityMarginHours;
    }
    
    /**
     * 验证配置参数的合理性
     * @return 配置是否有效
     */
    public boolean validateConfig() {
        if (maxWorkTimeHours <= 0) {
            log.error("❌ 最大工作时间必须大于0: {}", maxWorkTimeHours);
            return false;
        }
        
        if (flexibilityMarginHours < 0 || flexibilityMarginHours >= maxWorkTimeHours) {
            log.error("❌ 可动区间必须在[0, {}]范围内: {}", maxWorkTimeHours, flexibilityMarginHours);
            return false;
        }
        
        if (drivingSpeedKmh <= 0) {
            log.error("❌ 行驶速度必须大于0: {}", drivingSpeedKmh);
            return false;
        }
        
        if (immediateStopThreshold <= cautionThreshold || cautionThreshold <= optimalThreshold) {
            log.error("❌ 阈值必须满足: immediate({}) > caution({}) > optimal({})", 
                immediateStopThreshold, cautionThreshold, optimalThreshold);
            return false;
        }
        
        double totalWeight = timeWeight + efficiencyWeight + balanceWeight;
        if (Math.abs(totalWeight - 1.0) > 0.01) {
            log.warn("⚠️ 权重总和不等于1.0: {}, 将自动归一化", totalWeight);
            timeWeight = timeWeight / totalWeight;
            efficiencyWeight = efficiencyWeight / totalWeight;
            balanceWeight = balanceWeight / totalWeight;
        }
        
        return true;
    }
    
    /**
     * 打印配置信息
     */
    public void logConfig() {
        log.info("⚙️ 时间评估配置:");
        log.info("   ⏰ 时间约束: 最大{}h, 最优{}h, 可动区间{}h", 
            maxWorkTimeHours, optimalWorkTimeHours, flexibilityMarginHours);
        log.info("   🚗 行驶速度: {}km/h (统一速度)", drivingSpeedKmh);
        log.info("   🎯 决策阈值: 立即停止{}%, 谨慎{}%, 最优{}%", 
            immediateStopThreshold*100, cautionThreshold*100, optimalThreshold*100);
        log.info("   ⚖️ 权重配置: 时间{}%, 效率{}%, 均衡{}%", 
            timeWeight*100, efficiencyWeight*100, balanceWeight*100);
        log.info("   📝 注：配送时间直接使用Accumulation.deliveryTime字段");
    }
}