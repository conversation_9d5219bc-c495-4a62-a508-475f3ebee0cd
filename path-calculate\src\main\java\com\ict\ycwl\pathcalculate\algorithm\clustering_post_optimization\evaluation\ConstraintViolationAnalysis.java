package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 约束违反分析结果
 * 
 * 分析当前路线配置下的约束违反情况
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ConstraintViolationAnalysis {
    
    /**
     * 时间约束违反列表
     */
    private List<ConstraintViolation> timeViolations;
    
    /**
     * 违反约束的路线数量
     */
    private int violationCount;
    
    /**
     * 违反率（违反路线数/总路线数）
     */
    private double violationRate;
    
    /**
     * 总违反时间（分钟）
     */
    private double totalViolationTime;
    
    /**
     * 违反严重程度 (0.0-1.0)
     */
    private double violationSeverity;
    
    /**
     * 最大工作时间（分钟）
     */
    private double maxWorkTime;
    
    /**
     * 最小工作时间（分钟）
     */
    private double minWorkTime;
    
    /**
     * 时间差距（最大-最小工作时间）
     */
    private double timeGap;
    
    /**
     * 时间差距是否过大（>60分钟预警）
     */
    private boolean isTimeGapExcessive;
    
    /**
     * 获取约束满足等级
     */
    public ConstraintSatisfactionGrade getSatisfactionGrade() {
        if (violationRate == 0.0) {
            return ConstraintSatisfactionGrade.PERFECT;
        } else if (violationRate <= 0.05) {
            return ConstraintSatisfactionGrade.EXCELLENT;
        } else if (violationRate <= 0.1) {
            return ConstraintSatisfactionGrade.GOOD;
        } else if (violationRate <= 0.2) {
            return ConstraintSatisfactionGrade.FAIR;
        } else if (violationRate <= 0.3) {
            return ConstraintSatisfactionGrade.POOR;
        } else {
            return ConstraintSatisfactionGrade.CRITICAL;
        }
    }
    
    /**
     * 是否存在严重约束违反
     */
    public boolean hasCriticalViolations() {
        return violationSeverity > 0.2 || violationRate > 0.3;
    }
    
    /**
     * 是否需要基于约束违反调整路线数量
     */
    public boolean needsConstraintBasedAdjustment() {
        return violationRate > 0.1 || violationSeverity > 0.1;
    }
    
    /**
     * 获取基于约束违反的推荐调整方向
     */
    public RouteCountAction getConstraintBasedRecommendation() {
        if (hasCriticalViolations()) {
            return RouteCountAction.INCREASE; // 严重违反需要增加路线
        } else if (violationRate > 0.1) {
            return RouteCountAction.INCREASE; // 一般违反也需要增加路线
        } else {
            return RouteCountAction.MAINTAIN; // 约束满足良好
        }
    }
    
    /**
     * 获取约束满足评分 (0.0-1.0)
     */
    public double getConstraintSatisfactionScore() {
        return Math.max(0.0, 1.0 - violationSeverity * 2.0);
    }
    
    /**
     * 计算预期路线数量需求
     * 基于当前违反情况估算需要增加的路线数
     */
    public int getEstimatedAdditionalRoutes() {
        if (violationRate == 0.0) {
            return 0;
        }
        
        // 基于违反严重程度估算
        // 假设每增加1条路线可以减少25%的违反时间
        double violationTimePerRoute = totalViolationTime / violationCount;
        double estimatedReduction = violationTimePerRoute * 0.25;
        
        return (int) Math.ceil(totalViolationTime / estimatedReduction);
    }
    
    /**
     * 约束满足等级枚举
     */
    public enum ConstraintSatisfactionGrade {
        PERFECT("完美", 6, "所有约束完全满足"),
        EXCELLENT("优秀", 5, "极少约束违反"),
        GOOD("良好", 4, "少量约束违反"),
        FAIR("一般", 3, "中等约束违反"),
        POOR("较差", 2, "较多约束违反"),
        CRITICAL("严重", 1, "大量约束违反");
        
        private final String description;
        private final int score;
        private final String detail;
        
        ConstraintSatisfactionGrade(String description, int score, String detail) {
            this.description = description;
            this.score = score;
            this.detail = detail;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getScore() {
            return score;
        }
        
        public String getDetail() {
            return detail;
        }
    }
}