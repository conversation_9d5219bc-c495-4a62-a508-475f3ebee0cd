package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import java.util.List;
import java.util.Map;

/**
 * TSP求解器通用接口
 * 为不同的TSP求解器实现提供统一的接口
 */
public interface TSPSolver {
    
    /**
     * 求解TSP问题
     * @param depot 中转站
     * @param cluster 聚集区列表
     * @param timeMatrix 时间矩阵
     * @param timeLimitMs 时间限制（毫秒）
     * @return 优化后的访问序列
     */
    List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                    Map<String, TimeInfo> timeMatrix, long timeLimitMs);
    
    /**
     * 检查求解器是否可用
     * @return true表示求解器可用
     */
    boolean isORToolsAvailable();
    
    /**
     * 求解TSP问题（无时间限制版本）
     * @param depot 中转站
     * @param cluster 聚集区列表
     * @param timeMatrix 时间矩阵
     * @return 优化后的访问序列
     */
    default List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix) {
        return solve(depot, cluster, timeMatrix, 30000L); // 默认30秒
    }
}