package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * TSP求解器管理类 - 完全重构版
 * 集成多种先进算法，支持智能选择和多目标优化
 * 
 * 功能特性：
 * - 真正的分支定界算法
 * - 集成遗传算法优化
 * - OR-Tools高性能求解
 * - 多目标优化支持
 * - 智能算法选择
 * - 性能监控和调试
 */
@Slf4j
@Component
public class TSPSolverManager {
    
    // 求解策略枚举
    public enum SolverStrategy {
        AUTO("自动选择", "根据问题特征智能选择最佳算法"),
        QUALITY_FIRST("质量优先", "优先保证解的质量"),
        SPEED_FIRST("速度优先", "优先保证求解速度"),
        BALANCED("平衡策略", "平衡质量和速度"),
        MULTI_OBJECTIVE("多目标优化", "支持多种优化目标");
        
        private final String displayName;
        private final String description;
        
        SolverStrategy(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    // 注入所有求解器（高性能第三方库优先）
    private final BranchAndBoundTSP branchBoundSolver; // 将被ORToolsCPSATTSP替换
    private final EnhancedGeneticTSP geneticSolver; // 将被JeneticsGeneticTSP替换
    private final SafeORToolsTSP orToolsSolver; // Google OR-Tools VRP求解器
    // private final ORToolsCPSATTSP cpSatSolver; // TODO: CP-SAT求解器待API兼容性修复后启用  
    private final EnhancedGeneticTSP enhancedGeneticSolver; // 增强版遗传算法
    private final MultiObjectiveTSP multiObjectiveSolver;
    private final AdaptiveTSPSolver adaptiveSolver;
    private final TSPCacheManager cacheManager;
    
    // 性能统计
    private final Map<String, SolverPerformanceStats> performanceStats = new HashMap<>();
    
    /**
     * 无参构造器 - 用于向后兼容
     */
    public TSPSolverManager() {
        // 按依赖顺序创建算法实例（优先使用高性能第三方库）
        this.geneticSolver = new EnhancedGeneticTSP(); // 基础遗传算法
        this.enhancedGeneticSolver = new EnhancedGeneticTSP(); // 增强版遗传算法
        this.branchBoundSolver = new BranchAndBoundTSP(); // 保留作为备用
        // this.cpSatSolver = new ORToolsCPSATTSP(); // TODO: 待API兼容性修复后启用
        this.orToolsSolver = new SafeORToolsTSP(enhancedGeneticSolver); // OR-Tools VRP（使用增强遗传算法作为备用）
        this.multiObjectiveSolver = new MultiObjectiveTSP(orToolsSolver, enhancedGeneticSolver, branchBoundSolver);
        this.adaptiveSolver = new AdaptiveTSPSolver(branchBoundSolver, enhancedGeneticSolver, orToolsSolver, multiObjectiveSolver);
        this.cacheManager = new TSPCacheManager();
        
        log.info("🚀 TSP求解器管理器初始化完成（高性能配置）");
        log.info("📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1");
        log.info("🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法");
    }
    
    /**
     * 依赖注入构造器 - 用于Spring环境
     */
    public TSPSolverManager(BranchAndBoundTSP branchBoundSolver,
                           EnhancedGeneticTSP geneticSolver,
                           SafeORToolsTSP orToolsSolver,
                           MultiObjectiveTSP multiObjectiveSolver,
                           AdaptiveTSPSolver adaptiveSolver) {
        this.branchBoundSolver = branchBoundSolver;
        this.geneticSolver = geneticSolver;
        this.enhancedGeneticSolver = geneticSolver; // 使用同一个实例
        this.orToolsSolver = orToolsSolver;
        this.multiObjectiveSolver = multiObjectiveSolver;
        this.adaptiveSolver = adaptiveSolver;
        this.cacheManager = new TSPCacheManager();
        
        log.info("TSP求解器管理器初始化完成，集成算法: 分支定界、遗传算法、OR-Tools、多目标优化、自适应选择");
    }
    
    /**
     * 求解路线TSP问题 - 主入口（保持向后兼容）
     */
    public RouteResult solveRoute(TransitDepot depot, List<Accumulation> cluster, 
                                 Map<String, TimeInfo> timeMatrix, int routeNumber) {
        return solveRoute(depot, cluster, timeMatrix, routeNumber, SolverStrategy.AUTO, 
                         MultiObjectiveTSP.OptimizationGoal.BALANCED, AlgorithmParameters.TSP_TIME_LIMIT_SECONDS * 1000L);
    }
    
    /**
     * 求解路线TSP问题 - 完整接口
     */
    public RouteResult solveRoute(TransitDepot depot, List<Accumulation> cluster, 
                                 Map<String, TimeInfo> timeMatrix, int routeNumber,
                                 SolverStrategy strategy, MultiObjectiveTSP.OptimizationGoal goal, 
                                 long timeLimitMs) {
        
        long startTime = System.currentTimeMillis();
        String sessionId = generateSessionId(depot, cluster, routeNumber);
        
        log.debug("开始TSP求解 - 中转站: {}, 路线: {}, 节点数: {}, 策略: {}, 目标: {}, 时间限制: {}ms", 
                depot.getTransitDepotName(), routeNumber, cluster.size(), 
                strategy.getDisplayName(), goal.getDisplayName(), timeLimitMs);
        
        try {
            // 1. 边界条件处理
            if (cluster.isEmpty()) {
                return createEmptyRoute(depot, routeNumber);
            }
            
            if (cluster.size() == 1) {
                return createSinglePointRoute(depot, cluster.get(0), timeMatrix, routeNumber);
            }
            
            // 2. 缓存检查
            String problemSignature = cacheManager.generateProblemSignature(depot, cluster);
            List<Long> cachedSolution = cacheManager.getCachedSolution(problemSignature);
            if (cachedSolution != null && !cachedSolution.isEmpty()) {
                log.debug("使用缓存解决方案，会话: {}", sessionId);
                return buildRouteResult(depot, cluster, cachedSolution, timeMatrix, routeNumber);
            }
            
            // 3. 根据策略选择求解方法
            List<Long> optimizedSequence = executeStrategy(strategy, goal, depot, cluster, timeMatrix, timeLimitMs);
            
            // 4. 缓存结果
            if (!optimizedSequence.isEmpty()) {
                cacheManager.cacheSolution(problemSignature, optimizedSequence);
            }
            
            // 5. 构建最终结果
            RouteResult result = buildRouteResult(depot, cluster, optimizedSequence, timeMatrix, routeNumber);
            
            // 6. 性能统计
            long executionTime = System.currentTimeMillis() - startTime;
            recordPerformanceStats(strategy.name(), cluster.size(), executionTime, result.getTotalWorkTime());
            
            log.debug("TSP求解完成 - 会话: {}, 耗时: {}ms, 总工作时间: {:.2f}分钟", 
                     sessionId, executionTime, result.getTotalWorkTime());
            
            return result;
            
        } catch (Exception e) {
            log.error("TSP求解失败 - 会话: {}, 错误: {}", sessionId, e.getMessage(), e);
            // 降级到简单贪心算法
            List<Long> fallbackSequence = solveTSPWithGreedy(depot, cluster, timeMatrix);
            return buildRouteResult(depot, cluster, fallbackSequence, timeMatrix, routeNumber);
        }
    }
    
    /**
     * 创建空路线
     */
    private RouteResult createEmptyRoute(TransitDepot depot, int routeNumber) {
        return RouteResult.builder()
                .routeId(generateRouteId(depot.getTransitDepotId(), routeNumber))
                .routeName(generateRouteName(depot, routeNumber))
                .transitDepotId(depot.getTransitDepotId())
                .accumulationSequence(new ArrayList<>())
                .polyline(Arrays.asList(depot.getCoordinate()))
                .totalWorkTime(AlgorithmParameters.LOADING_TIME_MINUTES)
                .convexHull(new ArrayList<>())
                .build();
    }
    
    /**
     * 创建单点路线
     */
    private RouteResult createSinglePointRoute(
            TransitDepot depot, 
            Accumulation accumulation, 
            Map<String, TimeInfo> timeMatrix, 
            int routeNumber) {
        
        // 计算总工作时间
        double travelTime = getTravelTime(depot.getCoordinate(), accumulation.getCoordinate(), timeMatrix) * 2; // 往返
        double totalWorkTime = AlgorithmParameters.LOADING_TIME_MINUTES + travelTime + accumulation.getDeliveryTime();
        
        // 构建路线坐标
        List<CoordinatePoint> polyline = Arrays.asList(
                depot.getCoordinate(),
                accumulation.getCoordinate(),
                depot.getCoordinate()
        );
        
        return RouteResult.builder()
                .routeId(generateRouteId(depot.getTransitDepotId(), routeNumber))
                .routeName(generateRouteName(depot, routeNumber))
                .transitDepotId(depot.getTransitDepotId())
                .accumulationSequence(Arrays.asList(accumulation.getAccumulationId()))
                .polyline(polyline)
                .totalWorkTime(totalWorkTime)
                .convexHull(new ArrayList<>()) // 单点无需凸包
                .build();
    }
    
    /**
     * 使用动态规划求解TSP
     */
    private List<Long> solveTSPWithDP(
            TransitDepot depot, 
            List<Accumulation> cluster, 
            Map<String, TimeInfo> timeMatrix) {
        
        log.debug("使用动态规划求解TSP，节点数: {}", cluster.size());
        
        int n = cluster.size();
        List<Accumulation> nodes = new ArrayList<>(cluster);
        
        // 构建距离矩阵（包含点权）
        double[][] costMatrix = buildCostMatrix(depot, nodes, timeMatrix);
        
        // DP状态：dp[mask][i] = 从起点经过mask集合中的点到达点i的最小成本
        double[][] dp = new double[1 << n][n];
        int[][] parent = new int[1 << n][n];
        
        // 初始化
        for (int i = 0; i < (1 << n); i++) {
            Arrays.fill(dp[i], Double.MAX_VALUE);
            Arrays.fill(parent[i], -1);
        }
        
        // 从起点（中转站）到各个节点的初始成本
        for (int i = 0; i < n; i++) {
            dp[1 << i][i] = costMatrix[n][i]; // n是中转站的索引
            parent[1 << i][i] = n;
        }
        
        // 动态规划状态转移
        for (int mask = 1; mask < (1 << n); mask++) {
            for (int i = 0; i < n; i++) {
                if ((mask & (1 << i)) == 0 || dp[mask][i] == Double.MAX_VALUE) continue;
                
                for (int j = 0; j < n; j++) {
                    if (i == j || (mask & (1 << j)) != 0) continue;
                    
                    int newMask = mask | (1 << j);
                    double newCost = dp[mask][i] + costMatrix[i][j];
                    
                    if (newCost < dp[newMask][j]) {
                        dp[newMask][j] = newCost;
                        parent[newMask][j] = i;
                    }
                }
            }
        }
        
        // 找到最优解
        int fullMask = (1 << n) - 1;
        double minCost = Double.MAX_VALUE;
        int lastNode = -1;
        
        for (int i = 0; i < n; i++) {
            double cost = dp[fullMask][i] + costMatrix[i][n]; // 回到中转站
            if (cost < minCost) {
                minCost = cost;
                lastNode = i;
            }
        }
        
        // 重构路径
        List<Integer> path = new ArrayList<>();
        int currentMask = fullMask;
        int currentNode = lastNode;
        
        while (currentNode != n && currentNode != -1) {
            path.add(currentNode);
            int prevNode = parent[currentMask][currentNode];
            currentMask ^= (1 << currentNode);
            currentNode = prevNode;
        }
        
        Collections.reverse(path);
        
        // 转换为聚集区ID序列
        return path.stream()
                .map(i -> nodes.get(i).getAccumulationId())
                .collect(Collectors.toList());
    }
    
    /**
     * 根据策略执行求解
     */
    private List<Long> executeStrategy(SolverStrategy strategy, MultiObjectiveTSP.OptimizationGoal goal,
                                      TransitDepot depot, List<Accumulation> cluster, 
                                      Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        switch (strategy) {
            case AUTO:
                return adaptiveSolver.solve(depot, cluster, timeMatrix, timeLimitMs, 0.8);
                
            case QUALITY_FIRST:
                return solveForQuality(depot, cluster, timeMatrix, timeLimitMs);
                
            case SPEED_FIRST:
                return solveForSpeed(depot, cluster, timeMatrix, timeLimitMs);
                
            case BALANCED:
                return solveForBalance(depot, cluster, timeMatrix, timeLimitMs);
                
            case MULTI_OBJECTIVE:
                return multiObjectiveSolver.solve(depot, cluster, timeMatrix, goal);
                
            default:
                return adaptiveSolver.solve(depot, cluster, timeMatrix, timeLimitMs, 0.8);
        }
    }
    
    /**
     * 质量优先求解（使用高性能第三方库）
     */
    private List<Long> solveForQuality(TransitDepot depot, List<Accumulation> cluster, 
                                      Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        int nodeCount = cluster.size();
        String qualityLevel = AlgorithmParameters.TSP_OPTIMIZATION_QUALITY;
        String solverStrategy = AlgorithmParameters.TSP_SOLVER_STRATEGY;
        
        log.info("🎯 [质量优先] 优化质量: {}, 求解器策略: {}, 节点数: {}", 
            qualityLevel, solverStrategy, nodeCount);
        
        // 🔧 根据质量等级调整时间限制
        long adjustedTimeLimit = adjustTimeLimitByQuality(timeLimitMs, qualityLevel);
        log.info("⏱️ [时间调整] 原时间限制: {}ms, 调整后: {}ms", timeLimitMs, adjustedTimeLimit);
        
        // 🚀 优先尝试第三方高性能库（根据策略配置）
        List<Long> result = tryThirdPartyLibraries(depot, cluster, timeMatrix, adjustedTimeLimit, 
                                                  nodeCount, solverStrategy);
        if (result != null && !result.isEmpty()) {
            return result;
        }
        
        // 🔄 降级到传统算法
        return fallbackToTraditionalAlgorithms(depot, cluster, timeMatrix, adjustedTimeLimit, nodeCount);
    }
    
    /**
     * 根据质量等级调整时间限制
     */
    private long adjustTimeLimitByQuality(long originalTimeLimit, String qualityLevel) {
        switch (qualityLevel) {
            case "ULTRA":
                return originalTimeLimit * 4;  // 超高质量：4倍时间
            case "HIGH":
                return originalTimeLimit * 2;  // 高质量：2倍时间  
            case "STANDARD":
            default:
                return originalTimeLimit;      // 标准质量：原时间
        }
    }
    
    /**
     * 尝试使用第三方高性能库求解
     */
    private List<Long> tryThirdPartyLibraries(TransitDepot depot, List<Accumulation> cluster, 
                                            Map<String, TimeInfo> timeMatrix, long timeLimitMs,
                                            int nodeCount, String solverStrategy) {
        
        log.info("🚀 [第三方库尝试] 策略: {}, 节点数: {}, 时间限制: {}ms", 
            solverStrategy, nodeCount, timeLimitMs);
        
        // OR-Tools优先策略
        if ("OR_TOOLS_PRIORITY".equals(solverStrategy) || "AUTO".equals(solverStrategy)) {
            if (orToolsSolver.isORToolsAvailable()) {
                log.info("🎯 [OR-Tools] 开始使用OR-Tools求解 ({} 节点)", nodeCount);
                try {
                    List<Long> result = orToolsSolver.solve(depot, cluster, timeMatrix, timeLimitMs);
                    if (result != null && !result.isEmpty()) {
                        log.info("✅ [OR-Tools成功] 成功求解 {} 节点TSP", nodeCount);
                        return result;
                    }
                } catch (Exception e) {
                    log.warn("⚠️ [OR-Tools失败] OR-Tools求解失败: {}", e.getMessage());
                }
            } else {
                log.warn("❌ [OR-Tools不可用] OR-Tools库不可用，尝试其他算法");
            }
        }
        
        // 遗传算法优先策略 
        if ("GENETIC_PRIORITY".equals(solverStrategy) || "AUTO".equals(solverStrategy)) {
            log.info("🧬 [遗传算法] 开始使用增强遗传算法求解 ({} 节点)", nodeCount);
            try {
                List<Long> result = enhancedGeneticSolver.solve(depot, cluster, timeMatrix);
                if (result != null && !result.isEmpty()) {
                    log.info("✅ [遗传算法成功] 成功求解 {} 节点TSP", nodeCount);
                    return result;
                }
            } catch (Exception e) {
                log.warn("⚠️ [遗传算法失败] 遗传算法求解失败: {}", e.getMessage());
            }
        }
        
        log.warn("❌ [第三方库失败] 所有第三方库求解失败，将降级到传统算法");
        return null;
    }
    
    /**
     * 降级到传统算法
     */
    private List<Long> fallbackToTraditionalAlgorithms(TransitDepot depot, List<Accumulation> cluster, 
                                                      Map<String, TimeInfo> timeMatrix, long timeLimitMs,
                                                      int nodeCount) {
        
        log.info("🔄 [传统算法降级] 节点数: {}, 时间限制: {}ms", nodeCount, timeLimitMs);
        
        if (nodeCount <= 12) {
            // 小规模：动态规划（最高质量）
            log.info("🧮 [动态规划] 使用动态规划求解小规模TSP ({} 节点)", nodeCount);
            return solveTSPWithDP(depot, cluster, timeMatrix);
        } else if (nodeCount <= 25 && branchBoundSolver != null) {
            // 中等规模：分支定界算法（高质量精确求解）
            log.info("🎯 [分支定界] 使用分支定界算法求解中等规模TSP ({} 节点)", nodeCount);
            return branchBoundSolver.solve(depot, cluster, timeMatrix, timeLimitMs);
        } else {
            // 大规模：基础遗传算法
            log.info("🧬 [基础遗传] 使用基础遗传算法求解大规模TSP ({} 节点)", nodeCount);
            return geneticSolver.solve(depot, cluster, timeMatrix);
        }
    }
    
    /**
     * 速度优先求解（使用高性能第三方库）
     */
    private List<Long> solveForSpeed(TransitDepot depot, List<Accumulation> cluster, 
                                    Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        int nodeCount = cluster.size();
        
        log.info("⚡ [速度优先] 选择最快算法 - 节点数: {}, 时间限制: {}ms", nodeCount, timeLimitMs);
        
        if (nodeCount <= 15) {
            // 小规模：OR-Tools快速模式（速度和质量平衡）
            if (orToolsSolver.isORToolsAvailable()) {
                log.info("🚀 [第三方库] 使用OR-Tools快速模式 ({} 节点)", nodeCount);
                return orToolsSolver.solve(depot, cluster, timeMatrix, Math.min(timeLimitMs, 10000)); // 限制10秒
            } else {
                // 降级：贪心算法 + 2-opt
                log.info("🔄 [算法降级] 使用贪心+2-opt算法 ({} 节点)", nodeCount);
                List<Long> greedyResult = solveTSPWithGreedy(depot, cluster, timeMatrix);
                return improve2Opt(depot, cluster, greedyResult, timeMatrix, Math.min(timeLimitMs / 2, 5000));
            }
        } else if (nodeCount <= 30) {
            // 中等规模：增强遗传算法快速配置
            log.info("🧬 [算法优化] 使用增强遗传算法快速求解 ({} 节点)", nodeCount);
            return enhancedGeneticSolver.solve(depot, cluster, timeMatrix);
        } else {
            // 大规模：快速贪心（最快）
            log.info("⚡ [快速算法] 使用优化贪心算法 ({} 节点)", nodeCount);
            return solveTSPWithGreedy(depot, cluster, timeMatrix);
        }
    }
    
    /**
     * 平衡策略求解
     */
    private List<Long> solveForBalance(TransitDepot depot, List<Accumulation> cluster, 
                                     Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        return adaptiveSolver.solveWithHybridStrategy(depot, cluster, timeMatrix, timeLimitMs);
    }
    
    /**
     * 使用启发式算法求解TSP
     */
    private List<Long> solveTSPWithHeuristic(
            TransitDepot depot, 
            List<Accumulation> cluster, 
            Map<String, TimeInfo> timeMatrix) {
        
        log.debug("使用启发式算法求解TSP，节点数: {}", cluster.size());
        
        // 使用贪心算法 + 2-opt优化
        List<Long> greedyResult = solveTSPWithGreedy(depot, cluster, timeMatrix);
        return improve2Opt(depot, cluster, greedyResult, timeMatrix, 10000L); // 10秒时间限制
    }
    
    /**
     * 贪心算法求解TSP
     */
    private List<Long> solveTSPWithGreedy(
            TransitDepot depot, 
            List<Accumulation> cluster, 
            Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation nearest = null;
            double minCost = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                // 🔧 修复：多因子成本函数，避免路线交叉
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                double geographicDistance = calculateEuclideanDistance(currentPos, acc.getCoordinate());
                double deliveryTime = acc.getDeliveryTime();
                
                // 加权综合成本：时间40% + 地理距离30% + 配送时间30%
                double timeCost = travelTime * 0.4;
                double geographicCost = geographicDistance * 2.0 * 0.3; // 地理距离转换为时间等价（2分钟/公里）
                double deliveryCost = deliveryTime * 0.3;
                
                double totalCost = timeCost + geographicCost + deliveryCost;
                
                if (totalCost < minCost) {
                    minCost = totalCost;
                    nearest = acc;
                }
            }
            
            if (nearest != null) {
                sequence.add(nearest.getAccumulationId());
                visited.add(nearest.getAccumulationId());
                currentPos = nearest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 增强版2-opt优化（支持时间限制）
     */
    private List<Long> improve2Opt(TransitDepot depot, List<Accumulation> cluster, 
                                  List<Long> initialSequence, Map<String, TimeInfo> timeMatrix, 
                                  long timeLimitMs) {
        
        if (initialSequence.size() < 4) {
            return initialSequence;
        }
        
        List<Long> bestSequence = new ArrayList<>(initialSequence);
        double bestCost = calculateRouteCost(depot, cluster, bestSequence, timeMatrix);
        
        long startTime = System.currentTimeMillis();
        boolean improved = true;
        int iterations = 0;
        int maxIterations = Math.min(200, initialSequence.size() * 2);
        
        log.debug("开始2-opt优化，初始成本: {:.2f}, 时间限制: {}ms", bestCost, timeLimitMs);
        
        while (improved && iterations < maxIterations && 
               (System.currentTimeMillis() - startTime) < timeLimitMs) {
            
            improved = false;
            iterations++;
            
            // 优化：随机选择起始点，避免局部最优
            List<Integer> indices = new ArrayList<>();
            for (int i = 0; i < bestSequence.size() - 1; i++) {
                indices.add(i);
            }
            Collections.shuffle(indices);
            
            for (int i : indices) {
                if ((System.currentTimeMillis() - startTime) >= timeLimitMs) break;
                
                for (int j = i + 2; j < bestSequence.size(); j++) {
                    // 2-opt交换
                    List<Long> newSequence = new ArrayList<>(bestSequence);
                    Collections.reverse(newSequence.subList(i + 1, j + 1));
                    
                    double newCost = calculateRouteCost(depot, cluster, newSequence, timeMatrix);
                    
                    if (newCost < bestCost) {
                        bestSequence = newSequence;
                        bestCost = newCost;
                        improved = true;
                        
                        // 找到改进后立即尝试下一轮
                        break;
                    }
                }
                
                if (improved) break;
            }
        }
        
        long duration = System.currentTimeMillis() - startTime;
        double improvement = ((calculateRouteCost(depot, cluster, initialSequence, timeMatrix) - bestCost) / 
                            calculateRouteCost(depot, cluster, initialSequence, timeMatrix)) * 100;
        
        log.debug("2-opt优化完成，迭代: {}, 耗时: {}ms, 改进: {:.2f}%, 最终成本: {:.2f}", 
                 iterations, duration, improvement, bestCost);
        
        return bestSequence;
    }
    
    /**
     * 并行多算法求解（实验性功能）
     */
    public CompletableFuture<RouteResult> solveRouteAsync(TransitDepot depot, List<Accumulation> cluster, 
                                                         Map<String, TimeInfo> timeMatrix, int routeNumber,
                                                         SolverStrategy strategy, MultiObjectiveTSP.OptimizationGoal goal) {
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return solveRoute(depot, cluster, timeMatrix, routeNumber, strategy, goal, 
                               AlgorithmParameters.TSP_TIME_LIMIT_SECONDS * 1000L);
            } catch (Exception e) {
                log.error("异步TSP求解失败: {}", e.getMessage());
                return createEmptyRoute(depot, routeNumber);
            }
        });
    }
    
    /**
     * 批量路线求解
     */
    public List<RouteResult> solveBatchRoutes(List<BatchSolveRequest> requests) {
        
        log.info("开始批量TSP求解，请求数量: {}", requests.size());
        
        List<CompletableFuture<RouteResult>> futures = requests.stream()
                .map(request -> solveRouteAsync(request.depot, request.cluster, request.timeMatrix, 
                                              request.routeNumber, request.strategy, request.goal))
                .collect(Collectors.toList());
        
        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }
    
    // 批量求解请求类
    public static class BatchSolveRequest {
        public TransitDepot depot;
        public List<Accumulation> cluster;
        public Map<String, TimeInfo> timeMatrix;
        public int routeNumber;
        public SolverStrategy strategy = SolverStrategy.AUTO;
        public MultiObjectiveTSP.OptimizationGoal goal = MultiObjectiveTSP.OptimizationGoal.BALANCED;
        
        public BatchSolveRequest(TransitDepot depot, List<Accumulation> cluster, 
                               Map<String, TimeInfo> timeMatrix, int routeNumber) {
            this.depot = depot;
            this.cluster = cluster;
            this.timeMatrix = timeMatrix;
            this.routeNumber = routeNumber;
        }
    }
    
    /**
     * 构建成本矩阵（包含点权）
     */
    private double[][] buildCostMatrix(
            TransitDepot depot, 
            List<Accumulation> nodes, 
            Map<String, TimeInfo> timeMatrix) {
        
        int n = nodes.size();
        double[][] matrix = new double[n + 1][n + 1]; // +1 for depot
        
        // 中转站到聚集区的成本
        for (int i = 0; i < n; i++) {
            matrix[n][i] = getTravelTime(depot.getCoordinate(), nodes.get(i).getCoordinate(), timeMatrix)
                    + nodes.get(i).getDeliveryTime(); // 包含点权
            matrix[i][n] = getTravelTime(nodes.get(i).getCoordinate(), depot.getCoordinate(), timeMatrix);
        }
        
        // 聚集区之间的成本
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    matrix[i][j] = getTravelTime(nodes.get(i).getCoordinate(), nodes.get(j).getCoordinate(), timeMatrix)
                            + nodes.get(j).getDeliveryTime(); // 包含到达点的点权
                }
            }
        }
        
        return matrix;
    }
    
    /**
     * 计算路线总成本
     */
    private double calculateRouteCost(
            TransitDepot depot, 
            List<Accumulation> cluster, 
            List<Long> sequence, 
            Map<String, TimeInfo> timeMatrix) {
        
        if (sequence.isEmpty()) return AlgorithmParameters.LOADING_TIME_MINUTES;
        
        double totalCost = AlgorithmParameters.LOADING_TIME_MINUTES;
        CoordinatePoint currentPos = depot.getCoordinate();
        
        for (Long accId : sequence) {
            Accumulation acc = cluster.stream()
                    .filter(a -> a.getAccumulationId().equals(accId))
                    .findFirst()
                    .orElse(null);
            
            if (acc != null) {
                totalCost += getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                totalCost += acc.getDeliveryTime(); // 点权
                currentPos = acc.getCoordinate();
            }
        }
        
        // 回到中转站
        totalCost += getTravelTime(currentPos, depot.getCoordinate(), timeMatrix);
        
        return totalCost;
    }
    
    /**
     * 构建路线结果
     */
    private RouteResult buildRouteResult(
            TransitDepot depot, 
            List<Accumulation> cluster, 
            List<Long> sequence, 
            Map<String, TimeInfo> timeMatrix, 
            int routeNumber) {
        
        // 计算总工作时间
        double totalWorkTime = calculateRouteCost(depot, cluster, sequence, timeMatrix);
        
        // 构建路线坐标串
        List<CoordinatePoint> polyline = new ArrayList<>();
        polyline.add(depot.getCoordinate());
        
        for (Long accId : sequence) {
            Accumulation acc = cluster.stream()
                    .filter(a -> a.getAccumulationId().equals(accId))
                    .findFirst()
                    .orElse(null);
            if (acc != null) {
                polyline.add(acc.getCoordinate());
            }
        }
        
        polyline.add(depot.getCoordinate()); // 回到起点
        
        return RouteResult.builder()
                .routeId(generateRouteId(depot.getTransitDepotId(), routeNumber))
                .routeName(generateRouteName(depot, routeNumber))
                .transitDepotId(depot.getTransitDepotId())
                .accumulationSequence(sequence)
                .polyline(polyline)
                .totalWorkTime(totalWorkTime)
                .convexHull(new ArrayList<>()) // 凸包将在后续阶段生成
                .build();
    }
    
    /**
     * 获取两点间行驶时间
     */
    private double getTravelTime(CoordinatePoint from, CoordinatePoint to, Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null ? timeInfo.getTravelTime() : 0.0;
    }
    
    /**
     * 计算两点间欧几里得距离（公里）- 新增：用于地理因子计算
     */
    private double calculateEuclideanDistance(CoordinatePoint from, CoordinatePoint to) {
        double dLat = Math.toRadians(to.getLatitude() - from.getLatitude());
        double dLon = Math.toRadians(to.getLongitude() - from.getLongitude());
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(from.getLatitude())) * Math.cos(Math.toRadians(to.getLatitude())) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return 6371.0 * c; // 地球半径，返回公里
    }
    
    /**
     * 生成路线ID
     */
    private Long generateRouteId(Long transitDepotId, int routeNumber) {
        return transitDepotId * 1000 + routeNumber;
    }
    
    /**
     * 生成路线名称
     */
    private String generateRouteName(TransitDepot depot, int routeNumber) {
        return depot.getTransitDepotName() + "-路线" + routeNumber;
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId(TransitDepot depot, List<Accumulation> cluster, int routeNumber) {
        return String.format("%s-R%d-N%d-%d", 
                depot.getTransitDepotId(), routeNumber, cluster.size(), System.currentTimeMillis() % 10000);
    }
    
    /**
     * 记录性能统计
     */
    private void recordPerformanceStats(String algorithm, int nodeCount, long executionTime, double solutionCost) {
        SolverPerformanceStats stats = performanceStats.computeIfAbsent(algorithm, k -> new SolverPerformanceStats());
        stats.recordExecution(nodeCount, executionTime, solutionCost);
    }
    
    /**
     * 获取性能统计报告
     */
    public Map<String, SolverPerformanceStats> getPerformanceStats() {
        return new HashMap<>(performanceStats);
    }
    
    /**
     * 清理性能统计
     */
    public void clearPerformanceStats() {
        performanceStats.clear();
        log.info("TSP求解器性能统计已清理");
    }
    
    /**
     * 获取求解器状态报告
     */
    public SolverStatusReport getStatusReport() {
        SolverStatusReport report = new SolverStatusReport();
        report.orToolsAvailable = orToolsSolver.isORToolsAvailable();
        report.cacheSize = cacheManager.getCacheSize();
        report.totalSolveCount = performanceStats.values().stream()
                .mapToInt(stats -> stats.executionCount)
                .sum();
        report.avgExecutionTime = performanceStats.values().stream()
                .mapToDouble(stats -> stats.avgExecutionTime)
                .average()
                .orElse(0.0);
        
        return report;
    }
    
    // 性能统计类
    public static class SolverPerformanceStats {
        public int executionCount = 0;
        public double avgExecutionTime = 0.0;
        public double avgSolutionCost = 0.0;
        public double minExecutionTime = Double.MAX_VALUE;
        public double maxExecutionTime = 0.0;
        public Map<Integer, Integer> nodeSizeDistribution = new HashMap<>();
        
        public void recordExecution(int nodeCount, long executionTime, double solutionCost) {
            executionCount++;
            
            // 更新平均值
            avgExecutionTime = (avgExecutionTime * (executionCount - 1) + executionTime) / executionCount;
            avgSolutionCost = (avgSolutionCost * (executionCount - 1) + solutionCost) / executionCount;
            
            // 更新最值
            minExecutionTime = Math.min(minExecutionTime, executionTime);
            maxExecutionTime = Math.max(maxExecutionTime, executionTime);
            
            // 节点规模分布
            int sizeRange = (nodeCount / 10) * 10; // 按10归组
            nodeSizeDistribution.merge(sizeRange, 1, Integer::sum);
        }
        
        @Override
        public String toString() {
            return String.format(
                "Stats{count=%d, avgTime=%.2fms, avgCost=%.2f, minTime=%.2f, maxTime=%.2f}",
                executionCount, avgExecutionTime, avgSolutionCost, minExecutionTime, maxExecutionTime
            );
        }
    }
    
    // 求解器状态报告类
    public static class SolverStatusReport {
        public boolean orToolsAvailable;
        public int cacheSize;
        public int totalSolveCount;
        public double avgExecutionTime;
        
        @Override
        public String toString() {
            return String.format(
                "SolverStatus{orTools=%s, cache=%d, solves=%d, avgTime=%.2fms}",
                orToolsAvailable, cacheSize, totalSolveCount, avgExecutionTime
            );
        }
    }
    
    // 缓存管理器（简化实现）
    private static class TSPCacheManager {
        private final Map<String, List<Long>> solutionCache = new LinkedHashMap<String, List<Long>>() {
            @Override
            protected boolean removeEldestEntry(Map.Entry<String, List<Long>> eldest) {
                return size() > 1000; // 最多缓存1000个解
            }
        };
        
        public String generateProblemSignature(TransitDepot depot, List<Accumulation> cluster) {
            StringBuilder sb = new StringBuilder();
            sb.append(depot.getTransitDepotId()).append("_");
            cluster.stream()
                .sorted(Comparator.comparing(Accumulation::getAccumulationId))
                .forEach(acc -> sb.append(acc.getAccumulationId()).append(","));
            return sb.toString();
        }
        
        public List<Long> getCachedSolution(String signature) {
            return solutionCache.get(signature);
        }
        
        public void cacheSolution(String signature, List<Long> solution) {
            if (!solution.isEmpty()) {
                solutionCache.put(signature, new ArrayList<>(solution));
            }
        }
        
        public int getCacheSize() {
            return solutionCache.size();
        }
        
        public void clearCache() {
            solutionCache.clear();
        }
    }
} 