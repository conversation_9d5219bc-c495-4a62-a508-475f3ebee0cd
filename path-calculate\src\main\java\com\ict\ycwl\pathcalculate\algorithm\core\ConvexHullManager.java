package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.algorithm.ConvexHull;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 凸包管理器
 * 使用JTS库生成凸包并处理重叠冲突
 */
@Slf4j
@Component
public class ConvexHullManager {
    
    private final GeometryFactory geometryFactory = new GeometryFactory();
    
    // 凸包重叠检测和解决开关，默认关闭
    private boolean enableConflictResolution = false;
    
    /**
     * 设置凸包冲突解决开关
     * @param enable true-启用，false-禁用
     */
    public void setConflictResolutionEnabled(boolean enable) {
        this.enableConflictResolution = enable;
        log.info("凸包冲突解决功能已{}", enable ? "启用" : "禁用");
    }
    
    /**
     * 获取凸包冲突解决开关状态
     * @return true-已启用，false-已禁用
     */
    public boolean isConflictResolutionEnabled() {
        return this.enableConflictResolution;
    }
    
    /**
     * 为路线列表生成凸包
     * @param routes 路线列表
     * @param context 算法上下文
     */
    public void generateConvexHulls(List<RouteResult> routes, AlgorithmContext context) {
        log.debug("开始生成凸包，路线数量: {}", routes.size());
        
        for (RouteResult route : routes) {
            List<CoordinatePoint> convexHull = generateConvexHullForRoute(route, context);
            route.setConvexHull(convexHull);
        }
        
        log.debug("凸包生成完成");
    }
    
    /**
     * 检测和解决凸包冲突
     * @param transitDepotId 中转站ID
     * @param routes 路线列表
     * @param context 算法上下文
     * @return 解决的冲突数量
     */
    public int resolveConflicts(Long transitDepotId, List<RouteResult> routes, AlgorithmContext context) {
        log.debug("开始解决中转站 {} 的凸包冲突", transitDepotId);
        
        // 检查是否启用了冲突解决功能
        if (!enableConflictResolution) {
            log.info("凸包冲突解决功能已关闭，跳过冲突检测和解决");
            return 0;
        }
        
        int resolvedConflicts = 0;
        boolean hasConflicts = true;
        int attempts = 0;
        
        while (hasConflicts && attempts < AlgorithmParameters.MAX_CONFLICT_RESOLUTION_ATTEMPTS) {
            hasConflicts = false;
            attempts++;
            
            // 检测所有路线对之间的冲突
            for (int i = 0; i < routes.size(); i++) {
                for (int j = i + 1; j < routes.size(); j++) {
                    RouteResult route1 = routes.get(i);
                    RouteResult route2 = routes.get(j);
                    
                    if (isConvexHullOverlapping(route1.getConvexHull(), route2.getConvexHull())) {
                        log.debug("发现凸包重叠：路线 {} 和路线 {}", route1.getRouteName(), route2.getRouteName());
                        
                        if (resolveConflictBetweenRoutes(route1, route2, context)) {
                            resolvedConflicts++;
                            hasConflicts = true;
                            
                            // 重新生成凸包
                            route1.setConvexHull(generateConvexHullForRoute(route1, context));
                            route2.setConvexHull(generateConvexHullForRoute(route2, context));
                        }
                    }
                }
            }
        }
        
        log.debug("凸包冲突解决完成，解决了 {} 个冲突", resolvedConflicts);
        return resolvedConflicts;
    }
    
    /**
     * 为单条路线生成凸包（仅基于聚集区坐标，不包含中转站）
     * 这样生成的凸包体现的是中转站下需要服务的片区，而不会因为中转站位置导致所有路线凸包重叠
     */
    private List<CoordinatePoint> generateConvexHullForRoute(RouteResult route, AlgorithmContext context) {
        if (route.getAccumulationSequence().size() < 3) {
            // 少于3个点，返回空凸包
            return new ArrayList<>();
        }
        
        // 收集路线中所有聚集区的坐标点（排除中转站坐标）
        List<Coordinate> coordinates = new ArrayList<>();
        
        for (Long accId : route.getAccumulationSequence()) {
            Accumulation acc = context.getAccumulationById(accId);
            if (acc != null) {
                // 只添加聚集区的坐标，不添加中转站坐标
                coordinates.add(new Coordinate(acc.getLongitude(), acc.getLatitude()));
            }
        }
        
        if (coordinates.size() < 3) {
            // 如果聚集区数量少于3个，生成一个基于聚集区中心的小矩形凸包
            return generateMinimalConvexHull(coordinates);
        }
        
        try {
            // 使用JTS生成凸包（仅基于聚集区）
            Coordinate[] coordArray = coordinates.toArray(new Coordinate[0]);
            ConvexHull convexHull = new ConvexHull(coordArray, geometryFactory);
            Geometry hullGeometry = convexHull.getConvexHull();
            
            // 转换为CoordinatePoint列表
            List<CoordinatePoint> result = new ArrayList<>();
            for (Coordinate coord : hullGeometry.getCoordinates()) {
                result.add(new CoordinatePoint(coord.x, coord.y));
            }
            
            log.debug("为路线 {} 生成凸包，基于 {} 个聚集区坐标", route.getRouteName(), coordinates.size());
            return result;
            
        } catch (Exception e) {
            log.warn("生成凸包失败：{}", e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 为少于3个聚集区的路线生成最小凸包
     */
    private List<CoordinatePoint> generateMinimalConvexHull(List<Coordinate> coordinates) {
        if (coordinates.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<CoordinatePoint> result = new ArrayList<>();
        
        if (coordinates.size() == 1) {
            // 单点：生成一个小正方形
            Coordinate center = coordinates.get(0);
            double offset = 0.001; // 约100米的偏移
            result.add(new CoordinatePoint(center.x - offset, center.y - offset));
            result.add(new CoordinatePoint(center.x + offset, center.y - offset));
            result.add(new CoordinatePoint(center.x + offset, center.y + offset));
            result.add(new CoordinatePoint(center.x - offset, center.y + offset));
            result.add(new CoordinatePoint(center.x - offset, center.y - offset)); // 闭合
        } else if (coordinates.size() == 2) {
            // 两点：生成一个矩形
            Coordinate p1 = coordinates.get(0);
            Coordinate p2 = coordinates.get(1);
            double offset = 0.0005; // 约50米的偏移
            
            // 计算垂直于两点连线的方向
            double dx = p2.x - p1.x;
            double dy = p2.y - p1.y;
            double length = Math.sqrt(dx * dx + dy * dy);
            if (length > 0) {
                double perpX = -dy / length * offset;
                double perpY = dx / length * offset;
                
                result.add(new CoordinatePoint(p1.x + perpX, p1.y + perpY));
                result.add(new CoordinatePoint(p2.x + perpX, p2.y + perpY));
                result.add(new CoordinatePoint(p2.x - perpX, p2.y - perpY));
                result.add(new CoordinatePoint(p1.x - perpX, p1.y - perpY));
                result.add(new CoordinatePoint(p1.x + perpX, p1.y + perpY)); // 闭合
            }
        }
        
        return result;
    }
    
    /**
     * 检测两个凸包是否重叠
     */
    private boolean isConvexHullOverlapping(List<CoordinatePoint> hull1, List<CoordinatePoint> hull2) {
        if (hull1.size() < 3 || hull2.size() < 3) {
            return false; // 少于3个点的凸包不考虑重叠
        }
        
        try {
            // 转换为JTS几何对象
            Geometry geom1 = createPolygonFromPoints(hull1);
            Geometry geom2 = createPolygonFromPoints(hull2);
            
            if (geom1 == null || geom2 == null) {
                return false;
            }
            
            // 检查相交且不仅仅是边界接触
            boolean intersects = geom1.intersects(geom2);
            boolean touches = geom1.touches(geom2);
            
            // 计算重叠面积比例
            if (intersects && !touches) {
                Geometry intersection = geom1.intersection(geom2);
                double intersectionArea = intersection.getArea();
                double totalArea = Math.min(geom1.getArea(), geom2.getArea());
                
                double overlapRatio = intersectionArea / totalArea;
                return overlapRatio > AlgorithmParameters.CONVEX_OVERLAP_TOLERANCE;
            }
            
            return false;
            
        } catch (Exception e) {
            log.warn("凸包重叠检测失败：{}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 解决两条路线间的凸包冲突
     */
    private boolean resolveConflictBetweenRoutes(RouteResult route1, RouteResult route2, AlgorithmContext context) {
        // 策略1：尝试交换边界聚集区
        if (trySwapBoundaryAccumulations(route1, route2, context)) {
            return true;
        }
        
        // 策略2：尝试转移边界聚集区
        if (tryTransferBoundaryAccumulation(route1, route2, context)) {
            return true;
        }
        
        // 策略3：记录为轻微重叠，允许容忍
        log.warn("无法解决路线 {} 和 {} 的凸包重叠，标记为容忍", 
                route1.getRouteName(), route2.getRouteName());
        return false;
    }
    
    /**
     * 尝试交换边界聚集区
     */
    private boolean trySwapBoundaryAccumulations(RouteResult route1, RouteResult route2, AlgorithmContext context) {
        List<Long> seq1 = route1.getAccumulationSequence();
        List<Long> seq2 = route2.getAccumulationSequence();
        
        if (seq1.size() <= 1 || seq2.size() <= 1) {
            return false;
        }
        
        // 尝试交换两条路线的边界聚集区
        for (int i1 = 0; i1 < seq1.size(); i1++) {
            for (int i2 = 0; i2 < seq2.size(); i2++) {
                // 交换
                Long temp = seq1.get(i1);
                seq1.set(i1, seq2.get(i2));
                seq2.set(i2, temp);
                
                // 检查交换后是否还有重叠
                List<CoordinatePoint> newHull1 = generateConvexHullForRoute(route1, context);
                List<CoordinatePoint> newHull2 = generateConvexHullForRoute(route2, context);
                
                if (!isConvexHullOverlapping(newHull1, newHull2)) {
                    log.debug("通过交换聚集区解决了凸包重叠");
                    return true;
                }
                
                // 回滚交换
                seq2.set(i2, seq1.get(i1));
                seq1.set(i1, temp);
            }
        }
        
        return false;
    }
    
    /**
     * 尝试转移边界聚集区
     */
    private boolean tryTransferBoundaryAccumulation(RouteResult route1, RouteResult route2, AlgorithmContext context) {
        // 尝试从路线1转移到路线2
        if (tryTransferFromTo(route1, route2, context)) {
            return true;
        }
        
        // 尝试从路线2转移到路线1
        if (tryTransferFromTo(route2, route1, context)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 尝试从一条路线转移聚集区到另一条路线
     */
    private boolean tryTransferFromTo(RouteResult fromRoute, RouteResult toRoute, AlgorithmContext context) {
        List<Long> fromSeq = fromRoute.getAccumulationSequence();
        List<Long> toSeq = toRoute.getAccumulationSequence();
        
        if (fromSeq.size() <= 1) {
            return false;
        }
        
        // 尝试转移边界聚集区（首尾位置）
        int[] candidateIndices = {0, fromSeq.size() - 1};
        
        for (int index : candidateIndices) {
            Long accId = fromSeq.get(index);
            
            // 移除
            fromSeq.remove(index);
            
            // 添加到目标路线
            toSeq.add(accId);
            
            // 检查转移后是否还有重叠
            List<CoordinatePoint> newFromHull = generateConvexHullForRoute(fromRoute, context);
            List<CoordinatePoint> newToHull = generateConvexHullForRoute(toRoute, context);
            
            if (!isConvexHullOverlapping(newFromHull, newToHull)) {
                log.debug("通过转移聚集区解决了凸包重叠");
                return true;
            }
            
            // 回滚转移
            toSeq.remove(toSeq.size() - 1);
            fromSeq.add(index, accId);
        }
        
        return false;
    }
    
    /**
     * 从坐标点列表创建多边形
     */
    private Geometry createPolygonFromPoints(List<CoordinatePoint> points) {
        if (points.size() < 3) {
            return null;
        }
        
        try {
            Coordinate[] coordinates = new Coordinate[points.size()];
            for (int i = 0; i < points.size(); i++) {
                CoordinatePoint point = points.get(i);
                coordinates[i] = new Coordinate(point.getLongitude(), point.getLatitude());
            }
            
            // 确保多边形闭合
            if (!coordinates[0].equals(coordinates[coordinates.length - 1])) {
                Coordinate[] closedCoords = new Coordinate[coordinates.length + 1];
                System.arraycopy(coordinates, 0, closedCoords, 0, coordinates.length);
                closedCoords[coordinates.length] = new Coordinate(coordinates[0]);
                coordinates = closedCoords;
            }
            
            LinearRing shell = geometryFactory.createLinearRing(coordinates);
            return geometryFactory.createPolygon(shell);
            
        } catch (Exception e) {
            log.warn("创建多边形失败：{}", e.getMessage());
            return null;
        }
    }
} 