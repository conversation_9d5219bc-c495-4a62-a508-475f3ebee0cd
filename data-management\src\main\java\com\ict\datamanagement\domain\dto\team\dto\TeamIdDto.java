package com.ict.datamanagement.domain.dto.team.dto;

import com.ict.datamanagement.domain.dto.team.AddTeamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("更新班组表单")
@AllArgsConstructor
@NoArgsConstructor
public class TeamIdDto extends AddTeamRequest {
    //班组id
    @ApiModelProperty(value = "班组id",dataType = "int")
    private int teamId;
}
