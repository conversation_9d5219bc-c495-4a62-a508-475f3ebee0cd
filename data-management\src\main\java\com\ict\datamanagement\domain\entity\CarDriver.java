package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("user")
public class CarDriver {
    //驾驶人id
    private Long userId;
    //驾驶人名称
    private String userName;
    //驾驶人电话
    private String phone;
}
