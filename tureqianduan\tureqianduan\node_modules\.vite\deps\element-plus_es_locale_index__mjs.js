import {
  English
} from "./chunk-MMODTF76.js";
import "./chunk-G3PMV62Z.js";

// node_modules/element-plus/es/locale/lang/af.mjs
var af = {
  name: "af",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Bevestig",
      clear: "Maak skoon"
    },
    datepicker: {
      now: "Nou",
      today: "Vandag",
      cancel: "<PERSON><PERSON><PERSON><PERSON>",
      clear: "Maak skoon",
      confirm: "Bevestig",
      selectDate: "Kies datum",
      selectTime: "Kies tyd",
      startDate: "Begindatum",
      startTime: "Begintyd",
      endDate: "Einddatum",
      endTime: "Eindtyd",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "Jaar",
      month1: "Jan",
      month2: "Feb",
      month3: "Mrt",
      month4: "Apr",
      month5: "Mei",
      month6: "Jun",
      month7: "Jul",
      month8: "Aug",
      month9: "Sep",
      month10: "Okt",
      month11: "Nov",
      month12: "Des",
      weeks: {
        sun: "So",
        mon: "Ma",
        tue: "Di",
        wed: "Wo",
        thu: "Do",
        fri: "Vr",
        sat: "Sa"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mrt",
        apr: "Apr",
        may: "Mei",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Des"
      }
    },
    select: {
      loading: "Laai",
      noMatch: "Geen toepaslike data",
      noData: "Geen data",
      placeholder: "Kies"
    },
    mention: {
      loading: "Laai"
    },
    cascader: {
      noMatch: "Geen toepaslike data",
      loading: "Laai",
      placeholder: "Kies",
      noData: "Geen data"
    },
    pagination: {
      goto: "Gaan na",
      pagesize: "/page",
      total: "Totaal {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Boodskap",
      confirm: "Bevestig",
      cancel: "Kanselleer",
      error: "Ongeldige invoer"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "Verwyder",
      preview: "Voorskou",
      continue: "Gaan voort"
    },
    table: {
      emptyText: "Geen Data",
      confirmFilter: "Bevestig",
      resetFilter: "Herstel",
      clearFilter: "Alles",
      sumText: "Som"
    },
    tree: {
      emptyText: "Geen Data"
    },
    transfer: {
      noMatch: "Geen toepaslike data",
      noData: "Geen data",
      titles: ["Lys 1", "Lys 2"],
      filterPlaceholder: "Voer sleutelwoord in",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} gekies"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ar.mjs
var ar = {
  name: "ar",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "موافق",
      clear: "إزالة",
      defaultLabel: "إختر اللون",
      description: "اللون الحالي هو {color}. اضفط انتر لاختيار لون جديد"
    },
    datepicker: {
      now: "الآن",
      today: "اليوم",
      cancel: "إلغاء",
      clear: "إزالة",
      confirm: "موافق",
      dateTablePrompt: "استخدم مفاتيح الاسهم و اضغط انتر لاختيار اليوم المراد من الشهر",
      monthTablePrompt: "استخدم مفاتيح الاسهم واضغط انتر لاختيار الشهر",
      yearTablePrompt: "استخدم مفاتيح الاسهم واضغط انتر لاختيار السنة",
      selectDate: "إختر التاريخ",
      selectTime: "إختر الوقت",
      startDate: "تاريخ البدء",
      startTime: "وقت البدء",
      endDate: "تاريخ الإنتهاء",
      endTime: "وقت الإنتهاء",
      prevYear: "السنة السابقة",
      nextYear: "السنة التالية",
      prevMonth: "الشهر السابق",
      nextMonth: "الشهر التالي",
      year: "سنة",
      month1: "كانون الثاني",
      month2: "شباط",
      month3: "اذار",
      month4: "نيسان",
      month5: "أيار",
      month6: "حزيران",
      month7: "تموز",
      month8: "اّب",
      month9: "ايلول",
      month10: "تشرين الاول",
      month11: "تشرين الثاني",
      month12: "كانون الاول",
      week: "أسبوع",
      weeks: {
        sun: "الأحد",
        mon: "الأثنين",
        tue: "الثلاثاء",
        wed: "الأربعاء",
        thu: "الخميس",
        fri: "الجمعة",
        sat: "السبت"
      },
      months: {
        jan: "كانون الثاني",
        feb: "شباط",
        mar: "اذار",
        apr: "نيسان",
        may: "ايار",
        jun: "حزيران",
        jul: "تمور",
        aug: "اّب",
        sep: "ايلول",
        oct: "تشرين الاول",
        nov: "تشرين الثاني",
        dec: "كانون الاول"
      }
    },
    inputNumber: {
      decrease: "طرح رقم",
      increase: "زيادة رقم"
    },
    select: {
      loading: "جار التحميل",
      noMatch: "لايوجد بيانات مطابقة",
      noData: "لايوجد بيانات",
      placeholder: "إختر"
    },
    mention: {
      loading: "جار التحميل"
    },
    dropdown: {
      toggleDropdown: "تبديل القائمة"
    },
    cascader: {
      noMatch: "لايوجد بيانات مطابقة",
      loading: "جار التحميل",
      placeholder: "إختر",
      noData: "لايوجد بيانات"
    },
    pagination: {
      goto: "أذهب إلى",
      pagesize: "/صفحة",
      total: "الكل {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    dialog: {
      close: "أغلق هذا التبويب"
    },
    drawer: {
      close: "أغلق هذا التبويب"
    },
    messagebox: {
      title: "العنوان",
      confirm: "موافق",
      cancel: "إلغاء",
      error: "مدخل غير صحيح",
      close: "أغلق هذا التبويب"
    },
    upload: {
      deleteTip: "اضغط ازالة لحذف المحتوى",
      delete: "حذف",
      preview: "عرض",
      continue: "إستمرار"
    },
    table: {
      emptyText: "لايوجد بيانات",
      confirmFilter: "تأكيد",
      resetFilter: "حذف",
      clearFilter: "الكل",
      sumText: "المجموع"
    },
    tree: {
      emptyText: "لايوجد بيانات"
    },
    transfer: {
      noMatch: "لايوجد بيانات مطابقة",
      noData: "لايوجد بيانات",
      titles: ["قائمة 1", "قائمة 2"],
      filterPlaceholder: "ادخل كلمة",
      noCheckedFormat: "{total} عناصر",
      hasCheckedFormat: "{checked}/{total} مختار"
    },
    image: {
      error: "فشل"
    },
    pageHeader: {
      title: "عودة"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/az.mjs
var az = {
  name: "az",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Təsdiqlə",
      clear: "Təmizlə"
    },
    datepicker: {
      now: "İndi",
      today: "Bugün",
      cancel: "İmtina",
      clear: "Təmizlə",
      confirm: "Təsdiqlə",
      selectDate: "Tarix seç",
      selectTime: "Saat seç",
      startDate: "Başlanğıc Tarixi",
      startTime: "Başlanğıc Saatı",
      endDate: "Bitmə Tarixi",
      endTime: "Bitmə Saatı",
      prevYear: "Öncəki il",
      nextYear: "Sonrakı il",
      prevMonth: "Öncəki ay",
      nextMonth: "Sonrakı ay",
      year: "",
      month1: "Yanvar",
      month2: "Fevral",
      month3: "Mart",
      month4: "Aprel",
      month5: "May",
      month6: "İyun",
      month7: "İyul",
      month8: "Avqust",
      month9: "Sentyabr",
      month10: "Oktyabr",
      month11: "Noyabr",
      month12: "Dekabr",
      week: "həftə",
      weeks: {
        sun: "Baz",
        mon: "B.e",
        tue: "Ç.a",
        wed: "Çər",
        thu: "C.a",
        fri: "Cüm",
        sat: "Şən"
      },
      months: {
        jan: "Yan",
        feb: "Fev",
        mar: "Mar",
        apr: "Apr",
        may: "May",
        jun: "İyn",
        jul: "İyl",
        aug: "Avq",
        sep: "Sen",
        oct: "Okt",
        nov: "Noy",
        dec: "Dek"
      }
    },
    select: {
      loading: "Yüklənir",
      noMatch: "Nəticə tapılmadı",
      noData: "Məlumat yoxdur",
      placeholder: "Seç"
    },
    mention: {
      loading: "Yüklənir"
    },
    cascader: {
      noMatch: "Nəticə tapılmadı",
      loading: "Yüklənir",
      placeholder: "Seç",
      noData: "Məlumat yoxdur"
    },
    pagination: {
      goto: "Get",
      pagesize: "/səhifə",
      total: "Toplam {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mesaj",
      confirm: "Təsdiqlə",
      cancel: "İmtina",
      error: "Səhv"
    },
    upload: {
      deleteTip: "Sürüşdürmədən sonra sil",
      delete: "Sil",
      preview: "Ön izlə",
      continue: "Davam et"
    },
    table: {
      emptyText: "Məlumat yoxdur",
      confirmFilter: "Təsdiqlə",
      resetFilter: "Sıfırla",
      clearFilter: "Bütün",
      sumText: "Cəmi"
    },
    tree: {
      emptyText: "Məlumat yoxdur"
    },
    transfer: {
      noMatch: "Nəticə tapılmadı",
      noData: "Məlumat yoxdur",
      titles: ["Siyahı 1", "Siyahı 2"],
      filterPlaceholder: "Kəlimələri daxil et",
      noCheckedFormat: "{total} ədəd",
      hasCheckedFormat: "{checked}/{total} seçildi"
    },
    image: {
      error: "SƏHV"
    },
    pageHeader: {
      title: "Geri"
    },
    popconfirm: {
      confirmButtonText: "Bəli",
      cancelButtonText: "Xeyr"
    },
    empty: {
      description: "Məlumat yoxdur"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/bg.mjs
var bg = {
  name: "bg",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Изчисти"
    },
    datepicker: {
      now: "Сега",
      today: "Днес",
      cancel: "Откажи",
      clear: "Изчисти",
      confirm: "ОК",
      selectDate: "Избери дата",
      selectTime: "Избери час",
      startDate: "Начална дата",
      startTime: "Начален час",
      endDate: "Крайна дата",
      endTime: "Краен час",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "",
      month1: "Януари",
      month2: "Февруари",
      month3: "Март",
      month4: "Април",
      month5: "Май",
      month6: "Юни",
      month7: "Юли",
      month8: "Август",
      month9: "Септември",
      month10: "Октомври",
      month11: "Ноември",
      month12: "Декември",
      weeks: {
        sun: "Нед",
        mon: "Пон",
        tue: "Вто",
        wed: "Сря",
        thu: "Чет",
        fri: "Пет",
        sat: "Съб"
      },
      months: {
        jan: "Яну",
        feb: "Фев",
        mar: "Мар",
        apr: "Апр",
        may: "Май",
        jun: "Юни",
        jul: "Юли",
        aug: "Авг",
        sep: "Сеп",
        oct: "Окт",
        nov: "Ное",
        dec: "Дек"
      }
    },
    select: {
      loading: "Зареждане",
      noMatch: "Няма намерени",
      noData: "Няма данни",
      placeholder: "Избери"
    },
    mention: {
      loading: "Зареждане"
    },
    cascader: {
      noMatch: "Няма намерени",
      loading: "Зареждане",
      placeholder: "Избери",
      noData: "Няма данни"
    },
    pagination: {
      goto: "Иди на",
      pagesize: "/страница",
      total: "Общо {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Съобщение",
      confirm: "ОК",
      cancel: "Откажи",
      error: "Невалидни данни"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "Изтрий",
      preview: "Прегледай",
      continue: "Продължи"
    },
    table: {
      emptyText: "Няма данни",
      confirmFilter: "Потвърди",
      resetFilter: "Изчисти",
      clearFilter: "Всички",
      sumText: "Sum"
    },
    tree: {
      emptyText: "Няма данни"
    },
    transfer: {
      noMatch: "Няма намерени",
      noData: "Няма данни",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "Enter keyword",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/bn.mjs
var bn = {
  name: "bn",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "ঠিক আছে",
      clear: "ক্লিয়ার"
    },
    datepicker: {
      now: "এখন",
      today: "আজ",
      cancel: "বাতিল",
      clear: "ক্লিয়ার",
      confirm: "ঠিক আছে",
      selectDate: "তারিখ নির্বাচন করুন",
      selectTime: "সময় নির্বাচন করুন",
      startDate: "যে তারিখ থেকে",
      startTime: "যে সময় থেকে",
      endDate: "যে তারিখ পর্যন্ত",
      endTime: "যে সময় পর্যন্ত",
      prevYear: "পূর্ববর্তী বছর",
      nextYear: "পরবর্তী বছর",
      prevMonth: "পূর্ববর্তী মাস",
      nextMonth: "পরবর্তী মাস",
      year: "সাল",
      month1: "জানুয়ারি",
      month2: "ফেব্রুয়ারী",
      month3: "মার্চ",
      month4: "এপ্রিল",
      month5: "মে",
      month6: "জুন",
      month7: "জুলাই",
      month8: "আগষ্ট",
      month9: "সেপ্টেম্বর",
      month10: "অক্টোবর",
      month11: "নভেম্বর",
      month12: "ডিসেম্বর",
      week: "সাপ্তাহ",
      weeks: {
        sun: "রবি",
        mon: "সোম",
        tue: "মঙ্গল",
        wed: "বুধ",
        thu: "বৃহঃ",
        fri: "শুক্র",
        sat: "শনি"
      },
      months: {
        jan: "জানু",
        feb: "ফেব্রু",
        mar: "মার্চ",
        apr: "এপ্রি",
        may: "মে",
        jun: "জুন",
        jul: "জুলা",
        aug: "আগ",
        sep: "সেপ্টে",
        oct: "আক্টো",
        nov: "নভে",
        dec: "ডিসে"
      }
    },
    select: {
      loading: "লোড হচ্ছে",
      noMatch: "কোন মিল পওয়া যায়নি",
      noData: "কোন ডাটা নেই",
      placeholder: "নির্বাচন করুন"
    },
    mention: {
      loading: "লোড হচ্ছে"
    },
    cascader: {
      noMatch: "কোন মিল পওয়া যায়নি",
      loading: "লোড হচ্ছে",
      placeholder: "নির্বাচন করুন",
      noData: "কোন ডাটা নেই"
    },
    pagination: {
      goto: "যান",
      pagesize: "/পেজ",
      total: "মোট {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages",
      deprecationWarning: "অপ্রচলিত (Deprecated) ব্যাবহার পওয়া গেছে, আরও জানতে চাইলে, দয়া করে el-pagination এর ডকুমেন্টেশন দেখুন"
    },
    messagebox: {
      title: "বার্তা",
      confirm: "ঠিক আছে",
      cancel: "বাতিল",
      error: "ইনপুট ডাটা গ্রহনযোগ্য নয়"
    },
    upload: {
      deleteTip: 'অপসারণ করতে "ডিলিট" এ ক্লিক করুন',
      delete: "ডিলিট",
      preview: "প্রিভিউ",
      continue: "চালিয়ে যান"
    },
    table: {
      emptyText: "কোন ডাটা নেই",
      confirmFilter: "নিশ্চিত করুন",
      resetFilter: "রিসেট",
      clearFilter: "সব",
      sumText: "সারাংশ"
    },
    tree: {
      emptyText: "কোন ডাটা নেই"
    },
    transfer: {
      noMatch: "কোন মিল পওয়া যায়নি",
      noData: "কোন ডাটা নেই",
      titles: ["লিস্ট ১", "লিস্ট ২"],
      filterPlaceholder: "সার্চ করুন",
      noCheckedFormat: "{total} আইটেম",
      hasCheckedFormat: "{checked}/{total} টিক করা হয়েছে"
    },
    image: {
      error: "ব্যর্থ হয়েছে"
    },
    pageHeader: {
      title: "পিছনে"
    },
    popconfirm: {
      confirmButtonText: "হ্যা",
      cancelButtonText: "না"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ca.mjs
var ca = {
  name: "ca",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Confirmar",
      clear: "Netejar"
    },
    datepicker: {
      now: "Ara",
      today: "Avui",
      cancel: "Cancel·lar",
      clear: "Netejar",
      confirm: "Confirmar",
      selectDate: "Seleccionar data",
      selectTime: "Seleccionar hora",
      startDate: "Data Inici",
      startTime: "Hora Inici",
      endDate: "Data Final",
      endTime: "Hora Final",
      prevYear: "Any anterior",
      nextYear: "Pròxim Any",
      prevMonth: "Mes anterior",
      nextMonth: "Pròxim Mes",
      year: "",
      month1: "Gener",
      month2: "Febrer",
      month3: "Març",
      month4: "Abril",
      month5: "Maig",
      month6: "Juny",
      month7: "Juliol",
      month8: "Agost",
      month9: "Setembre",
      month10: "Octubre",
      month11: "Novembre",
      month12: "Desembre",
      weeks: {
        sun: "Dg",
        mon: "Dl",
        tue: "Dt",
        wed: "Dc",
        thu: "Dj",
        fri: "Dv",
        sat: "Ds"
      },
      months: {
        jan: "Gen",
        feb: "Febr",
        mar: "Març",
        apr: "Abr",
        may: "Maig",
        jun: "Juny",
        jul: "Jul",
        aug: "Ag",
        sep: "Set",
        oct: "Oct",
        nov: "Nov",
        dec: "Des"
      }
    },
    select: {
      loading: "Carregant",
      noMatch: "No hi ha dades que coincideixin",
      noData: "Sense Dades",
      placeholder: "Seleccionar"
    },
    mention: {
      loading: "Carregant"
    },
    cascader: {
      noMatch: "No hi ha dades que coincideixin",
      loading: "Carregant",
      placeholder: "Seleccionar",
      noData: "Sense Dades"
    },
    pagination: {
      goto: "Anar a",
      pagesize: "/pàgina",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      confirm: "Acceptar",
      cancel: "Cancel·lar",
      error: "Entrada invàlida"
    },
    upload: {
      deleteTip: "premi eliminar per descartar",
      delete: "Eliminar",
      preview: "Vista Prèvia",
      continue: "Continuar"
    },
    table: {
      emptyText: "Sense Dades",
      confirmFilter: "Confirmar",
      resetFilter: "Netejar",
      clearFilter: "Tot",
      sumText: "Tot"
    },
    tree: {
      emptyText: "Sense Dades"
    },
    transfer: {
      noMatch: "No hi ha dades que coincideixin",
      noData: "Sense Dades",
      titles: ["Llista 1", "Llista 2"],
      filterPlaceholder: "Introdueix la paraula clau",
      noCheckedFormat: "{total} ítems",
      hasCheckedFormat: "{checked}/{total} seleccionats"
    },
    image: {
      error: "HA FALLAT"
    },
    pageHeader: {
      title: "Tornar"
    },
    popconfirm: {
      confirmButtonText: "Sí",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/cs.mjs
var cs = {
  name: "cs",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Vymazat"
    },
    datepicker: {
      now: "Teď",
      today: "Dnes",
      cancel: "Zrušit",
      clear: "Vymazat",
      confirm: "OK",
      selectDate: "Vybrat datum",
      selectTime: "Vybrat čas",
      startDate: "Datum začátku",
      startTime: "Čas začátku",
      endDate: "Datum konce",
      endTime: "Čas konce",
      prevYear: "Předchozí rok",
      nextYear: "Příští rok",
      prevMonth: "Předchozí měsíc",
      nextMonth: "Příští měsíc",
      day: "Den",
      week: "Týden",
      month: "Měsíc",
      year: "Rok",
      month1: "Leden",
      month2: "Únor",
      month3: "Březen",
      month4: "Duben",
      month5: "Květen",
      month6: "Červen",
      month7: "Červenec",
      month8: "Srpen",
      month9: "Září",
      month10: "Říjen",
      month11: "Listopad",
      month12: "Prosinec",
      weeks: {
        sun: "Ne",
        mon: "Po",
        tue: "Út",
        wed: "St",
        thu: "Čt",
        fri: "Pá",
        sat: "So"
      },
      months: {
        jan: "Led",
        feb: "Úno",
        mar: "Bře",
        apr: "Dub",
        may: "Kvě",
        jun: "Čer",
        jul: "Čvc",
        aug: "Srp",
        sep: "Zář",
        oct: "Říj",
        nov: "Lis",
        dec: "Pro"
      }
    },
    select: {
      loading: "Načítání",
      noMatch: "Žádná shoda",
      noData: "Žádná data",
      placeholder: "Vybrat"
    },
    mention: {
      loading: "Načítání"
    },
    cascader: {
      noMatch: "Žádná shoda",
      loading: "Načítání",
      placeholder: "Vybrat",
      noData: "Žádná data"
    },
    pagination: {
      goto: "Jít na",
      pagesize: "na stranu",
      total: "Celkem {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Zpráva",
      confirm: "OK",
      cancel: "Zrušit",
      error: "Neplatný vstup"
    },
    upload: {
      deleteTip: "Stisknout pro smazání",
      delete: "Vymazat",
      preview: "Náhled",
      continue: "Pokračovat"
    },
    table: {
      emptyText: "Žádná data",
      confirmFilter: "Potvrdit",
      resetFilter: "Resetovat",
      clearFilter: "Vše",
      sumText: "Celkem"
    },
    tree: {
      emptyText: "Žádná data"
    },
    transfer: {
      noMatch: "Žádná shoda",
      noData: "Žádná data",
      titles: ["Seznam 1", "Seznam 2"],
      filterPlaceholder: "Klíčové slovo",
      noCheckedFormat: "{total} položek",
      hasCheckedFormat: "{checked}/{total} vybráno"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/da.mjs
var da = {
  name: "da",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Ryd"
    },
    datepicker: {
      now: "Nu",
      today: "I dag",
      cancel: "Annuller",
      clear: "Ryd",
      confirm: "OK",
      selectDate: "Vælg dato",
      selectTime: "Vælg tidspunkt",
      startDate: "Startdato",
      startTime: "Starttidspunkt",
      endDate: "Slutdato",
      endTime: "Sluttidspunkt",
      prevYear: "Forrige år",
      nextYear: "Næste år",
      prevMonth: "Forrige måned",
      nextMonth: "Næste måned",
      year: "",
      month1: "Januar",
      month2: "Februar",
      month3: "Marts",
      month4: "April",
      month5: "Maj",
      month6: "Juni",
      month7: "Juli",
      month8: "August",
      month9: "September",
      month10: "Oktober",
      month11: "November",
      month12: "December",
      week: "uge",
      weeks: {
        sun: "Søn",
        mon: "Man",
        tue: "Tir",
        wed: "Ons",
        thu: "Tor",
        fri: "Fre",
        sat: "Lør"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Maj",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Henter",
      noMatch: "Ingen matchende data",
      noData: "Ingen data",
      placeholder: "Vælg"
    },
    mention: {
      loading: "Henter"
    },
    cascader: {
      noMatch: "Ingen matchende data",
      loading: "Henter",
      placeholder: "Vælg",
      noData: "Ingen data"
    },
    pagination: {
      goto: "Gå til",
      pagesize: "/side",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      confirm: "OK",
      cancel: "Annuller",
      error: "Ugyldig input"
    },
    upload: {
      deleteTip: "tryk slet for at fjerne",
      delete: "Slet",
      preview: "Forhåndsvisning",
      continue: "Fortsæt"
    },
    table: {
      emptyText: "Ingen data",
      confirmFilter: "Bekræft",
      resetFilter: "Nulstil",
      clearFilter: "Alle",
      sumText: "Sum"
    },
    tree: {
      emptyText: "Ingen data"
    },
    transfer: {
      noMatch: "Ingen matchende data",
      noData: "Ingen data",
      titles: ["Liste 1", "Liste 2"],
      filterPlaceholder: "Indtast søgeord",
      noCheckedFormat: "{total} emner",
      hasCheckedFormat: "{checked}/{total} valgt"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/de.mjs
var de = {
  name: "de",
  el: {
    breadcrumb: {
      label: "Brotkrümel"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Leeren"
    },
    datepicker: {
      now: "Jetzt",
      today: "Heute",
      cancel: "Abbrechen",
      clear: "Leeren",
      confirm: "OK",
      selectDate: "Datum wählen",
      selectTime: "Uhrzeit wählen",
      startDate: "Startdatum",
      startTime: "Startzeit",
      endDate: "Enddatum",
      endTime: "Endzeit",
      prevYear: "Letztes Jahr",
      nextYear: "Nächtes Jahr",
      prevMonth: "Letzter Monat",
      nextMonth: "Nächster Monat",
      day: "Tag",
      week: "Woche",
      month: "Monat",
      year: "",
      month1: "Januar",
      month2: "Februar",
      month3: "März",
      month4: "April",
      month5: "Mai",
      month6: "Juni",
      month7: "Juli",
      month8: "August",
      month9: "September",
      month10: "Oktober",
      month11: "November",
      month12: "Dezember",
      weeks: {
        sun: "So",
        mon: "Mo",
        tue: "Di",
        wed: "Mi",
        thu: "Do",
        fri: "Fr",
        sat: "Sa"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mär",
        apr: "Apr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dez"
      }
    },
    select: {
      loading: "Lädt.",
      noMatch: "Nichts gefunden.",
      noData: "Keine Daten",
      placeholder: "Daten wählen"
    },
    mention: {
      loading: "Lädt."
    },
    cascader: {
      noMatch: "Nichts gefunden.",
      loading: "Lädt.",
      placeholder: "Daten wählen",
      noData: "Keine Daten"
    },
    pagination: {
      goto: "Gehe zu",
      pagesize: " pro Seite",
      total: "Gesamt {total}",
      pageClassifier: "",
      page: "Seite",
      prev: "Zur vorherigen Seite gehen",
      next: "Zur nächsten Seite gehen",
      currentPage: "Seite {pager}",
      prevPages: "Vorherige {pager} Seiten",
      nextPages: "Nächste {pager} Seiten"
    },
    messagebox: {
      confirm: "OK",
      cancel: "Abbrechen",
      error: "Fehler"
    },
    upload: {
      deleteTip: "Klicke löschen zum entfernen",
      delete: "Löschen",
      preview: "Vorschau",
      continue: "Fortsetzen"
    },
    table: {
      emptyText: "Keine Daten",
      confirmFilter: "Anwenden",
      resetFilter: "Zurücksetzen",
      clearFilter: "Alles ",
      sumText: "Summe"
    },
    tour: {
      next: "Weiter",
      previous: "Zurück",
      finish: "Fertig"
    },
    tree: {
      emptyText: "Keine Einträge"
    },
    transfer: {
      noMatch: "Nichts gefunden.",
      noData: "Keine Einträge",
      titles: ["Liste 1", "Liste 2"],
      filterPlaceholder: "Einträge filtern",
      noCheckedFormat: "{total} Einträge",
      hasCheckedFormat: "{checked}/{total} ausgewählt"
    },
    image: {
      error: "FEHLGESCHLAGEN"
    },
    pageHeader: {
      title: "Zurück"
    },
    popconfirm: {
      confirmButtonText: "Ja",
      cancelButtonText: "Nein"
    },
    carousel: {
      leftArrow: "Karussell-Pfeil links",
      rightArrow: "Karussell-Pfeil rechts",
      indicator: "Karussell zu Index {index} wechseln"
    }
  }
};

// node_modules/element-plus/es/locale/lang/el.mjs
var el = {
  name: "el",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Εντάξει",
      clear: "Καθαρισμός"
    },
    datepicker: {
      now: "Τώρα",
      today: "Σήμερα",
      cancel: "Ακύρωση",
      clear: "Καθαρισμός",
      confirm: "Εντάξει",
      selectDate: "Επιλέξτε ημέρα",
      selectTime: "Επιλέξτε ώρα",
      startDate: "Ημερομηνία Έναρξης",
      startTime: "Ωρα Έναρξης",
      endDate: "Ημερομηνία Λήξης",
      endTime: "Ωρα Λήξης",
      prevYear: "Προηγούμενο Έτος",
      nextYear: "Επόμενο Έτος",
      prevMonth: "Προηγούμενος Μήνας",
      nextMonth: "Επόμενος Μήνας",
      year: "Έτος",
      month1: "Ιανουάριος",
      month2: "Φεβρουάριος",
      month3: "Μάρτιος",
      month4: "Απρίλιος",
      month5: "Μάιος",
      month6: "Ιούνιος",
      month7: "Ιούλιος",
      month8: "Αύγουστος",
      month9: "Σεπτέμβριος",
      month10: "Οκτώβριος",
      month11: "Νοέμβριος",
      month12: "Δεκέμβριος",
      weeks: {
        sun: "Κυρ",
        mon: "Δευ",
        tue: "Τρι",
        wed: "Τετ",
        thu: "Πεμ",
        fri: "Παρ",
        sat: "Σαβ"
      },
      months: {
        jan: "Ιαν",
        feb: "Φεβ",
        mar: "Μαρ",
        apr: "Απρ",
        may: "Μαϊ",
        jun: "Ιουν",
        jul: "Ιουλ",
        aug: "Αυγ",
        sep: "Σεπ",
        oct: "Οκτ",
        nov: "Νοε",
        dec: "Δεκ"
      }
    },
    select: {
      loading: "Φόρτωση",
      noMatch: "Δεν βρέθηκαν αποτελέσματα",
      noData: "Χωρίς δεδομένα",
      placeholder: "Επιλογή"
    },
    mention: {
      loading: "Φόρτωση"
    },
    cascader: {
      noMatch: "Δεν βρέθηκαν αποτελέσματα",
      loading: "Φόρτωση",
      placeholder: "Επιλογή",
      noData: "Χωρίς δεδομένα"
    },
    pagination: {
      goto: "Μετάβαση σε",
      pagesize: "/σελίδα",
      total: "Σύνολο {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Μήνυμα",
      confirm: "Εντάξει",
      cancel: "Ακύρωση",
      error: "Άκυρη εισαγωγή"
    },
    upload: {
      deleteTip: "Πάτησε Διαγραφή για αφαίρεση",
      delete: "Διαγραφή",
      preview: "Προεπισκόπηση",
      continue: "Συνέχεια"
    },
    table: {
      emptyText: "Χωρίς Δεδομένα",
      confirmFilter: "Επιβεβαίωση",
      resetFilter: "Επαναφορά",
      clearFilter: "Όλα",
      sumText: "Σύνολο"
    },
    tree: {
      emptyText: "Χωρίς Δεδομένα"
    },
    transfer: {
      noMatch: "Δεν βρέθηκαν αποτελέσματα",
      noData: "Χωρίς δεδομένα",
      titles: ["Λίστα 1", "Λίστα 2"],
      filterPlaceholder: "Αναζήτηση",
      noCheckedFormat: "{total} Αντικείμενα",
      hasCheckedFormat: "{checked}/{total} επιλεγμένα"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/eo.mjs
var eo = {
  name: "eo",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Bone",
      clear: "Malplenigi"
    },
    datepicker: {
      now: "Nun",
      today: "Hodiaŭ",
      cancel: "Nuligi",
      clear: "Malplenigi",
      confirm: "Bone",
      selectDate: "Elektu daton",
      selectTime: "Elektu horon",
      startDate: "Komenca Dato",
      startTime: "Komenca Horo",
      endDate: "Fina Dato",
      endTime: "Fina Horo",
      prevYear: "Antaŭa Jaro",
      nextYear: "Sekva Jaro",
      prevMonth: "Antaŭa Monato",
      nextMonth: "Sekva Monato",
      year: "Jaro",
      month1: "Januaro",
      month2: "Februaro",
      month3: "Marto",
      month4: "Aprilo",
      month5: "Majo",
      month6: "Junio",
      month7: "Julio",
      month8: "Aŭgusto",
      month9: "Septembro",
      month10: "Oktobro",
      month11: "Novembro",
      month12: "Decembro",
      week: "Semajno",
      weeks: {
        sun: "Dim",
        mon: "Lun",
        tue: "Mar",
        wed: "Mer",
        thu: "Ĵaŭ",
        fri: "Ven",
        sat: "Sab"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Maj",
        jun: "Jun",
        jul: "Jul",
        aug: "Aŭg",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Ŝarĝante",
      noMatch: "Neniuj kongruaj datumoj",
      noData: "Neniuj datumoj",
      placeholder: "Bonvolu elekti"
    },
    mention: {
      loading: "Ŝarĝante"
    },
    cascader: {
      noMatch: "Neniuj kongruaj datumoj",
      loading: "Ŝarĝante",
      placeholder: "Bonvolu elekti",
      noData: "Neniuj datumoj"
    },
    pagination: {
      goto: "Iru al",
      pagesize: "/ paĝo",
      total: "Entute {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mesaĝo",
      confirm: "Bone",
      cancel: "Nuligi",
      error: "Nevalida Enigo!"
    },
    upload: {
      deleteTip: 'Premu "Delete" por forigi',
      delete: "Forigi",
      preview: "Antaŭrigardi",
      continue: "Daŭrigi"
    },
    table: {
      emptyText: "Neniuj datumoj",
      confirmFilter: "Konfirmi",
      resetFilter: "Restarigi",
      clearFilter: "Ĉiuj",
      sumText: "Sumo"
    },
    tree: {
      emptyText: "Neniuj datumoj"
    },
    transfer: {
      noMatch: "Neniuj kongruaj datumoj",
      noData: "Neniuj datumoj",
      titles: ["Listo 1", "Listo 2"],
      filterPlaceholder: "Enigu ŝlosilvorton",
      noCheckedFormat: "{total} elementoj",
      hasCheckedFormat: "{checked}/{total} elektitaj"
    },
    image: {
      error: "MALSUKCESIS"
    },
    pageHeader: {
      title: "Reen"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/es.mjs
var es = {
  name: "es",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Confirmar",
      clear: "Despejar"
    },
    datepicker: {
      now: "Ahora",
      today: "Hoy",
      cancel: "Cancelar",
      clear: "Despejar",
      confirm: "Confirmar",
      selectDate: "Seleccionar fecha",
      selectTime: "Seleccionar hora",
      startDate: "Fecha Incial",
      startTime: "Hora Inicial",
      endDate: "Fecha Final",
      endTime: "Hora Final",
      prevYear: "Año Anterior",
      nextYear: "Próximo Año",
      prevMonth: "Mes Anterior",
      nextMonth: "Próximo Mes",
      year: "",
      month1: "enero",
      month2: "febrero",
      month3: "marzo",
      month4: "abril",
      month5: "mayo",
      month6: "junio",
      month7: "julio",
      month8: "agosto",
      month9: "septiembre",
      month10: "octubre",
      month11: "noviembre",
      month12: "diciembre",
      weeks: {
        sun: "dom",
        mon: "lun",
        tue: "mar",
        wed: "mié",
        thu: "jue",
        fri: "vie",
        sat: "sáb"
      },
      months: {
        jan: "ene",
        feb: "feb",
        mar: "mar",
        apr: "abr",
        may: "may",
        jun: "jun",
        jul: "jul",
        aug: "ago",
        sep: "sep",
        oct: "oct",
        nov: "nov",
        dec: "dic"
      }
    },
    select: {
      loading: "Cargando",
      noMatch: "No hay datos que coincidan",
      noData: "Sin datos",
      placeholder: "Seleccionar"
    },
    mention: {
      loading: "Cargando"
    },
    cascader: {
      noMatch: "No hay datos que coincidan",
      loading: "Cargando",
      placeholder: "Seleccionar",
      noData: "Sin datos"
    },
    pagination: {
      goto: "Ir a",
      pagesize: "/página",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      confirm: "Aceptar",
      cancel: "Cancelar",
      error: "Entrada inválida"
    },
    upload: {
      deleteTip: "Pulse Eliminar para retirar",
      delete: "Eliminar",
      preview: "Vista Previa",
      continue: "Continuar"
    },
    table: {
      emptyText: "Sin Datos",
      confirmFilter: "Confirmar",
      resetFilter: "Reiniciar",
      clearFilter: "Despejar",
      sumText: "Suma"
    },
    tree: {
      emptyText: "Sin Datos"
    },
    transfer: {
      noMatch: "No hay datos que coincidan",
      noData: "Sin datos",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Ingresar palabra clave",
      noCheckedFormat: "{total} artículos",
      hasCheckedFormat: "{checked}/{total} revisados"
    },
    image: {
      error: "HA FALLADO"
    },
    pageHeader: {
      title: "Volver"
    },
    popconfirm: {
      confirmButtonText: "Si",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/et.mjs
var et = {
  name: "et",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Tühjenda"
    },
    datepicker: {
      now: "Praegu",
      today: "Täna",
      cancel: "Tühista",
      clear: "Tühjenda",
      confirm: "OK",
      selectDate: "Vali kuupäev",
      selectTime: "Vali kellaaeg",
      startDate: "Alguskuupäev",
      startTime: "Algusaeg",
      endDate: "Lõpukuupäev",
      endTime: "Lõpuaeg",
      prevYear: "Eelmine aasta",
      nextYear: "Järgmine aasta",
      prevMonth: "Eelmine kuu",
      nextMonth: "Järgmine kuu",
      year: "",
      month1: "Jaanuar",
      month2: "Veebruar",
      month3: "Märts",
      month4: "Aprill",
      month5: "Mai",
      month6: "Juuni",
      month7: "Juuli",
      month8: "August",
      month9: "September",
      month10: "Oktoober",
      month11: "November",
      month12: "Detsember",
      weeks: {
        sun: "P",
        mon: "E",
        tue: "T",
        wed: "K",
        thu: "N",
        fri: "R",
        sat: "L"
      },
      months: {
        jan: "Jaan",
        feb: "Veeb",
        mar: "Mär",
        apr: "Apr",
        may: "Mai",
        jun: "Juun",
        jul: "Juul",
        aug: "Aug",
        sep: "Sept",
        oct: "Okt",
        nov: "Nov",
        dec: "Dets"
      }
    },
    select: {
      loading: "Laadimine",
      noMatch: "Sobivad andmed puuduvad",
      noData: "Andmed puuduvad",
      placeholder: "Vali"
    },
    mention: {
      loading: "Laadimine"
    },
    cascader: {
      noMatch: "Sobivad andmed puuduvad",
      loading: "Laadimine",
      placeholder: "Vali",
      noData: "Andmed puuduvad"
    },
    pagination: {
      goto: "Mine lehele",
      pagesize: "/page",
      total: "Kokku {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Teade",
      confirm: "OK",
      cancel: "Tühista",
      error: "Vigane sisend"
    },
    upload: {
      deleteTip: 'Vajuta "Kustuta", et eemaldada',
      delete: "Kustuta",
      preview: "Eelvaate",
      continue: "Jätka"
    },
    table: {
      emptyText: "Andmed puuduvad",
      confirmFilter: "Kinnita",
      resetFilter: "Taasta",
      clearFilter: "Kõik",
      sumText: "Summa"
    },
    tree: {
      emptyText: "Andmed puuduvad"
    },
    transfer: {
      noMatch: "Sobivad andmed puuduvad",
      noData: "Andmed puuduvad",
      titles: ["Loend 1", "Loend 2"],
      filterPlaceholder: "Sisesta märksõna",
      noCheckedFormat: "{total} objekti",
      hasCheckedFormat: "{checked}/{total} valitud"
    },
    image: {
      error: "Ebaõnnestus"
    },
    pageHeader: {
      title: "Tagasi"
    },
    popconfirm: {
      confirmButtonText: "Jah",
      cancelButtonText: "Ei"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/eu.mjs
var eu = {
  name: "eu",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Ados",
      clear: "Garbitu"
    },
    datepicker: {
      now: "Orain",
      today: "Gaur",
      cancel: "Utzi",
      clear: "Garbitu",
      confirm: "Ados",
      selectDate: "Hautatu data",
      selectTime: "Hautatu ordua",
      startDate: "Hasierako data",
      startTime: "Hasierako ordua",
      endDate: "Amaierako data",
      endTime: "Amaierako ordua",
      prevYear: "Aurreko urtea",
      nextYear: "Hurrengo urtea",
      prevMonth: "Aurreko hilabetea",
      nextMonth: "Hurrengo hilabetea",
      year: "",
      month1: "Urtarrila",
      month2: "Otsaila",
      month3: "Martxoa",
      month4: "Apirila",
      month5: "Maiatza",
      month6: "Ekaina",
      month7: "Uztaila",
      month8: "Abuztua",
      month9: "Iraila",
      month10: "Urria",
      month11: "Azaroa",
      month12: "Abendua",
      weeks: {
        sun: "ig.",
        mon: "al.",
        tue: "ar.",
        wed: "az.",
        thu: "og.",
        fri: "ol.",
        sat: "lr."
      },
      months: {
        jan: "urt",
        feb: "ots",
        mar: "mar",
        apr: "api",
        may: "mai",
        jun: "eka",
        jul: "uzt",
        aug: "abu",
        sep: "ira",
        oct: "urr",
        nov: "aza",
        dec: "abe"
      }
    },
    select: {
      loading: "Kargatzen",
      noMatch: "Bat datorren daturik ez",
      noData: "Daturik ez",
      placeholder: "Hautatu"
    },
    mention: {
      loading: "Kargatzen"
    },
    cascader: {
      noMatch: "Bat datorren daturik ez",
      loading: "Kargatzen",
      placeholder: "Hautatu",
      noData: "Daturik ez"
    },
    pagination: {
      goto: "Joan",
      pagesize: "/orria",
      total: "Guztira {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mezua",
      confirm: "Ados",
      cancel: "Utzi",
      error: "Sarrera baliogabea"
    },
    upload: {
      deleteTip: "sakatu Ezabatu kentzeko",
      delete: "Ezabatu",
      preview: "Aurrebista",
      continue: "Jarraitu"
    },
    table: {
      emptyText: "Daturik ez",
      confirmFilter: "Baieztatu",
      resetFilter: "Berrezarri",
      clearFilter: "Guztia",
      sumText: "Batura"
    },
    tour: {
      next: "Hurrengoa",
      previous: "Aurrekoa",
      finish: "Bukatu"
    },
    tree: {
      emptyText: "Daturik ez"
    },
    transfer: {
      noMatch: "Bat datorren daturik ez",
      noData: "Daturik ez",
      titles: ["Zerrenda 1", "Zerrenda 2"],
      filterPlaceholder: "Sartu gako-hitza",
      noCheckedFormat: "{total} elementu",
      hasCheckedFormat: "{checked}/{total} hautatuta"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/fa.mjs
var fa = {
  name: "fa",
  el: {
    breadcrumb: {
      label: "مسیر راهنما"
    },
    colorpicker: {
      confirm: "تأیید",
      clear: "پاک کردن",
      defaultLabel: "انتخاب‌گر رنگ",
      description: "رنگ فعلی {color} است. برای انتخاب رنگ جدید، اینتر را فشار دهید.",
      alphaLabel: "مقدار آلفا را انتخاب کنید"
    },
    datepicker: {
      now: "اکنون",
      today: "امروز",
      cancel: "لغو",
      clear: "پاک کردن",
      confirm: "تأیید",
      dateTablePrompt: "از کلیدهای جهت‌دار و اینتر برای انتخاب روز ماه استفاده کنید",
      monthTablePrompt: "از کلیدهای جهت‌دار و اینتر برای انتخاب ماه استفاده کنید",
      yearTablePrompt: "از کلیدهای جهت‌دار و اینتر برای انتخاب سال استفاده کنید",
      selectedDate: "تاریخ انتخاب‌شده",
      selectDate: "انتخاب تاریخ",
      selectTime: "انتخاب زمان",
      startDate: "تاریخ شروع",
      startTime: "زمان شروع",
      endDate: "تاریخ پایان",
      endTime: "زمان پایان",
      prevYear: "سال قبل",
      nextYear: "سال بعد",
      prevMonth: "ماه قبل",
      nextMonth: "ماه بعد",
      year: "",
      month1: "ژانویه",
      month2: "فوریه",
      month3: "مارس",
      month4: "آوریل",
      month5: "مه",
      month6: "ژوئن",
      month7: "ژوئیه",
      month8: "اوت",
      month9: "سپتامبر",
      month10: "اکتبر",
      month11: "نوامبر",
      month12: "دسامبر",
      week: "هفته",
      weeks: {
        sun: "یک‌شنبه",
        mon: "دوشنبه",
        tue: "سه‌شنبه",
        wed: "چهارشنبه",
        thu: "پنج‌شنبه",
        fri: "جمعه",
        sat: "شنبه"
      },
      weeksFull: {
        sun: "یک‌شنبه",
        mon: "دوشنبه",
        tue: "سه‌شنبه",
        wed: "چهارشنبه",
        thu: "پنج‌شنبه",
        fri: "جمعه",
        sat: "شنبه"
      },
      months: {
        jan: "ژانویه",
        feb: "فوریه",
        mar: "مارچ",
        apr: "آوریل",
        may: "مه",
        jun: "ژوئن",
        jul: "ژوئیه",
        aug: "اوت",
        sep: "سپتامبر",
        oct: "اکتبر",
        nov: "نوامبر",
        dec: "دسامبر"
      }
    },
    inputNumber: {
      decrease: "کاهش عدد",
      increase: "افزایش عدد"
    },
    select: {
      loading: "در حال بارگذاری",
      noMatch: "هیچ داده منطبقی وجود ندارد",
      noData: "داده‌ای موجود نیست",
      placeholder: "انتخاب کنید"
    },
    mention: {
      loading: "در حال بارگذاری"
    },
    dropdown: {
      toggleDropdown: "باز و بسته کردن منوی کشویی"
    },
    cascader: {
      noMatch: "هیچ داده منطبقی وجود ندارد",
      loading: "در حال بارگذاری",
      placeholder: "انتخاب کنید",
      noData: "داده‌ای موجود نیست"
    },
    pagination: {
      goto: "برو به",
      pagesize: "/صفحه",
      total: "مجموع {total}",
      pageClassifier: "",
      page: "صفحه",
      prev: "برو به صفحه قبلی",
      next: "برو به صفحه بعدی",
      currentPage: "صفحه {pager}",
      prevPages: "{pager} صفحات قبلی",
      nextPages: "{pager} صفحات بعدی",
      deprecationWarning: "استفاده‌های منسوخ شناسایی شد، لطفاً به مستندات el-pagination مراجعه کنید"
    },
    dialog: {
      close: "بستن این دیالوگ"
    },
    drawer: {
      close: "بستن این دیالوگ"
    },
    messagebox: {
      title: "پیام",
      confirm: "تأیید",
      cancel: "لغو",
      error: "ورودی نامعتبر",
      close: "بستن این دیالوگ"
    },
    upload: {
      deleteTip: "برای حذف، کلید delete را فشار دهید",
      delete: "حذف",
      preview: "پیش‌نمایش",
      continue: "ادامه"
    },
    slider: {
      defaultLabel: "لغزنده بین {min} و {max}",
      defaultRangeStartLabel: "انتخاب مقدار شروع",
      defaultRangeEndLabel: "انتخاب مقدار پایان"
    },
    table: {
      emptyText: "داده‌ای موجود نیست",
      confirmFilter: "تأیید",
      resetFilter: "بازنشانی",
      clearFilter: "همه",
      sumText: "مجموع"
    },
    tour: {
      next: "بعدی",
      previous: "قبلی",
      finish: "پایان"
    },
    tree: {
      emptyText: "داده‌ای موجود نیست"
    },
    transfer: {
      noMatch: "داده‌ای مطابقت ندارد",
      noData: "داده‌ای موجود نیست",
      titles: ["فهرست ۱", "فهرست ۲"],
      filterPlaceholder: "کلمه کلیدی را وارد کنید",
      noCheckedFormat: "{total} آیتم",
      hasCheckedFormat: "{checked}/{total} انتخاب‌شده"
    },
    image: {
      error: "ناموفق"
    },
    pageHeader: {
      title: "بازگشت"
    },
    popconfirm: {
      confirmButtonText: "بله",
      cancelButtonText: "خیر"
    },
    carousel: {
      leftArrow: "پیکان به جهت چپ",
      rightArrow: "پیکان چرخان به جهت راست",
      indicator: "سوئیچ چرخان به شاخص {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/fi.mjs
var fi = {
  name: "fi",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Tyhjennä"
    },
    datepicker: {
      now: "Nyt",
      today: "Tänään",
      cancel: "Peruuta",
      clear: "Tyhjennä",
      confirm: "OK",
      selectDate: "Valitse päivä",
      selectTime: "Valitse aika",
      startDate: "Aloituspäivä",
      startTime: "Aloitusaika",
      endDate: "Lopetuspäivä",
      endTime: "Lopetusaika",
      prevYear: "Edellinen vuosi",
      nextYear: "Seuraava vuosi",
      prevMonth: "Edellinen kuukausi",
      nextMonth: "Seuraava kuukausi",
      year: "",
      month1: "tammikuu",
      month2: "helmikuu",
      month3: "maaliskuu",
      month4: "huhtikuu",
      month5: "toukokuu",
      month6: "kesäkuu",
      month7: "heinäkuu",
      month8: "elokuu",
      month9: "syyskuu",
      month10: "lokakuu",
      month11: "marraskuu",
      month12: "joulukuu",
      weeks: {
        sun: "su",
        mon: "ma",
        tue: "ti",
        wed: "ke",
        thu: "to",
        fri: "pe",
        sat: "la"
      },
      months: {
        jan: "tammi",
        feb: "helmi",
        mar: "maalis",
        apr: "huhti",
        may: "touko",
        jun: "kesä",
        jul: "heinä",
        aug: "elo",
        sep: "syys",
        oct: "loka",
        nov: "marras",
        dec: "joulu"
      }
    },
    select: {
      loading: "Lataa",
      noMatch: "Ei vastaavia tietoja",
      noData: "Ei tietoja",
      placeholder: "Valitse"
    },
    mention: {
      loading: "Lataa"
    },
    cascader: {
      noMatch: "Ei vastaavia tietoja",
      loading: "Lataa",
      placeholder: "Valitse",
      noData: "Ei tietoja"
    },
    pagination: {
      goto: "Mene",
      pagesize: "/sivu",
      total: "Yhteensä {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Viesti",
      confirm: "OK",
      cancel: "Peruuta",
      error: "Virheellinen syöte"
    },
    upload: {
      deleteTip: "Poista Delete-näppäimellä",
      delete: "Poista",
      preview: "Esikatsele",
      continue: "Jatka"
    },
    table: {
      emptyText: "Ei tietoja",
      confirmFilter: "Vahvista",
      resetFilter: "Tyhjennä",
      clearFilter: "Kaikki",
      sumText: "Summa"
    },
    tree: {
      emptyText: "Ei tietoja"
    },
    transfer: {
      noMatch: "Ei vastaavia tietoja",
      noData: "Ei tietoja",
      titles: ["Luettelo 1", "Luettelo 2"],
      filterPlaceholder: "Syötä hakusana",
      noCheckedFormat: "{total} kohdetta",
      hasCheckedFormat: "{checked}/{total} valittu"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/fr.mjs
var fr = {
  name: "fr",
  el: {
    breadcrumb: {
      label: `Fil d'Ariane`
    },
    colorpicker: {
      confirm: "OK",
      clear: "Effacer",
      defaultLabel: "color picker",
      description: "La couleur actuelle est {color}. Appuyer sur Entrée pour sélectionner une nouvelle couleur."
    },
    datepicker: {
      now: "Maintenant",
      today: "Auj.",
      cancel: "Annuler",
      clear: "Effacer",
      confirm: "OK",
      dateTablePrompt: "Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le jour du mois",
      monthTablePrompt: "Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le mois",
      yearTablePrompt: "Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner l'année",
      selectedDate: "Date sélectionnée",
      selectDate: "Choisir date",
      selectTime: "Choisir horaire",
      startDate: "Date début",
      startTime: "Horaire début",
      endDate: "Date fin",
      endTime: "Horaire fin",
      prevYear: "Année précédente",
      nextYear: "Année suivante",
      prevMonth: "Mois précédent",
      nextMonth: "Mois suivant",
      year: "",
      month1: "Janvier",
      month2: "Février",
      month3: "Mars",
      month4: "Avril",
      month5: "Mai",
      month6: "Juin",
      month7: "Juillet",
      month8: "Août",
      month9: "Septembre",
      month10: "Octobre",
      month11: "Novembre",
      month12: "Décembre",
      week: "Semaine",
      weeks: {
        sun: "Dim",
        mon: "Lun",
        tue: "Mar",
        wed: "Mer",
        thu: "Jeu",
        fri: "Ven",
        sat: "Sam"
      },
      weeksFull: {
        sun: "Dimanche",
        mon: "Lundi",
        tue: "Mardi",
        wed: "Mercredi",
        thu: "Jeudi",
        fri: "Vendredi",
        sat: "Samedi"
      },
      months: {
        jan: "Jan",
        feb: "Fév",
        mar: "Mar",
        apr: "Avr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Aoû",
        sep: "Sep",
        oct: "Oct",
        nov: "Nov",
        dec: "Déc"
      }
    },
    inputNumber: {
      decrease: "décrémenter",
      increase: "incrémenter"
    },
    select: {
      loading: "Chargement",
      noMatch: "Aucune correspondance",
      noData: "Aucune donnée",
      placeholder: "Choisir"
    },
    mention: {
      loading: "Chargement"
    },
    cascader: {
      noMatch: "Aucune correspondance",
      loading: "Chargement",
      placeholder: "Choisir",
      noData: "Aucune donnée"
    },
    pagination: {
      goto: "Aller à",
      pagesize: "/page",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Aller à la page précédente",
      next: "Aller à la page suivante",
      currentPage: "page {pager}",
      prevPages: "{pager} pages précédentes",
      nextPages: "{pager} pages suivantes",
      deprecationWarning: "Utilisations obsolètes détectées, veuillez vous référer à la documentation el-pagination pour plus de détails"
    },
    dialog: {
      close: "Fermer la boîte de dialogue"
    },
    drawer: {
      close: "Fermer la boîte de dialogue"
    },
    messagebox: {
      title: "Message",
      confirm: "Confirmer",
      cancel: "Annuler",
      error: "Erreur",
      close: "Fermer la boîte de dialogue"
    },
    upload: {
      deleteTip: "Cliquer sur supprimer pour retirer le fichier",
      delete: "Supprimer",
      preview: "Aperçu",
      continue: "Continuer"
    },
    slider: {
      defaultLabel: "curseur entre {min} et {max}",
      defaultRangeStartLabel: "choisir la valeur de départ",
      defaultRangeEndLabel: "sélectionner la valeur finale"
    },
    table: {
      emptyText: "Aucune donnée",
      confirmFilter: "Confirmer",
      resetFilter: "Réinitialiser",
      clearFilter: "Tous",
      sumText: "Somme"
    },
    tour: {
      next: "suivant",
      previous: "précédent",
      finish: "fin"
    },
    tree: {
      emptyText: "Aucune donnée"
    },
    transfer: {
      noMatch: "Aucune correspondance",
      noData: "Aucune donnée",
      titles: ["Liste 1", "Liste 2"],
      filterPlaceholder: "Entrer un mot clef",
      noCheckedFormat: "{total} elements",
      hasCheckedFormat: "{checked}/{total} coché(s)"
    },
    image: {
      error: "ECHEC"
    },
    pageHeader: {
      title: "Retour"
    },
    popconfirm: {
      confirmButtonText: "Oui",
      cancelButtonText: "Non"
    },
    carousel: {
      leftArrow: "Flèche du carrousel vers la gauche",
      rightArrow: "Flèche du carrousel vers la droite",
      indicator: "Passer au carrousel index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/he.mjs
var he = {
  name: "he",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "אישור",
      clear: "נקה"
    },
    datepicker: {
      now: "כעת",
      today: "היום",
      cancel: "בטל",
      clear: "נקה",
      confirm: "אישור",
      selectDate: "בחר תאריך",
      selectTime: "בחר זמן",
      startDate: "תאריך התחלה",
      startTime: "זמן התחלה",
      endDate: "תאריך סיום",
      endTime: "זמן סיום",
      prevYear: "שנה קודמת",
      nextYear: "שנה הבאה",
      prevMonth: "חודש קודם",
      nextMonth: "חודש הבא",
      year: "שנה",
      month1: "ינואר",
      month2: "פברואר",
      month3: "מרץ",
      month4: "אפריל",
      month5: "מאי",
      month6: "יוני",
      month7: "יולי",
      month8: "אוגוסט",
      month9: "ספטמבר",
      month10: "אוקטובר",
      month11: "נובמבר",
      month12: "דצמבר",
      week: "שבוע",
      weeks: {
        sun: "א׳",
        mon: "ב׳",
        tue: "ג׳",
        wed: "ד׳",
        thu: "ה׳",
        fri: "ו׳",
        sat: "שבת"
      },
      months: {
        jan: "ינואר",
        feb: "פברואר",
        mar: "מרץ",
        apr: "אפריל",
        may: "מאי",
        jun: "יוני",
        jul: "יולי",
        aug: "אוגוסט",
        sep: "ספטמבר",
        oct: "אוקטובר",
        nov: "נובמבר",
        dec: "דצמבר"
      }
    },
    select: {
      loading: "טוען",
      noMatch: "לא נמצאה התאמה",
      noData: "אין נתונים",
      placeholder: "שומר מקום"
    },
    mention: {
      loading: "טוען"
    },
    cascader: {
      noMatch: "לא נמצאה התאמה",
      loading: "טוען",
      placeholder: "שומר מקום",
      noData: "אין נתונים"
    },
    pagination: {
      goto: "עבור ל",
      pagesize: "/עמוד",
      total: "כולל {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "הודעה",
      confirm: "אישור",
      cancel: "בטל",
      error: "קלט לא תקין"
    },
    upload: {
      deleteTip: "לחץ כדי למחוק",
      delete: "מחק",
      preview: "תצוגה מקדימה",
      continue: "המשך"
    },
    table: {
      emptyText: "אין נתונים",
      confirmFilter: "אישור",
      resetFilter: "נקה",
      clearFilter: "הכל",
      sumText: "סך הכל"
    },
    tree: {
      emptyText: "אין נתונים"
    },
    transfer: {
      noMatch: "לא נמצאה התאמה",
      noData: "אין נתונים",
      titles: ["רשימה 1", "רשימה 2"],
      filterPlaceholder: "סנן לפי...",
      noCheckedFormat: "פריטים {total}",
      hasCheckedFormat: " נבחרו {checked}/{total}"
    },
    image: {
      error: "שגיאה"
    },
    pageHeader: {
      title: "חזרה"
    },
    popconfirm: {
      confirmButtonText: "כן",
      cancelButtonText: "לא"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/hr.mjs
var hr = {
  name: "hr",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Očisti"
    },
    datepicker: {
      now: "Sada",
      today: "Danas",
      cancel: "Otkaži",
      clear: "Očisti",
      confirm: "OK",
      selectDate: "Odaberi datum",
      selectTime: "Odaberi vrijeme",
      startDate: "Datum početka",
      startTime: "Vrijeme početka",
      endDate: "Datum završetka",
      endTime: "Vrijeme završetka",
      prevYear: "Prethodna godina",
      nextYear: "Sljedeća godina",
      prevMonth: "Prethodni mjesec",
      nextMonth: "Sljedeći mjesec",
      year: "",
      month1: "Siječanj",
      month2: "Veljača",
      month3: "Ožujak",
      month4: "Travanj",
      month5: "Svibanj",
      month6: "Lipanj",
      month7: "Srpanj",
      month8: "Kolovoz",
      month9: "Rujan",
      month10: "Listopad",
      month11: "Studeni",
      month12: "Prosinac",
      week: "tjedan",
      weeks: {
        sun: "Ned",
        mon: "Pon",
        tue: "Uto",
        wed: "Sri",
        thu: "Čet",
        fri: "Pet",
        sat: "Sub"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "May",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Oct",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Učitavanje",
      noMatch: "Nema pronađenih podataka",
      noData: "Nema podataka",
      placeholder: "Izaberi"
    },
    mention: {
      loading: "Učitavanje"
    },
    cascader: {
      noMatch: "Nema pronađenih podataka",
      loading: "Učitavanje",
      placeholder: "Izaberi",
      noData: "Nema podataka"
    },
    pagination: {
      goto: "Idi na",
      pagesize: "/stranica",
      total: "Ukupno {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Poruka",
      confirm: "OK",
      cancel: "Otkaži",
      error: "Pogrešan unos"
    },
    upload: {
      deleteTip: "pritisnite izbriši za brisanje",
      delete: "Izbriši",
      preview: "Pregled",
      continue: "Nastavak"
    },
    table: {
      emptyText: "Nema podataka",
      confirmFilter: "Potvrdi",
      resetFilter: "Resetiraj",
      clearFilter: "Sve",
      sumText: "Suma"
    },
    tree: {
      emptyText: "Nema podataka"
    },
    transfer: {
      noMatch: "Nema pronađenih podataka",
      noData: "Nema podataka",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Unesite ključnu riječ",
      noCheckedFormat: "{total} stavki",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/hu.mjs
var hu = {
  name: "hu",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Törlés"
    },
    datepicker: {
      now: "Most",
      today: "Ma",
      cancel: "Mégse",
      clear: "Törlés",
      confirm: "OK",
      selectDate: "Dátum",
      selectTime: "Időpont",
      startDate: "Dátum-tól",
      startTime: "Időpont-tól",
      endDate: "Dátum-ig",
      endTime: "Időpont-ig",
      prevYear: "Előző év",
      nextYear: "Következő év",
      prevMonth: "Előző hónap",
      nextMonth: "Következő hónap",
      year: "",
      month1: "Január",
      month2: "Február",
      month3: "Március",
      month4: "Április",
      month5: "Május",
      month6: "Június",
      month7: "Július",
      month8: "Augusztus",
      month9: "Szeptember",
      month10: "Október",
      month11: "November",
      month12: "December",
      weeks: {
        sun: "Vas",
        mon: "Hét",
        tue: "Ked",
        wed: "Sze",
        thu: "Csü",
        fri: "Pén",
        sat: "Szo"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Már",
        apr: "Ápr",
        may: "Máj",
        jun: "Jún",
        jul: "Júl",
        aug: "Aug",
        sep: "Szep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Betöltés",
      noMatch: "Nincs találat",
      noData: "Nincs adat",
      placeholder: "Válassz"
    },
    mention: {
      loading: "Betöltés"
    },
    cascader: {
      noMatch: "Nincs találat",
      loading: "Betöltés",
      placeholder: "Válassz",
      noData: "Nincs adat"
    },
    pagination: {
      goto: "Ugrás",
      pagesize: "/oldal",
      total: "Össz {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Üzenet",
      confirm: "OK",
      cancel: "Mégse",
      error: "Hibás adat"
    },
    upload: {
      deleteTip: "kattints a törléshez",
      delete: "Törlés",
      preview: "Előnézet",
      continue: "Tovább"
    },
    table: {
      emptyText: "Nincs adat",
      confirmFilter: "Megerősít",
      resetFilter: "Alaphelyet",
      clearFilter: "Mind",
      sumText: "Összeg"
    },
    tree: {
      emptyText: "Nincs adat"
    },
    transfer: {
      noMatch: "Nincs találat",
      noData: "Nincs adat",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Kulcsszó",
      noCheckedFormat: "{total} elem",
      hasCheckedFormat: "{checked}/{total} kiválasztva"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/hy-am.mjs
var hyAm = {
  name: "hy-am",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Լաւ",
      clear: "Մաքրել"
    },
    datepicker: {
      now: "Հիմա",
      today: "Այսօր",
      cancel: "Չեղարկել",
      clear: "Մաքրել",
      confirm: "Լաւ",
      selectDate: "Ընտրեք ամսաթիւը",
      selectTime: "Ընտրեք ժամանակը",
      startDate: "Սկզբ. ամսաթիւը",
      startTime: "Սկզբ. ժամանակը",
      endDate: "Վերջ. ամսաթիվը",
      endTime: "Վերջ. ժամանակը",
      prevYear: "Նախորդ տարի",
      nextYear: "Յաջորդ տարի",
      prevMonth: "Նախորդ ամիս",
      nextMonth: "Յաջորդ ամիս",
      year: "Տարի",
      month1: "Յունուար",
      month2: "Փետրուար",
      month3: "Մարտ",
      month4: "Ապրիլ",
      month5: "Մայիս",
      month6: "Յունիս",
      month7: "Յուլիս",
      month8: "Օգոստոս",
      month9: "Սեպտեմբեր",
      month10: "Յոկտեմբեր",
      month11: "Նոյեմբեր",
      month12: "Դեկտեմբեր",
      week: "Շաբաթ",
      weeks: {
        sun: "Կիր",
        mon: "Երկ",
        tue: "Եր",
        wed: "Չոր",
        thu: "Հինգ",
        fri: "Ուրբ",
        sat: "Շաբ"
      },
      months: {
        jan: "Յունվ",
        feb: "Փետ",
        mar: "Մար",
        apr: "Ապր",
        may: "Մայ",
        jun: "Յուն",
        jul: "Յուլ",
        aug: "Օգ",
        sep: "Սեպտ",
        oct: "Յոկ",
        nov: "Նոյ",
        dec: "Դեկ"
      }
    },
    select: {
      loading: "Բեռնում",
      noMatch: "Համապատասխան տուեալներ չկան",
      noData: "Տվյալներ չկան",
      placeholder: "Ընտրել"
    },
    mention: {
      loading: "Բեռնում"
    },
    cascader: {
      noMatch: "Համապատասխան տուեալներ չկան",
      loading: "Բեռնում",
      placeholder: "Ընտրել",
      noData: "Տվյալներ չկան"
    },
    pagination: {
      goto: "Անցնել",
      pagesize: " էջում",
      total: "Ընդամենը {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Հաղորդագրութիւն",
      confirm: "Լաւ",
      cancel: "Չեղարկել",
      error: "Անվաւեր տուեալների մուտք"
    },
    upload: {
      deleteTip: "Սեղմեք [Ջնջել] ջնջելու համար",
      delete: "Ջնջել",
      preview: "Նախադիտում",
      continue: "Շարունակել"
    },
    table: {
      emptyText: "Տուեալներ չկան",
      confirmFilter: "Յաստատել",
      resetFilter: "Վերագործարկել",
      clearFilter: "Բոլորը",
      sumText: "Գումարը"
    },
    tree: {
      emptyText: "Տուեալներ չկան"
    },
    transfer: {
      noMatch: "Համապատասխան տուեալներ չկան",
      noData: "Տուեալներ չկան",
      titles: ["Ցուցակ 1", "Ցուցակ 2"],
      filterPlaceholder: "Մուտքագրեք բանալի բառ",
      noCheckedFormat: "{total} միաւոր",
      hasCheckedFormat: "{checked}/{total} ընտրուած է"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/id.mjs
var id = {
  name: "id",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Pilih",
      clear: "Kosongkan"
    },
    datepicker: {
      now: "Sekarang",
      today: "Hari ini",
      cancel: "Batal",
      clear: "Kosongkan",
      confirm: "Ya",
      selectDate: "Pilih tanggal",
      selectTime: "Pilih waktu",
      startDate: "Tanggal Mulai",
      startTime: "Waktu Mulai",
      endDate: "Tanggal Selesai",
      endTime: "Waktu Selesai",
      prevYear: "Tahun Sebelumnya",
      nextYear: "Tahun Selanjutnya",
      prevMonth: "Bulan Sebelumnya",
      nextMonth: "Bulan Selanjutnya",
      year: "Tahun",
      month1: "Januari",
      month2: "Februari",
      month3: "Maret",
      month4: "April",
      month5: "Mei",
      month6: "Juni",
      month7: "Juli",
      month8: "Agustus",
      month9: "September",
      month10: "Oktober",
      month11: "November",
      month12: "Desember",
      week: "Minggu",
      weeks: {
        sun: "Min",
        mon: "Sen",
        tue: "Sel",
        wed: "Rab",
        thu: "Kam",
        fri: "Jum",
        sat: "Sab"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Mei",
        jun: "Jun",
        jul: "Jul",
        aug: "Agu",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Des"
      }
    },
    select: {
      loading: "Memuat",
      noMatch: "Tidak ada data yg cocok",
      noData: "Tidak ada data",
      placeholder: "Pilih"
    },
    mention: {
      loading: "Memuat"
    },
    cascader: {
      noMatch: "Tidak ada data yg cocok",
      loading: "Memuat",
      placeholder: "Pilih",
      noData: "Tidak ada data"
    },
    pagination: {
      goto: "Pergi ke",
      pagesize: "/halaman",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages",
      deprecationWarning: "Penggunaan yang tidak akan digunakan lagi terdeteksi, silakan lihat dokumentasi el-pagination untuk lebih jelasnya"
    },
    messagebox: {
      title: "Pesan",
      confirm: "Ya",
      cancel: "Batal",
      error: "Masukan ilegal"
    },
    upload: {
      deleteTip: "Tekan hapus untuk melanjutkan",
      delete: "Hapus",
      preview: "Pratinjau",
      continue: "Lanjutkan"
    },
    table: {
      emptyText: "Tidak ada data",
      confirmFilter: "Konfirmasi",
      resetFilter: "Atur ulang",
      clearFilter: "Semua",
      sumText: "Jumlah"
    },
    tree: {
      emptyText: "Tidak ada data"
    },
    transfer: {
      noMatch: "Tidak ada data yg cocok",
      noData: "Tidak ada data",
      titles: ["Daftar 1", "Daftar 2"],
      filterPlaceholder: "Masukan kata kunci",
      noCheckedFormat: "{total} item",
      hasCheckedFormat: "{checked}/{total} terpilih"
    },
    image: {
      error: "GAGAL"
    },
    pageHeader: {
      title: "Kembali"
    },
    popconfirm: {
      confirmButtonText: "Ya",
      cancelButtonText: "Tidak"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/it.mjs
var it = {
  name: "it",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Pulisci"
    },
    datepicker: {
      now: "Ora",
      today: "Oggi",
      cancel: "Cancella",
      clear: "Pulisci",
      confirm: "OK",
      selectDate: "Seleziona data",
      selectTime: "Seleziona ora",
      startDate: "Data inizio",
      startTime: "Ora inizio",
      endDate: "Data fine",
      endTime: "Ora fine",
      prevYear: "Anno precedente",
      nextYear: "Anno successivo",
      prevMonth: "Mese precedente",
      nextMonth: "Mese successivo",
      year: "",
      month1: "Gennaio",
      month2: "Febbraio",
      month3: "Marzo",
      month4: "Aprile",
      month5: "Maggio",
      month6: "Giugno",
      month7: "Luglio",
      month8: "Agosto",
      month9: "Settembre",
      month10: "Ottobre",
      month11: "Novembre",
      month12: "Dicembre",
      weeks: {
        sun: "Dom",
        mon: "Lun",
        tue: "Mar",
        wed: "Mer",
        thu: "Gio",
        fri: "Ven",
        sat: "Sab"
      },
      months: {
        jan: "Gen",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Mag",
        jun: "Giu",
        jul: "Lug",
        aug: "Ago",
        sep: "Set",
        oct: "Ott",
        nov: "Nov",
        dec: "Dic"
      }
    },
    select: {
      loading: "Caricamento",
      noMatch: "Nessuna corrispondenza",
      noData: "Nessun dato",
      placeholder: "Seleziona"
    },
    mention: {
      loading: "Caricamento"
    },
    cascader: {
      noMatch: "Nessuna corrispondenza",
      loading: "Caricamento",
      placeholder: "Seleziona",
      noData: "Nessun dato"
    },
    pagination: {
      goto: "Vai a",
      pagesize: "/page",
      total: "Totale {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      confirm: "OK",
      cancel: "Cancella",
      error: "Input non valido"
    },
    upload: {
      deleteTip: "Premi cancella per rimuovere",
      delete: "Cancella",
      preview: "Anteprima",
      continue: "Continua"
    },
    table: {
      emptyText: "Nessun dato",
      confirmFilter: "Conferma",
      resetFilter: "Reset",
      clearFilter: "Tutti",
      sumText: "Somma"
    },
    tree: {
      emptyText: "Nessun dato"
    },
    transfer: {
      noMatch: "Nessuna corrispondenza",
      noData: "Nessun dato",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Inserisci filtro",
      noCheckedFormat: "{total} elementi",
      hasCheckedFormat: "{checked}/{total} selezionati"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ja.mjs
var ja = {
  name: "ja",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "クリア"
    },
    datepicker: {
      now: "現在",
      today: "今日",
      cancel: "キャンセル",
      clear: "クリア",
      confirm: "OK",
      selectDate: "日付を選択",
      selectTime: "時間を選択",
      startDate: "開始日",
      startTime: "開始時間",
      endDate: "終了日",
      endTime: "終了時間",
      prevYear: "前年",
      nextYear: "翌年",
      prevMonth: "前月",
      nextMonth: "翌月",
      year: "年",
      month1: "1月",
      month2: "2月",
      month3: "3月",
      month4: "4月",
      month5: "5月",
      month6: "6月",
      month7: "7月",
      month8: "8月",
      month9: "9月",
      month10: "10月",
      month11: "11月",
      month12: "12月",
      weeks: {
        sun: "日",
        mon: "月",
        tue: "火",
        wed: "水",
        thu: "木",
        fri: "金",
        sat: "土"
      },
      months: {
        jan: "1月",
        feb: "2月",
        mar: "3月",
        apr: "4月",
        may: "5月",
        jun: "6月",
        jul: "7月",
        aug: "8月",
        sep: "9月",
        oct: "10月",
        nov: "11月",
        dec: "12月"
      }
    },
    select: {
      loading: "ロード中",
      noMatch: "データなし",
      noData: "データなし",
      placeholder: "選択してください"
    },
    mention: {
      loading: "ロード中"
    },
    cascader: {
      noMatch: "データなし",
      loading: "ロード中",
      placeholder: "選択してください",
      noData: "データなし"
    },
    pagination: {
      goto: "",
      pagesize: "件/ページ",
      total: "総計 {total} 件",
      pageClassifier: "ページ目へ",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "メッセージ",
      confirm: "OK",
      cancel: "キャンセル",
      error: "正しくない入力"
    },
    upload: {
      deleteTip: "Delキーを押して削除する",
      delete: "削除する",
      preview: "プレビュー",
      continue: "続行する"
    },
    table: {
      emptyText: "データなし",
      confirmFilter: "確認",
      resetFilter: "初期化",
      clearFilter: "すべて",
      sumText: "合計"
    },
    tour: {
      next: "次へ",
      previous: "前へ",
      finish: "ツアー終了"
    },
    tree: {
      emptyText: "データなし"
    },
    transfer: {
      noMatch: "データなし",
      noData: "データなし",
      titles: ["リスト 1", "リスト 2"],
      filterPlaceholder: "キーワードを入力",
      noCheckedFormat: "総計 {total} 件",
      hasCheckedFormat: "{checked}/{total} を選択した"
    },
    image: {
      error: "失敗"
    },
    pageHeader: {
      title: "戻る"
    },
    popconfirm: {
      confirmButtonText: "はい",
      cancelButtonText: "いいえ"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/kk.mjs
var kk = {
  name: "kk",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Қабылдау",
      clear: "Тазалау"
    },
    datepicker: {
      now: "Қазір",
      today: "Бүгін",
      cancel: "Болдырмау",
      clear: "Тазалау",
      confirm: "Қабылдау",
      selectDate: "Күнді таңдаңыз",
      selectTime: "Сағатты таңдаңыз",
      startDate: "Басталу күні",
      startTime: "Басталу сағаты",
      endDate: "Аяқталу күні",
      endTime: "Аяқталу сағаты",
      prevYear: "Алдыңғы жыл",
      nextYear: "Келесі жыл",
      prevMonth: "Алдыңғы ай",
      nextMonth: "Келесі ай",
      year: "Жыл",
      month1: "Қаңтар",
      month2: "Ақпан",
      month3: "Наурыз",
      month4: "Сәуір",
      month5: "Мамыр",
      month6: "Маусым",
      month7: "Шілде",
      month8: "Тамыз",
      month9: "Қыркүйек",
      month10: "Қазан",
      month11: "Қараша",
      month12: "Желтоқсан",
      week: "Апта",
      weeks: {
        sun: "Жек",
        mon: "Дүй",
        tue: "Сей",
        wed: "Сәр",
        thu: "Бей",
        fri: "Жұм",
        sat: "Сен"
      },
      months: {
        jan: "Қаң",
        feb: "Ақп",
        mar: "Нау",
        apr: "Сәу",
        may: "Мам",
        jun: "Мау",
        jul: "Шіл",
        aug: "Там",
        sep: "Қыр",
        oct: "Қаз",
        nov: "Қар",
        dec: "Жел"
      }
    },
    select: {
      loading: "Жүктелуде",
      noMatch: "Сәйкес деректер жоқ",
      noData: "Деректер жоқ",
      placeholder: "Таңдаңыз"
    },
    mention: {
      loading: "Жүктелуде"
    },
    cascader: {
      noMatch: "Сәйкес деректер жоқ",
      loading: "Жүктелуде",
      placeholder: "Таңдаңыз",
      noData: "Деректер жоқ"
    },
    pagination: {
      goto: "Бару",
      pagesize: "/page",
      total: "Барлығы {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Хабар",
      confirm: "Қабылдау",
      cancel: "Болдырмау",
      error: "Жарамсыз енгізулер"
    },
    upload: {
      deleteTip: "Өшіруді басып өшіріңіз",
      delete: "Өшіру",
      preview: "Алдын ала қарау",
      continue: "Жалғастыру"
    },
    table: {
      emptyText: "Деректер жоқ",
      confirmFilter: "Қабылдау",
      resetFilter: "Қалпына келтіру",
      clearFilter: "Барлығы",
      sumText: "Сомасы"
    },
    tree: {
      emptyText: "Деректер жоқ"
    },
    transfer: {
      noMatch: "Сәйкес деректер жоқ",
      noData: "Деректер жоқ",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "Кілт сөзді енгізіңіз",
      noCheckedFormat: "{total} элэмэнт",
      hasCheckedFormat: "{checked}/{total} құсбелгісі қойылды"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/km.mjs
var km = {
  name: "km",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "យល់ព្រម",
      clear: "លុប"
    },
    datepicker: {
      now: "ឥឡូវ​នេះ",
      today: "ថ្ងៃនេះ",
      cancel: "បោះបង់",
      clear: "លុប",
      confirm: "យល់ព្រម",
      selectDate: "ជ្រើសរើសថ្ងៃ",
      selectTime: "ជ្រើសរើសម៉ោង",
      startDate: "ថ្ងៃចាប់ផ្តើម",
      startTime: "ម៉ោងចាប់ផ្តើម",
      endDate: "ថ្ងៃបញ្ចប់",
      endTime: "ម៉ោងបញ្ចប់",
      prevYear: "ឆ្នាំមុន",
      nextYear: "ឆ្នាំក្រោយ",
      prevMonth: "ខែមុន",
      nextMonth: "ខែក្រោយ",
      year: "ឆ្នាំ",
      month1: "មករា",
      month2: "កុម្ភៈ",
      month3: "មីនា",
      month4: "មេសា",
      month5: "ឧសភា",
      month6: "មិថុនា",
      month7: "កក្កដា",
      month8: "សីហា",
      month9: "កញ្ញា",
      month10: "តុលា",
      month11: "វិច្ឆិកា",
      month12: "ធ្នូ",
      weeks: {
        sun: "អាទិត្យ",
        mon: "ចន្ទ",
        tue: "អង្គារ",
        wed: "ពុធ",
        thu: "ព្រហ",
        fri: "សុក្រ",
        sat: "សៅរ៍"
      },
      months: {
        jan: "មករា",
        feb: "កុម្ភៈ",
        mar: "មីនា",
        apr: "មេសា",
        may: "ឧសភា",
        jun: "មិថុនា",
        jul: "កក្កដា",
        aug: "សីហា",
        sep: "កញ្ញា",
        oct: "តុលា",
        nov: "វិច្ឆិកា",
        dec: "ធ្នូ"
      }
    },
    select: {
      loading: "កំពុងផ្ទុក",
      noMatch: "គ្មានទិន្នន័យដូច",
      noData: "គ្មានទិន្នន័យ",
      placeholder: "ជ្រើសរើស"
    },
    mention: {
      loading: "កំពុងផ្ទុក"
    },
    cascader: {
      noMatch: "គ្មានទិន្នន័យដូច",
      loading: "កំពុងផ្ទុក",
      placeholder: "ជ្រើសរើស",
      noData: "គ្មានទិន្នន័យ"
    },
    pagination: {
      goto: "ទៅកាន់",
      pagesize: "/ទំព័រ",
      total: "សរុប {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "សារ",
      confirm: "យល់ព្រម",
      cancel: "បោះបង់",
      error: "ការបញ្ចូលមិនត្រូវបានអនុញ្ញាត"
    },
    upload: {
      deleteTip: "ចុចលុបដើម្បីដកចេញ",
      delete: "លុប",
      preview: "មើល",
      continue: "បន្ត"
    },
    table: {
      emptyText: "គ្មានទិន្នន័យ",
      confirmFilter: "យល់ព្រម",
      resetFilter: "កំណត់ឡើងវិញ",
      clearFilter: "ទាំងអស់",
      sumText: "បូក"
    },
    tree: {
      emptyText: "គ្មានទិន្នន័យ"
    },
    transfer: {
      noMatch: "គ្មានទិន្នន័យដូច",
      noData: "គ្មានទិន្នន័យ",
      titles: ["បញ្ជី ១", "បញ្ជី ២"],
      filterPlaceholder: "បញ្ចូលពាក្យ",
      noCheckedFormat: "{total} ធាតុ",
      hasCheckedFormat: "{checked}/{total} បានជ្រើសយក"
    },
    image: {
      error: "មិនបានជោគជ័យ"
    },
    pageHeader: {
      title: "ត្រលប់ក្រោយ"
    },
    popconfirm: {
      confirmButtonText: "យល់ព្រម",
      cancelButtonText: "មិនព្រម"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ko.mjs
var ko = {
  name: "ko",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "확인",
      clear: "초기화",
      defaultLabel: "색상 선택기",
      description: "현재 색상은 {color}입니다. Enter 키를 눌러 새 색상을 선택합니다."
    },
    datepicker: {
      now: "지금",
      today: "오늘",
      cancel: "취소",
      clear: "초기화",
      confirm: "확인",
      dateTablePrompt: "화살표 키를 사용하고 Enter를 눌러 날짜를 선택하십시오.",
      monthTablePrompt: "화살표 키를 사용하고 Enter를 눌러 월을 선택합니다.",
      yearTablePrompt: "화살표 키를 사용하고 Enter 키를 눌러 연도를 선택합니다.",
      selectDate: "날짜 선택",
      selectTime: "시간 선택",
      startDate: "시작 날짜",
      startTime: "시작 시간",
      endDate: "종료 날짜",
      endTime: "종료 시간",
      prevYear: "지난해",
      nextYear: "다음해",
      prevMonth: "지난달",
      nextMonth: "다음달",
      year: "년",
      month1: "1월",
      month2: "2월",
      month3: "3월",
      month4: "4월",
      month5: "5월",
      month6: "6월",
      month7: "7월",
      month8: "8월",
      month9: "9월",
      month10: "10월",
      month11: "11월",
      month12: "12월",
      weeks: {
        sun: "일",
        mon: "월",
        tue: "화",
        wed: "수",
        thu: "목",
        fri: "금",
        sat: "토"
      },
      months: {
        jan: "1월",
        feb: "2월",
        mar: "3월",
        apr: "4월",
        may: "5월",
        jun: "6월",
        jul: "7월",
        aug: "8월",
        sep: "9월",
        oct: "10월",
        nov: "11월",
        dec: "12월"
      }
    },
    inputNumber: {
      decrease: "값 증가",
      increase: "값 감소"
    },
    select: {
      loading: "불러오는 중",
      noMatch: "검색된 데이터 없음",
      noData: "데이터 없음",
      placeholder: "선택"
    },
    mention: {
      loading: "불러오는 중"
    },
    dropdown: {
      toggleDropdown: "드롭다운 전환"
    },
    cascader: {
      noMatch: "검색된 데이터 없음",
      loading: "불러오는 중",
      placeholder: "선택",
      noData: "데이터 없음"
    },
    pagination: {
      goto: "",
      pagesize: "건/페이지",
      total: "총 {total} 건",
      pageClassifier: "페이지로",
      page: "페이지",
      prev: "이전 페이지로 이동",
      next: "다음 페이지로 이동",
      currentPage: "페이지 {pager}",
      prevPages: "이전 {pager} 페이지",
      nextPages: "다음 {pager} 페이지",
      deprecationWarning: "더 이상 사용되지 않는 동작이 감지되었습니다. 자세한 내용은 el-pagination 문서를 참조하세요."
    },
    dialog: {
      close: "대화 상자 닫기"
    },
    drawer: {
      close: "대화 상자 닫기"
    },
    messagebox: {
      title: "메시지",
      confirm: "확인",
      cancel: "취소",
      error: "올바르지 않은 입력",
      close: "대화 상자 닫기"
    },
    upload: {
      deleteTip: "Delete 키를 눌러 삭제",
      delete: "삭제",
      preview: "미리보기",
      continue: "계속하기"
    },
    slider: {
      defaultLabel: "{min}과 {max} 사이의 슬라이더",
      defaultRangeStartLabel: "시작 값 선택",
      defaultRangeEndLabel: "종료 값 선택"
    },
    table: {
      emptyText: "데이터 없음",
      confirmFilter: "확인",
      resetFilter: "초기화",
      clearFilter: "전체",
      sumText: "합계"
    },
    tour: {
      next: "다음",
      previous: "이전",
      finish: "종료"
    },
    tree: {
      emptyText: "데이터 없음"
    },
    transfer: {
      noMatch: "검색된 데이터 없음",
      noData: "데이터 없음",
      titles: ["리스트 1", "리스트 2"],
      filterPlaceholder: "검색어를 입력하세요",
      noCheckedFormat: "총 {total} 건",
      hasCheckedFormat: "{checked}/{total} 선택됨"
    },
    image: {
      error: "불러오기 실패"
    },
    pageHeader: {
      title: "뒤로"
    },
    popconfirm: {
      confirmButtonText: "예",
      cancelButtonText: "아니오"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ku.mjs
var ku = {
  name: "ku",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Temam",
      clear: "Paqij bike"
    },
    datepicker: {
      now: "Niha",
      today: "Îro",
      cancel: "Betal bike",
      clear: "Paqij bike",
      confirm: "Temam",
      selectDate: "Dîrokê bibijêre",
      selectTime: "Demê bibijêre",
      startDate: "Dîroka Destpêkê",
      startTime: "Dema Destpêkê",
      endDate: "Dîroka Dawî",
      endTime: "Dema Dawî",
      prevYear: "Sala Pêş",
      nextYear: "Sala Paş",
      prevMonth: "Meha Pêş",
      nextMonth: "Meha Paş",
      year: "Sal",
      month1: "Rêbendan",
      month2: "Reşemeh",
      month3: "Adar",
      month4: "Avrêl",
      month5: "Gulan",
      month6: "Pûşber",
      month7: "Tîrmeh",
      month8: "Gilavêj",
      month9: "Rezber",
      month10: "Kewçêr",
      month11: "Sarmawaz",
      month12: "Berfanbar",
      weeks: {
        sun: "Yek",
        mon: "Duş",
        tue: "Sêş",
        wed: "Çar",
        thu: "Pên",
        fri: "În",
        sat: "Şem"
      },
      months: {
        jan: "Rêb",
        feb: "Reş",
        mar: "Ada",
        apr: "Avr",
        may: "Gul",
        jun: "Pûş",
        jul: "Tîr",
        aug: "Gil",
        sep: "Rez",
        oct: "Kew",
        nov: "Sar",
        dec: "Ber"
      }
    },
    select: {
      loading: "Bardibe",
      noMatch: "Li hembere ve agahî tune",
      noData: "Agahî tune",
      placeholder: "Bibijêre"
    },
    mention: {
      loading: "Bardibe"
    },
    cascader: {
      noMatch: "Li hembere ve agahî tune",
      loading: "Bardibe",
      placeholder: "Bibijêre",
      noData: "Agahî tune"
    },
    pagination: {
      goto: "Biçe",
      pagesize: "/rupel",
      total: "Tevahî {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Peyam",
      confirm: "Temam",
      cancel: "Betal bike",
      error: "Beyana çewt"
    },
    upload: {
      deleteTip: 'ji bo rake pêl "delete" bike',
      delete: "Rake",
      preview: "Pêşdîtin",
      continue: "Berdewam"
    },
    table: {
      emptyText: "Agahî tune",
      confirmFilter: "Piştrast bike",
      resetFilter: "Jê bibe",
      clearFilter: "Hemû",
      sumText: "Kom"
    },
    tree: {
      emptyText: "Agahî tune"
    },
    transfer: {
      noMatch: "Li hembere ve agahî tune",
      noData: "Agahî tune",
      titles: ["Lîste 1", "Lîste 2"],
      filterPlaceholder: "Binivîse",
      noCheckedFormat: "{total} lib",
      hasCheckedFormat: "{checked}/{total} bijartin"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ky.mjs
var ky = {
  name: "ky",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Мурунку",
      clear: "ачык"
    },
    datepicker: {
      now: "азыр",
      today: "бүгүн",
      cancel: "жокко чыгарылды",
      clear: "ачык",
      confirm: "белгилөө",
      selectDate: "дата",
      selectTime: "тандоо убактысы",
      startDate: "Башталган датасы",
      startTime: "Start убакыт",
      endDate: "Бүткөн датасы",
      endTime: "End убакыт",
      prevYear: "өткөн жылы",
      nextYear: "бир жылдан кийин",
      prevMonth: "Өткөн айда",
      nextMonth: "Кийинки ай",
      year: "жыл",
      month1: "биринчи ай",
      month2: "Экинчи айда",
      month3: "Үчүнчү айда",
      month4: "Төртүнчү айда",
      month5: "бешинчи айда",
      month6: "Алгачкы алты ай",
      month7: "жетинчи айда",
      month8: "сегизинчи ай",
      month9: "Алгачкы тогуз ай",
      month10: "онунчу айда",
      month11: "он биринчи ай",
      month12: "он экинчи айда",
      weeks: {
        sun: "жети жума",
        mon: "дүйшөмбү",
        tue: "шейшемби",
        wed: "шаршемби",
        thu: "бейшемби",
        fri: "жума",
        sat: "ишемби"
      },
      months: {
        jan: "биринчи ай",
        feb: "Экинчи айда",
        mar: "Үчүнчү айда",
        apr: "Төртүнчү айда",
        may: "бешинчи айда",
        jun: "Алгачкы алты ай",
        jul: "жетинчи айда",
        aug: "сегизинчи ай",
        sep: "Алгачкы тогуз ай",
        oct: "онунчу айда",
        nov: "он биринчи ай",
        dec: "он экинчи айда"
      }
    },
    select: {
      loading: "Жүктөлүүдө",
      noMatch: "Дал келген маалыматтар",
      noData: "маалымат жок",
      placeholder: "тандоо"
    },
    mention: {
      loading: "Жүктөлүүдө"
    },
    cascader: {
      noMatch: "Дал келген маалыматтар",
      loading: "Жүктөлүүдө",
      placeholder: "тандоо",
      noData: "маалымат жок"
    },
    pagination: {
      goto: "Мурунку",
      pagesize: "бир",
      total: "бүтүндөй {total} сан ",
      pageClassifier: "бет",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "тез",
      confirm: "белгилөө",
      cancel: "жокко чыгарылды",
      error: "Маалыматтарды киргизүү мыйзамдуу эмес!"
    },
    upload: {
      deleteTip: "Жок кылуу баскычын басуу жок",
      delete: "жок кылуу",
      preview: "ЖМКнын картинки",
      continue: "жүктөп бер"
    },
    table: {
      emptyText: "маалымат жок",
      confirmFilter: "чыпка",
      resetFilter: "кайра орнотуу",
      clearFilter: "бүткөн",
      sumText: "Бардыгы болуп"
    },
    tree: {
      emptyText: "маалымат жок"
    },
    transfer: {
      noMatch: "Дал келген маалыматтар",
      noData: "маалымат жок",
      titles: ["1 тизмеси", "2 тизмеси"],
      filterPlaceholder: "Сураныч, издөө кирет",
      noCheckedFormat: "бүтүндөй {total} сан",
      hasCheckedFormat: "Тандалган {checked}/{total} сан"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/lt.mjs
var lt = {
  name: "lt",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Valyti"
    },
    datepicker: {
      now: "Dabar",
      today: "Šiandien",
      cancel: "Atšaukti",
      clear: "Valyti",
      confirm: "OK",
      selectDate: "Pasirink datą",
      selectTime: "Pasirink laiką",
      startDate: "Data nuo",
      startTime: "Laikas nuo",
      endDate: "Data iki",
      endTime: "Laikas iki",
      prevYear: "Metai atgal",
      nextYear: "Metai į priekį",
      prevMonth: "Mėn. atgal",
      nextMonth: "Mėn. į priekį",
      year: "",
      month1: "Sausis",
      month2: "Vasaris",
      month3: "Kovas",
      month4: "Balandis",
      month5: "Gegužė",
      month6: "Birželis",
      month7: "Liepa",
      month8: "Rugpjūtis",
      month9: "Rugsėjis",
      month10: "Spalis",
      month11: "Lapkritis",
      month12: "Gruodis",
      weeks: {
        sun: "S.",
        mon: "Pr.",
        tue: "A.",
        wed: "T.",
        thu: "K.",
        fri: "Pn.",
        sat: "Š."
      },
      months: {
        jan: "Sau",
        feb: "Vas",
        mar: "Kov",
        apr: "Bal",
        may: "Geg",
        jun: "Bir",
        jul: "Lie",
        aug: "Rugp",
        sep: "Rugs",
        oct: "Spa",
        nov: "Lap",
        dec: "Gruo"
      }
    },
    select: {
      loading: "Kraunasi",
      noMatch: "Duomenų nerasta",
      noData: "Nėra duomenų",
      placeholder: "Pasirink"
    },
    mention: {
      loading: "Kraunasi"
    },
    cascader: {
      noMatch: "Duomenų nerasta",
      loading: "Kraunasi",
      placeholder: "Pasirink",
      noData: "Nėra duomenų"
    },
    pagination: {
      goto: "Eiti į",
      pagesize: "/p",
      total: "Viso {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Žinutė",
      confirm: "OK",
      cancel: "Atšaukti",
      error: "Klaida įvestuose duomenyse"
    },
    upload: {
      deleteTip: 'spauskite "Trinti" norėdami pašalinti',
      delete: "Trinti",
      preview: "Peržiūrėti",
      continue: "Toliau"
    },
    table: {
      emptyText: "Duomenų nerasta",
      confirmFilter: "Patvirtinti",
      resetFilter: "Atstatyti",
      clearFilter: "Išvalyti",
      sumText: "Suma"
    },
    tour: {
      next: "Kitas",
      previous: "Ankstesnis",
      finish: "Baigti"
    },
    tree: {
      emptyText: "Nėra duomenų"
    },
    transfer: {
      noMatch: "Duomenų nerasta",
      noData: "Nėra duomenų",
      titles: ["Sąrašas 1", "Sąrašas 2"],
      filterPlaceholder: "Įvesk raktažodį",
      noCheckedFormat: "Viso: {total}",
      hasCheckedFormat: "Pažymėta {checked} iš {total}"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/lv.mjs
var lv = {
  name: "lv",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Labi",
      clear: "Notīrīt"
    },
    datepicker: {
      now: "Tagad",
      today: "Šodien",
      cancel: "Atcelt",
      clear: "Notīrīt",
      confirm: "Labi",
      selectDate: "Izvēlēties datumu",
      selectTime: "Izvēlēties laiku",
      startDate: "Sākuma datums",
      startTime: "Sākuma laiks",
      endDate: "Beigu datums",
      endTime: "Beigu laiks",
      prevYear: "Iepriekšējais gads",
      nextYear: "Nākamais gads",
      prevMonth: "Iepriekšējais mēnesis",
      nextMonth: "Nākamais mēnesis",
      year: "",
      month1: "Janvāris",
      month2: "Februāris",
      month3: "Marts",
      month4: "Aprīlis",
      month5: "Maijs",
      month6: "Jūnijs",
      month7: "Jūlijs",
      month8: "Augusts",
      month9: "Septembris",
      month10: "Oktobris",
      month11: "Novembris",
      month12: "Decembris",
      weeks: {
        sun: "Sv",
        mon: "Pr",
        tue: "Ot",
        wed: "Tr",
        thu: "Ce",
        fri: "Pk",
        sat: "Se"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Mai",
        jun: "Jūn",
        jul: "Jūl",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Ielādē",
      noMatch: "Nav atbilstošu datu",
      noData: "Nav datu",
      placeholder: "Izvēlēties"
    },
    mention: {
      loading: "Ielādē"
    },
    cascader: {
      noMatch: "Nav atbilstošu datu",
      loading: "Ielādē",
      placeholder: "Izvēlēties",
      noData: "Nav datu"
    },
    pagination: {
      goto: "Iet uz",
      pagesize: "/lapa",
      total: "Kopā {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Paziņojums",
      confirm: "Labi",
      cancel: "Atcelt",
      error: "Nederīga ievade"
    },
    upload: {
      deleteTip: "Nospiediet dzēst lai izņemtu",
      delete: "Dzēst",
      preview: "Priekšskatīt",
      continue: "Turpināt"
    },
    table: {
      emptyText: "Nav datu",
      confirmFilter: "Apstiprināt",
      resetFilter: "Atiestatīt",
      clearFilter: "Visi",
      sumText: "Summa"
    },
    tree: {
      emptyText: "Nav datu"
    },
    transfer: {
      noMatch: "Nav atbilstošu datu",
      noData: "Nav datu",
      titles: ["Saraksts 1", "Saraksts 2"],
      filterPlaceholder: "Ievadīt atslēgvārdu",
      noCheckedFormat: "{total} vienības",
      hasCheckedFormat: "{checked}/{total} atzīmēti"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/mn.mjs
var mn = {
  name: "mn",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Тийм",
      clear: "Цэвэрлэх"
    },
    datepicker: {
      now: "Одоо",
      today: "Өнөөдөр",
      cancel: "Болих",
      clear: "Цэвэрлэх",
      confirm: "Тийм",
      selectDate: "Огноог сонго",
      selectTime: "Цагийг сонго",
      startDate: "Эхлэх огноо",
      startTime: "Эхлэх цаг",
      endDate: "Дуусах огноо",
      endTime: "Дуусах цаг",
      prevYear: "Өмнөх жил",
      nextYear: "Дараа жил",
      prevMonth: "Өмнөх сар",
      nextMonth: "Дараа сар",
      year: "он",
      month1: "1 сар",
      month2: "2 сар",
      month3: "3 сар",
      month4: "4 сар",
      month5: "5 сар",
      month6: "6 сар",
      month7: "7 сар",
      month8: "8 сар",
      month9: "9 сар",
      month10: "10 сар",
      month11: "11 сар",
      month12: "12 сар",
      week: "Долоо хоног",
      weeks: {
        sun: "Ням",
        mon: "Дав",
        tue: "Мяг",
        wed: "Лха",
        thu: "Пүр",
        fri: "Баа",
        sat: "Бям"
      },
      months: {
        jan: "1 сар",
        feb: "2 сар",
        mar: "3 сар",
        apr: "4 сар",
        may: "5 сар",
        jun: "6 сар",
        jul: "7 сар",
        aug: "8 сар",
        sep: "9 сар",
        oct: "10 сар",
        nov: "11 сар",
        dec: "12 сар"
      }
    },
    select: {
      loading: "Ачаалж байна",
      noMatch: "Тохирох өгөгдөл байхгүй",
      noData: "Өгөгдөл байхгүй",
      placeholder: "Сонгох"
    },
    mention: {
      loading: "Ачаалж байна"
    },
    cascader: {
      noMatch: "Тохирох өгөгдөл байхгүй",
      loading: "Ачаалж байна",
      placeholder: "Сонгох",
      noData: "Өгөгдөл байхгүй"
    },
    pagination: {
      goto: "Очих",
      pagesize: "/хуудас",
      total: "Нийт {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Зурвас",
      confirm: "Тийм",
      cancel: "Болих",
      error: "Буруу утга"
    },
    upload: {
      deleteTip: "Устгахын дарж арилга",
      delete: "Устгах",
      preview: "Өмнөх",
      continue: "Үргэлжлүүлэх"
    },
    table: {
      emptyText: "Өгөгдөл байхгүй",
      confirmFilter: "Зөвшөөрөх",
      resetFilter: "Цэвэрлэх",
      clearFilter: "Бүгд",
      sumText: "Нийт"
    },
    tree: {
      emptyText: "Өгөгдөл байхгүй"
    },
    transfer: {
      noMatch: "Тохирох өгөгдөл байхгүй",
      noData: "Өгөгдөл байхгүй",
      titles: ["Жагсаалт 1", "Жагсаалт 2"],
      filterPlaceholder: "Утга оруул",
      noCheckedFormat: "{total} өгөгдөл",
      hasCheckedFormat: "{checked}/{total} сонгосон"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/my.mjs
var my = {
  name: "my",
  el: {
    breadcrumb: {
      label: "ဘရတ်ဒ်ခရမ်"
    },
    colorpicker: {
      confirm: "အိုကေ",
      clear: "ရှင်းမယ်",
      defaultLabel: "အရောင်ရွေးချယ်ပါ",
      description: "လက်ရှိအရောင်မှာ {color} ဖြစ်ပါသည်။ တခြားအရောင်ကိုရွေးချယ်လိုပါက enter ကိုနှိပ်ပါ။",
      alphaLabel: "alpha တန်ဖိုးကို ရွေးချယ်ပါ"
    },
    datepicker: {
      now: "ယခု",
      today: "ယနေ့",
      cancel: "ပယ်ဖျက်",
      clear: "ရှင်းမယ်",
      confirm: "အိုကေ",
      dateTablePrompt: "arrow keys နှင့် enter ကိုအသုံးပြုပြီး နေ့ရက် ကိုရွေးချယ်ပါ",
      monthTablePrompt: "arrow keys နှင့် enter ကိုအသုံးပြုပြီး လ ကိုရွေးချယ်ပါ",
      yearTablePrompt: "arrow keys နှင့် enter ကိုအသုံးပြုပြီး နှစ် ကိုရွေးချယ်ပါ",
      selectedDate: "ရွေးချယ်ထားသော ရက်စွဲ",
      selectDate: "ရက်စွဲကို ရွေးချယ်ပါ",
      selectTime: "အချိန်ကို ရွေးချယ်ပါ",
      startDate: "စတင်မည့်ရက်စွဲ",
      startTime: "စတင်မည့်အချိန်",
      endDate: "ကုန်ဆုံးမည့်ရက်စွဲ",
      endTime: "ကုန်ဆုံးမည့်အချိန်",
      prevYear: "ယခင်နှစ်",
      nextYear: "နောက်နှစ်",
      prevMonth: "ယခင်လ",
      nextMonth: "နောက်လ",
      year: "",
      month1: "ဇန်နဝါရီ",
      month2: "ဖေဖော်ဝါရီ",
      month3: "မတ်",
      month4: "ဧပြီ",
      month5: "မေ",
      month6: "ဇွန်",
      month7: "ဇူလိုင်",
      month8: "သြဂုတ်",
      month9: "စက်တင်ဘာ",
      month10: "အောက်တိုဘာ",
      month11: "နိုဝင်ဘာ",
      month12: "ဒီဇင်ဘာ",
      week: "ရက်သတ္တပတ်",
      weeks: {
        sun: "နွေ",
        mon: "လာ",
        tue: "ဂါ",
        wed: "ဟူး",
        thu: "ကြာ",
        fri: "သော",
        sat: "နေ"
      },
      weeksFull: {
        sun: "တနင်္ဂနွေ",
        mon: "တနင်္လာ",
        tue: "အင်္ဂါ",
        wed: "ဗုဒ္ဓဟူး",
        thu: "ကြာသပတေး",
        fri: "သောကြာ",
        sat: "စနေ"
      },
      months: {
        jan: "ဇန်",
        feb: "ဖေ",
        mar: "မတ်",
        apr: "ပြီ",
        may: "မေ",
        jun: "ဇွန်",
        jul: "လိုင်",
        aug: "ဩ",
        sep: "စက်",
        oct: "အောက်",
        nov: "နို",
        dec: "ဒီ"
      }
    },
    inputNumber: {
      decrease: "အရေအတွက်လျှော့ချ",
      increase: "အရေအတွက်တိုး"
    },
    select: {
      loading: "ဝန်တင်နေသည်",
      noMatch: "ကိုက်ညီသောဒေတာမရှိပါ",
      noData: "ဒေတာမရှိပါ",
      placeholder: "ရွေးပါ"
    },
    mention: {
      loading: "ဝန်တင်နေသည်"
    },
    dropdown: {
      toggleDropdown: "Dropdown စာရင်း ဖွင့်/ပိတ်"
    },
    cascader: {
      noMatch: "ကိုက်ညီသောဒေတာမရှိပါ",
      loading: "ဝန်တင်နေသည်",
      placeholder: "ရွေးပါ",
      noData: "ဒေတာမရှိပါ"
    },
    pagination: {
      goto: "သွားမယ်",
      pagesize: "/စာမျက်နှာ",
      total: "စုစုပေါင်း {total}",
      pageClassifier: "",
      page: "စာမျက်နှာ",
      prev: "ရှေ့စာမျက်နှာသို့",
      next: "နောက်စာမျက်နှာသို့",
      currentPage: "စာမျက်နှာ {pager}",
      prevPages: "ရှေ့စာမျက်နှာ {pager} သို့",
      nextPages: "နောက်စာမျက်နှာ {pager} သို့",
      deprecationWarning: "ကန့်ကွက်ထားသော အသုံးပြုမှုများကို တွေ့ရှိပါသည်။ အသေးစိတ်အချက်အလက်များကို el-pagination ရဲ့စာရွက်စာတမ်းတွင် ဖတ်ရှုပါ။"
    },
    dialog: {
      close: "ဤဒိုင်ယာလော့ဂ်ကို ပိတ်ပါ"
    },
    drawer: {
      close: "ဤဒိုင်ယာလော့ဂ်ကို ပိတ်ပါ"
    },
    messagebox: {
      title: "မက်ဆေ့ချ်",
      confirm: "အိုကေ",
      cancel: "ပယ်ဖျက်",
      error: "တရားမဝင်ထည့်သွင်းမှု",
      close: "ဤဒိုင်ယာလော့ဂ်ကို ပိတ်ပါ"
    },
    upload: {
      deleteTip: "ဖယ်ရှားရန် ဖျက်မည် ကိုနှိပ်ပါ",
      delete: "ဖျက်မည်",
      preview: "အစမ်းကြည့်မည်",
      continue: "ရှေ့ဆက်မည်"
    },
    slider: {
      defaultLabel: "{min} နှင့် {max} ကြားရှိ ဆလိုက်ဒါ",
      defaultRangeStartLabel: "စတင်တန်ဖိုးကို ရွေးပါ",
      defaultRangeEndLabel: "အဆုံးသတ်တန်ဖိုးကို ရွေးပါ"
    },
    table: {
      emptyText: "ဒေတာမရှိပါ",
      confirmFilter: "အတည်ပြုမည်",
      resetFilter: "ပြန်လည်သတ်မှတ်မည်",
      clearFilter: "အားလုံး",
      sumText: "ပေါင်းလဒ်"
    },
    tour: {
      next: "နောက်သို့",
      previous: "ရှေ့သို့",
      finish: "ပြီးပြီ"
    },
    tree: {
      emptyText: "ဒေတာမရှိပါ"
    },
    transfer: {
      noMatch: "ကိုက်ညီသောဒေတာမရှိပါ",
      noData: "ဒေတာမရှိပါ",
      titles: ["စာရင်း ၁", "စာရင်း ၂"],
      filterPlaceholder: "သော့ချက်စကားလုံးကို ရိုက်ထည့်ပါ",
      noCheckedFormat: "{total} ခု",
      hasCheckedFormat: "{checked}/{total} ရွေးပြီး"
    },
    image: {
      error: "မအောင်မြင်ပါ"
    },
    pageHeader: {
      title: "ပြန်မည်"
    },
    popconfirm: {
      confirmButtonText: "ဟုတ်ကဲ့",
      cancelButtonText: "မလုပ်တော့ဘူး"
    },
    carousel: {
      leftArrow: "ကာရူဆယ် မြား ဘယ်ဘက်",
      rightArrow: "ကာရူဆယ် မြား ညာဘက်",
      indicator: "ကာရူဆယ် အညွှန်း {index} သို့ ပြောင်းရန်"
    }
  }
};

// node_modules/element-plus/es/locale/lang/nb-no.mjs
var nbNo = {
  name: "nb-no",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Tøm"
    },
    datepicker: {
      now: "Nå",
      today: "I dag",
      cancel: "Avbryt",
      clear: "Tøm",
      confirm: "OK",
      selectDate: "Velg dato",
      selectTime: "Velg tidspunkt",
      startDate: "Startdato",
      startTime: "Starttidspunkt",
      endDate: "Sluttdato",
      endTime: "Sluttidspunkt",
      prevYear: "I fjor",
      nextYear: "Neste år",
      prevMonth: "Forrige Måned",
      nextMonth: "Neste Måned",
      year: "",
      month1: "Januar",
      month2: "Februar",
      month3: "Mars",
      month4: "April",
      month5: "Mai",
      month6: "Juni",
      month7: "Juli",
      month8: "August",
      month9: "September",
      month10: "Oktober",
      month11: "November",
      month12: "Desember",
      week: "uke",
      weeks: {
        sun: "Søn",
        mon: "Man",
        tue: "Tir",
        wed: "Ons",
        thu: "Tor",
        fri: "Fre",
        sat: "Lør"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Des"
      }
    },
    select: {
      loading: "Laster",
      noMatch: "Ingen samsvarende resulater",
      noData: "Ingen resulater",
      placeholder: "Velg"
    },
    mention: {
      loading: "Laster"
    },
    cascader: {
      noMatch: "Ingen samsvarende resultater",
      loading: "Laster",
      placeholder: "Velg",
      noData: "Ingen resultater"
    },
    pagination: {
      goto: "Gå til",
      pagesize: "/side",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      confirm: "OK",
      cancel: "Avbryt",
      error: "Ugyldig input"
    },
    upload: {
      deleteTip: "trykk på x for å slette",
      delete: "Slett",
      preview: "Forhåndsvisning",
      continue: "Fortsett"
    },
    table: {
      emptyText: "Ingen Data",
      confirmFilter: "Bekreft",
      resetFilter: "Tilbakestill",
      clearFilter: "Alle",
      sumText: "Sum"
    },
    tree: {
      emptyText: "Ingen Data"
    },
    transfer: {
      noMatch: "Ingen samsvarende data",
      noData: "Ingen data",
      titles: ["Liste 1", "Liste 2"],
      filterPlaceholder: "Skriv inn nøkkelord",
      noCheckedFormat: "{total} gjenstander",
      hasCheckedFormat: "{checked}/{total} valgt"
    },
    image: {
      error: "FEILET"
    },
    pageHeader: {
      title: "Tilbake"
    },
    popconfirm: {
      confirmButtonText: "Ja",
      cancelButtonText: "Nei"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/nl.mjs
var nl = {
  name: "nl",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Bevestig",
      clear: "Wissen"
    },
    datepicker: {
      now: "Nu",
      today: "Vandaag",
      cancel: "Annuleren",
      clear: "Legen",
      confirm: "Bevestig",
      selectDate: "Selecteer datum",
      selectTime: "Selecteer tijd",
      startDate: "Startdatum",
      startTime: "Starttijd",
      endDate: "Einddatum",
      endTime: "Eindtijd",
      prevYear: "Vorig jaar",
      nextYear: "Volgend jaar",
      prevMonth: "Vorige maand",
      nextMonth: "Volgende maand",
      year: "",
      month1: "januari",
      month2: "februari",
      month3: "maart",
      month4: "april",
      month5: "mei",
      month6: "juni",
      month7: "juli",
      month8: "augustus",
      month9: "september",
      month10: "oktober",
      month11: "november",
      month12: "december",
      weeks: {
        sun: "Zo",
        mon: "Ma",
        tue: "Di",
        wed: "Wo",
        thu: "Do",
        fri: "Vr",
        sat: "Za"
      },
      months: {
        jan: "jan",
        feb: "feb",
        mar: "maa",
        apr: "apr",
        may: "mei",
        jun: "jun",
        jul: "jul",
        aug: "aug",
        sep: "sep",
        oct: "okt",
        nov: "nov",
        dec: "dec"
      }
    },
    select: {
      loading: "Laden",
      noMatch: "Geen overeenkomende resultaten",
      noData: "Geen data",
      placeholder: "Selecteer"
    },
    mention: {
      loading: "Laden"
    },
    cascader: {
      noMatch: "Geen overeenkomende resultaten",
      loading: "Laden",
      placeholder: "Selecteer",
      noData: "Geen data"
    },
    pagination: {
      goto: "Ga naar",
      pagesize: "/pagina",
      total: "Totaal {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Bericht",
      confirm: "Bevestig",
      cancel: "Annuleren",
      error: "Ongeldige invoer"
    },
    upload: {
      deleteTip: "Kies verwijder om te wissen",
      delete: "Verwijder",
      preview: "Voorbeeld",
      continue: "Doorgaan"
    },
    table: {
      emptyText: "Geen data",
      confirmFilter: "Bevestigen",
      resetFilter: "Reset",
      clearFilter: "Alles",
      sumText: "Som"
    },
    tree: {
      emptyText: "Geen data"
    },
    transfer: {
      noMatch: "Geen overeenkomende resultaten",
      noData: "Geen data",
      titles: ["Lijst 1", "Lijst 2"],
      filterPlaceholder: "Geef zoekwoerd",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} geselecteerd"
    },
    image: {
      error: "MISLUKT"
    },
    pageHeader: {
      title: "Terug"
    },
    popconfirm: {
      confirmButtonText: "Ja",
      cancelButtonText: "Nee"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/pa.mjs
var pa = {
  name: "pa",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "تایید",
      clear: "پاکول"
    },
    datepicker: {
      now: "اوس",
      today: "نن",
      cancel: "ردول",
      clear: "پاکول",
      confirm: "تایید",
      selectDate: "نیټه وټاکئ",
      selectTime: "وخت وټاکئ",
      startDate: "پیل نیټه",
      startTime: "د پيل وخت",
      endDate: "د پای نیټه",
      endTime: "د پای وخت",
      prevYear: "تیر کال",
      nextYear: "راتلونکی کال",
      prevMonth: "تیره میاشت",
      nextMonth: "راتلونکې میاشت",
      year: "کال",
      month1: "جنوري",
      month2: "فبروري",
      month3: "مارچ",
      month4: "اپریل",
      month5: "می",
      month6: "جون",
      month7: "جولای",
      month8: "اګست",
      month9: "سپتمبر",
      month10: "اکتوبر",
      month11: "نومبر",
      month12: "دسمبر",
      weeks: {
        sun: "یکشنبه",
        mon: "دوشنبه",
        tue: "سه​ شنبه",
        wed: "چهارشنبه",
        thu: "پنج​شنبه",
        fri: "جمعه",
        sat: "شنبه"
      },
      months: {
        jan: "جنوري",
        feb: "فبروري",
        mar: "مارچ",
        apr: "اپریل",
        may: "می",
        jun: "جون",
        jul: "جولای",
        aug: "اګست",
        sep: "سپتمبر",
        oct: "اکتوبر",
        nov: "نومبر",
        dec: "دسمبر"
      }
    },
    select: {
      loading: "بار کول",
      noMatch: "هیڅه ونه موندل شول",
      noData: "هیڅ معلومات نشته",
      placeholder: "ځای لرونکی"
    },
    mention: {
      loading: "بار کول"
    },
    cascader: {
      noMatch: "هیڅه ونه موندل شول",
      loading: "بار کول",
      placeholder: "ځای لرونکی",
      noData: "هیڅ معلومات نشته"
    },
    pagination: {
      goto: "ورتګ",
      pagesize: "/د پاڼې اندازه",
      total: "مجموعه {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "عنوان",
      confirm: "تایید",
      cancel: "لغوه کول",
      error: "تيروتنه"
    },
    upload: {
      deleteTip: "د حذف کولو لپاره پاکه تڼۍ فشار کړئ",
      delete: "ړنګول",
      preview: "مخکتنه",
      continue: "ادامه"
    },
    table: {
      emptyText: "هیڅ معلومات ونه موندل شول",
      confirmFilter: "تایید",
      resetFilter: "پاکول",
      clearFilter: "ټول",
      sumText: "مجموعه"
    },
    tree: {
      emptyText: "هیڅ معلومات ونه موندل شول"
    },
    transfer: {
      noMatch: "هیڅه ونه موندل شول",
      noData: "هیڅ معلومات نشته",
      titles: ["لیسټ 1", "لیسټ 2"],
      filterPlaceholder: "د متن کلیمې دننه کړئ",
      noCheckedFormat: "{total} توکي",
      hasCheckedFormat: "{checked} توکي از {total} توکي ټاکل شوی دي"
    },
    image: {
      error: "د انځور پورته کولو کې ستونزه"
    },
    pageHeader: {
      title: "بیرته راتګ"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/pl.mjs
var pl = {
  name: "pl",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Wyczyść"
    },
    datepicker: {
      now: "Teraz",
      today: "Dzisiaj",
      cancel: "Anuluj",
      clear: "Wyczyść",
      confirm: "OK",
      selectDate: "Wybierz datę",
      selectTime: "Wybierz godzinę",
      startDate: "Data początkowa",
      startTime: "Godzina początkowa",
      endDate: "Data końcowa",
      endTime: "Czas końcowa",
      prevYear: "Poprzedni rok",
      nextYear: "Następny rok",
      prevMonth: "Poprzedni miesiąc",
      nextMonth: "Następny miesiąc",
      year: "rok",
      month1: "styczeń",
      month2: "luty",
      month3: "marzec",
      month4: "kwiecień",
      month5: "maj",
      month6: "czerwiec",
      month7: "lipiec",
      month8: "sierpień",
      month9: "wrzesień",
      month10: "październik",
      month11: "listopad",
      month12: "grudzień",
      week: "tydzień",
      weeks: {
        sun: "niedz.",
        mon: "pon.",
        tue: "wt.",
        wed: "śr.",
        thu: "czw.",
        fri: "pt.",
        sat: "sob."
      },
      months: {
        jan: "STY",
        feb: "LUT",
        mar: "MAR",
        apr: "KWI",
        may: "MAJ",
        jun: "CZE",
        jul: "LIP",
        aug: "SIE",
        sep: "WRZ",
        oct: "PAŹ",
        nov: "LIS",
        dec: "GRU"
      }
    },
    select: {
      loading: "Ładowanie",
      noMatch: "Brak dopasowań",
      noData: "Brak danych",
      placeholder: "Wybierz"
    },
    mention: {
      loading: "Ładowanie"
    },
    cascader: {
      noMatch: "Brak dopasowań",
      loading: "Ładowanie",
      placeholder: "Wybierz",
      noData: "Brak danych"
    },
    pagination: {
      goto: "Idź do",
      pagesize: "/stronę",
      total: "Wszystkich {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Wiadomość",
      confirm: "OK",
      cancel: "Anuluj",
      error: "Wiadomość zawiera niedozwolone znaki"
    },
    upload: {
      deleteTip: "kliknij kasuj aby usunąć",
      delete: "Kasuj",
      preview: "Podgląd",
      continue: "Kontynuuj"
    },
    table: {
      emptyText: "Brak danych",
      confirmFilter: "Potwierdź",
      resetFilter: "Resetuj",
      clearFilter: "Wszystko",
      sumText: "Razem"
    },
    tour: {
      next: "Dalej",
      previous: "Wróć",
      finish: "Zakończ"
    },
    tree: {
      emptyText: "Brak danych"
    },
    transfer: {
      noMatch: "Brak dopasowań",
      noData: "Brak danych",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Wpisz szukaną frazę",
      noCheckedFormat: "razem: {total}",
      hasCheckedFormat: "wybranych: {checked}/{total}"
    },
    image: {
      error: "BŁĄD"
    },
    pageHeader: {
      title: "Wstecz"
    },
    popconfirm: {
      confirmButtonText: "Tak",
      cancelButtonText: "Nie"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/pt-br.mjs
var ptBr = {
  name: "pt-br",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Confirmar",
      clear: "Limpar"
    },
    datepicker: {
      now: "Agora",
      today: "Hoje",
      cancel: "Cancelar",
      clear: "Limpar",
      confirm: "Confirmar",
      selectDate: "Selecione a data",
      selectTime: "Selecione a hora",
      startDate: "Data inicial",
      startTime: "Hora inicial",
      endDate: "Data final",
      endTime: "Hora final",
      prevYear: "Ano anterior",
      nextYear: "Próximo ano",
      prevMonth: "Mês anterior",
      nextMonth: "Próximo mês",
      year: "",
      month1: "Janeiro",
      month2: "Fevereiro",
      month3: "Março",
      month4: "Abril",
      month5: "Maio",
      month6: "Junho",
      month7: "Julho",
      month8: "Agosto",
      month9: "Setembro",
      month10: "Outubro",
      month11: "Novembro",
      month12: "Dezembro",
      weeks: {
        sun: "Dom",
        mon: "Seg",
        tue: "Ter",
        wed: "Qua",
        thu: "Qui",
        fri: "Sex",
        sat: "Sab"
      },
      months: {
        jan: "Jan",
        feb: "Fev",
        mar: "Mar",
        apr: "Abr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Ago",
        sep: "Set",
        oct: "Out",
        nov: "Nov",
        dec: "Dez"
      }
    },
    select: {
      loading: "Carregando",
      noMatch: "Sem resultados",
      noData: "Sem dados",
      placeholder: "Selecione"
    },
    mention: {
      loading: "Carregando"
    },
    cascader: {
      noMatch: "Sem resultados",
      loading: "Carregando",
      placeholder: "Selecione",
      noData: "Sem dados"
    },
    pagination: {
      goto: "Ir para",
      pagesize: "/página",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mensagem",
      confirm: "Confirmar",
      cancel: "Cancelar",
      error: "Erro!"
    },
    upload: {
      deleteTip: "aperte delete para apagar",
      delete: "Apagar",
      preview: "Pré-visualizar",
      continue: "Continuar"
    },
    table: {
      emptyText: "Sem dados",
      confirmFilter: "Confirmar",
      resetFilter: "Limpar",
      clearFilter: "Todos",
      sumText: "Total"
    },
    tour: {
      next: "Próximo",
      previous: "Anterior",
      finish: "Finalizar"
    },
    tree: {
      emptyText: "Sem dados"
    },
    transfer: {
      noMatch: "Sem resultados",
      noData: "Sem dados",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Digite uma palavra-chave",
      noCheckedFormat: "{total} itens",
      hasCheckedFormat: "{checked}/{total} selecionados"
    },
    image: {
      error: "Erro ao carregar imagem"
    },
    pageHeader: {
      title: "Voltar"
    },
    popconfirm: {
      confirmButtonText: "Sim",
      cancelButtonText: "Não"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/pt.mjs
var pt = {
  name: "pt",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Confirmar",
      clear: "Limpar"
    },
    datepicker: {
      now: "Agora",
      today: "Hoje",
      cancel: "Cancelar",
      clear: "Limpar",
      confirm: "Confirmar",
      selectDate: "Selecione a data",
      selectTime: "Selecione a hora",
      startDate: "Data de inicio",
      startTime: "Hora de inicio",
      endDate: "Data de fim",
      endTime: "Hora de fim",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "",
      month1: "Janeiro",
      month2: "Fevereiro",
      month3: "Março",
      month4: "Abril",
      month5: "Maio",
      month6: "Junho",
      month7: "Julho",
      month8: "Agosto",
      month9: "Setembro",
      month10: "Outubro",
      month11: "Novembro",
      month12: "Dezembro",
      weeks: {
        sun: "Dom",
        mon: "Seg",
        tue: "Ter",
        wed: "Qua",
        thu: "Qui",
        fri: "Sex",
        sat: "Sab"
      },
      months: {
        jan: "Jan",
        feb: "Fev",
        mar: "Mar",
        apr: "Abr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Ago",
        sep: "Set",
        oct: "Out",
        nov: "Nov",
        dec: "Dez"
      }
    },
    select: {
      loading: "A carregar",
      noMatch: "Sem correspondência",
      noData: "Sem dados",
      placeholder: "Selecione"
    },
    mention: {
      loading: "A carregar"
    },
    cascader: {
      noMatch: "Sem correspondência",
      loading: "A carregar",
      placeholder: "Selecione",
      noData: "Sem dados"
    },
    pagination: {
      goto: "Ir para",
      pagesize: "/pagina",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mensagem",
      confirm: "Confirmar",
      cancel: "Cancelar",
      error: "Erro!"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "Apagar",
      preview: "Previsualizar",
      continue: "Continuar"
    },
    table: {
      emptyText: "Sem dados",
      confirmFilter: "Confirmar",
      resetFilter: "Limpar",
      clearFilter: "Todos",
      sumText: "Sum"
    },
    tree: {
      emptyText: "Sem dados"
    },
    transfer: {
      noMatch: "Sem correspondência",
      noData: "Sem dados",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "Enter keyword",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ro.mjs
var ro = {
  name: "ro",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Șterge"
    },
    datepicker: {
      now: "Acum",
      today: "Azi",
      cancel: "Anulează",
      clear: "Șterge",
      confirm: "OK",
      selectDate: "Selectează data",
      selectTime: "Selectează ora",
      startDate: "Data de început",
      startTime: "Ora de început",
      endDate: "Data de sfârșit",
      endTime: "Ora de sfârșit",
      prevYear: "Anul trecut",
      nextYear: "Anul următor",
      prevMonth: "Luna trecută",
      nextMonth: "Luna următoare",
      year: "",
      month1: "Ianuarie",
      month2: "Februarie",
      month3: "Martie",
      month4: "Aprilie",
      month5: "Mai",
      month6: "Iunie",
      month7: "Iulie",
      month8: "August",
      month9: "Septembrie",
      month10: "Octombrie",
      month11: "Noiembrie",
      month12: "Decembrie",
      weeks: {
        sun: "Du",
        mon: "Lu",
        tue: "Ma",
        wed: "Mi",
        thu: "Jo",
        fri: "Vi",
        sat: "Sâ"
      },
      months: {
        jan: "Ian",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Mai",
        jun: "Iun",
        jul: "Iul",
        aug: "Aug",
        sep: "Sep",
        oct: "Oct",
        nov: "Noi",
        dec: "Dec"
      }
    },
    select: {
      loading: "Se încarcă",
      noMatch: "Nu există date potrivite",
      noData: "Nu există date",
      placeholder: "Selectează"
    },
    mention: {
      loading: "Se încarcă"
    },
    cascader: {
      noMatch: "Nu există date potrivite",
      loading: "Se încarcă",
      placeholder: "Selectează",
      noData: "Nu există date"
    },
    pagination: {
      goto: "Go to",
      pagesize: "/pagina",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mesaj",
      confirm: "OK",
      cancel: "Anulează",
      error: "Date introduse eronate"
    },
    upload: {
      deleteTip: "apăsați pe ștergeți pentru a elimina",
      delete: "șterge",
      preview: "previzualizare",
      continue: "continuă"
    },
    table: {
      emptyText: "Nu există date",
      confirmFilter: "Confirmă",
      resetFilter: "Resetează",
      clearFilter: "Tot",
      sumText: "Suma"
    },
    tree: {
      emptyText: "Nu există date"
    },
    transfer: {
      noMatch: "Nu există date potrivite",
      noData: "Nu există date",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Introduceți cuvântul cheie",
      noCheckedFormat: "{total} elemente",
      hasCheckedFormat: "{checked}/{total} verificate"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ru.mjs
var ru = {
  name: "ru",
  el: {
    breadcrumb: {
      label: "Хлебные крошки"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Очистить"
    },
    datepicker: {
      now: "Сейчас",
      today: "Сегодня",
      cancel: "Отмена",
      clear: "Очистить",
      confirm: "OK",
      selectDate: "Выбрать дату",
      selectTime: "Выбрать время",
      startDate: "Дата начала",
      startTime: "Время начала",
      endDate: "Дата окончания",
      endTime: "Время окончания",
      prevYear: "Предыдущий год",
      nextYear: "Следующий год",
      prevMonth: "Предыдущий месяц",
      nextMonth: "Следующий месяц",
      year: "",
      month1: "Январь",
      month2: "Февраль",
      month3: "Март",
      month4: "Апрель",
      month5: "Май",
      month6: "Июнь",
      month7: "Июль",
      month8: "Август",
      month9: "Сентябрь",
      month10: "Октябрь",
      month11: "Ноябрь",
      month12: "Декабрь",
      week: "неделя",
      weeks: {
        sun: "Вс",
        mon: "Пн",
        tue: "Вт",
        wed: "Ср",
        thu: "Чт",
        fri: "Пт",
        sat: "Сб"
      },
      months: {
        jan: "Янв",
        feb: "Фев",
        mar: "Мар",
        apr: "Апр",
        may: "Май",
        jun: "Июн",
        jul: "Июл",
        aug: "Авг",
        sep: "Сен",
        oct: "Окт",
        nov: "Ноя",
        dec: "Дек"
      }
    },
    select: {
      loading: "Загрузка",
      noMatch: "Совпадений не найдено",
      noData: "Нет данных",
      placeholder: "Выбрать"
    },
    mention: {
      loading: "Загрузка"
    },
    cascader: {
      noMatch: "Совпадений не найдено",
      loading: "Загрузка",
      placeholder: "Выбрать",
      noData: "Нет данных"
    },
    pagination: {
      goto: "Перейти",
      pagesize: " на странице",
      total: "Всего {total}",
      pageClassifier: "",
      page: "Страница",
      prev: "Перейти на предыдущую страницу",
      next: "Перейти на следующую страницу",
      currentPage: "страница {pager}",
      prevPages: "Предыдущие {pager} страниц",
      nextPages: "Следующие {pager} страниц"
    },
    messagebox: {
      title: "Сообщение",
      confirm: "OK",
      cancel: "Отмена",
      error: "Недопустимый ввод данных"
    },
    upload: {
      deleteTip: "Нажмите [Удалить] для удаления",
      delete: "Удалить",
      preview: "Превью",
      continue: "Продолжить"
    },
    table: {
      emptyText: "Нет данных",
      confirmFilter: "Подтвердить",
      resetFilter: "Сбросить",
      clearFilter: "Все",
      sumText: "Сумма"
    },
    tour: {
      next: "Далее",
      previous: "Назад",
      finish: "Завершить"
    },
    tree: {
      emptyText: "Нет данных"
    },
    transfer: {
      noMatch: "Совпадений не найдено",
      noData: "Нет данных",
      titles: ["Список 1", "Список 2"],
      filterPlaceholder: "Введите ключевое слово",
      noCheckedFormat: "{total} пунктов",
      hasCheckedFormat: "{checked}/{total} выбрано"
    },
    image: {
      error: "ОШИБКА"
    },
    pageHeader: {
      title: "Назад"
    },
    popconfirm: {
      confirmButtonText: "OK",
      cancelButtonText: "Отмена"
    },
    carousel: {
      leftArrow: "Слайдер стрелка влево",
      rightArrow: "Слайдер стрелка вправо",
      indicator: "Слайдер перейти на страницу под номером {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/sk.mjs
var sk = {
  name: "sk",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Zmazať"
    },
    datepicker: {
      now: "Teraz",
      today: "Dnes",
      cancel: "Zrušiť",
      clear: "Zmazať",
      confirm: "OK",
      selectDate: "Vybrať dátum",
      selectTime: "Vybrať čas",
      startDate: "Dátum začiatku",
      startTime: "Čas začiatku",
      endDate: "Dátum konca",
      endTime: "Čas konca",
      prevYear: "Predošlý rok",
      nextYear: "Ďalší rok",
      prevMonth: "Predošlý mesiac",
      nextMonth: "Ďalší mesiac",
      day: "Deň",
      week: "Týždeň",
      month: "Mesiac",
      year: "Rok",
      month1: "Január",
      month2: "Február",
      month3: "Marec",
      month4: "Apríl",
      month5: "Máj",
      month6: "Jún",
      month7: "Júl",
      month8: "August",
      month9: "September",
      month10: "Október",
      month11: "November",
      month12: "December",
      weeks: {
        sun: "Ne",
        mon: "Po",
        tue: "Ut",
        wed: "St",
        thu: "Št",
        fri: "Pi",
        sat: "So"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Máj",
        jun: "Jún",
        jul: "Júl",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Načítavanie",
      noMatch: "Žiadna zhoda",
      noData: "Žiadne dáta",
      placeholder: "Vybrať"
    },
    mention: {
      loading: "Načítavanie"
    },
    cascader: {
      noMatch: "Žiadna zhoda",
      loading: "Načítavanie",
      placeholder: "Vybrať",
      noData: "Žiadne dáta"
    },
    pagination: {
      goto: "Choď na",
      pagesize: "na stranu",
      total: "Všetko {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Správa",
      confirm: "OK",
      cancel: "Zrušiť",
      error: "Neplatný vstup"
    },
    upload: {
      deleteTip: "pre odstránenie stisni klávesu Delete",
      delete: "Vymazať",
      preview: "Prehliadať",
      continue: "Pokračovať"
    },
    table: {
      emptyText: "Žiadne dáta",
      confirmFilter: "Potvrdiť",
      resetFilter: "Zresetovať",
      clearFilter: "Všetko",
      sumText: "Spolu"
    },
    tree: {
      emptyText: "Žiadne dáta"
    },
    transfer: {
      noMatch: "Žiadna zhoda",
      noData: "Žiadne dáta",
      titles: ["Zoznam 1", "Zoznam 2"],
      filterPlaceholder: "Filtrovať podľa",
      noCheckedFormat: "{total} položiek",
      hasCheckedFormat: "{checked}/{total} označených"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/sl.mjs
var sl = {
  name: "sl",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "V redu",
      clear: "Počisti"
    },
    datepicker: {
      now: "Zdaj",
      today: "Danes",
      cancel: "Prekliči",
      clear: "Počisti",
      confirm: "Potrdi",
      selectDate: "Izberi datum",
      selectTime: "Izberi čas",
      startDate: "Začetni datum",
      startTime: "Začetni čas",
      endDate: "Končni datum",
      endTime: "Končni čas",
      prevYear: "Prejšnje leto",
      nextYear: "Naslednje leto",
      prevMonth: "Prejšnji mesec",
      nextMonth: "Naslednji mesec",
      year: "",
      month1: "Jan",
      month2: "Feb",
      month3: "Mar",
      month4: "Apr",
      month5: "Maj",
      month6: "Jun",
      month7: "Jul",
      month8: "Avg",
      month9: "Sep",
      month10: "Okt",
      month11: "Nov",
      month12: "Dec",
      week: "teden",
      weeks: {
        sun: "Ned",
        mon: "Pon",
        tue: "Tor",
        wed: "Sre",
        thu: "Čet",
        fri: "Pet",
        sat: "Sob"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Maj",
        jun: "Jun",
        jul: "Jul",
        aug: "Avg",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Nalaganje",
      noMatch: "Ni ustreznih podatkov",
      noData: "Ni podatkov",
      placeholder: "Izberi"
    },
    mention: {
      loading: "Nalaganje"
    },
    cascader: {
      noMatch: "Ni ustreznih podatkov",
      loading: "Nalaganje",
      placeholder: "Izberi",
      noData: "Ni podatkov"
    },
    pagination: {
      goto: "Pojdi na",
      pagesize: "/stran",
      total: "Skupno {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Sporočilo",
      confirm: "V redu",
      cancel: "Prekliči",
      error: "Nedovoljen vnos"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "Izbriši",
      preview: "Predogled",
      continue: "Nadaljuj"
    },
    table: {
      emptyText: "Ni podatkov",
      confirmFilter: "Potrdi",
      resetFilter: "Ponastavi",
      clearFilter: "Vse",
      sumText: "Skupno"
    },
    tree: {
      emptyText: "Ni podatkov"
    },
    transfer: {
      noMatch: "Ni ustreznih podatkov",
      noData: "Ni podatkov",
      titles: ["Seznam 1", "Seznam 2"],
      filterPlaceholder: "Vnesi ključno besedo",
      noCheckedFormat: "{total} elementov",
      hasCheckedFormat: "{checked}/{total} izbranih"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/sr.mjs
var sr = {
  name: "sr",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Поништи"
    },
    datepicker: {
      now: "Сада",
      today: "Данас",
      cancel: "Откажи",
      clear: "Бриши",
      confirm: "OK",
      selectDate: "Изабери датум",
      selectTime: "Изабери време",
      startDate: "Датум почетка",
      startTime: "Време почетка",
      endDate: "Датум завршетка",
      endTime: "Време завршетка",
      prevYear: "Претходна година",
      nextYear: "Следећа година",
      prevMonth: "Претходни месец",
      nextMonth: "Следећи месец",
      year: "година",
      month1: "јануар",
      month2: "фебруар",
      month3: "март",
      month4: "април",
      month5: "мај",
      month6: "јун",
      month7: "јул",
      month8: "август",
      month9: "септембар",
      month10: "октобар",
      month11: "новембар",
      month12: "децембар",
      week: "седмица",
      weeks: {
        sun: "Нед",
        mon: "Пон",
        tue: "Уто",
        wed: "Сре",
        thu: "Чет",
        fri: "Пет",
        sat: "Суб"
      },
      months: {
        jan: "јан",
        feb: "феб",
        mar: "мар",
        apr: "апр",
        may: "мај",
        jun: "јун",
        jul: "јул",
        aug: "авг",
        sep: "сеп",
        oct: "окт",
        nov: "нов",
        dec: "дец"
      }
    },
    select: {
      loading: "Учитавање",
      noMatch: "Нема резултата",
      noData: "Нема података",
      placeholder: "Изабери"
    },
    mention: {
      loading: "Учитавање"
    },
    cascader: {
      noMatch: "Нема резултата",
      loading: "Учитавање",
      placeholder: "Изабери",
      noData: "Нема података"
    },
    pagination: {
      goto: "Иди на",
      pagesize: "/страни",
      total: "Укупно {total}",
      pageClassifier: "",
      page: "Страна",
      prev: "Иди на претходну страну",
      next: "Иди на следећу страну",
      currentPage: "страна {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Порука",
      confirm: "OK",
      cancel: "Откажи",
      error: "Неисправан унос"
    },
    upload: {
      deleteTip: "притисни БРИШИ да обришеш",
      delete: "Бриши",
      preview: "Види",
      continue: "Настави"
    },
    table: {
      emptyText: "Нема података",
      confirmFilter: "Потврди",
      resetFilter: "Ресетуј",
      clearFilter: "Све",
      sumText: "Збир"
    },
    tree: {
      emptyText: "Нема података"
    },
    transfer: {
      noMatch: "Нема резултата",
      noData: "Нема података",
      titles: ["Листа 1", "Листа 2"],
      filterPlaceholder: "Унеси кључну реч",
      noCheckedFormat: "{total} ставки",
      hasCheckedFormat: "{checked}/{total} обележених"
    },
    image: {
      error: "НЕУСПЕШНО"
    },
    pageHeader: {
      title: "Назад"
    },
    popconfirm: {
      confirmButtonText: "Да",
      cancelButtonText: "Не"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/sv.mjs
var sv = {
  name: "sv",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Töm"
    },
    datepicker: {
      now: "Nu",
      today: "Idag",
      cancel: "Avbryt",
      clear: "Töm",
      confirm: "OK",
      selectDate: "Välj datum",
      selectTime: "Välj tid",
      startDate: "Startdatum",
      startTime: "Starttid",
      endDate: "Slutdatum",
      endTime: "Sluttid",
      prevYear: "Föregående år",
      nextYear: "Nästa år",
      prevMonth: "Föregående månad",
      nextMonth: "Nästa månad",
      year: "",
      month1: "Januari",
      month2: "Februari",
      month3: "Mars",
      month4: "April",
      month5: "Maj",
      month6: "Juni",
      month7: "Juli",
      month8: "Augusti",
      month9: "September",
      month10: "Oktober",
      month11: "November",
      month12: "December",
      weeks: {
        sun: "Sön",
        mon: "Mån",
        tue: "Tis",
        wed: "Ons",
        thu: "Tor",
        fri: "Fre",
        sat: "Lör"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Maj",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Laddar",
      noMatch: "Hittade inget",
      noData: "Ingen data",
      placeholder: "Välj"
    },
    mention: {
      loading: "Laddar"
    },
    cascader: {
      noMatch: "Hittade inget",
      loading: "Laddar",
      placeholder: "Välj",
      noData: "Ingen data"
    },
    pagination: {
      goto: "Gå till",
      pagesize: "/sida",
      total: "Totalt {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Meddelande",
      confirm: "OK",
      cancel: "Avbryt",
      error: "Felaktig inmatning"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "Radera",
      preview: "Förhandsvisa",
      continue: "Fortsätt"
    },
    table: {
      emptyText: "Inga Data",
      confirmFilter: "Bekräfta",
      resetFilter: "Återställ",
      clearFilter: "Alla",
      sumText: "Summa"
    },
    tour: {
      next: "Nästa",
      previous: "Föregående",
      finish: "Avsluta"
    },
    tree: {
      emptyText: "Ingen data"
    },
    transfer: {
      noMatch: "Hittade inget",
      noData: "Ingen data",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "Enter keyword",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Bakåt"
    },
    popconfirm: {
      confirmButtonText: "Ja",
      cancelButtonText: "Nej"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ta.mjs
var ta = {
  name: "ta",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "உறுதி செய்",
      clear: "தெளிவாக்கு"
    },
    datepicker: {
      now: "தற்போது",
      today: "இன்று",
      cancel: "ரத்து செய்",
      clear: "சரி",
      confirm: "உறுதி செய்",
      selectDate: "தேதியை தேர்வு செய்",
      selectTime: "நேரத்தை தேர்வு செய்",
      startDate: "தொடங்கும் நாள்",
      startTime: "தொடங்கும் நேரம்",
      endDate: "முடியும் தேதி",
      endTime: "முடியும் நேரம்",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "வருடம்",
      month1: "ஜனவரி",
      month2: "பிப்ரவரி",
      month3: "மார்ச்",
      month4: "ஏப்ரல்",
      month5: "மே",
      month6: "ஜூன்",
      month7: "ஜூலை",
      month8: "ஆகஸ்ட்",
      month9: "செப்டம்பர்",
      month10: "அக்டோபர்",
      month11: "நவம்பர்",
      month12: "டிசம்பர்",
      weeks: {
        sun: "ஞாயிறு",
        mon: "திங்கள்",
        tue: "செவ்வாய்",
        wed: "புதன்",
        thu: "வியாழன்",
        fri: "வெள்ளி",
        sat: "சனி"
      },
      months: {
        jan: "ஜனவரி",
        feb: "பிப்ரவரி",
        mar: "மார்ச்",
        apr: "ஏப்ரல்",
        may: "மே",
        jun: "ஜூன்",
        jul: "ஜூலை",
        aug: "ஆகஸ்ட்",
        sep: "செப்டம்பர்",
        oct: "அக்டோபர்",
        nov: "நவம்பர்",
        dec: "டிசம்பர்"
      }
    },
    select: {
      loading: "தயாராகிக்கொண்டிருக்கிறது",
      noMatch: "பொருத்தமான தரவு கிடைக்கவில்லை",
      noData: "தரவு இல்லை",
      placeholder: "தேர்வு செய்"
    },
    mention: {
      loading: "தயாராகிக்கொண்டிருக்கிறது"
    },
    cascader: {
      noMatch: "பொருத்தமான தரவு கிடைக்கவில்லை",
      loading: "தயாராகிக்கொண்டிருக்கிறது",
      placeholder: "தேர்வு செய்",
      noData: "தரவு இல்லை"
    },
    pagination: {
      goto: "தேவையான் பகுதிக்கு செல்",
      pagesize: "/page",
      total: "மொத்தம் {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "செய்தி",
      confirm: "உறுதி செய்",
      cancel: "ரத்து செய்",
      error: "பொருத்தாமில்லாத உள்ளீடு"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "நீக்கு",
      preview: "முன்னோட்டம் பார்",
      continue: "தொடரு"
    },
    table: {
      emptyText: "தரவு இல்லை",
      confirmFilter: "உறுதி செய்",
      resetFilter: "புதுமாற்றம் செய்",
      clearFilter: "அனைத்தும்",
      sumText: "கூட்டு"
    },
    tree: {
      emptyText: "தரவு இல்லை"
    },
    transfer: {
      noMatch: "பொருத்தமான தரவு கிடைக்கவில்லை",
      noData: "தரவு இல்லை",
      titles: ["பட்டியல் 1", "பட்டியல் 2"],
      filterPlaceholder: "சொல்லை உள்ளீடு செய்",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} தேர்வு செய்யப்பட்டவைகள்"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/th.mjs
var th = {
  name: "th",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "ตกลง",
      clear: "ล้างข้อมูล"
    },
    datepicker: {
      now: "ตอนนี้",
      today: "วันนี้",
      cancel: "ยกเลิก",
      clear: "ล้างข้อมูล",
      confirm: "ตกลง",
      selectDate: "เลือกวันที่",
      selectTime: "เลือกเวลา",
      startDate: "วันที่เริ่มต้น",
      startTime: "เวลาเริ่มต้น",
      endDate: "วันที่สิ้นสุด",
      endTime: "เวลาสิ้นสุด",
      prevYear: "ปีก่อนหน้า",
      nextYear: "ปีถัดไป",
      prevMonth: "เดือนก่อนหน้า",
      nextMonth: "เดือนถัดไป",
      year: "ปี",
      month1: "มกราคม",
      month2: "กุมภาพันธ์",
      month3: "มีนาคม",
      month4: "เมษายน",
      month5: "พฤษภาคม",
      month6: "มิถุนายน",
      month7: "กรกฎาคม",
      month8: "สิงหาคม",
      month9: "กันยายน",
      month10: "ตุลาคม",
      month11: "พฤศจิกายน",
      month12: "ธันวาคม",
      weeks: {
        sun: "อา",
        mon: "จ",
        tue: "อ",
        wed: "พ",
        thu: "พฤ",
        fri: "ศ",
        sat: "ส"
      },
      months: {
        jan: "ม.ค.",
        feb: "ก.พ.",
        mar: "มี.ค.",
        apr: "เม.ย.",
        may: "พ.ค.",
        jun: "มิ.ย.",
        jul: "ก.ค.",
        aug: "ส.ค.",
        sep: "ก.ย.",
        oct: "ต.ค.",
        nov: "พ.ย.",
        dec: "ธ.ค."
      }
    },
    select: {
      loading: "กำลังโหลด",
      noMatch: "ไม่พบข้อมูลที่ตรงกัน",
      noData: "ไม่พบข้อมูล",
      placeholder: "เลือก"
    },
    mention: {
      loading: "กำลังโหลด"
    },
    cascader: {
      noMatch: "ไม่พบข้อมูลที่ตรงกัน",
      loading: "กำลังโหลด",
      placeholder: "เลือก",
      noData: "ไม่พบข้อมูล"
    },
    pagination: {
      goto: "ไปที่",
      pagesize: "/หน้า",
      total: "ทั้งหมด {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "ข้อความ",
      confirm: "ตกลง",
      cancel: "ยกเลิก",
      error: "คุณป้อนข้อมูลไม่ถูกต้อง"
    },
    upload: {
      deleteTip: 'กดปุ่ม "ลบ" เพื่อลบออก',
      delete: "ลบ",
      preview: "ตัวอย่าง",
      continue: "ทำต่อ"
    },
    table: {
      emptyText: "ไม่พบข้อมูล",
      confirmFilter: "ยืนยัน",
      resetFilter: "รีเซ็ต",
      clearFilter: "ทั้งหมด",
      sumText: "รวม"
    },
    tour: {
      next: "ถัดไป",
      previous: "ย้อนกลับ",
      finish: "เสร็จสิ้น"
    },
    tree: {
      emptyText: "ไม่พบข้อมูล"
    },
    transfer: {
      noMatch: "ไม่พบข้อมูลที่ตรงกัน",
      noData: "ไม่พบข้อมูล",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "กรอกคีย์เวิร์ด",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "ย้อนกลับ"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/tk.mjs
var tk = {
  name: "tk",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Arassala"
    },
    datepicker: {
      now: "Şuwagt",
      today: "Şügün",
      cancel: "Bes et",
      clear: "Arassala",
      confirm: "OK",
      selectDate: "Güni saýlaň",
      selectTime: "Wagty saýlaň",
      startDate: "Başlaýan güni",
      startTime: "Başlaýan wagty",
      endDate: "Gutarýan güni",
      endTime: "Gutarýan wagty",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "",
      month1: "Ýan",
      month2: "Few",
      month3: "Mar",
      month4: "Apr",
      month5: "Maý",
      month6: "Iýn",
      month7: "Iýl",
      month8: "Awg",
      month9: "Sen",
      month10: "Okt",
      month11: "Noý",
      month12: "Dek",
      weeks: {
        sun: "Ýek",
        mon: "Duş",
        tue: "Siş",
        wed: "Çar",
        thu: "Pen",
        fri: "Ann",
        sat: "Şen"
      },
      months: {
        jan: "Ýan",
        feb: "Few",
        mar: "Mar",
        apr: "Apr",
        may: "Maý",
        jun: "Iýn",
        jul: "Iýl",
        aug: "Awg",
        sep: "Sep",
        oct: "Okt",
        nov: "Noý",
        dec: "Dek"
      }
    },
    select: {
      loading: "Indirilýär",
      noMatch: "Hiçzat tapylmady",
      noData: "Hiçzat ýok",
      placeholder: "Saýla"
    },
    mention: {
      loading: "Indirilýär"
    },
    cascader: {
      noMatch: "Hiçzat tapylmady",
      loading: "Indirilýär",
      placeholder: "Saýlaň",
      noData: "Hiçzat ýok"
    },
    pagination: {
      goto: "Git",
      pagesize: "/sahypa",
      total: "Umumy {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Hat",
      confirm: "OK",
      cancel: "Bes et",
      error: "Ýalňyş girizme"
    },
    upload: {
      deleteTip: 'Pozmak üçin "poz" düwmä basyň',
      delete: "Poz",
      preview: "Gör",
      continue: "Dowam et"
    },
    table: {
      emptyText: "Maglumat ýok",
      confirmFilter: "Tassykla",
      resetFilter: "Arassala",
      clearFilter: "Hemmesi",
      sumText: "Jemi"
    },
    tree: {
      emptyText: "Maglumat ýok"
    },
    transfer: {
      noMatch: "Hiçzat tapylmady",
      noData: "Hiçzat ýok",
      titles: ["Sanaw 1", "Sanaw 2"],
      filterPlaceholder: "Gözleg sözlerini giriziň",
      noCheckedFormat: "{total} sany",
      hasCheckedFormat: "{checked}/{total} saýlanan"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/tr.mjs
var tr = {
  name: "tr",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Onayla",
      clear: "Temizle"
    },
    datepicker: {
      now: "Şimdi",
      today: "Bugün",
      cancel: "İptal",
      clear: "Temizle",
      confirm: "Onayla",
      selectDate: "Tarih seç",
      selectTime: "Saat seç",
      startDate: "Başlangıç Tarihi",
      startTime: "Başlangıç Saati",
      endDate: "Bitiş Tarihi",
      endTime: "Bitiş Saati",
      prevYear: "Önceki Yıl",
      nextYear: "Sonraki Yıl",
      prevMonth: "Önceki Ay",
      nextMonth: "Sonraki Ay",
      year: "",
      month1: "Ocak",
      month2: "Şubat",
      month3: "Mart",
      month4: "Nisan",
      month5: "Mayıs",
      month6: "Haziran",
      month7: "Temmuz",
      month8: "Ağustos",
      month9: "Eylül",
      month10: "Ekim",
      month11: "Kasım",
      month12: "Aralık",
      weeks: {
        sun: "Paz",
        mon: "Pzt",
        tue: "Sal",
        wed: "Çar",
        thu: "Per",
        fri: "Cum",
        sat: "Cmt"
      },
      months: {
        jan: "Oca",
        feb: "Şub",
        mar: "Mar",
        apr: "Nis",
        may: "May",
        jun: "Haz",
        jul: "Tem",
        aug: "Ağu",
        sep: "Eyl",
        oct: "Eki",
        nov: "Kas",
        dec: "Ara"
      }
    },
    select: {
      loading: "Yükleniyor",
      noMatch: "Eşleşen veri bulunamadı",
      noData: "Veri yok",
      placeholder: "Seç"
    },
    mention: {
      loading: "Yükleniyor"
    },
    cascader: {
      noMatch: "Eşleşen veri bulunamadı",
      loading: "Yükleniyor",
      placeholder: "Seç",
      noData: "Veri yok"
    },
    pagination: {
      goto: "Git",
      pagesize: "/sayfa",
      total: "Toplam {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mesaj",
      confirm: "Onayla",
      cancel: "İptal",
      error: "İllegal giriş"
    },
    upload: {
      deleteTip: "kaldırmak için delete tuşuna bas",
      delete: "Sil",
      preview: "Görüntüle",
      continue: "Devam"
    },
    table: {
      emptyText: "Veri yok",
      confirmFilter: "Onayla",
      resetFilter: "Sıfırla",
      clearFilter: "Hepsi",
      sumText: "Sum"
    },
    tree: {
      emptyText: "Veri yok"
    },
    transfer: {
      noMatch: "Eşleşen veri bulunamadı",
      noData: "Veri yok",
      titles: ["Liste 1", "Liste 2"],
      filterPlaceholder: "Anahtar kelimeleri gir",
      noCheckedFormat: "{total} adet",
      hasCheckedFormat: "{checked}/{total} seçildi"
    },
    image: {
      error: "BAŞARISIZ OLDU"
    },
    pageHeader: {
      title: "Geri"
    },
    popconfirm: {
      confirmButtonText: "Evet",
      cancelButtonText: "Hayır"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/ug-cn.mjs
var ugCn = {
  name: "ug-cn",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "جەزملەش",
      clear: "قۇرۇقداش"
    },
    datepicker: {
      now: "ھازىرقى ۋاقىت",
      today: "بۈگۈن",
      cancel: "بىكار قىلىش",
      clear: "قۇرۇقداش",
      confirm: "جەزملەش",
      selectDate: "چىسلا تاللاڭ",
      selectTime: "ۋاقىت تاللاڭ",
      startDate: "باشلانغان چىسلا",
      startTime: "باشلانغان ۋاقىت",
      endDate: "ئاخىرلاشقان چىسلا",
      endTime: "ئاخىرلاشقان ۋاقىت",
      prevYear: "ئالدىنقى يىل",
      nextYear: "كىيىنكى يىل",
      prevMonth: "ئالدىنقى ئاي",
      nextMonth: "كىيىنكى ئاي",
      year: "- يىل",
      month1: "1-ئاي",
      month2: "2-ئاي",
      month3: "3-ئاي",
      month4: "4-ئاي",
      month5: "5-ئاي",
      month6: "6-ئاي",
      month7: "7-ئاي",
      month8: "8-ئاي",
      month9: "9-ئاي",
      month10: "10-ئاي",
      month11: "11-ئاي",
      month12: "12-ئاي",
      weeks: {
        sun: "يەكشەنبە",
        mon: "دۈشەنبە",
        tue: "سەيشەنبە",
        wed: "چارشەنبە",
        thu: "پەيشەنبە",
        fri: "جۈمە",
        sat: "شەنبە"
      },
      months: {
        jan: "1-ئاي",
        feb: "2-ئاي",
        mar: "3-ئاي",
        apr: "4-ئاي",
        may: "5-ئاي",
        jun: "6-ئاي",
        jul: "7-ئاي",
        aug: "8-ئاي",
        sep: "9-ئاي",
        oct: "10-ئاي",
        nov: "11-ئاي",
        dec: "12-ئاي"
      }
    },
    select: {
      loading: "يۈكلىنىۋاتىدۇ",
      noMatch: "ئۇچۇر تېپىلمىدى",
      noData: "ئۇچۇر يوق",
      placeholder: "تاللاڭ"
    },
    mention: {
      loading: "يۈكلىنىۋاتىدۇ"
    },
    cascader: {
      noMatch: "ئۇچۇر تېپىلمىدى",
      loading: "يۈكلىنىۋاتىدۇ",
      placeholder: "تاللاڭ",
      noData: "ئۇچۇر يوق"
    },
    pagination: {
      goto: "كىيىنكى بەت",
      pagesize: "تال/بەت",
      total: "جەمئىي {total} تال",
      pageClassifier: "بەت",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "ئەسكەرتىش",
      confirm: "جەزملەش",
      cancel: "بىكار قىلىش",
      error: "كىرگۈزگەن ئۇچۇرىڭىزدا خاتالىق بار!"
    },
    upload: {
      deleteTip: "delete كۇنپكىسىنى بېسىپ ئۆچۈرەلەيسىز",
      delete: "ئۆچۈرۈش",
      preview: "رەسىمنى كۆرۈش",
      continue: "رەسىم يوللاش"
    },
    table: {
      emptyText: "ئۇچۇر يوق",
      confirmFilter: "سۈزگۈچ",
      resetFilter: "قايتا تولدۇرۇش",
      clearFilter: "ھەممە",
      sumText: "جەمئىي"
    },
    tree: {
      emptyText: "ئۇچۇر يوق"
    },
    transfer: {
      noMatch: "ئۇچۇر تېپىلمىدى",
      noData: "ئۇچۇر يوق",
      titles: ["جەدۋەل 1", "جەدۋەل 2"],
      filterPlaceholder: "ئىزدىمەكچى بولغان مەزمۇننى كىرگۈزۈڭ",
      noCheckedFormat: "جەمئىي {total} تۈر",
      hasCheckedFormat: "تاللانغىنى {checked}/{total} تۈر"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/uk.mjs
var uk = {
  name: "uk",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Очистити"
    },
    datepicker: {
      now: "Зараз",
      today: "Сьогодні",
      cancel: "Відміна",
      clear: "Очистити",
      confirm: "OK",
      selectDate: "Вибрати дату",
      selectTime: "Вибрати час",
      startDate: "Дата початку",
      startTime: "Час початку",
      endDate: "Дата завершення",
      endTime: "Час завершення",
      prevYear: "Попередній Рік",
      nextYear: "Наступний Рік",
      prevMonth: "Попередній Місяць",
      nextMonth: "Наступний Місяць",
      year: "",
      month1: "Січень",
      month2: "Лютий",
      month3: "Березень",
      month4: "Квітень",
      month5: "Травень",
      month6: "Червень",
      month7: "Липень",
      month8: "Серпень",
      month9: "Вересень",
      month10: "Жовтень",
      month11: "Листопад",
      month12: "Грудень",
      week: "тиждень",
      weeks: {
        sun: "Нд",
        mon: "Пн",
        tue: "Вт",
        wed: "Ср",
        thu: "Чт",
        fri: "Пт",
        sat: "Сб"
      },
      months: {
        jan: "Січ",
        feb: "Лют",
        mar: "Бер",
        apr: "Кві",
        may: "Тра",
        jun: "Чер",
        jul: "Лип",
        aug: "Сер",
        sep: "Вер",
        oct: "Жов",
        nov: "Лис",
        dec: "Гру"
      }
    },
    select: {
      loading: "Завантаження",
      noMatch: "Співпадінь не знайдено",
      noData: "Немає даних",
      placeholder: "Обрати"
    },
    mention: {
      loading: "Завантаження"
    },
    cascader: {
      noMatch: "Співпадінь не знайдено",
      loading: "Завантаження",
      placeholder: "Обрати",
      noData: "Немає даних"
    },
    pagination: {
      goto: "Перейти",
      pagesize: "на сторінці",
      total: "Всього {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Повідомлення",
      confirm: "OK",
      cancel: "Відміна",
      error: "Неприпустимий ввід даних"
    },
    upload: {
      deleteTip: "натисніть кнопку щоб видалити",
      delete: "Видалити",
      preview: "Перегляд",
      continue: "Продовжити"
    },
    table: {
      emptyText: "Немає даних",
      confirmFilter: "Підтвердити",
      resetFilter: "Скинути",
      clearFilter: "Все",
      sumText: "Сума"
    },
    tour: {
      next: "Далі",
      previous: "Назад",
      finish: "Завершити"
    },
    tree: {
      emptyText: "Немає даних"
    },
    transfer: {
      noMatch: "Співпадінь не знайдено",
      noData: "Обрати",
      titles: ["Список 1", "Список 2"],
      filterPlaceholder: "Введіть ключове слово",
      noCheckedFormat: "{total} пунктів",
      hasCheckedFormat: "{checked}/{total} вибрано"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/uz-uz.mjs
var uzUz = {
  name: "uz-uz",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Qabul qilish",
      clear: "Tozalash"
    },
    datepicker: {
      now: "Hozir",
      today: "Bugun",
      cancel: "Bekor qilish",
      clear: "Tozalash",
      confirm: "Qabul qilish",
      selectDate: "Kunni tanlash",
      selectTime: "Soatni tanlash",
      startDate: "Boshlanish sanasi",
      startTime: "Boshlanish vaqti",
      endDate: "Tugash sanasi",
      endTime: "Tugash vaqti",
      prevYear: "Oʻtgan yil",
      nextYear: "Kelgusi yil",
      prevMonth: "Oʻtgan oy",
      nextMonth: "Kelgusi oy",
      year: "Yil",
      month1: "Yanvar",
      month2: "Fevral",
      month3: "Mart",
      month4: "Aprel",
      month5: "May",
      month6: "Iyun",
      month7: "Iyul",
      month8: "Avgust",
      month9: "Sentabr",
      month10: "Oktabr",
      month11: "Noyabr",
      month12: "Dekabr",
      week: "Hafta",
      weeks: {
        sun: "Yak",
        mon: "Dush",
        tue: "Sesh",
        wed: "Chor",
        thu: "Pay",
        fri: "Jum",
        sat: "Shan"
      },
      months: {
        jan: "Yan",
        feb: "Fev",
        mar: "Mar",
        apr: "Apr",
        may: "May",
        jun: "Iyun",
        jul: "Iyul",
        aug: "Avg",
        sep: "Sen",
        oct: "Okt",
        nov: "Noy",
        dec: "Dek"
      }
    },
    select: {
      loading: "Yuklanmoqda",
      noMatch: "Mos maʼlumot yoʻq",
      noData: "Maʼlumot yoʻq",
      placeholder: "Tanladizngiz"
    },
    mention: {
      loading: "Yuklanmoqda"
    },
    cascader: {
      noMatch: "Mos maʼlumot topilmadi",
      loading: "Yuklanmoqda",
      placeholder: "Tanlash",
      noData: "Maʼlumot yoʻq"
    },
    pagination: {
      goto: "Oʻtish",
      pagesize: "/sahifa",
      total: "Barchasi {total} ta",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Xabar",
      confirm: "Qabul qilish",
      cancel: "Bekor qilish",
      error: "Xatolik"
    },
    upload: {
      deleteTip: "Oʻchirish tugmasini bosib oʻchiring",
      delete: "Oʻchirish",
      preview: "Oldin koʻrish",
      continue: "Davom qilish"
    },
    table: {
      emptyText: "Boʻsh",
      confirmFilter: "Qabul qilish",
      resetFilter: "Oldingi holatga qaytarish",
      clearFilter: "Jami",
      sumText: "Summasi"
    },
    tree: {
      emptyText: "Maʼlumot yoʻq"
    },
    transfer: {
      noMatch: "Mos maʼlumot topilmadi",
      noData: "Maʼlumot yoʻq",
      titles: ["1-jadval", "2-jadval"],
      filterPlaceholder: "Kalit soʻzni kiriting",
      noCheckedFormat: "{total} ta element",
      hasCheckedFormat: "{checked}/{total} ta belgilandi"
    },
    image: {
      error: "Xatolik"
    },
    pageHeader: {
      title: "Orqaga"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/vi.mjs
var vi = {
  name: "vi",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Xóa"
    },
    datepicker: {
      now: "Hiện tại",
      today: "Hôm nay",
      cancel: "Hủy",
      clear: "Xóa",
      confirm: "OK",
      selectDate: "Chọn ngày",
      selectTime: "Chọn giờ",
      startDate: "Ngày bắt đầu",
      startTime: "Thời gian bắt đầu",
      endDate: "Ngày kết thúc",
      endTime: "Thời gian kết thúc",
      prevYear: "Năm trước",
      nextYear: "Năm tới",
      prevMonth: "Tháng trước",
      nextMonth: "Tháng tới",
      year: "Năm",
      month1: "Tháng 1",
      month2: "Tháng 2",
      month3: "Tháng 3",
      month4: "Tháng 4",
      month5: "Tháng 5",
      month6: "Tháng 6",
      month7: "Tháng 7",
      month8: "Tháng 8",
      month9: "Tháng 9",
      month10: "Tháng 10",
      month11: "Tháng 11",
      month12: "Tháng 12",
      weeks: {
        sun: "CN",
        mon: "T2",
        tue: "T3",
        wed: "T4",
        thu: "T5",
        fri: "T6",
        sat: "T7"
      },
      months: {
        jan: "Th.1",
        feb: "Th.2",
        mar: "Th.3",
        apr: "Th.4",
        may: "Th.5",
        jun: "Th.6",
        jul: "Th.7",
        aug: "Th.8",
        sep: "Th.9",
        oct: "Th.10",
        nov: "Th.11",
        dec: "Th.12"
      }
    },
    select: {
      loading: "Đang tải",
      noMatch: "Dữ liệu không phù hợp",
      noData: "Không tìm thấy dữ liệu",
      placeholder: "Chọn"
    },
    mention: {
      loading: "Đang tải"
    },
    cascader: {
      noMatch: "Dữ liệu không phù hợp",
      loading: "Đang tải",
      placeholder: "Chọn",
      noData: "Không tìm thấy dữ liệu"
    },
    pagination: {
      goto: "Nhảy tới",
      pagesize: "/trang",
      total: "Tổng {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Thông báo",
      confirm: "OK",
      cancel: "Hủy",
      error: "Dữ liệu không hợp lệ"
    },
    upload: {
      deleteTip: "Nhấn xoá để xoá",
      delete: "Xóa",
      preview: "Xem trước",
      continue: "Tiếp tục"
    },
    table: {
      emptyText: "Không có dữ liệu",
      confirmFilter: "Xác nhận",
      resetFilter: "Làm mới",
      clearFilter: "Xóa hết",
      sumText: "Tổng"
    },
    tour: {
      next: "Tiếp",
      previous: "Trước",
      finish: "Hoàn thành"
    },
    tree: {
      emptyText: "Không có dữ liệu"
    },
    transfer: {
      noMatch: "Dữ liệu không phù hợp",
      noData: "Không tìm thấy dữ liệu",
      titles: ["Danh sách 1", "Danh sách 2"],
      filterPlaceholder: "Nhập từ khóa",
      noCheckedFormat: "{total} mục",
      hasCheckedFormat: "{checked}/{total} đã chọn "
    },
    image: {
      error: "LỖI"
    },
    pageHeader: {
      title: "Quay lại"
    },
    popconfirm: {
      confirmButtonText: "Ok",
      cancelButtonText: "Huỷ"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/zh-cn.mjs
var zhCn = {
  name: "zh-cn",
  el: {
    breadcrumb: {
      label: "面包屑"
    },
    colorpicker: {
      confirm: "确定",
      clear: "清空",
      defaultLabel: "颜色选择器",
      description: "当前颜色 {color}，按 Enter 键选择新颜色",
      alphaLabel: "选择透明度的值"
    },
    datepicker: {
      now: "此刻",
      today: "今天",
      cancel: "取消",
      clear: "清空",
      confirm: "确定",
      dateTablePrompt: "使用方向键与 Enter 键可选择日期",
      monthTablePrompt: "使用方向键与 Enter 键可选择月份",
      yearTablePrompt: "使用方向键与 Enter 键可选择年份",
      selectedDate: "已选日期",
      selectDate: "选择日期",
      selectTime: "选择时间",
      startDate: "开始日期",
      startTime: "开始时间",
      endDate: "结束日期",
      endTime: "结束时间",
      prevYear: "前一年",
      nextYear: "后一年",
      prevMonth: "上个月",
      nextMonth: "下个月",
      year: "年",
      month1: "1 月",
      month2: "2 月",
      month3: "3 月",
      month4: "4 月",
      month5: "5 月",
      month6: "6 月",
      month7: "7 月",
      month8: "8 月",
      month9: "9 月",
      month10: "10 月",
      month11: "11 月",
      month12: "12 月",
      weeks: {
        sun: "日",
        mon: "一",
        tue: "二",
        wed: "三",
        thu: "四",
        fri: "五",
        sat: "六"
      },
      weeksFull: {
        sun: "星期日",
        mon: "星期一",
        tue: "星期二",
        wed: "星期三",
        thu: "星期四",
        fri: "星期五",
        sat: "星期六"
      },
      months: {
        jan: "一月",
        feb: "二月",
        mar: "三月",
        apr: "四月",
        may: "五月",
        jun: "六月",
        jul: "七月",
        aug: "八月",
        sep: "九月",
        oct: "十月",
        nov: "十一月",
        dec: "十二月"
      }
    },
    inputNumber: {
      decrease: "减少数值",
      increase: "增加数值"
    },
    select: {
      loading: "加载中",
      noMatch: "无匹配数据",
      noData: "无数据",
      placeholder: "请选择"
    },
    dropdown: {
      toggleDropdown: "切换下拉选项"
    },
    mention: {
      loading: "加载中"
    },
    cascader: {
      noMatch: "无匹配数据",
      loading: "加载中",
      placeholder: "请选择",
      noData: "暂无数据"
    },
    pagination: {
      goto: "前往",
      pagesize: "条/页",
      total: "共 {total} 条",
      pageClassifier: "页",
      page: "页",
      prev: "上一页",
      next: "下一页",
      currentPage: "第 {pager} 页",
      prevPages: "向前 {pager} 页",
      nextPages: "向后 {pager} 页",
      deprecationWarning: "你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"
    },
    dialog: {
      close: "关闭此对话框"
    },
    drawer: {
      close: "关闭此对话框"
    },
    messagebox: {
      title: "提示",
      confirm: "确定",
      cancel: "取消",
      error: "输入的数据不合法!",
      close: "关闭此对话框"
    },
    upload: {
      deleteTip: "按 delete 键可删除",
      delete: "删除",
      preview: "查看图片",
      continue: "继续上传"
    },
    slider: {
      defaultLabel: "滑块介于 {min} 至 {max}",
      defaultRangeStartLabel: "选择起始值",
      defaultRangeEndLabel: "选择结束值"
    },
    table: {
      emptyText: "暂无数据",
      confirmFilter: "筛选",
      resetFilter: "重置",
      clearFilter: "全部",
      sumText: "合计"
    },
    tour: {
      next: "下一步",
      previous: "上一步",
      finish: "结束导览"
    },
    tree: {
      emptyText: "暂无数据"
    },
    transfer: {
      noMatch: "无匹配数据",
      noData: "无数据",
      titles: ["列表 1", "列表 2"],
      filterPlaceholder: "请输入搜索内容",
      noCheckedFormat: "共 {total} 项",
      hasCheckedFormat: "已选 {checked}/{total} 项"
    },
    image: {
      error: "加载失败"
    },
    pageHeader: {
      title: "返回"
    },
    popconfirm: {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    },
    carousel: {
      leftArrow: "上一张幻灯片",
      rightArrow: "下一张幻灯片",
      indicator: "幻灯片切换至索引 {index}"
    }
  }
};

// node_modules/element-plus/es/locale/lang/zh-tw.mjs
var zhTw = {
  name: "zh-tw",
  el: {
    breadcrumb: {
      label: "面包屑"
    },
    colorpicker: {
      confirm: "確認",
      clear: "清空",
      defaultLabel: "色彩選擇器",
      description: "目前色彩為 {color}。按一下 Enter 以選擇新色彩。",
      alphaLabel: "選擇透明度的值"
    },
    datepicker: {
      now: "現在",
      today: "今天",
      cancel: "取消",
      clear: "清空",
      confirm: "確認",
      dateTablePrompt: "使用方向鍵與 Enter 鍵以選擇日期",
      monthTablePrompt: "使用方向鍵與 Enter 鍵以選擇月份",
      yearTablePrompt: "使用方向鍵與 Enter 鍵以選擇年份",
      selectedDate: "已選日期",
      selectDate: "選擇日期",
      selectTime: "選擇時間",
      startDate: "開始日期",
      startTime: "開始時間",
      endDate: "結束日期",
      endTime: "結束時間",
      prevYear: "前一年",
      nextYear: "後一年",
      prevMonth: "上個月",
      nextMonth: "下個月",
      year: "年",
      month1: "1 月",
      month2: "2 月",
      month3: "3 月",
      month4: "4 月",
      month5: "5 月",
      month6: "6 月",
      month7: "7 月",
      month8: "8 月",
      month9: "9 月",
      month10: "10 月",
      month11: "11 月",
      month12: "12 月",
      weeks: {
        sun: "日",
        mon: "一",
        tue: "二",
        wed: "三",
        thu: "四",
        fri: "五",
        sat: "六"
      },
      weeksFull: {
        sun: "星期日",
        mon: "星期一",
        tue: "星期二",
        wed: "星期三",
        thu: "星期四",
        fri: "星期五",
        sat: "星期六"
      },
      months: {
        jan: "一月",
        feb: "二月",
        mar: "三月",
        apr: "四月",
        may: "五月",
        jun: "六月",
        jul: "七月",
        aug: "八月",
        sep: "九月",
        oct: "十月",
        nov: "十一月",
        dec: "十二月"
      }
    },
    inputNumber: {
      decrease: "減少數值",
      increase: "增加數值"
    },
    select: {
      loading: "載入中",
      noMatch: "無相符資料",
      noData: "無資料",
      placeholder: "請選擇"
    },
    mention: {
      loading: "載入中"
    },
    dropdown: {
      toggleDropdown: "切換下拉選單"
    },
    cascader: {
      noMatch: "無相符資料",
      loading: "載入中",
      placeholder: "請選擇",
      noData: "無資料"
    },
    pagination: {
      goto: "前往",
      pagesize: "項/頁",
      total: "共 {total} 項",
      pageClassifier: "頁",
      page: "頁",
      prev: "上一頁",
      next: "下一頁",
      currentPage: "第 {pager} 頁",
      prevPages: "向前 {pager} 頁",
      nextPages: "向後 {pager} 頁",
      deprecationWarning: "偵測到已過時的使用方式，請參閱 el-pagination 說明文件以了解更多資訊"
    },
    dialog: {
      close: "關閉此對話框"
    },
    drawer: {
      close: "關閉此對話框"
    },
    messagebox: {
      title: "提示",
      confirm: "確定",
      cancel: "取消",
      error: "輸入的資料不符規定!",
      close: "關閉此對話框"
    },
    upload: {
      deleteTip: "按一下 Delete 鍵以刪除",
      delete: "刪除",
      preview: "查看圖片",
      continue: "繼續上傳"
    },
    slider: {
      defaultLabel: "滑桿介於 {min} 至 {max}",
      defaultRangeStartLabel: "選擇起始值",
      defaultRangeEndLabel: "選擇結束值"
    },
    table: {
      emptyText: "暫無資料",
      confirmFilter: "篩選",
      resetFilter: "重置",
      clearFilter: "全部",
      sumText: "合計"
    },
    tour: {
      next: "下一步",
      previous: "上一步",
      finish: "結束導覽"
    },
    tree: {
      emptyText: "暫無資料"
    },
    transfer: {
      noMatch: "無相符資料",
      noData: "無資料",
      titles: ["列表 1", "列表 2"],
      filterPlaceholder: "請輸入搜尋內容",
      noCheckedFormat: "共 {total} 項",
      hasCheckedFormat: "已選 {checked}/{total} 項"
    },
    image: {
      error: "載入失敗"
    },
    pageHeader: {
      title: "返回"
    },
    popconfirm: {
      confirmButtonText: "確認",
      cancelButtonText: "取消"
    },
    carousel: {
      leftArrow: "上一張投影片",
      rightArrow: "下一張投影片",
      indicator: "投影片切換至索引 {index}"
    }
  }
};
export {
  af,
  ar,
  az,
  bg,
  bn,
  ca,
  cs,
  da,
  de,
  el,
  English as en,
  eo,
  es,
  et,
  eu,
  fa,
  fi,
  fr,
  he,
  hr,
  hu,
  hyAm,
  id,
  it,
  ja,
  kk,
  km,
  ko,
  ku,
  ky,
  lt,
  lv,
  mn,
  my,
  nbNo,
  nl,
  pa,
  pl,
  pt,
  ptBr,
  ro,
  ru,
  sk,
  sl,
  sr,
  sv,
  ta,
  th,
  tk,
  tr,
  ugCn,
  uk,
  uzUz,
  vi,
  zhCn,
  zhTw
};
//# sourceMappingURL=element-plus_es_locale_index__mjs.js.map
