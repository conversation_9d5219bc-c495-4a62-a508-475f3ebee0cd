<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>JSAPI Loader - ES6</title>
    <style>
        html, body, #container {
            height: 100%;
            width: 100%;
            margin: 0;
        }
    </style>
</head>
<body>
<div id="container" tabindex="0"></div>
<script type="module">
    import AMapLoader from '../src/index.js';
    AMapLoader.load({
        key:'',//必填
        version:'2.0',
        plugins:['AMap.Scale']
    }).then((AMap)=>{
        debugger
        var map = new AMap.Map('container');
        map.addControl(new AMap.Scale())
    }).catch((e)=>{
        console.error(e);
    });
</script>


</script>
</body>
</html>