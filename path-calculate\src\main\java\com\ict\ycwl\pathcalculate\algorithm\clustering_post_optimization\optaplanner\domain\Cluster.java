package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * OptaPlanner聚类实体
 * 
 * 表示一个聚类，用作规划变量的值域
 * 每个聚类都有ID、容量限制等属性
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Cluster {
    
    /**
     * 聚类唯一标识符
     */
    @EqualsAndHashCode.Include
    private String id;
    
    /**
     * 聚类名称
     */
    private String name;
    
    /**
     * 最大工作时间容量（分钟）
     */
    private double maxWorkTimeCapacity;
    
    /**
     * 聚类中心坐标（经度）
     */
    private Double centerLongitude;
    
    /**
     * 聚类中心坐标（纬度）
     */
    private Double centerLatitude;
    
    /**
     * 聚类权重（用于负载均衡）
     */
    private double weight;
    
    /**
     * 是否启用该聚类
     */
    private boolean enabled;
    
    /**
     * 创建默认聚类
     * 
     * @param id 聚类ID
     * @return 默认配置的聚类
     */
    public static Cluster createDefault(String id) {
        return new Cluster(
            id,
            "Cluster_" + id,
            450.0, // 450分钟最大工作时间
            null, // 中心坐标待计算
            null,
            1.0, // 默认权重
            true // 默认启用
        );
    }
    
    /**
     * 创建带坐标的聚类
     * 
     * @param id 聚类ID
     * @param centerLon 中心经度
     * @param centerLat 中心纬度
     * @return 配置了中心坐标的聚类
     */
    public static Cluster createWithCenter(String id, double centerLon, double centerLat) {
        Cluster cluster = createDefault(id);
        cluster.setCenterLongitude(centerLon);
        cluster.setCenterLatitude(centerLat);
        return cluster;
    }
    
    /**
     * 检查聚类是否有有效的中心坐标
     * 
     * @return true如果有有效坐标
     */
    public boolean hasValidCenter() {
        return centerLongitude != null && centerLatitude != null 
            && centerLongitude != 0.0 && centerLatitude != 0.0;
    }
    
    /**
     * 获取中心坐标数组
     * 
     * @return [经度, 纬度]数组，如果无效坐标则返回[0, 0]
     */
    public double[] getCenterCoordinates() {
        if (hasValidCenter()) {
            return new double[]{centerLongitude, centerLatitude};
        }
        return new double[]{0.0, 0.0};
    }
    
    /**
     * 设置中心坐标
     * 
     * @param longitude 经度
     * @param latitude 纬度
     */
    public void setCenterCoordinates(double longitude, double latitude) {
        this.centerLongitude = longitude;
        this.centerLatitude = latitude;
    }
    
    /**
     * 检查是否为有效聚类
     * 
     * @return true如果聚类有效且启用
     */
    public boolean isValid() {
        return enabled && id != null && !id.trim().isEmpty() 
            && maxWorkTimeCapacity > 0;
    }
    
    /**
     * 获取工作时间容量（向上取整到整数分钟）
     * 
     * @return 整数工作时间容量
     */
    public int getWorkTimeCapacityInt() {
        return (int) Math.ceil(maxWorkTimeCapacity);
    }
    
    /**
     * 复制聚类
     * 
     * @return 深拷贝的聚类对象
     */
    public Cluster copy() {
        return new Cluster(
            this.id,
            this.name,
            this.maxWorkTimeCapacity,
            this.centerLongitude,
            this.centerLatitude,
            this.weight,
            this.enabled
        );
    }
    
    @Override
    public String toString() {
        return String.format("Cluster{id='%s', name='%s', capacity=%.1f, center=[%.6f,%.6f], enabled=%s}",
            id, name, maxWorkTimeCapacity, 
            centerLongitude != null ? centerLongitude : 0.0,
            centerLatitude != null ? centerLatitude : 0.0,
            enabled);
    }
}