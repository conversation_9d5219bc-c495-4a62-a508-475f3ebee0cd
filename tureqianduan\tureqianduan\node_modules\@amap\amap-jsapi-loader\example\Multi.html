<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>JSAPI Loader</title>
    <style>
        html,
        body,
        #container {
            height: 100%;
            width: 100%;
            margin: 0;
        }
    </style>
</head>

<body>
    <div id="container" tabindex="0"></div>
    <script src="https://a.amap.com/Loca/static/mock/districts.js"></script>
    <script src="../dist/index.js"></script>
    <script>
        AMapLoader.load({
            key: '',//首次load必填
            version: '1.4.15',
            AMapUI: {
                plugins: ['overlay/SimpleMarker'],
            },
            Loca: {
                version: '1.3.2'
            }
        }).then((AMap) => {
            var map = new AMap.Map('container', {
                zoom: 4,
                center: [107.4976, 32.1697],
                features: ['bg', 'road'],
                // Loca 自 1.2.0 起 viewMode 模式默认为 3D，如需 2D 模式，请显示配置。
                // viewMode: '3D'
            });

            new AMapUI.SimpleMarker({

                //前景文字
                iconLabel: 'A',

                //图标主题
                iconTheme: 'default',

                //背景图标样式
                iconStyle: 'red',

                //...其他Marker选项...，不包括content
                map: map,
                position: map.getCenter()
            });
            var layer = new Loca.PointLayer({
                map: map
            });

            layer.setData(districts, {
                // 指定经纬度所在字段
                lnglat: 'center'
            });

            layer.setOptions({
                style: {
                    // 圆形半径，单位像素
                    radius: 5,
                    // 填充颜色
                    color: '#07E8E4',
                    // 描边颜色
                    borderColor: '#5DFBF9',
                    // 描边宽度，单位像素
                    borderWidth: 1,
                    // 透明度 [0-1]
                    opacity: 0.9,
                }
            });

            layer.render();

        }).catch((e) => {
            console.error(e);
        });

    </script>


    </script>
</body>

</html>