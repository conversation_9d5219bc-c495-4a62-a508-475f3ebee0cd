<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>JSAPI Loader</title>
    <style>
        html,
        body,
        #container {
            height: 100%;
            width: 100%;
            margin: 0;
        }
    </style>
</head>

<body>
    <div id="mocha"></div>

    <link rel="stylesheet" href="./libs/mocha.min.css" />
    <script src="./libs/chai.min.js"></script>
    <script src="./libs/mocha.min.js"></script>

    <script>
        mocha.setup('bdd');
        mocha.traceIgnores = ['mocha.min.js', 'chai.min.js'];
        expect = chai.expect;
    </script>
    <div id="container" tabindex="0"></div>

    <script src="../dist/index.js"></script>
    <script>
        // 请把 key 写在这里
        const JSAPIKey = '';
        const chai = window.chai;
        const mocha = window.mocha;
        mocha.setup({
            timeout: '5000',
            ui: 'bdd',
        });
        const expect = chai.expect;
        describe('JSAPI', () => {
            beforeEach(function () {
                AMapLoader.reset()
            });

            it('Simple', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '2.0',
                    plugins: ['AMap.Scale'],
                    AMapUI: {
                        plugins: ['overlay/SimpleMarker']
                    }
                }).then(() => {
                    expect(Boolean(window.AMap)).eq(true)
                    expect(Boolean(window.AMapUI)).eq(true)
                    expect(Boolean(window.AMapUI.SimpleMarker)).eq(true)
                    expect(Boolean(window.Loca)).eq(false)
                    done();
                });

            });
            it('Request with AMapUI Loca', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '1.4.15',
                    plugins: ['AMap.Scale'],
                    AMapUI: {
                        plugins: ['overlay/SimpleMarker']
                    },
                    Loca: {
                        version: '1.3.2'
                    }
                }).then(() => {
                    expect(Boolean(window.AMap)).eq(true)
                    expect(Boolean(window.AMapUI)).eq(true)
                    expect(Boolean(window.AMapUI.SimpleMarker)).eq(true)

                    expect(Boolean(window.Loca)).eq(true)
                    done();
                });

            });
            it('fail with multi version', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '2.0',
                }).then(() => {
                    done()
                });
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '1.4.15',
                }).then(() => {
                    done(Error('should not here'))
                })
                    .catch(err => {
                        try {
                            expect(err).eq('不允许多个版本 JSAPI 混用')

                        } catch (error) {
                            done(error)
                        }
                    })

            });
            it('fail with multi key', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '2.0',
                }).then(() => {
                    done()
                });
                AMapLoader.load({
                    key: 'asdfas',//首次load必填
                }).then(() => {
                    done(Error('should not here'))
                })
                    .catch(err => {
                        try {
                            expect(err).eq('多个不一致的 key')

                        } catch (error) {
                            done(error)
                        }
                    })

            });
        })


        describe('AMapUI', () => {
            beforeEach(function () {
                AMapLoader.reset()
            });

            it('version', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '1.4.15',
                    AMapUI: {
                        version: '1.1',
                        plugins: ['misc/PathSimplifier']
                    }
                }).then(() => {
                    expect(Boolean(window.AMap)).eq(true)
                    expect(Boolean(window.AMapUI)).eq(true)
                    expect(Boolean(window.AMapUI.PathSimplifier)).eq(true)
                    expect(Boolean(window.AMapUI.version.startsWith('1.1'))).eq(true)
                    done();
                }).catch(done);
            });

            it('async', (done) => {
                console.log(window.AMap);
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '1.4.15',
                    AMapUI: {
                        version: '1.0',
                        plugins: ['overlay/SimpleMarker']
                    }
                }).then(() => {
                    AMapLoader.load({
                        AMapUI: {
                            plugins: ['overlay/SimpleInfoWindow']
                        }
                    }).then(() => {
                        expect(Boolean(window.AMap)).eq(true)
                        expect(Boolean(window.AMapUI.version.startsWith('1.0'))).eq(true)
                        expect(Boolean(window.AMapUI.SimpleMarker)).eq(true)
                        expect(Boolean(window.AMapUI.SimpleInfoWindow)).eq(true)
                        done()
                    });
                }).catch(done);
            })
        });

        describe('Loca', () => {
            beforeEach(function () {
                AMapLoader.reset()
            });

            it('version', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '1.4.15',
                    Loca: {
                        version: '1.3.1'
                    }
                }).then(() => {
                    expect(Boolean(window.AMap)).eq(true)
                    expect(Boolean(window.Loca)).eq(true)
                    expect(window.Loca.version).eq('1.3.1')
                    done()
                }).catch(done);
            });
            it('fail with jsapi2.0', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '2.0',
                    Loca: {
                        version: '1.3.1'
                    }
                }).then(() => {
                    done(Error('should not here'))
                }).catch(err => {
                    try {
                        expect(err).eq("Loca 暂不适配 JSAPI 2.0,请使用 1.4.15")
                        done()
                    } catch (error) {
                        done(error)
                    }
                });
            });


            it('async', (done) => {
                AMapLoader.load({
                    key: JSAPIKey,//首次load必填
                    version: '1.4.15',
                    Loca: {
                        version: '1.2.1'
                    }
                }).then(() => {
                    expect(Boolean(window.AMap)).eq(true)
                    expect(Boolean(window.Loca)).eq(true)
                    expect(window.Loca.version).eq('1.2.1')
                    AMapLoader.load({
                        Loca: {
                            version: '1.2.1'
                        }
                    }).then(() => {
                        expect(Boolean(window.AMap)).eq(true)
                        expect(Boolean(window.Loca)).eq(true)
                        expect(window.Loca.version).eq('1.2.1')
                        done()
                    }).catch(done);

                });
            })
        });


        describe('Advance', () => {
            beforeEach(function () {
                AMapLoader.reset()
            });
            it('Multi', (done) => {
                AMapLoader.load({ key: JSAPIKey, version: '1.4.15' }).then(() => {
                    expect(Boolean(window.AMap)).eq(true);
                    expect(window.AMap.v).eq('1.4.15');

                    expect(Boolean(window.AMapUI)).eq(false);
                    expect(Boolean(window.Loca)).eq(false);
                    AMapLoader.load({ AMapUI: {} }).then(() => {
                        expect(Boolean(window.AMap)).eq(true);
                        expect(window.AMap.v).eq('1.4.15');

                        expect(Boolean(window.AMapUI)).eq(true);
                        expect(window.AMapUI.version.startsWith('1.1')).eq(true);
                        expect(Boolean(window.Loca)).eq(false);
                        AMapLoader.load({ Loca: {} }).then(() => {
                            expect(Boolean(window.AMap)).eq(true);
                            expect(window.AMap.v).eq('1.4.15');
                            expect(Boolean(window.AMapUI)).eq(true);
                            expect(window.AMapUI.version.startsWith('1.1')).eq(true);
                            expect(Boolean(window.Loca)).eq(true);
                            expect(window.Loca.version).eq('1.3.2');
                            done()
                        })
                    })
                }).catch(done)
            })
        })
        mocha.run()
    </script>


    </script>
</body>

</html>