package com.ict.datamanagement.domain.info;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeliveryInfo {
    //配送域id
    @ApiModelProperty(value = "配送域id",dataType = "int")
    private int deliveryId;
    //配送域名称
    @ApiModelProperty(value = "配送域名称",dataType = "String")
    private String deliveryName;
    //车辆数量
    @ApiModelProperty(value = "车辆数量",dataType = "int")
    private int carNumber;

    @ApiModelProperty(value = "路径数量",dataType = "int")
    //路径数量
    private int routeNumber;

    //中转站
    @ApiModelProperty(value = "中转站",dataType = "int")
    private TransitDepotInfo transitDepotInfo;
}
