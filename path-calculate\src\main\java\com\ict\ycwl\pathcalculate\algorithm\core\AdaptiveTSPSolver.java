package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * 自适应TSP求解器
 * 根据问题特征智能选择最佳求解算法
 */
@Slf4j
@Component
public class AdaptiveTSPSolver {
    
    /**
     * TSP算法枚举
     */
    public enum TSPAlgorithm {
        DYNAMIC_PROGRAMMING("动态规划", "精确算法，适用于小规模问题"),
        BRANCH_AND_BOUND("分支定界", "精确算法，适用于中等规模问题"),
        OR_TOOLS("OR-Tools", "高性能优化库，适用于大部分规模"),
        GENETIC_ALGORITHM("遗传算法", "元启发式算法，适用于大规模问题"),
        MULTI_OBJECTIVE("多目标优化", "多目标权衡优化"),
        GREEDY("贪心算法", "快速启发式算法"),
        HYBRID("混合策略", "多算法组合优化");
        
        private final String displayName;
        private final String description;
        
        TSPAlgorithm(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * 问题特征分析器
     */
    public static class ProblemCharacteristics {
        public int nodeCount;              // 节点数量
        public double avgDistance;         // 平均距离
        public double distanceVariance;    // 距离方差
        public double avgDeliveryTime;     // 平均配送时间
        public double deliveryTimeVariance; // 配送时间方差
        public double geographicSpread;    // 地理分布范围
        public double clusteringIndex;     // 聚类指数
        public boolean hasTimeConstraints; // 是否有时间约束
        public double dataQuality;         // 数据质量
        public String problemType;         // 问题类型
        
        @Override
        public String toString() {
            return String.format(
                "ProblemChar{nodes=%d, avgDist=%.2f, spread=%.2f, clustering=%.2f, type=%s}",
                nodeCount, avgDistance, geographicSpread, clusteringIndex, problemType
            );
        }
    }
    
    /**
     * 算法性能预测器
     */
    public static class AlgorithmPredictor {
        
        /**
         * 预测算法执行时间（秒）
         */
        public double predictExecutionTime(TSPAlgorithm algorithm, ProblemCharacteristics chars) {
            int n = chars.nodeCount;
            
            switch (algorithm) {
                case DYNAMIC_PROGRAMMING:
                    // O(2^n * n²) - 指数复杂度
                    return Math.pow(2, n) * n * n * 0.000001;
                    
                case BRANCH_AND_BOUND:
                    // 平均情况 O(1.5^n * n) - 取决于剪枝效果
                    double pruningFactor = calculatePruningFactor(chars);
                    return Math.pow(1.5 * pruningFactor, n) * n * 0.00001;
                    
                case OR_TOOLS:
                    // 近似 O(n * log n) 到 O(n²) - 取决于问题复杂度
                    if (n <= 50) {
                        return n * Math.log(n) * 0.001;
                    } else {
                        return n * n * 0.0001;
                    }
                    
                case GENETIC_ALGORITHM:
                    // O(g * p * n²) - 代数 * 种群 * 评估复杂度
                    int generations = Math.min(500, n * 10);
                    int population = Math.min(200, n * 4);
                    return generations * population * n * 0.00001;
                    
                case MULTI_OBJECTIVE:
                    // 多算法组合，取最慢的
                    return Math.max(
                        predictExecutionTime(TSPAlgorithm.OR_TOOLS, chars),
                        predictExecutionTime(TSPAlgorithm.GENETIC_ALGORITHM, chars)
                    ) * 1.5;
                    
                case GREEDY:
                    // O(n²)
                    return n * n * 0.000001;
                    
                case HYBRID:
                    // 多种算法的加权平均
                    return (predictExecutionTime(TSPAlgorithm.OR_TOOLS, chars) +
                           predictExecutionTime(TSPAlgorithm.GENETIC_ALGORITHM, chars)) * 0.7;
                    
                default:
                    return n * n * 0.00001;
            }
        }
        
        /**
         * 预测算法解质量（0-1，1为最优）
         */
        public double predictSolutionQuality(TSPAlgorithm algorithm, ProblemCharacteristics chars) {
            switch (algorithm) {
                case DYNAMIC_PROGRAMMING:
                    return 1.0; // 最优解
                    
                case BRANCH_AND_BOUND:
                    // 质量取决于时间限制和剪枝效果
                    double pruningEffectiveness = calculatePruningFactor(chars);
                    return Math.min(0.98, 0.85 + 0.13 * (1.0 - pruningEffectiveness));
                    
                case OR_TOOLS:
                    // OR-Tools通常能得到高质量解
                    if (chars.nodeCount <= 100) {
                        return 0.95 + 0.03 * Math.exp(-chars.nodeCount / 50.0);
                    } else {
                        return 0.90 + 0.05 * Math.exp(-chars.nodeCount / 100.0);
                    }
                    
                case GENETIC_ALGORITHM:
                    // 遗传算法质量与问题规模和复杂度相关
                    double baseQuality = 0.85;
                    double sizeBonus = Math.min(0.1, 50.0 / chars.nodeCount);
                    double complexityPenalty = chars.clusteringIndex > 0.7 ? 0.05 : 0.0;
                    return baseQuality + sizeBonus - complexityPenalty;
                    
                case MULTI_OBJECTIVE:
                    // 多目标优化通常能取得不错的平衡
                    return 0.88 + 0.07 * Math.exp(-chars.nodeCount / 30.0);
                    
                case GREEDY:
                    // 贪心算法质量较低，但与问题结构相关
                    return Math.max(0.60, 0.80 - chars.clusteringIndex * 0.2);
                    
                case HYBRID:
                    // 混合策略综合多种算法优势
                    return Math.min(0.95, 
                        Math.max(
                            predictSolutionQuality(TSPAlgorithm.OR_TOOLS, chars),
                            predictSolutionQuality(TSPAlgorithm.GENETIC_ALGORITHM, chars)
                        ) + 0.03);
                    
                default:
                    return 0.75;
            }
        }
        
        /**
         * 预测算法稳定性（0-1，1为最稳定）
         */
        public double predictStability(TSPAlgorithm algorithm, ProblemCharacteristics chars) {
            switch (algorithm) {
                case DYNAMIC_PROGRAMMING:
                case BRANCH_AND_BOUND:
                    return 1.0; // 确定性算法
                    
                case OR_TOOLS:
                    return 0.95; // 高度稳定
                    
                case GENETIC_ALGORITHM:
                    return 0.75; // 随机性算法，稳定性较低
                    
                case MULTI_OBJECTIVE:
                    return 0.85; // 较稳定
                    
                case GREEDY:
                    return 1.0; // 确定性算法
                    
                case HYBRID:
                    return 0.90; // 综合稳定性
                    
                default:
                    return 0.80;
            }
        }
        
        /**
         * 计算剪枝因子
         */
        private double calculatePruningFactor(ProblemCharacteristics chars) {
            // 地理分布越集中，剪枝效果越好
            double geographicFactor = 1.0 - chars.clusteringIndex;
            // 距离方差越小，剪枝效果越好
            double varianceFactor = Math.min(1.0, chars.distanceVariance / chars.avgDistance);
            return 0.3 + 0.7 * (geographicFactor + varianceFactor) / 2.0;
        }
    }
    
    private final BranchAndBoundTSP branchBoundSolver;
    private final EnhancedGeneticTSP geneticSolver;
    private final TSPSolver orToolsSolver;
    private final MultiObjectiveTSP multiObjectiveSolver;
    private final DynamicProgrammingTSP dpSolver; // 真正的动态规划求解器
    private final AlgorithmPredictor predictor;
    private final ExecutorService executor;
    
    /**
     * 无参构造器 - 用于向后兼容
     */
    public AdaptiveTSPSolver() {
        // 按依赖顺序创建算法实例
        this.dpSolver = new DynamicProgrammingTSP();
        this.geneticSolver = new EnhancedGeneticTSP();
        this.branchBoundSolver = new BranchAndBoundTSP();
        this.orToolsSolver = new ManualORToolsTSP();
        this.multiObjectiveSolver = new MultiObjectiveTSP(orToolsSolver, geneticSolver, branchBoundSolver);
        this.predictor = new AlgorithmPredictor();
        this.executor = ForkJoinPool.commonPool();
    }
    
    /**
     * 依赖注入构造器 - 用于Spring环境
     */
    public AdaptiveTSPSolver(BranchAndBoundTSP branchBoundSolver, 
                           EnhancedGeneticTSP geneticSolver,
                           TSPSolver orToolsSolver,
                           MultiObjectiveTSP multiObjectiveSolver) {
        this.dpSolver = new DynamicProgrammingTSP();
        this.branchBoundSolver = branchBoundSolver;
        this.geneticSolver = geneticSolver;
        this.orToolsSolver = orToolsSolver;
        this.multiObjectiveSolver = multiObjectiveSolver;
        this.predictor = new AlgorithmPredictor();
        this.executor = ForkJoinPool.commonPool();
    }
    
    /**
     * 自适应TSP求解
     */
    public List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix, long timeLimitMs, 
                           double qualityRequirement) {
        
        log.debug("开始自适应TSP求解，节点数: {}, 时间限制: {}ms, 质量要求: {}", 
                 cluster.size(), timeLimitMs, qualityRequirement);
        
        if (cluster.isEmpty()) {
            return new ArrayList<>();
        }
        
        if (cluster.size() == 1) {
            return Arrays.asList(cluster.get(0).getAccumulationId());
        }
        
        // 1. 分析问题特征
        ProblemCharacteristics characteristics = analyzeProblemCharacteristics(depot, cluster, timeMatrix);
        log.debug("问题特征: {}", characteristics);
        
        // 2. 选择最佳算法
        TSPAlgorithm bestAlgorithm = selectBestAlgorithm(characteristics, timeLimitMs, qualityRequirement);
        log.debug("选择算法: {}", bestAlgorithm.getDisplayName());
        
        // 3. 执行算法
        List<Long> result = executeAlgorithm(bestAlgorithm, depot, cluster, timeMatrix, timeLimitMs);
        
        // 4. 质量检查和降级处理
        if (result.isEmpty() || !isQualityAcceptable(result, depot, cluster, timeMatrix, qualityRequirement)) {
            log.warn("主算法结果不满意，尝试降级策略");
            result = fallbackStrategy(depot, cluster, timeMatrix, timeLimitMs, bestAlgorithm);
        }
        
        log.debug("自适应求解完成，解长度: {}", result.size());
        return result;
    }
    
    /**
     * 混合策略求解
     */
    public List<Long> solveWithHybridStrategy(TransitDepot depot, List<Accumulation> cluster, 
                                            Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        log.debug("开始混合策略TSP求解");
        
        ProblemCharacteristics chars = analyzeProblemCharacteristics(depot, cluster, timeMatrix);
        int nodeCount = chars.nodeCount;
        
        if (nodeCount <= 10) {
            // 小规模：直接使用动态规划
            return solveTSPWithDP(depot, cluster, timeMatrix);
            
        } else if (nodeCount <= 25) {
            // 中等规模：并行尝试多种算法
            return parallelMultiAlgorithmSolve(depot, cluster, timeMatrix, timeLimitMs);
            
        } else if (nodeCount <= 100) {
            // 大规模：遗传算法 + 局部搜索
            List<Long> geneticResult = geneticSolver.solve(depot, cluster, timeMatrix);
            return improveWithLocalSearch(geneticResult, depot, cluster, timeMatrix, timeLimitMs / 4);
            
        } else {
            // 特大规模：分治 + 启发式
            return solveWithDivideAndConquer(depot, cluster, timeMatrix, timeLimitMs);
        }
    }
    
    /**
     * 分析问题特征
     */
    private ProblemCharacteristics analyzeProblemCharacteristics(TransitDepot depot, 
                                                               List<Accumulation> cluster, 
                                                               Map<String, TimeInfo> timeMatrix) {
        
        ProblemCharacteristics chars = new ProblemCharacteristics();
        chars.nodeCount = cluster.size();
        
        if (cluster.isEmpty()) return chars;
        
        // 计算距离统计
        List<Double> distances = new ArrayList<>();
        List<Double> deliveryTimes = new ArrayList<>();
        
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                double distance = calculateDistance(
                    cluster.get(i).getCoordinate(), 
                    cluster.get(j).getCoordinate(), 
                    timeMatrix
                );
                distances.add(distance);
            }
            deliveryTimes.add(cluster.get(i).getDeliveryTime());
        }
        
        // 距离统计
        chars.avgDistance = distances.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        chars.distanceVariance = calculateVariance(distances, chars.avgDistance);
        
        // 配送时间统计
        chars.avgDeliveryTime = deliveryTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        chars.deliveryTimeVariance = calculateVariance(deliveryTimes, chars.avgDeliveryTime);
        
        // 地理分布范围
        chars.geographicSpread = calculateGeographicSpread(cluster);
        
        // 聚类指数
        chars.clusteringIndex = calculateClusteringIndex(cluster);
        
        // 时间约束检查
        chars.hasTimeConstraints = deliveryTimes.stream().anyMatch(t -> t > 0);
        
        // 数据质量评估
        chars.dataQuality = assessDataQuality(timeMatrix, distances);
        
        // 问题类型识别
        chars.problemType = identifyProblemType(chars);
        
        return chars;
    }
    
    /**
     * 选择最佳算法
     */
    private TSPAlgorithm selectBestAlgorithm(ProblemCharacteristics chars, long timeLimitMs, 
                                           double qualityRequirement) {
        
        double timeLimit = timeLimitMs / 1000.0; // 转换为秒
        TSPAlgorithm bestAlgorithm = TSPAlgorithm.GREEDY;
        double bestScore = Double.MIN_VALUE;
        
        // 评估所有可用算法
        for (TSPAlgorithm algorithm : TSPAlgorithm.values()) {
            if (!isAlgorithmApplicable(algorithm, chars)) continue;
            
            double predictedTime = predictor.predictExecutionTime(algorithm, chars);
            double predictedQuality = predictor.predictSolutionQuality(algorithm, chars);
            double stability = predictor.predictStability(algorithm, chars);
            
            // 时间约束检查
            if (predictedTime > timeLimit * 1.2) continue; // 20%时间余量
            
            // 质量要求检查
            if (predictedQuality < qualityRequirement) continue;
            
            // 综合评分：质量50% + 速度30% + 稳定性20%
            double timeScore = Math.min(1.0, timeLimit / Math.max(predictedTime, 0.1));
            double score = 0.5 * predictedQuality + 0.3 * timeScore + 0.2 * stability;
            
            if (score > bestScore) {
                bestScore = score;
                bestAlgorithm = algorithm;
            }
        }
        
        return bestAlgorithm;
    }
    
    /**
     * 执行选定的算法
     */
    private List<Long> executeAlgorithm(TSPAlgorithm algorithm, TransitDepot depot, 
                                       List<Accumulation> cluster, Map<String, TimeInfo> timeMatrix, 
                                       long timeLimitMs) {
        
        try {
            switch (algorithm) {
                case DYNAMIC_PROGRAMMING:
                    return solveTSPWithDP(depot, cluster, timeMatrix);
                    
                case BRANCH_AND_BOUND:
                    return branchBoundSolver.solve(depot, cluster, timeMatrix, timeLimitMs);
                    
                case OR_TOOLS:
                    return orToolsSolver.solve(depot, cluster, timeMatrix, timeLimitMs);
                    
                case GENETIC_ALGORITHM:
                    return geneticSolver.solve(depot, cluster, timeMatrix);
                    
                case MULTI_OBJECTIVE:
                    return multiObjectiveSolver.solve(depot, cluster, timeMatrix, 
                                                    MultiObjectiveTSP.OptimizationGoal.BALANCED);
                    
                case GREEDY:
                    return solveTSPWithGreedy(depot, cluster, timeMatrix);
                    
                case HYBRID:
                    return solveWithHybridStrategy(depot, cluster, timeMatrix, timeLimitMs);
                    
                default:
                    return solveTSPWithGreedy(depot, cluster, timeMatrix);
            }
        } catch (Exception e) {
            log.error("算法执行失败: {} - {}", algorithm.getDisplayName(), e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 并行多算法求解
     */
    private List<Long> parallelMultiAlgorithmSolve(TransitDepot depot, List<Accumulation> cluster, 
                                                  Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        List<CompletableFuture<AlgorithmResult>> futures = new ArrayList<>();
        
        // 启动多个算法并行运行
        futures.add(CompletableFuture.supplyAsync(() -> {
            try {
                List<Long> result = branchBoundSolver.solve(depot, cluster, timeMatrix, timeLimitMs / 2);
                double cost = calculateSolutionCost(result, depot, cluster, timeMatrix);
                return new AlgorithmResult(TSPAlgorithm.BRANCH_AND_BOUND, result, cost);
            } catch (Exception e) {
                return new AlgorithmResult(TSPAlgorithm.BRANCH_AND_BOUND, new ArrayList<>(), Double.MAX_VALUE);
            }
        }, executor));
        
        if (orToolsSolver.isORToolsAvailable()) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    List<Long> result = orToolsSolver.solve(depot, cluster, timeMatrix, timeLimitMs / 2);
                    double cost = calculateSolutionCost(result, depot, cluster, timeMatrix);
                    return new AlgorithmResult(TSPAlgorithm.OR_TOOLS, result, cost);
                } catch (Exception e) {
                    return new AlgorithmResult(TSPAlgorithm.OR_TOOLS, new ArrayList<>(), Double.MAX_VALUE);
                }
            }, executor));
        }
        
        futures.add(CompletableFuture.supplyAsync(() -> {
            try {
                List<Long> result = geneticSolver.solve(depot, cluster, timeMatrix);
                double cost = calculateSolutionCost(result, depot, cluster, timeMatrix);
                return new AlgorithmResult(TSPAlgorithm.GENETIC_ALGORITHM, result, cost);
            } catch (Exception e) {
                return new AlgorithmResult(TSPAlgorithm.GENETIC_ALGORITHM, new ArrayList<>(), Double.MAX_VALUE);
            }
        }, executor));
        
        // 收集结果
        List<AlgorithmResult> results = futures.stream()
                .map(CompletableFuture::join)
                .filter(result -> !result.solution.isEmpty())
                .collect(Collectors.toList());
        
        // 选择最优结果
        return results.stream()
                .min(Comparator.comparingDouble(r -> r.cost))
                .map(r -> r.solution)
                .orElse(solveTSPWithGreedy(depot, cluster, timeMatrix));
    }
    
    /**
     * 分治求解（特大规模）
     */
    private List<Long> solveWithDivideAndConquer(TransitDepot depot, List<Accumulation> cluster, 
                                                Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        // 如果规模不大，直接用遗传算法
        if (cluster.size() <= 200) {
            return geneticSolver.solve(depot, cluster, timeMatrix);
        }
        
        // 地理分区
        List<List<Accumulation>> subClusters = geographicPartition(cluster, 4);
        List<List<Long>> subSolutions = new ArrayList<>();
        
        // 并行求解子问题
        List<CompletableFuture<List<Long>>> subFutures = subClusters.stream()
                .map(subCluster -> CompletableFuture.supplyAsync(() -> 
                    geneticSolver.solve(depot, subCluster, timeMatrix), executor))
                .collect(Collectors.toList());
        
        // 收集子解
        for (CompletableFuture<List<Long>> future : subFutures) {
            List<Long> subSolution = future.join();
            if (!subSolution.isEmpty()) {
                subSolutions.add(subSolution);
            }
        }
        
        // 合并子解
        return mergeSubSolutions(subSolutions, depot, cluster, timeMatrix);
    }
    
    /**
     * 降级策略
     */
    private List<Long> fallbackStrategy(TransitDepot depot, List<Accumulation> cluster, 
                                       Map<String, TimeInfo> timeMatrix, long timeLimitMs, 
                                       TSPAlgorithm failedAlgorithm) {
        
        // 选择不同的降级算法
        if (failedAlgorithm != TSPAlgorithm.GREEDY) {
            List<Long> greedyResult = solveTSPWithGreedy(depot, cluster, timeMatrix);
            if (!greedyResult.isEmpty()) {
                return greedyResult;
            }
        }
        
        if (failedAlgorithm != TSPAlgorithm.GENETIC_ALGORITHM && cluster.size() > 20) {
            try {
                return geneticSolver.solve(depot, cluster, timeMatrix);
            } catch (Exception e) {
                log.error("遗传算法降级失败: {}", e.getMessage());
            }
        }
        
        // 最后的降级：简单排序
        return cluster.stream()
                .map(Accumulation::getAccumulationId)
                .collect(Collectors.toList());
    }
    
    // 辅助类
    private static class AlgorithmResult {
        TSPAlgorithm algorithm;
        List<Long> solution;
        double cost;
        
        AlgorithmResult(TSPAlgorithm algorithm, List<Long> solution, double cost) {
            this.algorithm = algorithm;
            this.solution = solution;
            this.cost = cost;
        }
    }
    
    // 辅助方法
    private boolean isAlgorithmApplicable(TSPAlgorithm algorithm, ProblemCharacteristics chars) {
        switch (algorithm) {
            case DYNAMIC_PROGRAMMING:
                return chars.nodeCount <= 12;
            case BRANCH_AND_BOUND:
                return chars.nodeCount <= 25;
            case OR_TOOLS:
                return orToolsSolver.isORToolsAvailable();
            case GENETIC_ALGORITHM:
                return chars.nodeCount > 10;
            case MULTI_OBJECTIVE:
                return chars.nodeCount > 5;
            default:
                return true;
        }
    }
    
    private boolean isQualityAcceptable(List<Long> solution, TransitDepot depot, 
                                      List<Accumulation> cluster, Map<String, TimeInfo> timeMatrix, 
                                      double qualityRequirement) {
        
        if (solution.isEmpty() || solution.size() != cluster.size()) {
            return false;
        }
        
        // 简单的质量检查：解是否包含所有节点且无重复
        Set<Long> solutionSet = new HashSet<>(solution);
        Set<Long> expectedSet = cluster.stream()
                .map(Accumulation::getAccumulationId)
                .collect(Collectors.toSet());
        
        return solutionSet.equals(expectedSet);
    }
    
    private double calculateVariance(List<Double> values, double mean) {
        return values.stream()
                .mapToDouble(v -> Math.pow(v - mean, 2))
                .average()
                .orElse(0.0);
    }
    
    private double calculateGeographicSpread(List<Accumulation> cluster) {
        if (cluster.size() < 2) return 0.0;
        
        double minLat = cluster.stream().mapToDouble(acc -> acc.getCoordinate().getLatitude()).min().orElse(0.0);
        double maxLat = cluster.stream().mapToDouble(acc -> acc.getCoordinate().getLatitude()).max().orElse(0.0);
        double minLon = cluster.stream().mapToDouble(acc -> acc.getCoordinate().getLongitude()).min().orElse(0.0);
        double maxLon = cluster.stream().mapToDouble(acc -> acc.getCoordinate().getLongitude()).max().orElse(0.0);
        
        return Math.sqrt(Math.pow(maxLat - minLat, 2) + Math.pow(maxLon - minLon, 2));
    }
    
    private double calculateClusteringIndex(List<Accumulation> cluster) {
        // 简化的聚类指数计算
        if (cluster.size() < 3) return 0.0;
        
        double totalDistance = 0.0;
        int count = 0;
        
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                totalDistance += calculateEuclideanDistance(
                    cluster.get(i).getCoordinate(),
                    cluster.get(j).getCoordinate()
                );
                count++;
            }
        }
        
        double avgDistance = totalDistance / count;
        double spread = calculateGeographicSpread(cluster);
        
        return Math.min(1.0, avgDistance / Math.max(spread, 0.1));
    }
    
    private double assessDataQuality(Map<String, TimeInfo> timeMatrix, List<Double> distances) {
        if (timeMatrix.isEmpty()) return 0.5;
        
        // 检查时间矩阵的完整性
        int validEntries = (int) timeMatrix.values().stream()
                .filter(info -> info.getTravelTime() > 0)
                .count();
        
        double completeness = (double) validEntries / timeMatrix.size();
        
        // 检查数据一致性（简化）
        double consistency = distances.isEmpty() ? 0.5 : 
            Math.min(1.0, distances.stream().mapToDouble(d -> d > 0 ? 1.0 : 0.0).average().orElse(0.0));
        
        return (completeness + consistency) / 2.0;
    }
    
    private String identifyProblemType(ProblemCharacteristics chars) {
        if (chars.nodeCount <= 10) {
            return "SMALL";
        } else if (chars.nodeCount <= 50) {
            return "MEDIUM";
        } else if (chars.nodeCount <= 200) {
            return "LARGE";
        } else {
            return "XLARGE";
        }
    }
    
    private double calculateDistance(CoordinatePoint p1, CoordinatePoint p2, 
                                   Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                p1.getLongitude(), p1.getLatitude(),
                p2.getLongitude(), p2.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        if (timeInfo != null && timeInfo.getDistance() > 0) {
            return timeInfo.getDistance();
        }
        
        return calculateEuclideanDistance(p1, p2);
    }
    
    private double calculateEuclideanDistance(CoordinatePoint p1, CoordinatePoint p2) {
        double dLat = Math.toRadians(p2.getLatitude() - p1.getLatitude());
        double dLon = Math.toRadians(p2.getLongitude() - p1.getLongitude());
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(p1.getLatitude())) * Math.cos(Math.toRadians(p2.getLatitude())) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return 6371.0 * c;
    }
    
    private double calculateSolutionCost(List<Long> solution, TransitDepot depot, 
                                       List<Accumulation> cluster, Map<String, TimeInfo> timeMatrix) {
        if (solution.isEmpty()) return Double.MAX_VALUE;
        
        double totalCost = AlgorithmParameters.LOADING_TIME_MINUTES;
        CoordinatePoint currentPos = depot.getCoordinate();
        
        for (Long accId : solution) {
            Accumulation acc = cluster.stream()
                    .filter(a -> a.getAccumulationId().equals(accId))
                    .findFirst()
                    .orElse(null);
            
            if (acc != null) {
                totalCost += getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                totalCost += acc.getDeliveryTime();
                currentPos = acc.getCoordinate();
            }
        }
        
        totalCost += getTravelTime(currentPos, depot.getCoordinate(), timeMatrix);
        return totalCost;
    }
    
    private double getTravelTime(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null ? timeInfo.getTravelTime() : 
               calculateEuclideanDistance(from, to) / 50.0 * 60.0; // 估算时间
    }
    
    // 这些方法需要具体实现
    private List<Long> solveTSPWithDP(TransitDepot depot, List<Accumulation> cluster, 
                                    Map<String, TimeInfo> timeMatrix) {
        // 调用真正的Held-Karp动态规划算法
        return dpSolver.solve(depot, cluster, timeMatrix);
    }
    
    private List<Long> solveTSPWithGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                        Map<String, TimeInfo> timeMatrix) {
        // 调用现有的贪心算法实现
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation nearest = null;
            double minCost = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                double totalCost = travelTime + acc.getDeliveryTime();
                
                if (totalCost < minCost) {
                    minCost = totalCost;
                    nearest = acc;
                }
            }
            
            if (nearest != null) {
                sequence.add(nearest.getAccumulationId());
                visited.add(nearest.getAccumulationId());
                currentPos = nearest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    private List<Long> improveWithLocalSearch(List<Long> solution, TransitDepot depot, 
                                            List<Accumulation> cluster, Map<String, TimeInfo> timeMatrix, 
                                            long timeLimitMs) {
        // 简化的局部搜索实现
        return solution;
    }
    
    private List<List<Accumulation>> geographicPartition(List<Accumulation> cluster, int partitions) {
        // 简化的地理分区实现
        List<List<Accumulation>> result = new ArrayList<>();
        int partitionSize = cluster.size() / partitions;
        
        for (int i = 0; i < partitions; i++) {
            int start = i * partitionSize;
            int end = (i == partitions - 1) ? cluster.size() : (i + 1) * partitionSize;
            result.add(new ArrayList<>(cluster.subList(start, end)));
        }
        
        return result;
    }
    
    private List<Long> mergeSubSolutions(List<List<Long>> subSolutions, TransitDepot depot, 
                                       List<Accumulation> cluster, Map<String, TimeInfo> timeMatrix) {
        // 简化的解合并实现
        List<Long> merged = new ArrayList<>();
        for (List<Long> subSolution : subSolutions) {
            merged.addAll(subSolution);
        }
        return merged;
    }
}