!function(){function t(e){var n=t.resolve(e),r=t.modules[n];if(!r)throw new Error('failed to require "'+e+'"');return r.exports||(r.exports={},r.call(r.exports,r,r.exports,t.relative(n))),r.exports}function e(){for(var t=(new r).getTime();f.length&&(new r).getTime()-t<100;)f.shift()();l=f.length?o(e,0):null}t.modules={},t.resolve=function(e){var n=e,r=e+".js",o=e+"/index.js";return t.modules[r]&&r||t.modules[o]&&o||n},t.register=function(e,n){t.modules[e]=n},t.relative=function(e){return function(n){if("."!=n.charAt(0))return t(n);var r=e.split("/"),o=n.split("/");r.pop();for(var i=0;i<o.length;i++){var s=o[i];".."==s?r.pop():"."!=s&&r.push(s)}return t(r.join("/"))}},t.register("browser/debug.js",function(t){t.exports=function(){return function(){}}}),t.register("browser/diff.js",function(t){var e=function(){function t(t){return{newPos:t.newPos,components:t.components.slice(0)}}function e(t){for(var e=[],n=0;n<t.length;n++)t[n]&&e.push(t[n]);return e}function n(t){var e=t;return e=e.replace(/&/g,"&amp;"),e=e.replace(/</g,"&lt;"),e=e.replace(/>/g,"&gt;"),e=e.replace(/"/g,"&quot;")}var r=function(t){this.ignoreWhitespace=t};r.prototype={diff:function(e,n){if(n===e)return[{value:n}];if(!n)return[{value:e,removed:!0}];if(!e)return[{value:n,added:!0}];n=this.tokenize(n),e=this.tokenize(e);var r=n.length,o=e.length,i=r+o,s=[{newPos:-1,components:[]}],a=this.extractCommon(s[0],n,e,0);if(s[0].newPos+1>=r&&a+1>=o)return s[0].components;for(var u=1;i>=u;u++)for(var c=-1*u;u>=c;c+=2){var l,f=s[c-1],p=s[c+1];a=(p?p.newPos:0)-c,f&&(s[c-1]=void 0);var h=f&&f.newPos+1<r,d=p&&a>=0&&o>a;if(h||d){!h||d&&f.newPos<p.newPos?(l=t(p),this.pushComponent(l.components,e[a],void 0,!0)):(l=t(f),l.newPos++,this.pushComponent(l.components,n[l.newPos],!0,void 0));var a=this.extractCommon(l,n,e,c);if(l.newPos+1>=r&&a+1>=o)return l.components;s[c]=l}else s[c]=void 0}},pushComponent:function(t,e,n,r){var o=t[t.length-1];o&&o.added===n&&o.removed===r?t[t.length-1]={value:this.join(o.value,e),added:n,removed:r}:t.push({value:e,added:n,removed:r})},extractCommon:function(t,e,n,r){for(var o=e.length,i=n.length,s=t.newPos,a=s-r;o>s+1&&i>a+1&&this.equals(e[s+1],n[a+1]);)s++,a++,this.pushComponent(t.components,e[s],void 0,void 0);return t.newPos=s,a},equals:function(t,e){var n=/\S/;return!this.ignoreWhitespace||n.test(t)||n.test(e)?t===e:!0},join:function(t,e){return t+e},tokenize:function(t){return t}};var o=new r,i=new r(!0),s=new r;i.tokenize=s.tokenize=function(t){return e(t.split(/(\s+|\b)/))};var a=new r(!0);a.tokenize=function(t){return e(t.split(/([{}:;,]|\s+)/))};var u=new r;return u.tokenize=function(t){for(var e=[],n=t.split(/^/m),r=0;r<n.length;r++){var o=n[r],i=n[r-1];"\n"==o&&i&&"\r"===i[i.length-1]?e[e.length-1]+="\n":o&&e.push(o)}return e},{Diff:r,diffChars:function(t,e){return o.diff(t,e)},diffWords:function(t,e){return i.diff(t,e)},diffWordsWithSpace:function(t,e){return s.diff(t,e)},diffLines:function(t,e){return u.diff(t,e)},diffCss:function(t,e){return a.diff(t,e)},createPatch:function(t,e,n,r,o){function i(t){return t.map(function(t){return" "+t})}function s(t,e,n){var r=c[c.length-2],o=e===c.length-2,i=e===c.length-3&&(n.added!==r.added||n.removed!==r.removed);/\n$/.test(n.value)||!o&&!i||t.push("\\ No newline at end of file")}var a=[];a.push("Index: "+t),a.push("==================================================================="),a.push("--- "+t+("undefined"==typeof r?"":"	"+r)),a.push("+++ "+t+("undefined"==typeof o?"":"	"+o));var c=u.diff(e,n);c[c.length-1].value||c.pop(),c.push({value:"",lines:[]});for(var l=0,f=0,p=[],h=1,d=1,g=0;g<c.length;g++){var m=c[g],v=m.lines||m.value.replace(/\n$/,"").split("\n");if(m.lines=v,m.added||m.removed){if(!l){var y=c[g-1];l=h,f=d,y&&(p=i(y.lines.slice(-4)),l-=p.length,f-=p.length)}p.push.apply(p,v.map(function(t){return(m.added?"+":"-")+t})),s(p,g,m),m.added?d+=v.length:h+=v.length}else{if(l)if(v.length<=8&&g<c.length-2)p.push.apply(p,i(v));else{var w=Math.min(v.length,4);a.push("@@ -"+l+","+(h-l+w)+" +"+f+","+(d-f+w)+" @@"),a.push.apply(a,p),a.push.apply(a,i(v.slice(0,w))),v.length<=4&&s(a,g,m),l=0,f=0,p=[]}h+=v.length,d+=v.length}}return a.join("\n")+"\n"},applyPatch:function(t,e){for(var n=e.split("\n"),r=[],o=!1,i=!1,s="I"===n[0][0]?4:0;s<n.length;s++)if("@"===n[s][0]){var a=n[s].split(/@@ -(\d+),(\d+) \+(\d+),(\d+) @@/);r.unshift({start:a[3],oldlength:a[2],oldlines:[],newlength:a[4],newlines:[]})}else"+"===n[s][0]?r[0].newlines.push(n[s].substr(1)):"-"===n[s][0]?r[0].oldlines.push(n[s].substr(1)):" "===n[s][0]?(r[0].newlines.push(n[s].substr(1)),r[0].oldlines.push(n[s].substr(1))):"\\"===n[s][0]&&("+"===n[s-1][0]?o=!0:"-"===n[s-1][0]&&(i=!0));for(var u=t.split("\n"),s=r.length-1;s>=0;s--){for(var c=r[s],l=0;l<c.oldlength;l++)if(u[c.start-1+l]!==c.oldlines[l])return!1;Array.prototype.splice.apply(u,[c.start-1,+c.oldlength].concat(c.newlines))}if(o)for(;!u[u.length-1];)u.pop();else i&&u.push("");return u.join("\n")},convertChangesToXML:function(t){for(var e=[],r=0;r<t.length;r++){var o=t[r];o.added?e.push("<ins>"):o.removed&&e.push("<del>"),e.push(n(o.value)),o.added?e.push("</ins>"):o.removed&&e.push("</del>")}return e.join("")},convertChangesToDMP:function(t){for(var e,n=[],r=0;r<t.length;r++)e=t[r],n.push([e.added?1:e.removed?-1:0,e.value]);return n}}}();"undefined"!=typeof t&&(t.exports=e)}),t.register("browser/escape-string-regexp.js",function(t){"use strict";var e=/[|\\{}()[\]^$+*?.]/g;t.exports=function(t){if("string"!=typeof t)throw new TypeError("Expected a string");return t.replace(e,"\\$&")}}),t.register("browser/events.js",function(t,e){function n(t){return"[object Array]"=={}.toString.call(t)}function r(){}e.EventEmitter=r,r.prototype.on=function(t,e){return this.$events||(this.$events={}),this.$events[t]?n(this.$events[t])?this.$events[t].push(e):this.$events[t]=[this.$events[t],e]:this.$events[t]=e,this},r.prototype.addListener=r.prototype.on,r.prototype.once=function(t,e){function n(){r.removeListener(t,n),e.apply(this,arguments)}var r=this;return n.listener=e,this.on(t,n),this},r.prototype.removeListener=function(t,e){if(this.$events&&this.$events[t]){var r=this.$events[t];if(n(r)){for(var o=-1,i=0,s=r.length;s>i;i++)if(r[i]===e||r[i].listener&&r[i].listener===e){o=i;break}if(0>o)return this;r.splice(o,1),r.length||delete this.$events[t]}else(r===e||r.listener&&r.listener===e)&&delete this.$events[t]}return this},r.prototype.removeAllListeners=function(t){return void 0===t?(this.$events={},this):(this.$events&&this.$events[t]&&(this.$events[t]=null),this)},r.prototype.listeners=function(t){return this.$events||(this.$events={}),this.$events[t]||(this.$events[t]=[]),n(this.$events[t])||(this.$events[t]=[this.$events[t]]),this.$events[t]},r.prototype.emit=function(t){if(!this.$events)return!1;var e=this.$events[t];if(!e)return!1;var r=[].slice.call(arguments,1);if("function"==typeof e)e.apply(this,r);else{if(!n(e))return!1;for(var o=e.slice(),i=0,s=o.length;s>i;i++)o[i].apply(this,r)}return!0}}),t.register("browser/fs.js",function(){}),t.register("browser/glob.js",function(){}),t.register("browser/path.js",function(){}),t.register("browser/progress.js",function(t){function e(){this.percent=0,this.size(0),this.fontSize(11),this.font("helvetica, arial, sans-serif")}t.exports=e,e.prototype.size=function(t){return this._size=t,this},e.prototype.text=function(t){return this._text=t,this},e.prototype.fontSize=function(t){return this._fontSize=t,this},e.prototype.font=function(t){return this._font=t,this},e.prototype.update=function(t){return this.percent=t,this},e.prototype.draw=function(t){try{var e=Math.min(this.percent,100),n=this._size,r=n/2,o=r,i=r,s=r-1,a=this._fontSize;t.font=a+"px "+this._font;var u=2*Math.PI*(e/100);t.clearRect(0,0,n,n),t.strokeStyle="#9f9f9f",t.beginPath(),t.arc(o,i,s,0,u,!1),t.stroke(),t.strokeStyle="#eee",t.beginPath(),t.arc(o,i,s-1,0,u,!0),t.stroke();var c=this._text||(0|e)+"%",l=t.measureText(c).width;t.fillText(c,o-l/2+1,i+a/2-1)}catch(f){}return this}}),t.register("browser/tty.js",function(t,e){e.isatty=function(){return!0},e.getWindowSize=function(){return"innerHeight"in n?[n.innerHeight,n.innerWidth]:[640,480]}}),t.register("context.js",function(t){function e(){}t.exports=e,e.prototype.runnable=function(t){return 0==arguments.length?this._runnable:(this.test=this._runnable=t,this)},e.prototype.timeout=function(t){return 0===arguments.length?this.runnable().timeout():(this.runnable().timeout(t),this)},e.prototype.enableTimeouts=function(t){return this.runnable().enableTimeouts(t),this},e.prototype.slow=function(t){return this.runnable().slow(t),this},e.prototype.inspect=function(){return JSON.stringify(this,function(t,e){return"_runnable"!=t&&"test"!=t?e:void 0},2)}}),t.register("hook.js",function(t,e,n){function r(t,e){i.call(this,t,e),this.type="hook"}function o(){}var i=n("./runnable");t.exports=r,o.prototype=i.prototype,r.prototype=new o,r.prototype.constructor=r,r.prototype.error=function(t){if(0==arguments.length){var t=this._error;return this._error=null,t}this._error=t}}),t.register("interfaces/bdd.js",function(t,e,n){var r=n("../suite"),o=n("../test"),i=(n("../utils"),n("browser/escape-string-regexp"));t.exports=function(t){var e=[t];t.on("pre-require",function(t,n,s){t.before=function(t,n){e[0].beforeAll(t,n)},t.after=function(t,n){e[0].afterAll(t,n)},t.beforeEach=function(t,n){e[0].beforeEach(t,n)},t.afterEach=function(t,n){e[0].afterEach(t,n)},t.describe=t.context=function(t,o){var i=r.create(e[0],t);return i.file=n,e.unshift(i),o.call(i),e.shift(),i},t.xdescribe=t.xcontext=t.describe.skip=function(t,n){var o=r.create(e[0],t);o.pending=!0,e.unshift(o),n.call(o),e.shift()},t.describe.only=function(e,n){var r=t.describe(e,n);return s.grep(r.fullTitle()),r},t.it=t.specify=function(t,r){var i=e[0];i.pending&&(r=null);var s=new o(t,r);return s.file=n,i.addTest(s),s},t.it.only=function(e,n){var r=t.it(e,n),o="^"+i(r.fullTitle())+"$";return s.grep(new RegExp(o)),r},t.xit=t.xspecify=t.it.skip=function(e){t.it(e)}})}}),t.register("interfaces/exports.js",function(t,e,n){var r=n("../suite"),o=n("../test");t.exports=function(t){function e(t,i){var s;for(var a in t)if("function"==typeof t[a]){var u=t[a];switch(a){case"before":n[0].beforeAll(u);break;case"after":n[0].afterAll(u);break;case"beforeEach":n[0].beforeEach(u);break;case"afterEach":n[0].afterEach(u);break;default:var c=new o(a,u);c.file=i,n[0].addTest(c)}}else s=r.create(n[0],a),n.unshift(s),e(t[a]),n.shift()}var n=[t];t.on("require",e)}}),t.register("interfaces/index.js",function(t,e,n){e.bdd=n("./bdd"),e.tdd=n("./tdd"),e.qunit=n("./qunit"),e.exports=n("./exports")}),t.register("interfaces/qunit.js",function(t,e,n){{var r=n("../suite"),o=n("../test"),i=n("browser/escape-string-regexp");n("../utils")}t.exports=function(t){var e=[t];t.on("pre-require",function(t,n,s){t.before=function(t,n){e[0].beforeAll(t,n)},t.after=function(t,n){e[0].afterAll(t,n)},t.beforeEach=function(t,n){e[0].beforeEach(t,n)},t.afterEach=function(t,n){e[0].afterEach(t,n)},t.suite=function(t){e.length>1&&e.shift();var o=r.create(e[0],t);return o.file=n,e.unshift(o),o},t.suite.only=function(e,n){var r=t.suite(e,n);s.grep(r.fullTitle())},t.test=function(t,r){var i=new o(t,r);return i.file=n,e[0].addTest(i),i},t.test.only=function(e,n){var r=t.test(e,n),o="^"+i(r.fullTitle())+"$";s.grep(new RegExp(o))},t.test.skip=function(e){t.test(e)}})}}),t.register("interfaces/tdd.js",function(t,e,n){{var r=n("../suite"),o=n("../test"),i=n("browser/escape-string-regexp");n("../utils")}t.exports=function(t){var e=[t];t.on("pre-require",function(t,n,s){t.setup=function(t,n){e[0].beforeEach(t,n)},t.teardown=function(t,n){e[0].afterEach(t,n)},t.suiteSetup=function(t,n){e[0].beforeAll(t,n)},t.suiteTeardown=function(t,n){e[0].afterAll(t,n)},t.suite=function(t,o){var i=r.create(e[0],t);return i.file=n,e.unshift(i),o.call(i),e.shift(),i},t.suite.skip=function(t,n){var o=r.create(e[0],t);o.pending=!0,e.unshift(o),n.call(o),e.shift()},t.suite.only=function(e,n){var r=t.suite(e,n);s.grep(r.fullTitle())},t.test=function(t,r){var i=e[0];i.pending&&(r=null);var s=new o(t,r);return s.file=n,i.addTest(s),s},t.test.only=function(e,n){var r=t.test(e,n),o="^"+i(r.fullTitle())+"$";s.grep(new RegExp(o))},t.test.skip=function(e){t.test(e)}})}}),t.register("mocha.js",function(t,e,r){function o(t){return __dirname+"/../images/"+t+".png"}function s(t){t=t||{},this.files=[],this.options=t,this.grep(t.grep),this.suite=new e.Suite("",new e.Context),this.ui(t.ui),this.bail(t.bail),this.reporter(t.reporter,t.reporterOptions),null!=t.timeout&&this.timeout(t.timeout),this.useColors(t.useColors),null!==t.enableTimeouts&&this.enableTimeouts(t.enableTimeouts),t.slow&&this.slow(t.slow),this.suite.on("pre-require",function(t){e.afterEach=t.afterEach||t.teardown,e.after=t.after||t.suiteTeardown,e.beforeEach=t.beforeEach||t.setup,e.before=t.before||t.suiteSetup,e.describe=t.describe||t.suite,e.it=t.it||t.test,e.setup=t.setup||t.beforeEach,e.suiteSetup=t.suiteSetup||t.before,e.suiteTeardown=t.suiteTeardown||t.after,e.suite=t.suite||t.describe,e.teardown=t.teardown||t.afterEach,e.test=t.test||t.it})}var a=r("browser/path"),u=r("browser/escape-string-regexp"),c=r("./utils");if(e=t.exports=s,"undefined"!=typeof i&&"function"==typeof i.cwd){var l=a.join,f=i.cwd();t.paths.push(f,l(f,"node_modules"))}e.utils=c,e.interfaces=r("./interfaces"),e.reporters=r("./reporters"),e.Runnable=r("./runnable"),e.Context=r("./context"),e.Runner=r("./runner"),e.Suite=r("./suite"),e.Hook=r("./hook"),e.Test=r("./test"),s.prototype.bail=function(t){return 0==arguments.length&&(t=!0),this.suite.bail(t),this},s.prototype.addFile=function(t){return this.files.push(t),this},s.prototype.reporter=function(t,e){if("function"==typeof t)this._reporter=t;else{t=t||"spec";var n;try{n=r("./reporters/"+t)}catch(o){}if(!n)try{n=r(t)}catch(o){}if(n||"teamcity"!==t||console.warn("The Teamcity reporter was moved to a package named mocha-teamcity-reporter (https://npmjs.org/package/mocha-teamcity-reporter)."),!n)throw new Error('invalid reporter "'+t+'"');this._reporter=n}return this.options.reporterOptions=e,this},s.prototype.ui=function(t){if(t=t||"bdd",this._ui=e.interfaces[t],!this._ui)try{this._ui=r(t)}catch(n){}if(!this._ui)throw new Error('invalid interface "'+t+'"');return this._ui=this._ui(this.suite),this},s.prototype.loadFiles=function(t){var e=this,o=this.suite,i=this.files.length;this.files.forEach(function(s){s=a.resolve(s),o.emit("pre-require",n,s,e),o.emit("require",r(s),s,e),o.emit("post-require",n,s,e),--i||t&&t()})},s.prototype._growl=function(t,e){var n=r("growl");t.on("end",function(){var r=e.stats;if(r.failures){var i=r.failures+" of "+t.total+" tests failed";n(i,{name:"mocha",title:"Failed",image:o("error")})}else n(r.passes+" tests passed in "+r.duration+"ms",{name:"mocha",title:"Passed",image:o("ok")})})},s.prototype.grep=function(t){return this.options.grep="string"==typeof t?new RegExp(u(t)):t,this},s.prototype.invert=function(){return this.options.invert=!0,this},s.prototype.ignoreLeaks=function(t){return this.options.ignoreLeaks=!!t,this},s.prototype.checkLeaks=function(){return this.options.ignoreLeaks=!1,this},s.prototype.growl=function(){return this.options.growl=!0,this},s.prototype.globals=function(t){return this.options.globals=(this.options.globals||[]).concat(t),this},s.prototype.useColors=function(t){return void 0!==t&&(this.options.useColors=t),this},s.prototype.useInlineDiffs=function(t){return this.options.useInlineDiffs=arguments.length&&void 0!=t?t:!1,this},s.prototype.timeout=function(t){return this.suite.timeout(t),this},s.prototype.slow=function(t){return this.suite.slow(t),this},s.prototype.enableTimeouts=function(t){return this.suite.enableTimeouts(arguments.length&&void 0!==t?t:!0),this},s.prototype.asyncOnly=function(){return this.options.asyncOnly=!0,this},s.prototype.noHighlighting=function(){return this.options.noHighlighting=!0,this},s.prototype.run=function(t){function n(e){s.done?s.done(e,t):t(e)}this.files.length&&this.loadFiles();var r=this.suite,o=this.options;o.files=this.files;var i=new e.Runner(r),s=new this._reporter(i,o);return i.ignoreLeaks=!1!==o.ignoreLeaks,i.asyncOnly=o.asyncOnly,o.grep&&i.grep(o.grep,o.invert),o.globals&&i.globals(o.globals),o.growl&&this._growl(i,s),void 0!==o.useColors&&(e.reporters.Base.useColors=o.useColors),e.reporters.Base.inlineDiffs=o.useInlineDiffs,i.run(n)}}),t.register("ms.js",function(t){function e(t){var e=/^((?:\d+)?\.?\d+) *(ms|seconds?|s|minutes?|m|hours?|h|days?|d|years?|y)?$/i.exec(t);if(e){var n=parseFloat(e[1]),r=(e[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"y":return n*c;case"days":case"day":case"d":return n*u;case"hours":case"hour":case"h":return n*a;case"minutes":case"minute":case"m":return n*s;case"seconds":case"second":case"s":return n*i;case"ms":return n}}}function n(t){return t>=u?Math.round(t/u)+"d":t>=a?Math.round(t/a)+"h":t>=s?Math.round(t/s)+"m":t>=i?Math.round(t/i)+"s":t+"ms"}function r(t){return o(t,u,"day")||o(t,a,"hour")||o(t,s,"minute")||o(t,i,"second")||t+" ms"}function o(t,e,n){return e>t?void 0:1.5*e>t?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}var i=1e3,s=60*i,a=60*s,u=24*a,c=365.25*u;t.exports=function(t,o){return o=o||{},"string"==typeof t?e(t):o["long"]?r(t):n(t)}}),t.register("reporters/base.js",function(t,e,r){function o(t){var e=this.stats={suites:0,tests:0,passes:0,pending:0,failures:0},n=this.failures=[];t&&(this.runner=t,t.stats=e,t.on("start",function(){e.start=new v}),t.on("suite",function(t){e.suites=e.suites||0,t.root||e.suites++}),t.on("test end",function(){e.tests=e.tests||0,e.tests++}),t.on("pass",function(t){e.passes=e.passes||0;var n=t.slow()/2;t.speed=t.duration>t.slow()?"slow":t.duration>n?"medium":"fast",e.passes++}),t.on("fail",function(t,r){e.failures=e.failures||0,e.failures++,t.err=r,n.push(t)}),t.on("end",function(){e.end=new v,e.duration=new v-e.start}),t.on("pending",function(){e.pending++}))}function s(t,e){return t=String(t),Array(e-t.length+1).join(" ")+t}function a(t,e){var n=c(t,"WordsWithSpace",e),r=n.split("\n");if(r.length>4){var o=String(r.length).length;n=r.map(function(t,e){return s(++e,o)+" | "+t}).join("\n")}return n="\n"+w("diff removed","actual")+" "+w("diff added","expected")+"\n\n"+n+"\n",n=n.replace(/^/gm,"      ")}function u(t,e){function n(t){return e&&(t=l(t)),"+"===t[0]?o+f("diff added",t):"-"===t[0]?o+f("diff removed",t):t.match(/\@\@/)?null:t.match(/\\ No newline/)?null:o+t}function r(t){return null!=t}var o="      ";msg=d.createPatch("string",t.actual,t.expected);var i=msg.split("\n").splice(4);return"\n      "+f("diff added","+ expected")+" "+f("diff removed","- actual")+"\n\n"+i.map(n).filter(r).join("\n")}function c(t,e,n){var r=n?l(t.actual):t.actual,o=n?l(t.expected):t.expected;return d["diff"+e](r,o).map(function(t){return t.added?f("diff added",t.value):t.removed?f("diff removed",t.value):t.value}).join("")}function l(t){return t.replace(/\t/g,"<tab>").replace(/\r/g,"<CR>").replace(/\n/g,"<LF>\n")}function f(t,e){return e.split("\n").map(function(e){return w(t,e)}).join("\n")}function p(t,e){return t=Object.prototype.toString.call(t),e=Object.prototype.toString.call(e),t==e}var h=r("browser/tty"),d=r("browser/diff"),g=r("../ms"),m=r("../utils"),v=n.Date,y=(n.setTimeout,n.setInterval,n.clearTimeout,n.clearInterval,h.isatty(1)&&h.isatty(2));e=t.exports=o,e.useColors=y||void 0!==i.env.MOCHA_COLORS,e.inlineDiffs=!1,e.colors={pass:90,fail:31,"bright pass":92,"bright fail":91,"bright yellow":93,pending:36,suite:0,"error title":0,"error message":31,"error stack":90,checkmark:32,fast:90,medium:33,slow:31,green:32,light:90,"diff gutter":90,"diff added":42,"diff removed":41},e.symbols={ok:"✓",err:"✖",dot:"․"},"win32"==i.platform&&(e.symbols.ok="√",e.symbols.err="×",e.symbols.dot=".");var w=e.color=function(t,n){return e.useColors?"["+e.colors[t]+"m"+n+"[0m":String(n)};e.window={width:y?i.stdout.getWindowSize?i.stdout.getWindowSize(1)[0]:h.getWindowSize()[1]:75},e.cursor={hide:function(){y&&i.stdout.write("[?25l")},show:function(){y&&i.stdout.write("[?25h")},deleteLine:function(){y&&i.stdout.write("[2K")},beginningOfLine:function(){y&&i.stdout.write("[0G")},CR:function(){y?(e.cursor.deleteLine(),e.cursor.beginningOfLine()):i.stdout.write("\r")}},e.list=function(t){console.log(),t.forEach(function(t,n){var r=w("error title","  %s) %s:\n")+w("error message","     %s")+w("error stack","\n%s\n"),o=t.err,i=o.message||"",s=o.stack||i,c=s.indexOf(i)+i.length,l=s.slice(0,c),f=o.actual,h=o.expected,d=!0;if(o.uncaught&&(l="Uncaught "+l),o.showDiff&&p(f,h)){"string"!=typeof f&&(d=!1,o.actual=f=m.stringify(f),o.expected=h=m.stringify(h)),r=w("error title","  %s) %s:\n%s")+w("error stack","\n%s\n");var g=i.match(/^([^:]+): expected/);l="\n      "+w("error message",g?g[1]:l),l+=e.inlineDiffs?a(o,d):u(o,d)}s=s.slice(c?c+1:c).replace(/^/gm,"  "),console.log(r,n+1,t.fullTitle(),l,s)})},o.prototype.epilogue=function(){var t,e=this.stats;console.log(),t=w("bright pass"," ")+w("green"," %d passing")+w("light"," (%s)"),console.log(t,e.passes||0,g(e.duration)),e.pending&&(t=w("pending"," ")+w("pending"," %d pending"),console.log(t,e.pending)),e.failures&&(t=w("fail","  %d failing"),console.log(t,e.failures),o.list(this.failures),console.log()),console.log()}}),t.register("reporters/doc.js",function(t,e,n){function r(t){function e(){return Array(n).join("  ")}o.call(this,t);var n=(this.stats,t.total,2);t.on("suite",function(t){t.root||(++n,console.log('%s<section class="suite">',e()),++n,console.log("%s<h1>%s</h1>",e(),i.escape(t.title)),console.log("%s<dl>",e()))}),t.on("suite end",function(t){t.root||(console.log("%s</dl>",e()),--n,console.log("%s</section>",e()),--n)}),t.on("pass",function(t){console.log("%s  <dt>%s</dt>",e(),i.escape(t.title));var n=i.escape(i.clean(t.fn.toString()));console.log("%s  <dd><pre><code>%s</code></pre></dd>",e(),n)}),t.on("fail",function(t,n){console.log('%s  <dt class="error">%s</dt>',e(),i.escape(t.title));var r=i.escape(i.clean(t.fn.toString()));console.log('%s  <dd class="error"><pre><code>%s</code></pre></dd>',e(),r),console.log('%s  <dd class="error">%s</dd>',e(),i.escape(n))})}var o=n("./base"),i=n("../utils");e=t.exports=r}),t.register("reporters/dot.js",function(t,e,n){function r(t){s.call(this,t);var e=this,n=(this.stats,.75*s.window.width|0),r=-1;t.on("start",function(){i.stdout.write("\n  ")}),t.on("pending",function(){++r%n==0&&i.stdout.write("\n  "),i.stdout.write(a("pending",s.symbols.dot))}),t.on("pass",function(t){++r%n==0&&i.stdout.write("\n  "),i.stdout.write("slow"==t.speed?a("bright yellow",s.symbols.dot):a(t.speed,s.symbols.dot))}),t.on("fail",function(){++r%n==0&&i.stdout.write("\n  "),i.stdout.write(a("fail",s.symbols.dot))}),t.on("end",function(){console.log(),e.epilogue()})}function o(){}var s=n("./base"),a=s.color;e=t.exports=r,o.prototype=s.prototype,r.prototype=new o,r.prototype.constructor=r}),t.register("reporters/html-cov.js",function(t,e,n){function r(t){var e=n("jade"),r=__dirname+"/templates/coverage.jade",u=a.readFileSync(r,"utf8"),c=e.compile(u,{filename:r}),l=this;s.call(this,t,!1),t.on("end",function(){i.stdout.write(c({cov:l.cov,coverageClass:o}))})}function o(t){return t>=75?"high":t>=50?"medium":t>=25?"low":"terrible"}var s=n("./json-cov"),a=n("browser/fs");e=t.exports=r}),t.register("reporters/html.js",function(t,e,r){function o(t){f.call(this,t);var e,n,r=this,o=this.stats,v=(t.total,s(m)),y=v.getElementsByTagName("li"),w=y[1].getElementsByTagName("em")[0],b=y[1].getElementsByTagName("a")[0],x=y[2].getElementsByTagName("em")[0],T=y[2].getElementsByTagName("a")[0],k=y[3].getElementsByTagName("em")[0],E=v.getElementsByTagName("canvas")[0],j=s('<ul id="mocha-report"></ul>'),_=[j],S=document.getElementById("mocha");if(E.getContext){var C=window.devicePixelRatio||1;E.style.width=E.width,E.style.height=E.height,E.width*=C,E.height*=C,n=E.getContext("2d"),n.scale(C,C),e=new h}return S?(l(b,"click",function(){u();var t=/pass/.test(j.className)?"":" pass";j.className=j.className.replace(/fail|pass/g,"")+t,j.className.trim()&&a("test pass")}),l(T,"click",function(){u();var t=/fail/.test(j.className)?"":" fail";j.className=j.className.replace(/fail|pass/g,"")+t,j.className.trim()&&a("test fail")}),S.appendChild(v),S.appendChild(j),e&&e.size(40),t.on("suite",function(t){if(!t.root){var e=r.suiteURL(t),n=s('<li class="suite"><h1><a href="%s">%s</a></h1></li>',e,d(t.title));_[0].appendChild(n),_.unshift(document.createElement("ul")),n.appendChild(_[0])}}),t.on("suite end",function(t){t.root||_.shift()}),t.on("fail",function(e){"hook"==e.type&&t.emit("test end",e)}),void t.on("test end",function(t){var i=o.tests/this.total*100|0;e&&e.update(i).draw(n);var a=new g-o.start;if(c(w,o.passes),c(x,o.failures),c(k,(a/1e3).toFixed(2)),"passed"==t.state)var u=r.testURL(t),f=s('<li class="test pass %e"><h2>%e<span class="duration">%ems</span> <a href="%s" class="replay">‣</a></h2></li>',t.speed,t.title,t.duration,u);else if(t.pending)var f=s('<li class="test pass pending"><h2>%e</h2></li>',t.title);else{var f=s('<li class="test fail"><h2>%e <a href="%e" class="replay">‣</a></h2></li>',t.title,r.testURL(t)),h=t.err.stack||t.err.toString();~h.indexOf(t.err.message)||(h=t.err.message+"\n"+h),"[object Error]"==h&&(h=t.err.message),!t.err.stack&&t.err.sourceURL&&void 0!==t.err.line&&(h+="\n("+t.err.sourceURL+":"+t.err.line+")"),f.appendChild(s('<pre class="error">%e</pre>',h))}if(!t.pending){var d=f.getElementsByTagName("h2")[0];l(d,"click",function(){m.style.display="none"==m.style.display?"block":"none"});var m=s("<pre><code>%e</code></pre>",p.clean(t.fn.toString()));f.appendChild(m),m.style.display="none"}_[0]&&_[0].appendChild(f)})):i("#mocha div missing, add it to your document")}function i(t){document.body.appendChild(s('<div id="mocha-error">%s</div>',t))}function s(t){var e=arguments,n=document.createElement("div"),r=1;return n.innerHTML=t.replace(/%([se])/g,function(t,n){switch(n){case"s":return String(e[r++]);case"e":return d(e[r++])}}),n.firstChild}function a(t){for(var e=document.getElementsByClassName("suite"),n=0;n<e.length;n++){var r=e[n].getElementsByClassName(t);0==r.length&&(e[n].className+=" hidden")}}function u(){for(var t=document.getElementsByClassName("suite hidden"),e=0;e<t.length;++e)t[e].className=t[e].className.replace("suite hidden","suite")}function c(t,e){t.textContent?t.textContent=e:t.innerText=e}function l(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent("on"+e,n)}{var f=r("./base"),p=r("../utils"),h=r("../browser/progress"),d=p.escape,g=n.Date;n.setTimeout,n.setInterval,n.clearTimeout,n.clearInterval}e=t.exports=o;var m='<ul id="mocha-stats"><li class="progress"><canvas width="40" height="40"></canvas></li><li class="passes"><a href="#">passes:</a> <em>0</em></li><li class="failures"><a href="#">failures:</a> <em>0</em></li><li class="duration">duration: <em>0</em>s</li></ul>',v=function(t){var e=window.location.search;return window.location.pathname+(e?e+"&":"?")+"grep="+encodeURIComponent(t)};o.prototype.suiteURL=function(t){return v(t.fullTitle())},o.prototype.testURL=function(t){return v(t.fullTitle())}}),t.register("reporters/index.js",function(t,e,n){e.Base=n("./base"),e.Dot=n("./dot"),e.Doc=n("./doc"),e.TAP=n("./tap"),e.JSON=n("./json"),e.HTML=n("./html"),e.List=n("./list"),e.Min=n("./min"),e.Spec=n("./spec"),e.Nyan=n("./nyan"),e.XUnit=n("./xunit"),e.Markdown=n("./markdown"),e.Progress=n("./progress"),e.Landing=n("./landing"),e.JSONCov=n("./json-cov"),e.HTMLCov=n("./html-cov"),e.JSONStream=n("./json-stream")}),t.register("reporters/json-cov.js",function(t,e,r){function o(t,e){var r=this,e=1==arguments.length?!0:e;c.call(this,t);var o=[],a=[],l=[];t.on("test end",function(t){o.push(t)}),t.on("pass",function(t){l.push(t)}),t.on("fail",function(t){a.push(t)}),t.on("end",function(){var t=n._$jscoverage||{},c=r.cov=s(t);c.stats=r.stats,c.tests=o.map(u),c.failures=a.map(u),c.passes=l.map(u),e&&i.stdout.write(JSON.stringify(c,null,2))})}function s(t){var e={instrumentation:"node-jscoverage",sloc:0,hits:0,misses:0,coverage:0,files:[]};for(var n in t){var r=a(n,t[n]);e.files.push(r),e.hits+=r.hits,e.misses+=r.misses,e.sloc+=r.sloc}return e.files.sort(function(t,e){return t.filename.localeCompare(e.filename)}),e.sloc>0&&(e.coverage=e.hits/e.sloc*100),e}function a(t,e){var n={filename:t,coverage:0,hits:0,misses:0,sloc:0,source:{}};return e.source.forEach(function(t,r){r++,0===e[r]?(n.misses++,n.sloc++):void 0!==e[r]&&(n.hits++,n.sloc++),n.source[r]={source:t,coverage:void 0===e[r]?"":e[r]}}),n.coverage=n.hits/n.sloc*100,n}function u(t){return{title:t.title,fullTitle:t.fullTitle(),duration:t.duration}}var c=r("./base");e=t.exports=o}),t.register("reporters/json-stream.js",function(t,e,n){function r(t){s.call(this,t);var e=this,n=(this.stats,t.total);t.on("start",function(){console.log(JSON.stringify(["start",{total:n}]))}),t.on("pass",function(t){console.log(JSON.stringify(["pass",o(t)]))}),t.on("fail",function(t,e){t=o(t),t.err=e.message,console.log(JSON.stringify(["fail",t]))}),t.on("end",function(){i.stdout.write(JSON.stringify(["end",e.stats]))})}function o(t){return{title:t.title,fullTitle:t.fullTitle(),duration:t.duration}}{var s=n("./base");s.color}e=t.exports=r}),t.register("reporters/json.js",function(t,e,n){function r(t){var e=this;a.call(this,t);var n=[],r=[],s=[],u=[];t.on("test end",function(t){n.push(t)}),t.on("pass",function(t){u.push(t)}),t.on("fail",function(t){s.push(t)}),t.on("pending",function(t){r.push(t)}),t.on("end",function(){var a={stats:e.stats,tests:n.map(o),pending:r.map(o),failures:s.map(o),passes:u.map(o)};t.testResults=a,i.stdout.write(JSON.stringify(a,null,2))})}function o(t){return{title:t.title,fullTitle:t.fullTitle(),duration:t.duration,err:s(t.err||{})}}function s(t){var e={};return Object.getOwnPropertyNames(t).forEach(function(n){e[n]=t[n]},t),e}{var a=n("./base");a.cursor,a.color}e=t.exports=r}),t.register("reporters/landing.js",function(t,e,n){function r(t){function e(){var t=Array(r).join("-");return"  "+u("runway",t)}s.call(this,t);var n=this,r=(this.stats,.75*s.window.width|0),o=t.total,c=i.stdout,l=u("plane","✈"),f=-1,p=0;t.on("start",function(){c.write("\n\n\n  "),a.hide()}),t.on("test end",function(t){var n=-1==f?r*++p/o|0:f;"failed"==t.state&&(l=u("plane crash","✈"),f=n),c.write("["+(r+1)+"D[2A"),c.write(e()),c.write("\n  "),c.write(u("runway",Array(n).join("⋅"))),c.write(l),c.write(u("runway",Array(r-n).join("⋅")+"\n")),c.write(e()),c.write("[0m")}),t.on("end",function(){a.show(),console.log(),n.epilogue()})}function o(){}var s=n("./base"),a=s.cursor,u=s.color;e=t.exports=r,s.colors.plane=0,s.colors["plane crash"]=31,s.colors.runway=90,o.prototype=s.prototype,r.prototype=new o,r.prototype.constructor=r}),t.register("reporters/list.js",function(t,e,n){function r(t){s.call(this,t);var e=this,n=(this.stats,0);t.on("start",function(){console.log()}),t.on("test",function(t){i.stdout.write(u("pass","    "+t.fullTitle()+": "))}),t.on("pending",function(t){var e=u("checkmark","  -")+u("pending"," %s");console.log(e,t.fullTitle())}),t.on("pass",function(t){var e=u("checkmark","  "+s.symbols.dot)+u("pass"," %s: ")+u(t.speed,"%dms");a.CR(),console.log(e,t.fullTitle(),t.duration)}),t.on("fail",function(t){a.CR(),console.log(u("fail","  %d) %s"),++n,t.fullTitle())}),t.on("end",e.epilogue.bind(e))}function o(){}var s=n("./base"),a=s.cursor,u=s.color;e=t.exports=r,o.prototype=s.prototype,r.prototype=new o,r.prototype.constructor=r}),t.register("reporters/markdown.js",function(t,e,n){function r(t){function e(t){return Array(c).join("#")+" "+t}function n(t,e){var r=e,o=a+t.title;return e=e[o]=e[o]||{suite:t},t.suites.forEach(function(t){n(t,e)}),r}function r(t,e){++e;var n,o="";for(var i in t)"suite"!=i&&(i!==a&&(n=" - ["+i.substring(1)+"]",n+="(#"+s.slug(t[i].suite.fullTitle())+")\n",o+=Array(e).join("  ")+n),o+=r(t[i],e));return o}function u(t){var e=n(t,{});return r(e,0)}o.call(this,t);var c=(this.stats,0),l="";u(t.suite),t.on("suite",function(t){++c;var n=s.slug(t.fullTitle());
l+='<a name="'+n+'"></a>\n',l+=e(t.title)+"\n"}),t.on("suite end",function(){--c}),t.on("pass",function(t){var e=s.clean(t.fn.toString());l+=t.title+".\n",l+="\n```js\n",l+=e+"\n",l+="```\n\n"}),t.on("end",function(){i.stdout.write("# TOC\n"),i.stdout.write(u(t.suite)),i.stdout.write(l)})}var o=n("./base"),s=n("../utils"),a="$";e=t.exports=r}),t.register("reporters/min.js",function(t,e,n){function r(t){s.call(this,t),t.on("start",function(){i.stdout.write("[2J"),i.stdout.write("[1;3H")}),t.on("end",this.epilogue.bind(this))}function o(){}var s=n("./base");e=t.exports=r,o.prototype=s.prototype,r.prototype=new o,r.prototype.constructor=r}),t.register("reporters/nyan.js",function(t,e,n){function r(t){a.call(this,t);{var e=this,n=(this.stats,.75*a.window.width|0),r=(this.rainbowColors=e.generateColors(),this.colorIndex=0,this.numberOfLines=4,this.trajectories=[[],[],[],[]],this.nyanCatWidth=11);this.trajectoryWidthMax=n-r,this.scoreboardWidth=5,this.tick=0}t.on("start",function(){a.cursor.hide(),e.draw()}),t.on("pending",function(){e.draw()}),t.on("pass",function(){e.draw()}),t.on("fail",function(){e.draw()}),t.on("end",function(){a.cursor.show();for(var t=0;t<e.numberOfLines;t++)o("\n");e.epilogue()})}function o(t){i.stdout.write(t)}function s(){}var a=n("./base");e=t.exports=r,r.prototype.draw=function(){this.appendRainbow(),this.drawScoreboard(),this.drawRainbow(),this.drawNyanCat(),this.tick=!this.tick},r.prototype.drawScoreboard=function(){function t(t,e){o(" "),o(a.color(t,e)),o("\n")}var e=this.stats;t("green",e.passes),t("fail",e.failures),t("pending",e.pending),o("\n"),this.cursorUp(this.numberOfLines)},r.prototype.appendRainbow=function(){for(var t=this.tick?"_":"-",e=this.rainbowify(t),n=0;n<this.numberOfLines;n++){var r=this.trajectories[n];r.length>=this.trajectoryWidthMax&&r.shift(),r.push(e)}},r.prototype.drawRainbow=function(){var t=this;this.trajectories.forEach(function(e){o("["+t.scoreboardWidth+"C"),o(e.join("")),o("\n")}),this.cursorUp(this.numberOfLines)},r.prototype.drawNyanCat=function(){var t=this,e=this.scoreboardWidth+this.trajectories[0].length,n="["+e+"C",r="";o(n),o("_,------,"),o("\n"),o(n),r=t.tick?"  ":"   ",o("_|"+r+"/\\_/\\ "),o("\n"),o(n),r=t.tick?"_":"__";var i=t.tick?"~":"^";o(i+"|"+r+this.face()+" "),o("\n"),o(n),r=t.tick?" ":"  ",o(r+'""  "" '),o("\n"),this.cursorUp(this.numberOfLines)},r.prototype.face=function(){var t=this.stats;return t.failures?"( x .x)":t.pending?"( o .o)":t.passes?"( ^ .^)":"( - .-)"},r.prototype.cursorUp=function(t){o("["+t+"A")},r.prototype.cursorDown=function(t){o("["+t+"B")},r.prototype.generateColors=function(){for(var t=[],e=0;42>e;e++){var n=Math.floor(Math.PI/3),r=e*(1/6),o=Math.floor(3*Math.sin(r)+3),i=Math.floor(3*Math.sin(r+2*n)+3),s=Math.floor(3*Math.sin(r+4*n)+3);t.push(36*o+6*i+s+16)}return t},r.prototype.rainbowify=function(t){if(!a.useColors)return t;var e=this.rainbowColors[this.colorIndex%this.rainbowColors.length];return this.colorIndex+=1,"[38;5;"+e+"m"+t+"[0m"},s.prototype=a.prototype,r.prototype=new s,r.prototype.constructor=r}),t.register("reporters/progress.js",function(t,e,n){function r(t,e){s.call(this,t);var n=this,e=e||{},r=(this.stats,.5*s.window.width|0),o=t.total,c=0,l=(Math.max,-1);e.open=e.open||"[",e.complete=e.complete||"▬",e.incomplete=e.incomplete||s.symbols.dot,e.close=e.close||"]",e.verbose=!1,t.on("start",function(){console.log(),a.hide()}),t.on("test end",function(){c++;var t=c/o,n=r*t|0,s=r-n;(l!==n||e.verbose)&&(l=n,a.CR(),i.stdout.write("[J"),i.stdout.write(u("progress","  "+e.open)),i.stdout.write(Array(n).join(e.complete)),i.stdout.write(Array(s).join(e.incomplete)),i.stdout.write(u("progress",e.close)),e.verbose&&i.stdout.write(u("progress"," "+c+" of "+o)))}),t.on("end",function(){a.show(),console.log(),n.epilogue()})}function o(){}var s=n("./base"),a=s.cursor,u=s.color;e=t.exports=r,s.colors.progress=90,o.prototype=s.prototype,r.prototype=new o,r.prototype.constructor=r}),t.register("reporters/spec.js",function(t,e,n){function r(t){function e(){return Array(r).join("  ")}i.call(this,t);var n=this,r=(this.stats,0),o=0;t.on("start",function(){console.log()}),t.on("suite",function(t){++r,console.log(a("suite","%s%s"),e(),t.title)}),t.on("suite end",function(){--r,1==r&&console.log()}),t.on("pending",function(t){var n=e()+a("pending","  - %s");console.log(n,t.title)}),t.on("pass",function(t){if("fast"==t.speed){var n=e()+a("checkmark","  "+i.symbols.ok)+a("pass"," %s ");s.CR(),console.log(n,t.title)}else{var n=e()+a("checkmark","  "+i.symbols.ok)+a("pass"," %s ")+a(t.speed,"(%dms)");s.CR(),console.log(n,t.title,t.duration)}}),t.on("fail",function(t){s.CR(),console.log(e()+a("fail","  %d) %s"),++o,t.title)}),t.on("end",n.epilogue.bind(n))}function o(){}var i=n("./base"),s=i.cursor,a=i.color;e=t.exports=r,o.prototype=i.prototype,r.prototype=new o,r.prototype.constructor=r}),t.register("reporters/tap.js",function(t,e,n){function r(t){i.call(this,t);var e=(this.stats,1),n=0,r=0;t.on("start",function(){var e=t.grepTotal(t.suite);console.log("%d..%d",1,e)}),t.on("test end",function(){++e}),t.on("pending",function(t){console.log("ok %d %s # SKIP -",e,o(t))}),t.on("pass",function(t){n++,console.log("ok %d %s",e,o(t))}),t.on("fail",function(t,n){r++,console.log("not ok %d %s",e,o(t)),n.stack&&console.log(n.stack.replace(/^/gm,"  "))}),t.on("end",function(){console.log("# tests "+(n+r)),console.log("# pass "+n),console.log("# fail "+r)})}function o(t){return t.fullTitle().replace(/#/g,"")}{var i=n("./base");i.cursor,i.color}e=t.exports=r}),t.register("reporters/xunit.js",function(t,e,r){function o(t,e){u.call(this,t);var n=this.stats,r=[],o=this;if(e.reporterOptions&&e.reporterOptions.output){if(!l.createWriteStream)throw new Error("file output not supported in browser");o.fileStream=l.createWriteStream(e.reporterOptions.output)}t.on("pending",function(t){r.push(t)}),t.on("pass",function(t){r.push(t)}),t.on("fail",function(t){r.push(t)}),t.on("end",function(){o.write(s("testsuite",{name:"Mocha Tests",tests:n.tests,failures:n.failures,errors:n.failures,skipped:n.tests-n.failures-n.passes,timestamp:(new p).toUTCString(),time:n.duration/1e3||0},!1)),r.forEach(function(t){o.test(t)}),o.write("</testsuite>")})}function i(){}function s(t,e,n,r){var o,i=n?"/>":">",s=[];for(var a in e)s.push(a+'="'+f(e[a])+'"');return o="<"+t+(s.length?" "+s.join(" "):"")+i,r&&(o+=r+"</"+t+i),o}function a(t){return"<![CDATA["+f(t)+"]]>"}{var u=r("./base"),c=r("../utils"),l=r("browser/fs"),f=c.escape,p=n.Date;n.setTimeout,n.setInterval,n.clearTimeout,n.clearInterval}e=t.exports=o,o.prototype.done=function(t,e){this.fileStream?this.fileStream.end(function(){e(t)}):e(t)},i.prototype=u.prototype,o.prototype=new i,o.prototype.constructor=o,o.prototype.write=function(t){this.fileStream?this.fileStream.write(t+"\n"):console.log(t)},o.prototype.test=function(t){var e={classname:t.parent.fullTitle(),name:t.title,time:t.duration/1e3||0};if("failed"==t.state){var n=t.err;this.write(s("testcase",e,!1,s("failure",{},!1,a(f(n.message)+"\n"+n.stack))))}else this.write(t.pending?s("testcase",e,!1,s("skipped",{},!0)):s("testcase",e,!0))}}),t.register("runnable.js",function(t,e,r){function o(t,e){this.title=t,this.fn=e,this.async=e&&e.length,this.sync=!this.async,this._timeout=2e3,this._slow=75,this._enableTimeouts=!0,this.timedOut=!1,this._trace=new Error("done() called multiple times")}function i(){}var s=r("browser/events").EventEmitter,a=r("browser/debug")("mocha:runnable"),u=r("./ms"),c=r("./utils"),l=n.Date,f=n.setTimeout,p=(n.setInterval,n.clearTimeout),h=(n.clearInterval,Object.prototype.toString);t.exports=o,i.prototype=s.prototype,o.prototype=new i,o.prototype.constructor=o,o.prototype.timeout=function(t){return 0==arguments.length?this._timeout:(0===t&&(this._enableTimeouts=!1),"string"==typeof t&&(t=u(t)),a("timeout %d",t),this._timeout=t,this.timer&&this.resetTimeout(),this)},o.prototype.slow=function(t){return 0===arguments.length?this._slow:("string"==typeof t&&(t=u(t)),a("timeout %d",t),this._slow=t,this)},o.prototype.enableTimeouts=function(t){return 0===arguments.length?this._enableTimeouts:(a("enableTimeouts %s",t),this._enableTimeouts=t,this)},o.prototype.fullTitle=function(){return this.parent.fullTitle()+" "+this.title},o.prototype.clearTimeout=function(){p(this.timer)},o.prototype.inspect=function(){return JSON.stringify(this,function(t,e){return"_"!=t[0]?"parent"==t?"#<Suite>":"ctx"==t?"#<Context>":e:void 0},2)},o.prototype.resetTimeout=function(){var t=this,e=this.timeout()||1e9;this._enableTimeouts&&(this.clearTimeout(),this.timer=f(function(){t._enableTimeouts&&(t.callback(new Error("timeout of "+e+"ms exceeded")),t.timedOut=!0)},e))},o.prototype.globals=function(t){this._allowedGlobals=t},o.prototype.run=function(t){function e(t){i||(i=!0,s.emit("error",t||new Error("done() called multiple times; stacktrace may be inaccurate")))}function n(n){var r=s.timeout();if(!s.timedOut){if(o)return e(n||s._trace);s.clearTimeout(),s.duration=new l-a,o=!0,!n&&s.duration>r&&s._enableTimeouts&&(n=new Error("timeout of "+r+"ms exceeded")),t(n)}}function r(t){var e=t.call(u);e&&"function"==typeof e.then?(s.resetTimeout(),e.then(function(){n()},function(t){n(t||new Error("Promise rejected with no or falsy reason"))})):n()}var o,i,s=this,a=new l,u=this.ctx;if(u&&u.runnable&&u.runnable(this),this.callback=n,this.async){this.resetTimeout();try{this.fn.call(u,function(t){return t instanceof Error||"[object Error]"===h.call(t)?n(t):null!=t?n("[object Object]"===Object.prototype.toString.call(t)?new Error("done() invoked with non-Error: "+JSON.stringify(t)):new Error("done() invoked with non-Error: "+t)):void n()})}catch(f){n(c.getError(f))}}else{if(this.asyncOnly)return n(new Error("--async-only option in use without declaring `done()`"));try{this.pending?n():r(this.fn)}catch(f){n(c.getError(f))}}}}),t.register("runner.js",function(t,e,r){function o(t){var e=this;this._globals=[],this._abort=!1,this.suite=t,this.total=t.total(),this.failures=0,this.on("test end",function(t){e.checkGlobals(t)}),this.on("hook end",function(t){e.checkGlobals(t)}),this.grep(/.*/),this.globals(this.globalProps().concat(u()))}function s(){}function a(t,e){return p(e,function(e){if(/^d+/.test(e))return!1;if(n.navigator&&/^getInterface/.test(e))return!1;if(n.navigator&&/^\d+/.test(e))return!1;if(/^mocha-/.test(e))return!1;var r=p(t,function(t){return~t.indexOf("*")?0==e.indexOf(t.split("*")[0]):e==t});return 0==r.length&&(!n.navigator||"onerror"!==e)})}function u(){if("object"==typeof i&&"string"==typeof i.version){var t=i.version.split(".").reduce(function(t,e){return t<<8|e});if(2315>t)return["errno"]}return[]}var c=r("browser/events").EventEmitter,l=r("browser/debug")("mocha:runner"),f=(r("./test"),r("./utils")),p=f.filter,h=(f.keys,["setTimeout","clearTimeout","setInterval","clearInterval","XMLHttpRequest","Date","setImmediate","clearImmediate"]);t.exports=o,o.immediately=n.setImmediate||i.nextTick,s.prototype=c.prototype,o.prototype=new s,o.prototype.constructor=o,o.prototype.grep=function(t,e){return l("grep %s",t),this._grep=t,this._invert=e,this.total=this.grepTotal(this.suite),this},o.prototype.grepTotal=function(t){var e=this,n=0;return t.eachTest(function(t){var r=e._grep.test(t.fullTitle());e._invert&&(r=!r),r&&n++}),n},o.prototype.globalProps=function(){for(var t=f.keys(n),e=0;e<h.length;++e)~f.indexOf(t,h[e])||t.push(h[e]);return t},o.prototype.globals=function(t){return 0==arguments.length?this._globals:(l("globals %j",t),this._globals=this._globals.concat(t),this)},o.prototype.checkGlobals=function(t){if(!this.ignoreLeaks){var e,n=this._globals,r=this.globalProps();t&&(n=n.concat(t._allowedGlobals||[])),this.prevGlobalsLength!=r.length&&(this.prevGlobalsLength=r.length,e=a(n,r),this._globals=this._globals.concat(e),e.length>1?this.fail(t,new Error("global leaks detected: "+e.join(", "))):e.length&&this.fail(t,new Error("global leak detected: "+e[0])))}},o.prototype.fail=function(t,e){++this.failures,t.state="failed","string"==typeof e&&(e=new Error('the string "'+e+'" was thrown, throw an Error :)')),this.emit("fail",t,e)},o.prototype.failHook=function(t,e){this.fail(t,e),this.suite.bail()&&this.emit("end")},o.prototype.hook=function(t,e){function n(t){var r=i[t];return r?(s.currentRunnable=r,r.ctx.currentTest=s.test,s.emit("hook",r),r.on("error",function(t){s.failHook(r,t)}),void r.run(function(o){r.removeAllListeners("error");var i=r.error();return i&&s.fail(s.test,i),o?(s.failHook(r,o),e(o)):(s.emit("hook end",r),delete r.ctx.currentTest,void n(++t))})):e()}var r=this.suite,i=r["_"+t],s=this;o.immediately(function(){n(0)})},o.prototype.hooks=function(t,e,n){function r(s){return o.suite=s,s?void o.hook(t,function(t){if(t){var s=o.suite;return o.suite=i,n(t,s)}r(e.pop())}):(o.suite=i,n())}var o=this,i=this.suite;r(e.pop())},o.prototype.hookUp=function(t,e){var n=[this.suite].concat(this.parents()).reverse();this.hooks(t,n,e)},o.prototype.hookDown=function(t,e){var n=[this.suite].concat(this.parents());this.hooks(t,n,e)},o.prototype.parents=function(){for(var t=this.suite,e=[];t=t.parent;)e.push(t);return e},o.prototype.runTest=function(t){var e=this.test,n=this;this.asyncOnly&&(e.asyncOnly=!0);try{e.on("error",function(t){n.fail(e,t)}),e.run(t)}catch(r){t(r)}},o.prototype.runTests=function(t,e){function n(t,r,o){var s=i.suite;i.suite=o?r.parent:r,i.suite?i.hookUp("afterEach",function(t,o){return i.suite=s,t?n(t,o,!0):void e(r)}):(i.suite=s,e(r))}function r(a,u){if(i.failures&&t._bail)return e();if(i._abort)return e();if(a)return n(a,u,!0);if(o=s.shift(),!o)return e();var c=i._grep.test(o.fullTitle());return i._invert&&(c=!c),c?o.pending?(i.emit("pending",o),i.emit("test end",o),r()):(i.emit("test",i.test=o),void i.hookDown("beforeEach",function(t,e){return t?n(t,e,!1):(i.currentRunnable=i.test,void i.runTest(function(t){return o=i.test,t?(i.fail(o,t),i.emit("test end",o),i.hookUp("afterEach",r)):(o.state="passed",i.emit("pass",o),i.emit("test end",o),void i.hookUp("afterEach",r))}))})):r()}var o,i=this,s=t.tests.slice();this.next=r,r()},o.prototype.runSuite=function(t,e){function n(e){if(e)return e==t?r():r(e);if(i._abort)return r();var o=t.suites[s++];return o?void i.runSuite(o,n):r()}function r(n){i.suite=t,i.hook("afterAll",function(){i.emit("suite end",t),e(n)})}var o=this.grepTotal(t),i=this,s=0;return l("run suite %s",t.fullTitle()),o?(this.emit("suite",this.suite=t),void this.hook("beforeAll",function(e){return e?r():void i.runTests(t,n)})):e()},o.prototype.uncaught=function(t){t?l("uncaught exception %s",t!==function(){return this}.call(t)?t:t.message||t):(l("uncaught undefined exception"),t=f.undefinedError()),t.uncaught=!0;var e=this.currentRunnable;if(e){var n=e.state;if(this.fail(e,t),e.clearTimeout(),!n)return"test"==e.type?(this.emit("test end",e),void this.hookUp("afterEach",this.next)):void this.emit("end")}},o.prototype.run=function(t){function e(t){n.uncaught(t)}var n=this,t=t||function(){};return l("start"),this.on("end",function(){l("end"),i.removeListener("uncaughtException",e),t(n.failures)}),this.emit("start"),this.runSuite(this.suite,function(){l("finished running"),n.emit("end")}),i.on("uncaughtException",e),this},o.prototype.abort=function(){l("aborting"),this._abort=!0}}),t.register("suite.js",function(t,e,n){function r(t,e){this.title=t;var n=function(){};n.prototype=e,this.ctx=new n,this.suites=[],this.tests=[],this.pending=!1,this._beforeEach=[],this._beforeAll=[],this._afterEach=[],this._afterAll=[],this.root=!t,this._timeout=2e3,this._enableTimeouts=!0,this._slow=75,this._bail=!1}function o(){}var i=n("browser/events").EventEmitter,s=n("browser/debug")("mocha:suite"),a=n("./ms"),u=n("./utils"),c=n("./hook");e=t.exports=r,e.create=function(t,e){var n=new r(e,t.ctx);return n.parent=t,t.pending&&(n.pending=!0),e=n.fullTitle(),t.addSuite(n),n},o.prototype=i.prototype,r.prototype=new o,r.prototype.constructor=r,r.prototype.clone=function(){var t=new r(this.title);return s("clone"),t.ctx=this.ctx,t.timeout(this.timeout()),t.enableTimeouts(this.enableTimeouts()),t.slow(this.slow()),t.bail(this.bail()),t},r.prototype.timeout=function(t){return 0==arguments.length?this._timeout:("0"===t.toString()&&(this._enableTimeouts=!1),"string"==typeof t&&(t=a(t)),s("timeout %d",t),this._timeout=parseInt(t,10),this)},r.prototype.enableTimeouts=function(t){return 0===arguments.length?this._enableTimeouts:(s("enableTimeouts %s",t),this._enableTimeouts=t,this)},r.prototype.slow=function(t){return 0===arguments.length?this._slow:("string"==typeof t&&(t=a(t)),s("slow %d",t),this._slow=t,this)},r.prototype.bail=function(t){return 0==arguments.length?this._bail:(s("bail %s",t),this._bail=t,this)},r.prototype.beforeAll=function(t,e){if(this.pending)return this;"function"==typeof t&&(e=t,t=e.name),t='"before all" hook'+(t?": "+t:"");var n=new c(t,e);return n.parent=this,n.timeout(this.timeout()),n.enableTimeouts(this.enableTimeouts()),n.slow(this.slow()),n.ctx=this.ctx,this._beforeAll.push(n),this.emit("beforeAll",n),this},r.prototype.afterAll=function(t,e){if(this.pending)return this;"function"==typeof t&&(e=t,t=e.name),t='"after all" hook'+(t?": "+t:"");var n=new c(t,e);return n.parent=this,n.timeout(this.timeout()),n.enableTimeouts(this.enableTimeouts()),n.slow(this.slow()),n.ctx=this.ctx,this._afterAll.push(n),this.emit("afterAll",n),this},r.prototype.beforeEach=function(t,e){if(this.pending)return this;"function"==typeof t&&(e=t,t=e.name),t='"before each" hook'+(t?": "+t:"");var n=new c(t,e);return n.parent=this,n.timeout(this.timeout()),n.enableTimeouts(this.enableTimeouts()),n.slow(this.slow()),n.ctx=this.ctx,this._beforeEach.push(n),this.emit("beforeEach",n),this},r.prototype.afterEach=function(t,e){if(this.pending)return this;"function"==typeof t&&(e=t,t=e.name),t='"after each" hook'+(t?": "+t:"");var n=new c(t,e);return n.parent=this,n.timeout(this.timeout()),n.enableTimeouts(this.enableTimeouts()),n.slow(this.slow()),n.ctx=this.ctx,this._afterEach.push(n),this.emit("afterEach",n),this},r.prototype.addSuite=function(t){return t.parent=this,t.timeout(this.timeout()),t.enableTimeouts(this.enableTimeouts()),t.slow(this.slow()),t.bail(this.bail()),this.suites.push(t),this.emit("suite",t),this},r.prototype.addTest=function(t){return t.parent=this,t.timeout(this.timeout()),t.enableTimeouts(this.enableTimeouts()),t.slow(this.slow()),t.ctx=this.ctx,this.tests.push(t),this.emit("test",t),this},r.prototype.fullTitle=function(){if(this.parent){var t=this.parent.fullTitle();if(t)return t+" "+this.title}return this.title},r.prototype.total=function(){return u.reduce(this.suites,function(t,e){return t+e.total()},0)+this.tests.length},r.prototype.eachTest=function(t){return u.forEach(this.tests,t),u.forEach(this.suites,function(e){e.eachTest(t)}),this}}),t.register("test.js",function(t,e,n){function r(t,e){i.call(this,t,e),this.pending=!e,this.type="test"}function o(){}var i=n("./runnable");t.exports=r,o.prototype=i.prototype,r.prototype=new o,r.prototype.constructor=r}),t.register("utils.js",function(t,e,n){function r(t){return!~p.indexOf(t)}function o(t){return t.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\/\/(.*)/gm,'<span class="comment">//$1</span>').replace(/('.*?')/gm,'<span class="string">$1</span>').replace(/(\d+\.\d+)/gm,'<span class="number">$1</span>').replace(/(\d+)/gm,'<span class="number">$1</span>').replace(/\bnew[ \t]+(\w+)/gm,'<span class="keyword">new</span> <span class="init">$1</span>').replace(/\b(function|new|throw|return|var|if|else)\b/gm,'<span class="keyword">$1</span>')}var i=n("browser/fs"),s=n("browser/path"),a=s.basename,u=i.existsSync||s.existsSync,c=n("browser/glob"),l=s.join,f=n("browser/debug")("mocha:watch"),p=["node_modules",".git"];e.escape=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},e.forEach=function(t,e,n){for(var r=0,o=t.length;o>r;r++)e.call(n,t[r],r)},e.map=function(t,e,n){for(var r=[],o=0,i=t.length;i>o;o++)r.push(e.call(n,t[o],o));return r},e.indexOf=function(t,e,n){for(var r=n||0,o=t.length;o>r;r++)if(t[r]===e)return r;return-1},e.reduce=function(t,e,n){for(var r=n,o=0,i=t.length;i>o;o++)r=e(r,t[o],o,t);return r},e.filter=function(t,e){for(var n=[],r=0,o=t.length;o>r;r++){var i=t[r];e(i,r,t)&&n.push(i)}return n},e.keys=Object.keys||function(t){var e=[],n=Object.prototype.hasOwnProperty;for(var r in t)n.call(t,r)&&e.push(r);return e},e.watch=function(t,e){var n={interval:100};t.forEach(function(t){f("file %s",t),i.watchFile(t,n,function(n,r){r.mtime<n.mtime&&e(t)})})},e.files=function(t,n,o){o=o||[],n=n||["js"];var s=new RegExp("\\.("+n.join("|")+")$");return i.readdirSync(t).filter(r).forEach(function(r){r=l(t,r),i.statSync(r).isDirectory()?e.files(r,n,o):r.match(s)&&o.push(r)}),o},e.slug=function(t){return t.toLowerCase().replace(/ +/g,"-").replace(/[^-\w]/g,"")},e.clean=function(t){t=t.replace(/\r\n?|[\n\u2028\u2029]/g,"\n").replace(/^\uFEFF/,"").replace(/^function *\(.*\) *{|\(.*\) *=> *{?/,"").replace(/\s+\}$/,"");var n=t.match(/^\n?( *)/)[1].length,r=t.match(/^\n?(\t*)/)[1].length,o=new RegExp("^\n?"+(r?"	":" ")+"{"+(r?r:n)+"}","gm");return t=t.replace(o,""),e.trim(t)},e.trim=function(t){return t.replace(/^\s+|\s+$/g,"")},e.parseQuery=function(t){return e.reduce(t.replace("?","").split("&"),function(t,e){var n=e.indexOf("="),r=e.slice(0,n),o=e.slice(++n);return t[r]=decodeURIComponent(o),t},{})},e.highlightTags=function(t){for(var e=document.getElementById("mocha").getElementsByTagName(t),n=0,r=e.length;r>n;++n)e[n].innerHTML=o(e[n].innerHTML)};var h=function(t,n){switch(n=n||e.type(t)){case"function":return"[Function]";case"object":return"{}";case"array":return"[]";default:return t.toString()}};e.type=function(t){return"undefined"!=typeof Buffer&&Buffer.isBuffer(t)?"buffer":Object.prototype.toString.call(t).replace(/^\[.+\s(.+?)\]$/,"$1").toLowerCase()},e.stringify=function(t){var n,r=e.type(t);if("null"===r||"undefined"===r)return"["+r+"]";if("date"===r)return"[Date: "+t.toISOString()+"]";if(!~e.indexOf(["object","array","function"],r))return t.toString();for(n in t)if(t.hasOwnProperty(n))return JSON.stringify(e.canonicalize(t),null,2).replace(/,(\n|$)/g,"$1");return h(t,r)},e.isBuffer=function(t){return"undefined"!=typeof Buffer&&Buffer.isBuffer(t)},e.canonicalize=function(t,n){var r,o,i=e.type(t),s=function(t,e){n.push(t),e(),n.pop()};if(n=n||[],-1!==e.indexOf(n,t))return"[Circular]";switch(i){case"undefined":r="[undefined]";break;case"buffer":case"null":r=t;break;case"array":s(t,function(){r=e.map(t,function(t){return e.canonicalize(t,n)})});break;case"date":r="[Date: "+t.toISOString()+"]";break;case"function":for(o in t){r={};break}if(!r){r=h(t,i);break}case"object":r=r||{},s(t,function(){e.forEach(e.keys(t).sort(),function(o){r[o]=e.canonicalize(t[o],n)})});break;case"number":case"boolean":r=t;break;default:r=t.toString()}return r},e.lookupFiles=function d(t,e,n){var r=[],o=new RegExp("\\.("+e.join("|")+")$");if(!u(t)){if(!u(t+".js")){if(r=c.sync(t),!r.length)throw new Error("cannot resolve path (or pattern) '"+t+"'");return r}t+=".js"}try{var s=i.statSync(t);if(s.isFile())return t}catch(f){return}return i.readdirSync(t).forEach(function(s){s=l(t,s);try{var u=i.statSync(s);if(u.isDirectory())return void(n&&(r=r.concat(d(s,e,n))))}catch(c){return}u.isFile()&&o.test(s)&&"."!==a(s)[0]&&r.push(s)}),r},e.undefinedError=function(){return new Error("Caught undefined error, did you throw without specifying what?")},e.getError=function(t){return t||e.undefinedError()}});var n=function(){return this}(),r=n.Date,o=n.setTimeout,i=(n.setInterval,n.clearTimeout,n.clearInterval,{});i.exit=function(){},i.stdout={};var s=[],a=n.onerror;i.removeListener=function(t,e){if("uncaughtException"==t){n.onerror=a?a:function(){};var r=u.utils.indexOf(s,e);-1!=r&&s.splice(r,1)}},i.on=function(t,e){"uncaughtException"==t&&(n.onerror=function(t,n,r){return e(new Error(t+" ("+n+":"+r+")")),!0},s.push(e))};var u=n.Mocha=t("mocha"),c=n.mocha=new u({reporter:"html"});c.suite.removeAllListeners("pre-require");var l,f=[];u.Runner.immediately=function(t){f.push(t),l||(l=o(e,0))},c.throwError=function(t){throw u.utils.forEach(s,function(e){e(t)}),t},c.ui=function(t){return u.prototype.ui.call(this,t),this.suite.emit("pre-require",n,null,this),this},c.setup=function(t){"string"==typeof t&&(t={ui:t});for(var e in t)this[e](t[e]);return this},c.run=function(t){var e=c.options;c.globals("location");var r=u.utils.parseQuery(n.location.search||"");return r.grep&&c.grep(r.grep),r.invert&&c.invert(),u.prototype.run.call(c,function(r){var o=n.document;o&&o.getElementById("mocha")&&e.noHighlighting!==!0&&u.utils.highlightTags("code"),t&&t(r)})},u.process=i}();