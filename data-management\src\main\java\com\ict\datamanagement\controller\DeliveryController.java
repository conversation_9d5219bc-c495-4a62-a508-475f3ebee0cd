package com.ict.datamanagement.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.common.StatusCode;
import com.ict.datamanagement.domain.dto.delivery.AddDeliveryRequest;
import com.ict.datamanagement.domain.dto.delivery.DeliveryListRequest;
import com.ict.datamanagement.domain.dto.delivery.UpdateDeliveryRequest;
import com.ict.datamanagement.domain.entity.Delivery;
import com.ict.datamanagement.domain.vo.deliveryVO.AddDeliveryDownBoxVO;
import com.ict.datamanagement.domain.vo.deliveryVO.DeliveryVO;
import com.ict.datamanagement.domain.vo.deliveryVO.SearchFormDeliveryVO;
import com.ict.datamanagement.service.DeliveryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "配送域信息")
@Slf4j
@RestController("/delivery")
public class DeliveryController {

    @Autowired
    private DeliveryService deliveryService;

    //-------------------------------搜索和列表展示--------------------------------------------------
    @ApiOperation("配送域信息列表")
    @GetMapping("/deliveryList")
    public BaseResponse<Page<DeliveryVO>> list(DeliveryListRequest deliveryListRequest) {
        LambdaQueryWrapper<Delivery> queryWrapper = deliveryService.getQueryWrapper(deliveryListRequest);
        List<DeliveryVO> deliveryVOS = deliveryService.dliveryTODeliveryVO(queryWrapper);
        Integer pageNum = deliveryListRequest.getPageNum();
        Integer pageSize = deliveryListRequest.getPageSize();
        Page<DeliveryVO> page = deliveryService.getPage(pageNum, pageSize, deliveryVOS);
        return ResultUtils.success(page);
    }

    @ApiOperation("获取搜索列表")
    @PostMapping("/getSelect")
    public BaseResponse<SearchFormDeliveryVO> searchFormDelivery() {
        SearchFormDeliveryVO searchFormDeliveryVO = deliveryService.searchFormDelivery();
        return ResultUtils.success(searchFormDeliveryVO);
    }

    //-------------------------删除配送域
    @ApiOperation("删除配送域")
    @DeleteMapping("/deleteDelivery")
    public String  deleteDelivery(int id) {
         String info = deliveryService.deleteDelivery(id);
        return info;
    }

    //------------------------添加配送域
    @ApiOperation("添加配送域")
    @PostMapping("/addDelivery")
    public BaseResponse addDelivery(@Valid AddDeliveryRequest addDeliveryRequest) {
        int i = deliveryService.addDeliveryRequest(addDeliveryRequest);
        if (i == 1) {
            return ResultUtils.success("添加成功");
        }
        return ResultUtils.error(StatusCode.valueOf("添加失败"));
    }

    @ApiOperation("获取添加配送域下拉框")
    @PostMapping("/addDeliveryDownBox")
    public BaseResponse<AddDeliveryDownBoxVO> getAddDeliveryDownBox() {
        AddDeliveryDownBoxVO addDeliveryDownBoxVO = deliveryService.getAddDeliveryDownBox();
        return ResultUtils.success(addDeliveryDownBoxVO);
    }


    //----------------------修改配送域信息
    @ApiOperation("修改配送域信息")
    @PostMapping("/updateDelivery")
    public String updateDelivery(UpdateDeliveryRequest updateDeliveryRequest) {
        String info = deliveryService.updateDelivery(updateDeliveryRequest);
        return info;
    }
}
