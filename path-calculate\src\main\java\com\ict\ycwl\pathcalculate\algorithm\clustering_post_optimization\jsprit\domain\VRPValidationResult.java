package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * VRP问题验证结果
 * 
 * 包含VRP问题配置的验证结果、错误信息、警告信息和修复建议
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPValidationResult {
    
    /**
     * 验证是否通过
     */
    private Boolean isValid;
    
    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * 警告信息列表
     */
    @Builder.Default
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 修复建议列表
     */
    @Builder.Default
    private List<String> suggestions = new ArrayList<>();
    
    /**
     * 验证详情描述
     */
    private String validationDetails;
    
    /**
     * 验证类型
     */
    private ValidationType validationType;
    
    /**
     * 验证时间戳
     */
    private Long validationTimestamp;
    
    /**
     * 验证类型枚举
     */
    public enum ValidationType {
        BASIC_VALIDATION("基础验证"),
        COMPREHENSIVE_VALIDATION("全面验证"),
        PERFORMANCE_VALIDATION("性能验证"),
        CONSTRAINT_VALIDATION("约束验证");
        
        private final String description;
        
        ValidationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建成功的验证结果
     * 
     * @return 成功的验证结果
     */
    public static VRPValidationResult success() {
        return VRPValidationResult.builder()
            .isValid(true)
            .validationType(ValidationType.BASIC_VALIDATION)
            .validationTimestamp(System.currentTimeMillis())
            .validationDetails("验证通过")
            .build();
    }
    
    /**
     * 创建失败的验证结果
     * 
     * @param errorMessage 错误信息
     * @return 失败的验证结果
     */
    public static VRPValidationResult failure(String errorMessage) {
        VRPValidationResult result = VRPValidationResult.builder()
            .isValid(false)
            .validationType(ValidationType.BASIC_VALIDATION)
            .validationTimestamp(System.currentTimeMillis())
            .validationDetails("验证失败")
            .build();
        
        result.getErrors().add(errorMessage);
        return result;
    }
    
    /**
     * 创建带警告的验证结果
     * 
     * @param warningMessage 警告信息
     * @return 带警告的验证结果
     */
    public static VRPValidationResult withWarning(String warningMessage) {
        VRPValidationResult result = VRPValidationResult.builder()
            .isValid(true)
            .validationType(ValidationType.BASIC_VALIDATION)
            .validationTimestamp(System.currentTimeMillis())
            .validationDetails("验证通过但有警告")
            .build();
        
        result.getWarnings().add(warningMessage);
        return result;
    }
    
    /**
     * 检查是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 检查是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    /**
     * 检查是否有修复建议
     * 
     * @return 是否有修复建议
     */
    public boolean hasSuggestions() {
        return suggestions != null && !suggestions.isEmpty();
    }
    
    /**
     * 添加错误信息
     * 
     * @param error 错误信息
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        isValid = false;
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        if (warnings == null) {
            warnings = new ArrayList<>();
        }
        warnings.add(warning);
    }
    
    /**
     * 添加修复建议
     * 
     * @param suggestion 修复建议
     */
    public void addSuggestion(String suggestion) {
        if (suggestions == null) {
            suggestions = new ArrayList<>();
        }
        suggestions.add(suggestion);
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings != null ? warnings.size() : 0;
    }
    
    /**
     * 获取建议数量
     * 
     * @return 建议数量
     */
    public int getSuggestionCount() {
        return suggestions != null ? suggestions.size() : 0;
    }
    
    /**
     * 获取验证状态描述
     * 
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (isValid) {
            if (hasWarnings()) {
                return "通过（有警告）";
            } else {
                return "完全通过";
            }
        } else {
            return "失败";
        }
    }
    
    /**
     * 获取完整的验证摘要
     * 
     * @return 验证摘要
     */
    public String getValidationSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (isValid) {
            summary.append("✅ VRP问题验证通过");
        } else {
            summary.append("❌ VRP问题验证失败");
        }
        
        if (hasErrors()) {
            summary.append(String.format(" - %d个错误", getErrorCount()));
        }
        
        if (hasWarnings()) {
            summary.append(String.format(" - %d个警告", getWarningCount()));
        }
        
        if (hasSuggestions()) {
            summary.append(String.format(" - %d个建议", getSuggestionCount()));
        }
        
        return summary.toString();
    }
    
    /**
     * 获取详细的验证报告
     * 
     * @return 详细报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append(getValidationSummary()).append("\n\n");
        
        if (hasErrors()) {
            report.append("❌ 错误信息:\n");
            for (int i = 0; i < errors.size(); i++) {
                report.append(String.format("  %d. %s\n", i + 1, errors.get(i)));
            }
            report.append("\n");
        }
        
        if (hasWarnings()) {
            report.append("⚠️ 警告信息:\n");
            for (int i = 0; i < warnings.size(); i++) {
                report.append(String.format("  %d. %s\n", i + 1, warnings.get(i)));
            }
            report.append("\n");
        }
        
        if (hasSuggestions()) {
            report.append("💡 修复建议:\n");
            for (int i = 0; i < suggestions.size(); i++) {
                report.append(String.format("  %d. %s\n", i + 1, suggestions.get(i)));
            }
            report.append("\n");
        }
        
        if (validationDetails != null && !validationDetails.trim().isEmpty()) {
            report.append("📝 详情: ").append(validationDetails);
        }
        
        return report.toString().trim();
    }
    
    /**
     * 合并另一个验证结果
     * 
     * @param otherResult 另一个验证结果
     */
    public void merge(VRPValidationResult otherResult) {
        if (otherResult == null) {
            return;
        }
        
        // 合并验证状态
        this.isValid = this.isValid && otherResult.isValid;
        
        // 合并错误信息
        if (otherResult.hasErrors()) {
            if (this.errors == null) {
                this.errors = new ArrayList<>();
            }
            this.errors.addAll(otherResult.errors);
        }
        
        // 合并警告信息
        if (otherResult.hasWarnings()) {
            if (this.warnings == null) {
                this.warnings = new ArrayList<>();
            }
            this.warnings.addAll(otherResult.warnings);
        }
        
        // 合并建议信息
        if (otherResult.hasSuggestions()) {
            if (this.suggestions == null) {
                this.suggestions = new ArrayList<>();
            }
            this.suggestions.addAll(otherResult.suggestions);
        }
    }
    
    /**
     * 清除所有验证信息
     */
    public void clear() {
        isValid = true;
        if (errors != null) {
            errors.clear();
        }
        if (warnings != null) {
            warnings.clear();
        }
        if (suggestions != null) {
            suggestions.clear();
        }
        validationDetails = null;
        validationTimestamp = System.currentTimeMillis();
    }
    
    @Override
    public String toString() {
        return getValidationSummary();
    }
}