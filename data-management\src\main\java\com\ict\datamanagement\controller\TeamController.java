package com.ict.datamanagement.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.common.StatusCode;
import com.ict.datamanagement.domain.dto.team.AddTeamRequest;
import com.ict.datamanagement.domain.dto.team.TeamListRequest;
import com.ict.datamanagement.domain.dto.team.dto.TeamIdDto;
import com.ict.datamanagement.domain.entity.Team;

import com.ict.datamanagement.domain.info.DeliveryInfo;
import com.ict.datamanagement.domain.vo.teamVO.AddTeamInfoVO;
import com.ict.datamanagement.domain.vo.teamVO.GetUpdateTeamInfoVO;
import com.ict.datamanagement.domain.vo.teamVO.SearchFormVO;
import com.ict.datamanagement.service.TeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "班组信息")
@Slf4j
@RestController("/team")
public class TeamController {
    @Autowired
    private TeamService teamService;

    //-------------------------------搜索和列表展示--------------------------------------------------
    @ApiOperation("班组信息列表")
    @GetMapping("/teamList")
    public BaseResponse<Page<Team>> list(TeamListRequest teamListRequest) {
        List<Team> teams = teamService.getTeams(teamListRequest);
        Integer pageNum = teamListRequest.getPageNum();
        Integer pageSize = teamListRequest.getPageSize();
        Page<Team> page= teamService.getPage(pageNum,pageSize,teams);
        return ResultUtils.success(page);
    }

    //-----------------显示搜索框可选内容-----------------
    @ApiOperation("班组搜索框信息")
    @PostMapping("searchFormInfo")
    public BaseResponse<SearchFormVO> searchFormInfo() {
        SearchFormVO searchFormVO = teamService.searchFormInfo();
        return ResultUtils.success(searchFormVO);
    }


    //-------------------------------添加班组-----------------------------------------
    @ApiOperation("获取添加班组表单固定信息")
    @GetMapping("/getAddTeamInfo")
    public BaseResponse<AddTeamInfoVO> getAddTeamInfo() {
        AddTeamInfoVO addTeamInfo = teamService.getAddTeamInfo();
        return ResultUtils.success(addTeamInfo);
    }

    @ApiOperation("添加班组信息")
    @PostMapping("/saveTeam")
    public BaseResponse saveTeam(@RequestBody AddTeamRequest addTeamRequest) {
        int num = teamService.saveTeam(addTeamRequest);
        if (num == 1) {
            return ResultUtils.success("添加成功");
        }
        return ResultUtils.error(StatusCode.valueOf("添加失败"));
    }

    //------通用
    @ApiOperation("查询班组变更情况")
    @PostMapping("/queryChanges")
    public BaseResponse<String> queryChanges(@RequestBody AddTeamRequest addTeamRequest) {
        String s = teamService.queryChanges(addTeamRequest);
        return ResultUtils.success(s);
    }

    //-------修改班组
//    @ApiOperation("获取修改班组时选择配送域后的中转站信息")
//    @PostMapping("/getUpdateTeamInfo")
//    public BaseResponse<List<GetUpdateTeamInfoVO>> getUpdateTeamInfo(){
//        List<GetUpdateTeamInfoVO> list=teamService.getUpdateTeamInfo();
//        return ResultUtils.success(list);
//    }
    @ApiOperation("获取修改班组时选择配送域后的中转站信息")
    @PostMapping("/getUpdateTeamInfo")
    public BaseResponse<List<DeliveryInfo>> getUpdateTeamInfo() {
        AddTeamInfoVO addTeamInfo  = teamService.getAddTeamInfo();
        return ResultUtils.success(addTeamInfo.getDeliveryList());
    }


    @ApiOperation("修改班组")
    @PostMapping("/updateTeam")
    public BaseResponse updateTeam(@RequestBody TeamIdDto teamIdDto) {
        int num = teamService.updateTeam(teamIdDto);
        if (num == 1) {
            return ResultUtils.success("修改成功");
        }
        return ResultUtils.myError("修改失败");
    }

    //-------删除班组
    @ApiOperation("删除班组")
    @DeleteMapping("/deleteTeam")
    public BaseResponse deleteTeam(int teamId) {
        int i = teamService.deleteTeamById(teamId);
        if (i == 1) {
            return ResultUtils.success("修改成功");
        }
        return ResultUtils.error(StatusCode.valueOf("修改失败"));
    }
}
