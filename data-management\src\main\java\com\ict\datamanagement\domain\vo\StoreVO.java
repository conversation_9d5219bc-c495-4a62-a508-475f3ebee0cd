package com.ict.datamanagement.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class StoreVO implements Serializable {
    //店铺id
    private Long storeId;
    //客户编码
    private String customerCode;
    //店铺名称
    private String storeName;
    //地址
    private String storeAddress;
    //店铺经度
    private Double longitude;
    //店铺纬度
    private Double latitude;
    //商圈类型
    private String type;
    //订货周期
    private String orderCycle;
    //店铺所属行政区
    private String district;
    //店铺所属大区
    private String areaName;
    // 客户名称
    private String contactName;
    // 订货电话
    private String contactPhone;
    //状态（0：异常；1：正常）客户状态
    private String status;
    // 客户专员id
    private Long customerManagerId;
    //聚集区名称
    private String accumulationName;
    //聚集区地址
    private String accumulationAddress;
    //路线名称（表中是路线id修改成路线名称）
    private String routeName;
    //客户专员名称
    private String customerManagerName;
    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;
    // 客户档位
    private String gear;
    // 商铺类型
    private String locationType;
    //负责人
    private String head;
    //备用收货电话
    private String sparePhone;
    //收货电话
    private String receivingPhone;
    //返销周期
    private String resaleCycle;
    /*
     * 是否是特殊点
     * */
    private String isSpecial;
    /*
     * 特殊点备注
     * */
    private String remark;
    /*
    * 特殊点类型
    * */
    private String specialType;
}
