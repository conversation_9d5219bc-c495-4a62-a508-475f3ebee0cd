package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * MILP问题表示类
 * 
 * 封装混合整数线性规划问题的完整定义，包括变量、约束、目标函数
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Data
public class MILPProblem {
    
    /**
     * 问题ID
     */
    private String problemId;
    
    /**
     * 问题描述
     */
    private String description;
    
    /**
     * 决策变量集合
     */
    private Map<String, MILPVariable> variables;
    
    /**
     * 线性约束集合
     */
    private Map<String, LinearConstraint> constraints;
    
    /**
     * 目标函数
     */
    private ObjectiveFunction objectiveFunction;
    
    /**
     * 问题创建时间
     */
    private long createdTime;
    
    /**
     * 求解状态
     */
    private SolutionStatus solutionStatus;
    
    /**
     * 求解结果
     */
    private MILPSolution solution;
    
    public MILPProblem() {
        this.problemId = "milp_" + System.currentTimeMillis();
        this.variables = new LinkedHashMap<>();
        this.constraints = new LinkedHashMap<>();
        this.createdTime = System.currentTimeMillis();
        this.solutionStatus = SolutionStatus.NOT_SOLVED;
        
        log.debug("创建MILP问题: {}", problemId);
    }
    
    public MILPProblem(String problemId, String description) {
        this();
        this.problemId = problemId;
        this.description = description;
        
        log.debug("创建MILP问题: {} - {}", problemId, description);
    }
    
    /**
     * 添加决策变量
     */
    public MILPVariable addVariable(String name, VariableType type, double lowerBound, double upperBound) {
        MILPVariable variable = MILPVariable.builder()
            .name(name)
            .type(type)
            .lowerBound(lowerBound)
            .upperBound(upperBound)
            .build();
        
        variables.put(name, variable);
        // 删除冗余的变量添加日志，避免大规模问题时产生上万行日志
        
        return variable;
    }
    
    /**
     * 添加二进制变量
     */
    public MILPVariable addBinaryVariable(String name) {
        return addVariable(name, VariableType.BINARY, 0.0, 1.0);
    }
    
    /**
     * 添加整数变量
     */
    public MILPVariable addIntegerVariable(String name, double lowerBound, double upperBound) {
        return addVariable(name, VariableType.INTEGER, lowerBound, upperBound);
    }
    
    /**
     * 添加连续变量
     */
    public MILPVariable addContinuousVariable(String name, double lowerBound, double upperBound) {
        return addVariable(name, VariableType.CONTINUOUS, lowerBound, upperBound);
    }
    
    /**
     * 创建线性约束
     */
    public LinearConstraint createConstraint() {
        return new LinearConstraint();
    }
    
    /**
     * 创建指定名称的线性约束
     */
    public LinearConstraint createConstraint(String name) {
        LinearConstraint constraint = new LinearConstraint(name);
        return constraint;
    }
    
    /**
     * 添加约束到问题中
     */
    public void addConstraint(String name, LinearConstraint constraint) {
        constraint.setName(name);
        constraints.put(name, constraint);
        // 删除冗余的约束添加日志，避免大规模问题时产生上万行日志
    }
    
    /**
     * 设置目标函数
     */
    public void setObjectiveFunction(ObjectiveType type, Map<String, Double> coefficients) {
        this.objectiveFunction = ObjectiveFunction.builder()
            .type(type)
            .coefficients(new HashMap<>(coefficients))
            .build();
        
        log.debug("设置目标函数: {} (变量数: {})", type, coefficients.size());
    }
    
    /**
     * 设置最小化目标函数
     */
    public void setMinimizeObjective(Map<String, Double> coefficients) {
        setObjectiveFunction(ObjectiveType.MINIMIZE, coefficients);
    }
    
    /**
     * 设置最大化目标函数
     */
    public void setMaximizeObjective(Map<String, Double> coefficients) {
        setObjectiveFunction(ObjectiveType.MAXIMIZE, coefficients);
    }
    
    /**
     * 获取问题统计信息
     */
    public ProblemStatistics getStatistics() {
        Map<VariableType, Integer> variableCount = new HashMap<>();
        for (MILPVariable var : variables.values()) {
            variableCount.merge(var.getType(), 1, Integer::sum);
        }
        
        Map<ConstraintType, Integer> constraintCount = new HashMap<>();
        for (LinearConstraint constraint : constraints.values()) {
            constraintCount.merge(constraint.getType(), 1, Integer::sum);
        }
        
        return ProblemStatistics.builder()
            .totalVariables(variables.size())
            .variableTypeCount(variableCount)
            .totalConstraints(constraints.size())
            .constraintTypeCount(constraintCount)
            .hasObjectiveFunction(objectiveFunction != null)
            .problemSize(calculateProblemSize())
            .build();
    }
    
    /**
     * 计算问题规模
     */
    private String calculateProblemSize() {
        int vars = variables.size();
        int constrs = constraints.size();
        
        if (vars <= 100 && constrs <= 100) {
            return "SMALL";
        } else if (vars <= 1000 && constrs <= 1000) {
            return "MEDIUM";
        } else if (vars <= 10000 && constrs <= 10000) {
            return "LARGE";
        } else {
            return "VERY_LARGE";
        }
    }
    
    /**
     * 验证问题定义完整性
     */
    public ValidationResult validate() {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 检查基本完整性
        if (variables.isEmpty()) {
            errors.add("问题没有定义任何决策变量");
        }
        
        if (constraints.isEmpty()) {
            warnings.add("问题没有定义任何约束条件");
        }
        
        if (objectiveFunction == null) {
            errors.add("问题没有定义目标函数");
        }
        
        // 检查变量一致性
        Set<String> referencedVariables = new HashSet<>();
        for (LinearConstraint constraint : constraints.values()) {
            referencedVariables.addAll(constraint.getCoefficients().keySet());
        }
        
        if (objectiveFunction != null) {
            referencedVariables.addAll(objectiveFunction.getCoefficients().keySet());
        }
        
        for (String varName : referencedVariables) {
            if (!variables.containsKey(varName)) {
                errors.add("约束或目标函数中引用了未定义的变量: " + varName);
            }
        }
        
        // 检查约束合理性
        for (Map.Entry<String, LinearConstraint> entry : constraints.entrySet()) {
            LinearConstraint constraint = entry.getValue();
            if (constraint.getCoefficients().isEmpty()) {
                warnings.add("约束 " + entry.getKey() + " 没有定义任何系数");
            }
        }
        
        return ValidationResult.builder()
            .isValid(errors.isEmpty())
            .errors(errors)
            .warnings(warnings)
            .build();
    }
    
    /**
     * 生成问题摘要
     */
    public String generateSummary() {
        ProblemStatistics stats = getStatistics();
        ValidationResult validation = validate();
        
        StringBuilder summary = new StringBuilder();
        summary.append("=== MILP问题摘要 ===\n");
        summary.append(String.format("问题ID: %s\n", problemId));
        summary.append(String.format("描述: %s\n", description != null ? description : "无"));
        summary.append(String.format("问题规模: %s\n", stats.getProblemSize()));
        summary.append(String.format("变量数: %d, 约束数: %d\n", stats.getTotalVariables(), stats.getTotalConstraints()));
        summary.append(String.format("目标函数: %s\n", objectiveFunction != null ? objectiveFunction.getType() : "未定义"));
        summary.append(String.format("验证状态: %s\n", validation.isValid() ? "通过" : "失败"));
        summary.append(String.format("求解状态: %s\n", solutionStatus));
        
        if (!validation.isValid()) {
            summary.append("验证错误:\n");
            for (String error : validation.getErrors()) {
                summary.append("  - ").append(error).append("\n");
            }
        }
        
        return summary.toString();
    }
    
    /**
     * 变量类型枚举
     */
    public enum VariableType {
        CONTINUOUS("连续变量"),
        INTEGER("整数变量"),
        BINARY("二进制变量");
        
        private final String description;
        
        VariableType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 目标函数类型
     */
    public enum ObjectiveType {
        MINIMIZE("最小化"),
        MAXIMIZE("最大化");
        
        private final String description;
        
        ObjectiveType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 求解状态枚举
     */
    public enum SolutionStatus {
        NOT_SOLVED("未求解"),
        OPTIMAL("最优解"),
        FEASIBLE("可行解"),
        INFEASIBLE("不可行"),
        UNBOUNDED("无界"),
        TIME_LIMIT("时间限制"),  
        ERROR("求解错误");
        
        private final String description;
        
        SolutionStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}