package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("system_parameter")
public class SystemParameter {
    //系统参数记录id
    private int id;
    //聚集区密集度系数
    private double accumulationIntensity;
    //商铺平均卸货时长(小时)城区
    private double shoreUnloadCityTime;
    //商铺平均卸货时长(小时)乡村
    private double shoreUnloadTownshipTime;
    //车辆时速(千米每时)-高速公路
    private double freeway;
    //车辆时速(千米每时)-城区公路
    private double urbanRoads;
    //车辆时速(千米每时)-乡镇公路
    private double townshipRoads;
    //装车时长分钟
    private double loadingTime;
}
