package com.ict.ycwl.clustercalculate.utlis;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class CoordinateUtils {

    /**
     * 重构坐标串，以目标坐标为首尾
     * @param coordinateString 原始坐标串，格式为 "x1,y1;x2,y2;...;xn,yn"
     * @param targetCoordinate 目标坐标，格式为 "x,y"
     * @return 重构后的坐标串，以目标坐标开头和结尾
     */
    public static String rebuildCoordinateString(String coordinateString, String targetCoordinate) {
        if (coordinateString == null || coordinateString.trim().isEmpty() || targetCoordinate == null) {
            return coordinateString;
        }

        // 分割坐标串为列表
        List<String> coordinates = Arrays.stream(coordinateString.split(";"))
                .map(String::trim)
                .collect(Collectors.toList());

        // 查找目标坐标的索引
        int targetIndex = coordinates.indexOf(targetCoordinate.trim());
        if (targetIndex == -1) {
            // 如果目标坐标不存在，返回原始坐标串
            return coordinateString;
        }

        // 构建新的坐标列表
        List<String> newCoordinates = coordinates.subList(targetIndex, coordinates.size());

        // 添加从开头到目标坐标前的部分（不包括目标坐标）
        newCoordinates.addAll(coordinates.subList(0, targetIndex));

        // 确保首尾都是目标坐标
        if (!newCoordinates.get(0).equals(targetCoordinate.trim())) {
            newCoordinates.add(0, targetCoordinate.trim());
        }
        if (!newCoordinates.get(newCoordinates.size() - 1).equals(targetCoordinate.trim())) {
            newCoordinates.add(targetCoordinate.trim());
        }

        // 拼接成字符串返回
        return String.join(";", newCoordinates);
    }

    public static void main(String[] args) {
        // 测试用例1：目标坐标在中间
        String coords1 = "114.154214,25.025656;114.480184,25.150070;114.479767,25.152038;114.478969,25.149890;\n" +
                "114.467663,25.147045;114.451557,25.156723;114.438184,25.086212;114.473623,25.116658;\n" +
                "114.479169,25.148869;114.154214,25.025656";
        String target1 = "114.154214,25.025656";
        String result1 = rebuildCoordinateString(coords1, target1);
        System.out.println(result1);
    }
}