package com.ict.datamanagement.domain.dto.car.actual;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("添加车辆实情表单")
@AllArgsConstructor
@NoArgsConstructor
public class AddCarActualRequest {
    @ApiModelProperty(value = "车牌号", dataType = "String")
    private String licensePlateNumber;

    @ApiModelProperty(value = "配送域", dataType = "String")
    private String deliveryAreaName;

    @ApiModelProperty(value = "驾驶人", dataType = "String")
    private String carDriverName;

    @ApiModelProperty(value = "星期", dataType = "String")
    private String week;

    @ApiModelProperty(value = "实际载货量", dataType = "String")
    private String actualLoad;

    @ApiModelProperty(value = "日期", dataType = "String")
    private String date;

    @ApiModelProperty(value = "实际工作时长", dataType = "String")
    private String actualTime;

    @ApiModelProperty(value = "路线", dataType = "String")
    private String route;
}
