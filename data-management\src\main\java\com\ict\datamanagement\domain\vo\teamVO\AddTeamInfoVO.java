package com.ict.datamanagement.domain.vo.teamVO;

import com.ict.datamanagement.domain.info.DeliveryInfo;
import com.ict.datamanagement.domain.info.TransitDepotInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddTeamInfoVO {
    //班组名称
    @ApiModelProperty(value = "班组名称",dataType = "String")
    private String teamName;
    //配送域
    @ApiModelProperty(value = "配送域对象集合",dataType = "List")
    private List<DeliveryInfo> deliveryList;


}

