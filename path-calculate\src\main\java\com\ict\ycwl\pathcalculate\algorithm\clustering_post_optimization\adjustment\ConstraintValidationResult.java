package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 约束验证结果
 * 
 * 封装MILP约束验证的结果信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ConstraintValidationResult {
    
    /**
     * 验证是否通过
     */
    private boolean valid;
    
    /**
     * 约束违反列表
     */
    private List<String> violations;
    
    /**
     * MILP求解状态
     */
    private MILPProblem.SolutionStatus solutionStatus;
    
    /**
     * 获取违反数量
     */
    public int getViolationCount() {
        return violations != null ? violations.size() : 0;
    }
    
    /**
     * 是否有违反
     */
    public boolean hasViolations() {
        return getViolationCount() > 0;
    }
    
    /**
     * 获取验证状态描述
     */
    public String getValidationStatusDescription() {
        if (valid) {
            return "通过验证";
        } else if (hasViolations()) {
            return String.format("验证失败 (%d个违反)", getViolationCount());
        } else {
            return "验证失败";
        }
    }
    
    /**
     * 获取求解状态描述
     */
    public String getSolutionStatusDescription() {
        if (solutionStatus == null) {
            return "未知";
        }
        
        switch (solutionStatus) {
            case OPTIMAL:
                return "最优解";
            case FEASIBLE:
                return "可行解";
            case INFEASIBLE:
                return "无可行解";
            case UNBOUNDED:
                return "无界解";
            case ERROR:
                return "求解错误";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 生成验证结果摘要
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("约束验证结果: %s\n", getValidationStatusDescription()));
        summary.append(String.format("MILP求解状态: %s\n", getSolutionStatusDescription()));
        
        if (hasViolations()) {
            summary.append("约束违反详情:\n");
            for (int i = 0; i < violations.size(); i++) {
                summary.append(String.format("  %d. %s\n", i + 1, violations.get(i)));
            }
        }
        
        return summary.toString();
    }
    
    /**
     * 是否为致命错误
     */
    public boolean isFatalError() {
        return !valid && (solutionStatus == MILPProblem.SolutionStatus.ERROR ||
                         solutionStatus == MILPProblem.SolutionStatus.INFEASIBLE);
    }
    
    /**
     * 是否可以修复
     */
    public boolean isRepairable() {
        return !valid && !isFatalError() && hasViolations();
    }
}