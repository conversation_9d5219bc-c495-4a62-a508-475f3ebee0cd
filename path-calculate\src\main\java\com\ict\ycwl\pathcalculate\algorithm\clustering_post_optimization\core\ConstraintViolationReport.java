package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 约束违反报告
 * 
 * 详细记录聚类结果中的各种约束违反情况，
 * 为优化策略选择和效果评估提供数据支持
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ConstraintViolationReport {
    
    /**
     * 中转站信息
     */
    private TransitDepot depot;
    
    /**
     * 每个聚类的工作时间（分钟）
     */
    private List<Double> clusterWorkTimes;
    
    /**
     * 违反450分钟约束的聚类索引列表
     */
    private List<Integer> maxTimeViolations;
    
    /**
     * 是否违反30分钟时间差异约束
     */
    private boolean timeGapViolation;
    
    /**
     * 违反地理合理性约束的聚类索引列表
     */
    private List<Integer> geographicViolations;
    
    /**
     * 违反严重程度
     */
    private ConstraintAnalyzer.ViolationSeverity severity;
    
    /**
     * 推荐的优化策略
     */
    private OptimizationStrategy recommendedStrategy;
    
    /**
     * 最大工作时间（分钟）
     */
    private double maxWorkTime;
    
    /**
     * 最小工作时间（分钟）
     */
    private double minWorkTime;
    
    /**
     * 平均工作时间（分钟）
     */
    private double averageWorkTime;
    
    /**
     * 工作时间方差
     */
    private double workTimeVariance;
    
    /**
     * 最大时间差距（分钟）
     */
    private double maxTimeGap;
    
    /**
     * 检查是否有违反
     */
    public boolean hasViolations() {
        return !maxTimeViolations.isEmpty() || timeGapViolation || !geographicViolations.isEmpty();
    }
    
    /**
     * 获取450分钟约束违反数量
     */
    public int getMaxTimeViolationCount() {
        return maxTimeViolations.size();
    }
    
    /**
     * 获取30分钟差异约束违反数量
     */
    public int getTimeGapViolationCount() {
        return timeGapViolation ? 1 : 0;
    }
    
    /**
     * 获取地理约束违反数量
     */
    public int getGeographicViolationCount() {
        return geographicViolations.size();
    }
    
    /**
     * 获取总违反数量
     */
    public int getTotalViolationCount() {
        return getMaxTimeViolationCount() + getTimeGapViolationCount() + getGeographicViolationCount();
    }
    
    /**
     * 获取约束满足率
     */
    public double getConstraintSatisfactionRate() {
        int totalConstraints = clusterWorkTimes.size() + 1; // 每个聚类的时间约束 + 时间差异约束
        int violatedConstraints = getTotalViolationCount();
        
        if (totalConstraints == 0) {
            return 1.0;
        }
        
        return Math.max(0.0, (double) (totalConstraints - violatedConstraints) / totalConstraints);
    }
    
    /**
     * 检查是否为严重违反
     */
    public boolean isSevereViolation() {
        return severity == ConstraintAnalyzer.ViolationSeverity.SEVERE;
    }
    
    /**
     * 检查是否需要紧急处理
     */
    public boolean needsUrgentProcessing() {
        // 任何聚类超过480分钟或时间差距超过60分钟都需要紧急处理
        return maxWorkTime > 480.0 || maxTimeGap > 60.0;
    }
    
    /**
     * 获取最严重的违反类型
     */
    public ViolationType getMostSevereViolationType() {
        if (maxWorkTime > 500.0) {
            return ViolationType.EXTREME_WORK_TIME;
        } else if (!maxTimeViolations.isEmpty()) {
            return ViolationType.MAX_WORK_TIME;
        } else if (timeGapViolation) {
            return ViolationType.TIME_GAP;
        } else if (!geographicViolations.isEmpty()) {
            return ViolationType.GEOGRAPHIC_DISPERSION;
        } else {
            return ViolationType.NONE;
        }
    }
    
    /**
     * 获取优化优先级
     */
    public OptimizationPriority getOptimizationPriority() {
        if (needsUrgentProcessing()) {
            return OptimizationPriority.URGENT;
        } else if (isSevereViolation()) {
            return OptimizationPriority.HIGH;
        } else if (hasViolations()) {
            return OptimizationPriority.MEDIUM;
        } else {
            return OptimizationPriority.LOW;
        }
    }
    
    /**
     * 生成简要报告
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("中转站: %s, ", depot.getTransitDepotName()));
        summary.append(String.format("聚类数: %d, ", clusterWorkTimes.size()));
        summary.append(String.format("450分钟违反: %d, ", getMaxTimeViolationCount()));
        summary.append(String.format("30分钟差异违反: %s, ", timeGapViolation ? "是" : "否"));
        summary.append(String.format("地理违反: %d, ", getGeographicViolationCount()));
        summary.append(String.format("严重程度: %s, ", severity));
        summary.append(String.format("最大工作时间: %.1f分钟, ", maxWorkTime));
        summary.append(String.format("时间差距: %.1f分钟", maxTimeGap));
        
        return summary.toString();
    }
    
    /**
     * 违反类型枚举
     */
    public enum ViolationType {
        NONE,                   // 无违反
        MAX_WORK_TIME,         // 450分钟工作时间违反
        EXTREME_WORK_TIME,     // 极端工作时间违反(>500分钟)
        TIME_GAP,              // 30分钟时间差异违反
        GEOGRAPHIC_DISPERSION  // 地理分散违反
    }
    
    /**
     * 优化优先级枚举
     */
    public enum OptimizationPriority {
        URGENT,     // 紧急（需要立即处理）
        HIGH,       // 高优先级
        MEDIUM,     // 中等优先级
        LOW         // 低优先级（可选优化）
    }
}