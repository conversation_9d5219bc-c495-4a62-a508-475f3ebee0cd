package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain.AccumulationAssignment;
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;
import org.optaplanner.core.api.score.stream.ConstraintProvider;

import static org.optaplanner.core.api.score.stream.ConstraintCollectors.sum;

/**
 * 核心业务约束提供者：聚类二次优化核心约束
 * 
 * 实现最核心的业务约束逻辑：
 * - 硬约束1：聚集区必须分配
 * - 硬约束2：450分钟工作时间上限
 * - 软约束1：工作量平衡
 * 
 * 简化实现，确保编译通过，满足核心业务需求
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
public class CoreBusinessConstraintProvider implements ConstraintProvider {
    
    // 约束权重常量
    private static final int ASSIGNMENT_HARD_WEIGHT = 100000;     
    private static final int MAX_WORK_TIME_HARD_WEIGHT = 50000;   
    private static final int WORKLOAD_BALANCE_SOFT_WEIGHT = 1000; 
    
    @Override
    public Constraint[] defineConstraints(ConstraintFactory constraintFactory) {
        return new Constraint[] {
            // 硬约束
            allAccumulationsMustBeAssigned(constraintFactory),
            maxWorkTimePerCluster(constraintFactory),
            
            // 软约束
            balanceClusterWorkloads(constraintFactory)
        };
    }
    
    /**
     * 硬约束1：所有聚集区必须被分配到聚类
     * 业务要求：确保没有聚集区被遗漏，这是最基本的约束
     */
    private Constraint allAccumulationsMustBeAssigned(ConstraintFactory constraintFactory) {
        return constraintFactory
            .from(AccumulationAssignment.class)
            .filter(assignment -> assignment.getAssignedCluster() == null)
            .penalize("所有聚集区必须分配", HardSoftScore.ONE_HARD.multiply(ASSIGNMENT_HARD_WEIGHT));
    }
    
    /**
     * 硬约束2：每个聚类的工作时间不能超过450分钟
     * 业务要求：这是路线工作时间的硬性上限，绝对不能违反
     */
    private Constraint maxWorkTimePerCluster(ConstraintFactory constraintFactory) {
        return constraintFactory
            .from(AccumulationAssignment.class)
            .filter(assignment -> assignment.getAssignedCluster() != null)
            .groupBy(AccumulationAssignment::getAssignedCluster, 
                    sum(this::calculateAccumulationWorkTime))
            .filter((cluster, totalWorkTime) -> totalWorkTime > 450)
            .penalize("450分钟工作时间硬约束", HardSoftScore.ONE_HARD.multiply(MAX_WORK_TIME_HARD_WEIGHT),
                (cluster, totalWorkTime) -> totalWorkTime - 450);
    }
    
    /**
     * 软约束1：平衡各聚类工作负载
     * 业务要求：在满足硬约束前提下，尽量让各路线工作量均衡
     * 实现方式：惩罚工作时间过高的聚类
     */
    private Constraint balanceClusterWorkloads(ConstraintFactory constraintFactory) {
        return constraintFactory
            .from(AccumulationAssignment.class)
            .filter(assignment -> assignment.getAssignedCluster() != null)
            .groupBy(AccumulationAssignment::getAssignedCluster, 
                    sum(this::calculateAccumulationWorkTime))
            .filter((cluster, totalWorkTime) -> totalWorkTime > 300) // 超过300分钟开始软惩罚
            .penalize("工作负载平衡", HardSoftScore.ONE_SOFT.multiply(WORKLOAD_BALANCE_SOFT_WEIGHT),
                (cluster, totalWorkTime) -> Math.max(0, totalWorkTime - 300));
    }
    
    /**
     * 计算聚集区工作时间
     * 包括配送时间和往返中转站的时间
     * 
     * 业务逻辑：
     * - 基础配送时间：从聚集区数据中获取
     * - 往返时间：配送时间的20%作为简化估算
     */
    private int calculateAccumulationWorkTime(AccumulationAssignment assignment) {
        if (assignment.getAccumulation() == null) {
            return 0;
        }
        
        // 基础配送时间
        double deliveryTime = assignment.getAccumulation().getDeliveryTime() != null 
            ? assignment.getAccumulation().getDeliveryTime().doubleValue()
            : 0.0;
        
        // 往返中转站时间（简化估算：配送时间的20%）
        // 这里可以接入实际的时间矩阵计算
        double travelTime = deliveryTime * 0.2;
        
        // 返回总工作时间的整数值
        return (int) Math.ceil(deliveryTime + travelTime);
    }
    
    /**
     * 获取约束提供者描述
     */
    public String getDescription() {
        return "核心业务约束提供者 - 硬约束[分配、450分钟], 软约束[负载平衡]";
    }
    
    /**
     * 获取约束权重统计
     */
    public String getConstraintWeights() {
        return String.format(
            "约束权重 - 硬约束[分配:%d, 450分钟:%d], 软约束[负载平衡:%d]",
            ASSIGNMENT_HARD_WEIGHT, MAX_WORK_TIME_HARD_WEIGHT, WORKLOAD_BALANCE_SOFT_WEIGHT
        );
    }
    
    /**
     * 验证约束配置是否合理
     * 硬约束权重应该远大于软约束权重
     */
    public boolean validateConstraintWeights() {
        int minHardWeight = Math.min(ASSIGNMENT_HARD_WEIGHT, MAX_WORK_TIME_HARD_WEIGHT);
        return minHardWeight >= WORKLOAD_BALANCE_SOFT_WEIGHT * 10; // 硬约束至少是软约束的10倍
    }
}