package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TSP后优化管理器
 * 专门在TSP阶段完成后，使用第三方高性能库进行动态调整
 * 
 * 核心职责：
 * 1. 检测TSP结果是否违反450分钟和30分钟时间差约束
 * 2. 使用OptaPlanner/JSPRIT等第三方库重新分配聚集区
 * 3. 实现同一中转站内不同路线间的聚集区转移
 * 4. 不侵入聚类算法，完全在TSP阶段后处理
 */
@Slf4j
@Component
public class TSPPostOptimizationManager {
    
    // 约束参数
    private static final double MAX_ROUTE_TIME_MINUTES = 450.0;  // 450分钟硬约束
    private static final double TIME_GAP_LIMIT_MINUTES = 30.0;   // 30分钟时间差硬约束
    private static final double MIN_ROUTE_TIME_MINUTES = 300.0;  // 最小路线时间
    
    // 第三方库优化器
    private final OptaPlannerVRPReoptimizer optaPlannerOptimizer;
    private final JSPRITVRPReoptimizer jspritOptimizer;
    private final UnifiedTimeCalculationService timeCalculationService;
    
    public TSPPostOptimizationManager() {
        this.optaPlannerOptimizer = new OptaPlannerVRPReoptimizer();
        this.jspritOptimizer = new JSPRITVRPReoptimizer();
        this.timeCalculationService = new UnifiedTimeCalculationService();
    }
    
    /**
     * TSP后动态优化入口
     * 在所有TSP求解完成后调用，使用第三方库进行约束优化
     */
    public boolean performTSPPostOptimization(AlgorithmContext context) {
        log.info("🎯 [TSP后优化] 开始TSP后优化流程");
        
        try {
            // 🔧 强制启用统一时间计算服务，确保一致性
            timeCalculationService.validateAllTimeCalculations(context);
            log.info("✅ [时间统一] 已强制启用统一时间计算服务");
            
            // 分析当前约束违反情况
            ConstraintViolationAnalysis analysis = analyzeConstraintViolations(context);
            
            // 🔍 详细约束违反日志
            logDetailedConstraintViolations(analysis, context);
            
            log.info("📊 [约束总结] 超时路线: {}, 超差距中转站: {}, 最大时间违反: {:.1f}分钟, 最大差距违反: {:.1f}分钟",
                analysis.getViolatingRoutes().size(),
                analysis.getExcessiveGapDepots().size(),
                analysis.getMaxTimeViolation(),
                analysis.getMaxGapViolation());
            
            if (analysis.getViolatingRoutes().isEmpty() && analysis.getExcessiveGapDepots().isEmpty()) {
                log.info("✅ [无需优化] 所有约束均已满足");
                return true;
            }
            
            // 🎛️ 检查动态调整开关状态
            if (!AlgorithmParameters.ENABLE_TSP_DYNAMIC_ADJUSTMENT) {
                log.warn("🚫 [动态调整已禁用] TSP动态调整功能已关闭，专注于TSP本身优化");
                log.info("💡 [建议] 当前约束违反需要在TSP求解阶段优化，或者在聚类阶段改进分配");
                log.info("🔧 [配置] 如需启用动态调整，请设置 AlgorithmParameters.ENABLE_TSP_DYNAMIC_ADJUSTMENT = true");
                
                // 输出详细的改进建议
                provideTSPOptimizationSuggestions(analysis, context);
                return false;  // 返回false表示需要其他阶段改进
            }
            
            // 🔒 动态调整已启用，备份数据并进行第三方库优化
            Map<Long, List<RouteResult>> backupRoutes = createRouteBackup(context);
            
            // 🎯 根据约束违反程度选择合适的第三方库进行重优化
            // 🔧 降低触发门槛，确保第三方库能更积极地介入优化
            boolean success = false;
            
            if (analysis.getViolatingRoutes().size() > 3 || analysis.getMaxTimeViolation() > 20 || analysis.getMaxGapViolation() > 60) {
                // 🔧 降低门槛：严重违反使用OptaPlanner深度优化
                log.info("🔧 [严重违反] 触发OptaPlanner深度重优化 (违反路线:{}, 时间超出:{:.1f}分钟, 差距超出:{:.1f}分钟)",
                    analysis.getViolatingRoutes().size(), analysis.getMaxTimeViolation(), analysis.getMaxGapViolation());
                success = optaPlannerOptimizer.reoptimizeWithOptaPlanner(context, analysis);
                
                if (!success) {
                    // OptaPlanner失败，尝试JSPRIT作为备选
                    log.warn("⚠️ [OptaPlanner失败] 尝试使用JSPRIT作为备选方案");
                    success = jspritOptimizer.reoptimizeWithJSPRIT(context, analysis);
                }
                
            } else {
                // 轻微违反：使用JSPRIT快速优化
                log.info("⚡ [轻微违反] 使用JSPRIT进行快速重优化 (违反路线:{}, 时间超出:{:.1f}分钟, 差距超出:{:.1f}分钟)",
                    analysis.getViolatingRoutes().size(), analysis.getMaxTimeViolation(), analysis.getMaxGapViolation());
                success = jspritOptimizer.reoptimizeWithJSPRIT(context, analysis);
                
                if (!success) {
                    // JSPRIT失败，尝试OptaPlanner作为备选
                    log.warn("⚠️ [JSPRIT失败] 尝试使用OptaPlanner作为备选方案");
                    success = optaPlannerOptimizer.reoptimizeWithOptaPlanner(context, analysis);
                }
            }
            
            if (success) {
                // 第三方库重优化成功，验证最终结果
                validateFinalResults(context);
                log.info("🎉 [TSP后优化成功] 第三方库重优化完成，约束满足情况已改善");
                
                // 清理备份，因为已经成功
                backupRoutes.clear();
                
            } else {
                // 所有第三方库都失败，恢复原始数据
                log.warn("❌ [TSP后优化失败] 所有第三方库重优化都失败，保持原始数据");
                restoreFromBackup(context, backupRoutes);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("❌ [TSP后优化异常] {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建路线数据备份
     */
    private Map<Long, List<RouteResult>> createRouteBackup(AlgorithmContext context) {
        Map<Long, List<RouteResult>> backup = new HashMap<>();
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> originalRoutes = entry.getValue();
            
            // 深度复制路线数据
            List<RouteResult> backupRoutes = new ArrayList<>();
            for (RouteResult route : originalRoutes) {
                RouteResult backupRoute = new RouteResult();
                backupRoute.setRouteId(route.getRouteId());
                backupRoute.setRouteName(route.getRouteName());
                backupRoute.setTransitDepotId(route.getTransitDepotId());
                backupRoute.setAccumulationSequence(new ArrayList<>(route.getAccumulationSequence()));
                backupRoute.setTotalWorkTime(route.getTotalWorkTime());
                backupRoute.setPolyline(new ArrayList<>(route.getPolyline()));
                backupRoutes.add(backupRoute);
            }
            
            backup.put(transitDepotId, backupRoutes);
        }
        
        log.debug("🔒 [数据备份] 备份了{}个中转站的路线数据", backup.size());
        return backup;
    }
    
    /**
     * 分析约束违反情况
     */
    private ConstraintViolationAnalysis analyzeConstraintViolations(AlgorithmContext context) {
        ConstraintViolationAnalysis analysis = new ConstraintViolationAnalysis();
        
        List<RouteResult> violatingRoutes = new ArrayList<>();
        List<Long> excessiveGapDepots = new ArrayList<>();
        double maxTimeViolation = 0.0;
        double maxGapViolation = 0.0;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            // 检查450分钟约束违反
            for (RouteResult route : routes) {
                if (route.getTotalWorkTime() > MAX_ROUTE_TIME_MINUTES) {
                    violatingRoutes.add(route);
                    double violation = route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES;
                    maxTimeViolation = Math.max(maxTimeViolation, violation);
                    
                    log.warn("🚨 [超时路线] {}站-{}: {:.1f}分钟 (超过{:.1f}分钟)",
                        depot.getTransitDepotName(),
                        route.getRouteName(),
                        route.getTotalWorkTime(),
                        MAX_ROUTE_TIME_MINUTES);
                }
            }
            
            // 检查30分钟时间差约束违反
            if (routes.size() > 1) {
                double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
                double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
                double timeGap = maxTime - minTime;
                
                if (timeGap > TIME_GAP_LIMIT_MINUTES) {
                    excessiveGapDepots.add(transitDepotId);
                    double violation = timeGap - TIME_GAP_LIMIT_MINUTES;
                    maxGapViolation = Math.max(maxGapViolation, violation);
                    
                    log.warn("⏰ [超差距中转站] {}: 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟 (超过{:.1f}分钟)",
                        depot.getTransitDepotName(),
                        maxTime, minTime, timeGap, TIME_GAP_LIMIT_MINUTES);
                }
            }
        }
        
        analysis.setViolatingRoutes(violatingRoutes);
        analysis.setExcessiveGapDepots(excessiveGapDepots);
        analysis.setMaxTimeViolation(maxTimeViolation);
        analysis.setMaxGapViolation(maxGapViolation);
        
        return analysis;
    }
    
    /**
     * 详细记录约束违反情况
     */
    private void logDetailedConstraintViolations(ConstraintViolationAnalysis analysis, AlgorithmContext context) {
        log.info("🔍 [详细约束分析] ========== 开始详细约束违反分析 ==========");
        
        // 统计所有中转站的情况
        int totalDepots = context.getOptimizedRoutes().size();
        int totalRoutes = context.getOptimizedRoutes().values().stream()
            .mapToInt(List::size).sum();
        
        log.info("📊 [全局统计] 总中转站: {}, 总路线: {}", totalDepots, totalRoutes);
        
        // 详细分析每个中转站
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            if (routes.isEmpty()) continue;
            
            // 计算中转站统计信息
            double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
            double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
            double avgTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).average().orElse(0.0);
            double timeGap = maxTime - minTime;
            
            int violatingRoutes = (int) routes.stream()
                .filter(route -> route.getTotalWorkTime() > MAX_ROUTE_TIME_MINUTES)
                .count();
            
            boolean hasTimeGapViolation = timeGap > TIME_GAP_LIMIT_MINUTES;
            boolean hasTimeViolation = violatingRoutes > 0;
            
            // 记录中转站级别的违反情况
            String violationStatus = "";
            if (hasTimeViolation && hasTimeGapViolation) {
                violationStatus = "🚨 双重违反";
            } else if (hasTimeViolation) {
                violationStatus = "⚠️ 时间违反";
            } else if (hasTimeGapViolation) {
                violationStatus = "⏰ 差距违反";
            } else {
                violationStatus = "✅ 无违反";
            }
            
            log.info("🏭 [中转站{}] {} - 路线数:{}, 违反路线:{}, 最长:{:.1f}分, 最短:{:.1f}分, 平均:{:.1f}分, 差距:{:.1f}分",
                depot.getTransitDepotName(), violationStatus, routes.size(), violatingRoutes,
                maxTime, minTime, avgTime, timeGap);
            
            // 详细记录违反的路线
            if (hasTimeViolation) {
                routes.stream()
                    .filter(route -> route.getTotalWorkTime() > MAX_ROUTE_TIME_MINUTES)
                    .forEach(route -> {
                        double violation = route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES;
                        int pointCount = route.getAccumulationSequence().size();
                        log.warn("  🚨 [超时路线] {}: {:.1f}分钟 (超出{:.1f}分钟), {}个聚集区",
                            route.getRouteName(), route.getTotalWorkTime(), violation, pointCount);
                    });
            }
            
            // 如果有时间差距违反，记录最长和最短路线的详情
            if (hasTimeGapViolation) {
                RouteResult maxRoute = routes.stream()
                    .max((r1, r2) -> Double.compare(r1.getTotalWorkTime(), r2.getTotalWorkTime()))
                    .orElse(null);
                RouteResult minRoute = routes.stream()
                    .min((r1, r2) -> Double.compare(r1.getTotalWorkTime(), r2.getTotalWorkTime()))
                    .orElse(null);
                
                if (maxRoute != null && minRoute != null) {
                    log.warn("  ⏰ [差距违反] 最长路线{}: {:.1f}分钟({}个点), 最短路线{}: {:.1f}分钟({}个点), 差距{:.1f}分钟",
                        maxRoute.getRouteName(), maxRoute.getTotalWorkTime(), maxRoute.getAccumulationSequence().size(),
                        minRoute.getRouteName(), minRoute.getTotalWorkTime(), minRoute.getAccumulationSequence().size(),
                        timeGap);
                }
            }
        }
        
        // 全局违反统计
        log.info("🔍 [违反总结] 超时路线总数: {}, 超差距中转站总数: {}", 
            analysis.getViolatingRoutes().size(), analysis.getExcessiveGapDepots().size());
        
        if (!analysis.getViolatingRoutes().isEmpty()) {
            log.warn("⚠️ [最严重违反] 最大时间违反: {:.1f}分钟, 最大差距违反: {:.1f}分钟",
                analysis.getMaxTimeViolation(), analysis.getMaxGapViolation());
        }
        
        log.info("🔍 [详细约束分析] ========== 约束违反分析完成 ==========");
    }
    
    /**
     * 提供TSP优化建议（当动态调整被禁用时）
     */
    private void provideTSPOptimizationSuggestions(ConstraintViolationAnalysis analysis, AlgorithmContext context) {
        log.info("💡 [TSP优化建议] ========== 开始提供TSP本身优化建议 ==========");
        
        // 分析超时路线的特征
        if (!analysis.getViolatingRoutes().isEmpty()) {
            log.info("🔍 [超时路线分析] 共{}条路线超过450分钟约束", analysis.getViolatingRoutes().size());
            
            Map<Integer, Integer> sizeDistribution = new HashMap<>();
            double totalOvertime = 0.0;
            
            for (RouteResult route : analysis.getViolatingRoutes()) {
                int pointCount = route.getAccumulationSequence().size();
                sizeDistribution.put(pointCount, sizeDistribution.getOrDefault(pointCount, 0) + 1);
                totalOvertime += (route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES);
                
                log.warn("  🚨 [超时详情] 路线{}: {}个聚集区, {:.1f}分钟 (超出{:.1f}分钟)", 
                    route.getRouteName(), pointCount, route.getTotalWorkTime(), 
                    route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES);
            }
            
            // 建议1：针对路线规模的优化建议
            log.info("📊 [路线规模分析] 超时路线的聚集区数量分布:");
            sizeDistribution.entrySet().stream()
                .sorted(Map.Entry.<Integer, Integer>comparingByKey())
                .forEach(entry -> {
                    log.info("  📏 {}个聚集区: {}条路线", entry.getKey(), entry.getValue());
                    if (entry.getKey() > 20) {
                        log.warn("    ⚠️ [规模过大] {}个聚集区可能导致TSP求解质量下降", entry.getKey());
                    }
                });
            
            // 建议2：TSP算法优化建议
            log.info("🛠️ [TSP算法建议]:");
            log.info("  1. 🔧 当前TSP优化质量: {}", AlgorithmParameters.TSP_OPTIMIZATION_QUALITY);
            log.info("  2. 🎯 当前求解器策略: {}", AlgorithmParameters.TSP_SOLVER_STRATEGY);
            log.info("  3. ⏱️ 当前求解时间限制: {}秒", AlgorithmParameters.TSP_TIME_LIMIT_SECONDS);
            
            // 建议3：具体优化方案
            log.info("🎯 [具体建议]:");
            log.info("  1. 💪 增加TSP求解时间: TSP_TIME_LIMIT_SECONDS从{}秒增加到120秒", AlgorithmParameters.TSP_TIME_LIMIT_SECONDS);
            log.info("  2. 🎨 优化求解器选择: 确保OR-Tools优先并有效降级到遗传算法");
            log.info("  3. 🔄 多轮TSP优化: 对超时路线进行2-3轮独立TSP重优化");
            log.info("  4. 🎛️ 调整遗传算法参数: 增加种群大小和代数以提高解质量");
            
            log.info("💰 [成本估算] 平均超时{:.1f}分钟/路线, 总超时{:.1f}分钟", 
                totalOvertime / analysis.getViolatingRoutes().size(), totalOvertime);
        }
        
        // 分析时间差距问题
        if (!analysis.getExcessiveGapDepots().isEmpty()) {
            log.info("⏰ [时间差距分析] 共{}个中转站超过30分钟时间差约束", analysis.getExcessiveGapDepots().size());
            
            for (Long depotId : analysis.getExcessiveGapDepots()) {
                List<RouteResult> routes = context.getOptimizedRoutes().get(depotId);
                TransitDepot depot = context.getTransitDepotById(depotId);
                
                if (routes != null && routes.size() > 1) {
                    double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
                    double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
                    double timeGap = maxTime - minTime;
                    
                    log.warn("  ⏰ [差距详情] 中转站{}: 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟", 
                        depot.getTransitDepotName(), maxTime, minTime, timeGap);
                    
                    // 分析是否可以通过TSP优化解决
                    if (timeGap > 60.0) {
                        log.warn("    🚨 [严重不平衡] 差距超过60分钟，可能需要聚类阶段重新分配");
                    } else {
                        log.info("    💡 [TSP可优化] 差距适中，可能通过更好的TSP求解改善");
                    }
                }
            }
        }
        
        // 总结建议
        log.info("📋 [总结建议]:");
        log.info("  🎯 短期: 专注提升TSP求解质量和时间限制");
        log.info("  🔄 中期: 实现多轮TSP重优化机制");
        log.info("  🏗️ 长期: 考虑改进聚类算法的初始分配质量");
        log.info("  🎛️ 开关: 如需动态调整，设置ENABLE_TSP_DYNAMIC_ADJUSTMENT=true");
        
        log.info("💡 [TSP优化建议] ========== TSP优化建议完成 ==========");
    }
    
    /**
     * 从备份恢复路线数据
     */
    private void restoreFromBackup(AlgorithmContext context, Map<Long, List<RouteResult>> backupRoutes) {
        try {
            log.info("🔄 [数据恢复] 开始从备份恢复路线数据");
            
            for (Map.Entry<Long, List<RouteResult>> entry : backupRoutes.entrySet()) {
                Long transitDepotId = entry.getKey();
                List<RouteResult> backupRouteList = entry.getValue();
                
                // 恢复到context中
                context.getOptimizedRoutes().put(transitDepotId, backupRouteList);
            }
            
            log.info("✅ [数据恢复成功] 已从备份恢复{}个中转站的路线数据", backupRoutes.size());
            
        } catch (Exception e) {
            log.error("💥 [数据恢复失败] 从备份恢复数据时发生异常: {}", e.getMessage());
        }
    }
    
    /**
     * 验证最终结果
     */
    private void validateFinalResults(AlgorithmContext context) {
        log.info("🔍 [最终验证] 检查第三方库优化后的约束满足情况");
        
        int remainingViolatingRoutes = 0;
        int remainingExcessiveGapDepots = 0;
        double maxTimeViolation = 0.0;
        double maxGapViolation = 0.0;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            // 重新检查约束
            for (RouteResult route : routes) {
                if (route.getTotalWorkTime() > MAX_ROUTE_TIME_MINUTES) {
                    remainingViolatingRoutes++;
                    maxTimeViolation = Math.max(maxTimeViolation, 
                        route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES);
                }
            }
            
            if (routes.size() > 1) {
                double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
                double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
                double timeGap = maxTime - minTime;
                
                if (timeGap > TIME_GAP_LIMIT_MINUTES) {
                    remainingExcessiveGapDepots++;
                    maxGapViolation = Math.max(maxGapViolation, timeGap - TIME_GAP_LIMIT_MINUTES);
                }
                
                log.info("✅ [中转站结果] {}: 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟",
                    depot.getTransitDepotName(), maxTime, minTime, timeGap);
            }
        }
        
        if (remainingViolatingRoutes == 0 && remainingExcessiveGapDepots == 0) {
            log.info("🎉 [完美结果] 第三方库优化后所有约束均已满足！");
        } else {
            log.warn("⚠️ [剩余违反] 超时路线: {}, 超差距中转站: {}, 最大时间违反: {:.1f}分钟, 最大差距违反: {:.1f}分钟",
                remainingViolatingRoutes, remainingExcessiveGapDepots, 
                maxTimeViolation, maxGapViolation);
        }
    }
    
    /**
     * 约束违反分析结果类
     */
    public static class ConstraintViolationAnalysis {
        private List<RouteResult> violatingRoutes = new ArrayList<>();
        private List<Long> excessiveGapDepots = new ArrayList<>();
        private double maxTimeViolation = 0.0;
        private double maxGapViolation = 0.0;
        
        // Getters and Setters
        public List<RouteResult> getViolatingRoutes() { return violatingRoutes; }
        public void setViolatingRoutes(List<RouteResult> violatingRoutes) { this.violatingRoutes = violatingRoutes; }
        
        public List<Long> getExcessiveGapDepots() { return excessiveGapDepots; }
        public void setExcessiveGapDepots(List<Long> excessiveGapDepots) { this.excessiveGapDepots = excessiveGapDepots; }
        
        public double getMaxTimeViolation() { return maxTimeViolation; }
        public void setMaxTimeViolation(double maxTimeViolation) { this.maxTimeViolation = maxTimeViolation; }
        
        public double getMaxGapViolation() { return maxGapViolation; }
        public void setMaxGapViolation(double maxGapViolation) { this.maxGapViolation = maxGapViolation; }
    }
}