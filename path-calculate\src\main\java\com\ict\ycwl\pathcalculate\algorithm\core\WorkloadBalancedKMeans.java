package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import com.ict.ycwl.pathcalculate.algorithm.utils.ConvexHullGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于工作量均衡的K-means聚类算法
 * 目标是将聚集区分配到各路线，使总工作时间尽可能均衡
 */
@Slf4j
@Component
public class WorkloadBalancedKMeans {
    
    // ===================== 算法配置参数 =====================
    
    // 聚类工作时间控制参数 - 根据用户450分钟要求调整
    private static final double MIN_CLUSTER_WORK_TIME = 360.0;    // 最小工作时间360分钟
    private static final double MAX_CLUSTER_WORK_TIME = 450.0;    // 最大工作时间450分钟（用户要求）
    private static final double IDEAL_CLUSTER_WORK_TIME = 420.0;  // 理想工作时间420分钟
    private static final double MERGE_MAX_WORK_TIME = 450.0;      // 合并上限450分钟（与硬约束一致）
    private static final double TIME_BALANCE_THRESHOLD = 30.0;    // 时间平衡阈值30分钟
    
    // 拆分合并阈值参数
    private static final double MERGE_THRESHOLD_RATIO = 0.85;     // 85%阈值需要合并（放松）
    private static final double SPLIT_THRESHOLD_RATIO = 1.4;      // 140%阈值需要拆分（防止过度拆分）
    private static final double MERGE_MAX_RATIO = 1.15;          // 合并不超过115%上限（匹配拆分阈值）
    
    // 地理约束优化参数（基于七大问题分析的改进）
    private static final double SHAPE_COMPACTNESS_THRESHOLD = 0.7;      // 形态紧凑度阈值
    private static final double CONNECTIVITY_PROTECTION_RATIO = 2.0;     // 连通性保护比例
    private static final double OUTLIER_PREVENTION_RADIUS_MULT = 2.0;    // 游离点预防半径倍数
    private static final double DENSITY_ADAPTATION_THRESHOLD = 3.0;      // 密度自适应阈值
    private static final int LOCAL_IMPORTANCE_KNN = 5;                   // 局部重要性K近邻数量
    private static final double BRIDGE_POINT_TOLERANCE = 1.5;           // 桥接点容忍度
    
    // ===================== 核心组件 =====================
    
    private final ClusteringQualityEvaluator qualityEvaluator;
    private final UnifiedTimeCalculationService timeCalculationService;
    private final Random random = new Random();
    
    // 距离计算缓存
    private Map<String, Double> distanceCache = new HashMap<>();
    
    public WorkloadBalancedKMeans(ClusteringQualityEvaluator qualityEvaluator) {
        this.qualityEvaluator = qualityEvaluator;
        this.timeCalculationService = new UnifiedTimeCalculationService();
    }
    
    /**
     * 带缓存的距离计算方法 - 优化性能
     */
    private double calculateDistanceWithCache(double lat1, double lon1, double lat2, double lon2) {
        // 生成缓存键（保留6位小数精度，平衡精度和缓存效率）
        String key = String.format("%.6f,%.6f,%.6f,%.6f", 
            Math.min(lat1, lat2), Math.min(lon1, lon2), 
            Math.max(lat1, lat2), Math.max(lon1, lon2));
        
        return distanceCache.computeIfAbsent(key, k -> calculateDistance(lat1, lon1, lat2, lon2));
    }
    
    /**
     * 清理距离缓存 - 在新的聚类轮次开始时调用
     */
    private void clearDistanceCache() {
        if (distanceCache.size() > 10000) { // 防止缓存过大
            distanceCache.clear();
            debugLog("距离缓存已清理，重新开始计算");
        }
    }
    
    /**
     * 调试日志方法 - 替代log.debug以解决编译问题
     */
    private void debugLog(String message) {
        // 使用System.out.println以避免log变量问题
        System.out.println("[DEBUG] " + message);
    }
    
    /**
     * 带格式化的调试日志方法
     */
    private void debugLog(String format, Object... args) {
        String message = String.format(format, args);
        System.out.println("[DEBUG] " + message);
    }
    
    // ===================== 参数计算方法 =====================
    
    /**
     * 计算合并阈值 - 基于理想工作时间和配置比例
     */
    private double calculateMergeThreshold() {
        return Math.max(MIN_CLUSTER_WORK_TIME, IDEAL_CLUSTER_WORK_TIME * MERGE_THRESHOLD_RATIO);
    }
    
    /**
     * 计算拆分阈值 - 基于理想工作时间和配置比例  
     */
    private double calculateSplitThreshold() {
        return Math.min(MAX_CLUSTER_WORK_TIME, IDEAL_CLUSTER_WORK_TIME * SPLIT_THRESHOLD_RATIO);
    }
    
    /**
     * 计算合并上限 - 基于理想工作时间和配置比例
     */
    private double calculateMergeMaxConstraint() {
        return Math.min(MAX_CLUSTER_WORK_TIME, IDEAL_CLUSTER_WORK_TIME * MERGE_MAX_RATIO);
    }
    
    /**
     * 验证聚类是否符合绝对大小约束
     */
    private boolean isClusterSizeValid(double workTime) {
        return workTime >= MIN_CLUSTER_WORK_TIME && workTime <= MAX_CLUSTER_WORK_TIME;
    }
    
    /**
     * 强制验证和修复聚类大小
     */
    private List<List<Accumulation>> enforceClusterSizeConstraints(
            List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix, int targetClusterCount) {
        
        log.info("========== 强制聚类大小约束验证 ==========");
        log.info("当前聚类数: {}, 目标聚类数: {}", clusters.size(), targetClusterCount);
        
        List<List<Accumulation>> validatedClusters = new ArrayList<>();
        List<List<Accumulation>> oversizedClusters = new ArrayList<>();
        List<List<Accumulation>> undersizedClusters = new ArrayList<>();
        
        // 分类聚类
        for (List<Accumulation> cluster : clusters) {
            if (cluster.isEmpty()) continue;
            
            double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
            
            if (workTime > MAX_CLUSTER_WORK_TIME) {
                // 🔧 关键修复：450分钟硬约束，无论聚类数量如何都必须拆分
                oversizedClusters.add(cluster); // 强制拆分超大聚类
                log.warn("🚨 [硬约束违反] 发现超大聚类: {}分钟 (>{}分钟硬约束), {}个点，强制拆分", 
                    String.format("%.1f", workTime), String.format("%.1f", MAX_CLUSTER_WORK_TIME), cluster.size());
                    
                // 记录聚类数量状态但不影响拆分决策
                if (clusters.size() >= targetClusterCount) {
                    log.info("💡 [算法调整] 聚类数量已达标({}>={})，但仍需拆分超大聚类以满足时间约束", 
                        clusters.size(), targetClusterCount);
                }
            } else if (workTime < MIN_CLUSTER_WORK_TIME) {
                undersizedClusters.add(cluster);
                log.warn("发现过小聚类: {}分钟 (<{}分钟下限), {}个点", 
                    String.format("%.1f", workTime), String.format("%.1f", MIN_CLUSTER_WORK_TIME), cluster.size());
            } else {
                validatedClusters.add(cluster);
            }
        }
        
        // 处理超大聚类 - 强制拆分
        for (List<Accumulation> oversizedCluster : oversizedClusters) {
            List<List<Accumulation>> splitResults = forceSplitOversizedCluster(oversizedCluster, depot, timeMatrix);
            validatedClusters.addAll(splitResults);
        }
        
        // 处理过小聚类 - 强制合并
        validatedClusters.addAll(forceMergeUndersizedClusters(undersizedClusters, validatedClusters, depot, timeMatrix));
        
        log.info("聚类大小约束验证完成: {}个有效聚类", validatedClusters.size());
        return validatedClusters;
    }
    
    /**
     * 计算最优拆分数量：确保拆分后每个子聚类>300分钟（用户设计）
     */
    private int calculateOptimalSplitParts(double totalWorkTime) {
        // 最大拆分数：确保每份>300分钟
        int maxSplitParts = (int) Math.floor(totalWorkTime / MIN_CLUSTER_WORK_TIME);
        // 理想拆分数：尽量接近350分钟
        int idealSplitParts = (int) Math.ceil(totalWorkTime / IDEAL_CLUSTER_WORK_TIME);
        
        // 取较小值，确保不会产生过小聚类
        int optimalParts = Math.min(maxSplitParts, idealSplitParts);
        
        // 至少拆分成2份
        return Math.max(2, optimalParts);
    }
    
    /**
     * 强制拆分超大聚类
     */
    private List<List<Accumulation>> forceSplitOversizedCluster(
            List<Accumulation> oversizedCluster, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        double totalWorkTime = calculateClusterWorkTime(oversizedCluster, depot, timeMatrix);
        
        // 计算理想拆分数量，确保每个子聚类>300分钟（用户设计）
        int optimalSplitCount = calculateOptimalSplitParts(totalWorkTime);
        optimalSplitCount = Math.max(2, Math.min(optimalSplitCount, oversizedCluster.size() / 2));
        
        log.info("强制拆分超大聚类: {}分钟 -> {}个子聚类", 
            String.format("%.1f", totalWorkTime), optimalSplitCount);
        
        // 使用地理聚类拆分，确保地理紧密性
        List<List<Accumulation>> splitResults = splitClusterGeographically(oversizedCluster, optimalSplitCount, depot);
        
        // 验证拆分结果，如果仍有超大聚类，递归拆分
        List<List<Accumulation>> finalResults = new ArrayList<>();
        for (List<Accumulation> subCluster : splitResults) {
            double subWorkTime = calculateClusterWorkTime(subCluster, depot, timeMatrix);
            if (subWorkTime > MAX_CLUSTER_WORK_TIME && subCluster.size() > 1) {
                // 递归拆分
                finalResults.addAll(forceSplitOversizedCluster(subCluster, depot, timeMatrix));
            } else {
                finalResults.add(subCluster);
            }
        }
        
        return finalResults;
    }
    
    /**
     * 强制合并过小聚类
     */
    private List<List<Accumulation>> forceMergeUndersizedClusters(
            List<List<Accumulation>> undersizedClusters, 
            List<List<Accumulation>> validClusters,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        List<List<Accumulation>> result = new ArrayList<>();
        
        for (List<Accumulation> smallCluster : undersizedClusters) {
            boolean merged = false;
            double smallClusterTime = calculateClusterWorkTime(smallCluster, depot, timeMatrix);
            
            // 尝试合并到最合适的有效聚类
            List<Accumulation> bestTarget = null;
            double bestScore = Double.MAX_VALUE;
            
            for (List<Accumulation> validCluster : validClusters) {
                double validClusterTime = calculateClusterWorkTime(validCluster, depot, timeMatrix);
                double mergedTime = smallClusterTime + validClusterTime;
                
                // 检查合并后是否仍在600分钟限制内（用户设计）
                if (mergedTime <= MERGE_MAX_WORK_TIME) {
                    // 计算地理距离作为合并适配度
                    double distance = calculateClusterToClusterDistance(smallCluster, validCluster);
                    double score = distance + Math.abs(mergedTime - IDEAL_CLUSTER_WORK_TIME) * 0.1;
                    
                    if (score < bestScore) {
                        bestScore = score;
                        bestTarget = validCluster;
                    }
                }
            }
            
            if (bestTarget != null) {
                log.info("强制合并过小聚类: {}分钟 -> 合并到目标聚类", 
                    String.format("%.1f", smallClusterTime));
                bestTarget.addAll(smallCluster);
                merged = true;
            }
            
            if (!merged) {
                // 无法合并，使用打散策略让附近聚类瓜分（用户设计）
                log.warn("过小聚类无法合并，启用打散策略: {}分钟", 
                    String.format("%.1f", smallClusterTime));
                disperseClusterToNearby(smallCluster, validClusters, depot, timeMatrix);
            }
        }
        
        return result;
    }
    
    /**
     * 聚类打散策略：无法合并时让附近聚类瓜分（用户设计）
     */
    private void disperseClusterToNearby(
            List<Accumulation> smallCluster,
            List<List<Accumulation>> allClusters,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        log.info("开始打散聚类: {}分钟，{}个点", 
            String.format("%.1f", calculateClusterWorkTime(smallCluster, depot, timeMatrix)), 
            smallCluster.size());
        
        for (Accumulation point : smallCluster) {
            // 找到地理距离最近且工作时间允许的聚类
            List<Accumulation> bestTarget = findBestTargetForDispersion(
                point, allClusters, depot, timeMatrix);
            if (bestTarget != null) {
                bestTarget.add(point);
                debugLog("点%s被分配给附近聚类", point.getAccumulationId());
            } else {
                log.warn("点{}无法找到合适的分配目标", point.getAccumulationId());
                // 强制分配给工作时间最小的聚类
                List<Accumulation> minTimeCluster = allClusters.stream()
                    .min(Comparator.comparingDouble(cluster -> 
                        calculateClusterWorkTime(cluster, depot, timeMatrix)))
                    .orElse(null);
                if (minTimeCluster != null) {
                    minTimeCluster.add(point);
                    log.info("点{}被强制分配给工作时间最小的聚类", point.getAccumulationId());
                }
            }
        }
    }
    
    /**
     * 为打散点找到最佳分配目标（遵循地理聚集、就近原则）
     */
    private List<Accumulation> findBestTargetForDispersion(
            Accumulation point,
            List<List<Accumulation>> allClusters,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        List<Accumulation> bestTarget = null;
        double bestScore = Double.MAX_VALUE;
        
        for (List<Accumulation> cluster : allClusters) {
            if (cluster.isEmpty()) continue;
            
            // 计算加入该聚类后的工作时间
            double currentTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
            double pointWorkTime = calculateAccumulationWorkTime(point, depot);
            double newTime = currentTime + pointWorkTime;
            
            // 检查是否在600分钟限制内
            if (newTime <= MERGE_MAX_WORK_TIME) {
                // 计算地理距离作为评分
                double distance = calculatePointToClusterDistance(point, cluster);
                // 综合评分：距离 + 时间偏差惩罚
                double score = distance + Math.abs(newTime - IDEAL_CLUSTER_WORK_TIME) * 0.1;
                
                if (score < bestScore) {
                    bestScore = score;
                    bestTarget = cluster;
                }
            }
        }
        
        return bestTarget;
    }
    
    /**
     * 计算点到聚类的地理距离
     */
    private double calculatePointToClusterDistance(Accumulation point, List<Accumulation> cluster) {
        // 计算到聚类中心点的距离
        double avgLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        return calculateDistance(point.getLongitude(), point.getLatitude(), avgLng, avgLat);
    }
    
    /**
     * 计算两个聚类之间的地理距离（取聚类中心点距离）
     */
    private double calculateClusterToClusterDistance(List<Accumulation> cluster1, List<Accumulation> cluster2) {
        // 计算cluster1的中心点
        double avgLng1 = cluster1.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat1 = cluster1.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        // 计算cluster2的中心点
        double avgLng2 = cluster2.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat2 = cluster2.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        // 返回两个中心点之间的距离
        return calculateDistance(avgLng1, avgLat1, avgLng2, avgLat2);
    }
    
    /**
     * 按工作量均衡进行聚类
     * @param accumulations 待分配的聚集区列表
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 分组结果，每个组代表一条路线
     */
    public List<List<Accumulation>> clusterByWorkload(
            List<Accumulation> accumulations, 
            TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix) {
        
        int k = calculateOptimalClusterCount(depot, accumulations, timeMatrix);
        log.info("中转站 {} 根据工作量计算最优聚类数: {} (原硬编码: {})", 
            depot.getTransitDepotName(), k, depot.getRouteCount());
        log.info("开始K-means聚类：{} 个聚集区分配到 {} 条路线", accumulations.size(), k);
        
        if (accumulations.size() <= k) {
            // 聚集区数量少于或等于路线数，每个聚集区一条路线
            return createSingletonClusters(accumulations);
        }
        
        // 阶段1：纯地理K-means聚类（完全忽略时间，只考虑地理距离）
        log.info("阶段1：纯地理K-means聚类 - 完全基于地理位置聚类");
        List<List<Accumulation>> clusters = pureGeographicClustering(accumulations, k);
        
        // 阶段2：基于拆分合并的时间平衡优化（保持地理聚集，实现时间均衡）
        log.info("阶段2：激进转移策略时间平衡优化");
        log.info("=== 激进转移策略流程说明 ===");
        log.info("1. 激进拆分：650分钟以上聚类强制拆分，允许临时超过目标聚类数");
        log.info("2. 智能合并：基于600分钟上限智能合并回目标数量");
        log.info("3. 强制合并：处理剩余小聚类，确保所有聚类符合300-600分钟范围");
        log.info("4. 方差优化：基于整体方差判断的激进转移策略");
        clusters = splitAndMergeTimeBalance(clusters, depot, timeMatrix, k);
        
        // ❌ 移除阶段3：强制聚类大小约束验证与修复
        // 原因：阶段间设计冲突 - 阶段2使用600分钟标准，阶段3使用400分钟标准
        // 冲突影响：阶段2成功产生符合激进转移策略的18个聚类，但阶段3重新拆分成27个过小聚类
        // 解决方案：移除阶段3，让激进转移策略的设计目标得以实现
        log.info("=== 阶段3移除说明 ===");
        log.info("已移除强制聚类大小约束验证阶段，避免与激进转移策略的600分钟标准冲突");
        log.info("激进转移策略已在阶段2完成所有必要工作：拆分→合并→转移→达到目标");
        
        // 直接使用阶段2的结果，体现激进转移策略的完整性
        List<List<Accumulation>> finalClusters = clusters;
        
        // 原阶段4代码（已禁用）：
        // List<List<Accumulation>> finalClusters = finalOptimizationWithGeographicConstraints(clusters, depot, timeMatrix);
        
        // 评估和报告聚类质量
        ClusteringQualityEvaluator.ClusteringQualityMetrics metrics = 
                qualityEvaluator.evaluateQuality(finalClusters, depot, timeMatrix);
        
        log.info("K-means聚类完成，最终均衡度: {}分钟", String.format("%.2f", calculateWorkloadBalance(finalClusters)));
        log.info("聚类质量评估:\n{}", metrics.generateReport());
        
        return finalClusters;
    }
    
    /**
     * 创建单例聚类（每个聚集区一个聚类）
     */
    private List<List<Accumulation>> createSingletonClusters(List<Accumulation> accumulations) {
        List<List<Accumulation>> clusters = new ArrayList<>();
        for (Accumulation acc : accumulations) {
            clusters.add(Arrays.asList(acc));
        }
        return clusters;
    }
    
    /**
     * 初始化聚类中心 - 改进版K-means++算法
     * 基于地理分布优先选择初始中心，专注于聚集区分布，忽略中转站影响
     */
    private List<ClusterCenter> initializeCenters(
            List<Accumulation> accumulations, TransitDepot depot, int k) {
        
        List<ClusterCenter> centers = new ArrayList<>();
        List<Accumulation> remainingAccs = new ArrayList<>(accumulations);
        
        if (accumulations.isEmpty()) {
            // 如果没有聚集区，创建虚拟中心点（避免使用中转站坐标影响聚类）
            Random random = new Random(42);
            for (int i = 0; i < k; i++) {
                // 在合理范围内生成随机坐标作为初始中心
                double offsetLng = (random.nextDouble() - 0.5) * 0.1;
                double offsetLat = (random.nextDouble() - 0.5) * 0.1;
                centers.add(new ClusterCenter(depot.getLongitude() + offsetLng, depot.getLatitude() + offsetLat, 0.0));
            }
            return centers;
        }
        
        // 第一个中心：选择聚集区几何中心附近的点（不再考虑中转站距离）
        double avgLng = accumulations.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat = accumulations.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        Accumulation firstCenter = remainingAccs.stream()
                .min(Comparator.comparingDouble(acc -> 
                        calculateDistance(acc.getLongitude(), acc.getLatitude(), avgLng, avgLat)))
                .orElse(remainingAccs.get(0));
        centers.add(new ClusterCenter(firstCenter.getLongitude(), firstCenter.getLatitude(), 0.0));
        remainingAccs.remove(firstCenter);
        
        // 使用K-means++算法选择后续中心
        Random random = new Random(42); // 固定种子确保可重现
        
        for (int i = 1; i < k && !remainingAccs.isEmpty(); i++) {
            // 计算每个剩余聚集区到最近中心的距离平方
            List<Double> distances = new ArrayList<>();
            double totalDistanceSquared = 0.0;
            
            for (Accumulation acc : remainingAccs) {
                double minDistance = Double.MAX_VALUE;
                for (ClusterCenter center : centers) {
                    double distance = calculateDistance(
                            acc.getLongitude(), acc.getLatitude(),
                            center.getLongitude(), center.getLatitude());
                    minDistance = Math.min(minDistance, distance);
                }
                double distanceSquared = minDistance * minDistance;
                distances.add(distanceSquared);
                totalDistanceSquared += distanceSquared;
            }
            
            // 按距离平方的概率选择下一个中心
            double target = random.nextDouble() * totalDistanceSquared;
            double cumulative = 0.0;
            int selectedIndex = 0;
            
            for (int j = 0; j < distances.size(); j++) {
                cumulative += distances.get(j);
                if (cumulative >= target) {
                    selectedIndex = j;
                    break;
                }
            }
            
            Accumulation selectedAcc = remainingAccs.get(selectedIndex);
            centers.add(new ClusterCenter(selectedAcc.getLongitude(), selectedAcc.getLatitude(), 0.0));
            remainingAccs.remove(selectedIndex);
        }
        
        // 如果聚集区数量少于k，在现有中心周围添加扰动中心
        while (centers.size() < k) {
            ClusterCenter baseCenter = centers.get(centers.size() % Math.max(1, centers.size()));
            double offsetLng = (random.nextDouble() - 0.5) * 0.01; // 小幅偏移
            double offsetLat = (random.nextDouble() - 0.5) * 0.01;
            centers.add(new ClusterCenter(
                    baseCenter.getLongitude() + offsetLng,
                    baseCenter.getLatitude() + offsetLat, 0.0));
        }
        
        return centers;
    }
    
    /**
     * 分配聚集区到最近的聚类中心
     */
    private List<List<Accumulation>> assignToNearestCenters(
            List<Accumulation> accumulations,
            List<ClusterCenter> centers,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        int k = centers.size();
        List<List<Accumulation>> clusters = new ArrayList<>();
        for (int i = 0; i < k; i++) {
            clusters.add(new ArrayList<>());
        }
        
        // 计算目标工作时间（总工作时间 / 聚类数）
        double totalDeliveryTime = accumulations.stream()
                .mapToDouble(Accumulation::getDeliveryTime)
                .sum();
        double targetWorkTimePerCluster = (totalDeliveryTime + AlgorithmParameters.LOADING_TIME_MINUTES * k) / k;
        
        log.info("目标工作时间平衡：每个聚类目标工作时间约 {} 分钟", String.format("%.1f", targetWorkTimePerCluster));
        
        for (Accumulation acc : accumulations) {
            int bestCluster = -1;
            double bestCost = Double.MAX_VALUE;
            
            for (int i = 0; i < k; i++) {
                List<Accumulation> currentCluster = clusters.get(i);
                
                // 计算当前聚类的工作时间
                double currentWorkTime = calculateClusterWorkTime(currentCluster, depot, timeMatrix);
                
                // 工作时间硬约束：如果加入会导致严重超时，跳过
                double newWorkTime = currentWorkTime + acc.getDeliveryTime() + 
                        estimateAdditionalTravelTime(acc, currentCluster, depot, timeMatrix);
                
                // 地理优先模式：放松时间约束，只在极端情况下限制
                if (newWorkTime > targetWorkTimePerCluster * 3.0) {
                    continue; // 只在极端超时情况下跳过
                }
                
                // 地理约束：距离中心太远直接跳过
                double distanceToCenter = calculateDistance(
                        acc.getLongitude(), acc.getLatitude(),
                        centers.get(i).getLongitude(), centers.get(i).getLatitude());
                
                if (distanceToCenter > 25.0) { // 25公里硬限制
                    continue;
                }
                
                // 计算以工作时间平衡为核心的成本
                double cost = calculateWorkTimeBalancedCost(acc, currentCluster, centers.get(i), 
                        depot, timeMatrix, targetWorkTimePerCluster);
                
                if (cost < bestCost) {
                    bestCost = cost;
                    bestCluster = i;
                }
            }
            
            // 如果没有找到合适的聚类，尝试放松约束
            if (bestCluster == -1) {
                // 第二次尝试：放松工作时间约束到1.5倍
                for (int i = 0; i < k; i++) {
                    List<Accumulation> currentCluster = clusters.get(i);
                    double currentWorkTime = calculateClusterWorkTime(currentCluster, depot, timeMatrix);
                    double newWorkTime = currentWorkTime + acc.getDeliveryTime() + 
                            estimateAdditionalTravelTime(acc, currentCluster, depot, timeMatrix);
                    
                    if (newWorkTime <= targetWorkTimePerCluster * 1.5) {
                        double cost = calculateWorkTimeBalancedCost(acc, currentCluster, centers.get(i), 
                                depot, timeMatrix, targetWorkTimePerCluster);
                        if (cost < bestCost) {
                            bestCost = cost;
                            bestCluster = i;
                        }
                    }
                }
                
                // 最后手段：分配给工作时间最少的聚类
                if (bestCluster == -1) {
                    bestCluster = 0;
                    double minWorkTime = Double.MAX_VALUE;
                    for (int i = 0; i < k; i++) {
                        double workTime = calculateClusterWorkTime(clusters.get(i), depot, timeMatrix);
                        if (workTime < minWorkTime) {
                            minWorkTime = workTime;
                            bestCluster = i;
                        }
                    }
                }
            }
            
            clusters.get(bestCluster).add(acc);
        }
        
        return clusters;
    }
    
    /**
     * 计算以工作时间平衡为核心的分配成本
     */
    private double calculateWorkTimeBalancedCost(
            Accumulation acc,
            List<Accumulation> currentCluster,
            ClusterCenter center,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix,
            double targetWorkTime) {
        
        // 1. 工作时间偏差成本（最高优先级）
        double currentWorkTime = calculateClusterWorkTime(currentCluster, depot, timeMatrix);
        double newWorkTime = currentWorkTime + acc.getDeliveryTime() + 
                estimateAdditionalTravelTime(acc, currentCluster, depot, timeMatrix);
        
        double workTimeDeviation = Math.abs(newWorkTime - targetWorkTime);
        double deviationRatio = workTimeDeviation / targetWorkTime;
        
        // 指数惩罚：偏差越大，惩罚越严重
        double workTimeDeviationCost;
        if (deviationRatio > 0.5) {
            // 超过50%偏差时，使用指数惩罚
            workTimeDeviationCost = Math.pow(deviationRatio, 4) * 10000.0;
        } else {
            workTimeDeviationCost = Math.pow(deviationRatio, 2) * 2000.0;
        }
        
        // 2. 地理聚集成本（次要优先级，确保地理合理性）
        double geographicCost = calculateGeographicCost(acc, currentCluster, center);
        
        // 3. 服务半径约束
        double radiusPenalty = calculateRadiusPenalty(acc, depot);
        
        // 应用权重：地理聚集是绝对优先级，时间平衡为辅助
        double weightedGeographicCost = geographicCost * 1000.0;        // 地理聚集绝对优先（最高权重）
        double weightedRadiusPenalty = radiusPenalty * 500.0;           // 中转站距离约束（高权重）
        double weightedWorkTimeCost = workTimeDeviationCost * 10.0;     // 时间平衡为辅助（低权重）
        
        double totalCost = weightedWorkTimeCost + weightedGeographicCost + weightedRadiusPenalty;
        
        // 调试信息
        if (log.isDebugEnabled() && Math.random() < 0.001) {
            debugLog("工作时间平衡成本[%s]: 当前%s分钟, 新增%s分钟, 偏差%s, 成本%s",
                acc.getAccumulationId(), currentWorkTime, newWorkTime, workTimeDeviation, totalCost);
        }
        
        return totalCost;
    }
    
    /**
     * 计算单个聚集区的工作时间（不包括装载时间）
     */
    private double calculateAccumulationWorkTime(Accumulation accumulation, TransitDepot depot) {
        // 配送时间（点权）
        double deliveryTime = accumulation.getDeliveryTime();
        
        // 到中转站的往返时间
        double distanceToDepot = calculateDistance(
            accumulation.getLongitude(), accumulation.getLatitude(), 
            depot.getLongitude(), depot.getLatitude());
        double travelTimeToDepot = distanceToDepot * 2.0; // 往返，简化估算每公里2分钟
        
        return deliveryTime + travelTimeToDepot;
    }
    
    /**
     * 计算聚类的总工作时间 - 🔧 修复：使用统一时间计算服务确保一致性
     */
    private double calculateClusterWorkTime(List<Accumulation> cluster, TransitDepot depot, 
                                          Map<String, TimeInfo> timeMatrix) {
        // 🎯 关键修复：由于方法签名限制，这里需要适配时间矩阵格式
        // 将原来的简化时间矩阵转换为统一服务期望的格式
        if (cluster.isEmpty()) {
            return AlgorithmParameters.LOADING_TIME_MINUTES;
        }
        
        // 使用统一时间计算服务的聚类时间计算逻辑
        return timeCalculationService.calculateClusterWorkTime(cluster, depot, null); // 暂时传null，使用地理距离估算
    }
    
    /**
     * 估算加入新点的额外行驶时间
     */
    private double estimateAdditionalTravelTime(Accumulation acc, List<Accumulation> currentCluster, 
                                              TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (currentCluster.isEmpty()) {
            // 到中转站的往返时间
            double distance = calculateDistance(acc.getLongitude(), acc.getLatitude(), 
                    depot.getLongitude(), depot.getLatitude());
            return distance * 2.0; // 往返
        }
        
        // 估算到聚类中心的距离作为额外行驶时间
        double avgLng = currentCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat = currentCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        double distanceToClusterCenter = calculateDistance(acc.getLongitude(), acc.getLatitude(), avgLng, avgLat);
        return distanceToClusterCenter * 1.5; // 估算每公里1.5分钟
    }
    
    /**
     * 计算地理聚集成本（简化版，重点确保紧密聚团）
     */
    private double calculateGeographicCost(Accumulation acc, List<Accumulation> currentCluster, ClusterCenter center) {
        
        // 1. 到聚类中心的基础距离（主要因子）
        double centerDistance = calculateDistance(
                acc.getLongitude(), acc.getLatitude(),
                center.getLongitude(), center.getLatitude());
        
        // 2. 聚类内部紧密性成本（与现有点的平均距离）
        double intraClusterCompactness = 0.0;
        if (!currentCluster.isEmpty()) {
            double totalDistance = 0.0;
            for (Accumulation existing : currentCluster) {
                double distance = calculateDistance(
                        acc.getLongitude(), acc.getLatitude(),
                        existing.getLongitude(), existing.getLatitude());
                totalDistance += distance;
            }
            // 平均距离作为紧密性指标
            intraClusterCompactness = totalDistance / currentCluster.size();
            
            // 如果平均距离超过8公里，施加额外惩罚
            if (intraClusterCompactness > 8.0) {
                intraClusterCompactness *= 2.0; // 惩罚系数
            }
        }
        
        // 简化计算，重点关注距离和紧密性
        return centerDistance + intraClusterCompactness * 1.5;
    }
    
    /**
     * 计算总时间成本（配送时间 + 中转站时间，统一考虑）
     */
    private double calculateTotalTimeCost(Accumulation acc, List<Accumulation> currentCluster, 
                                        TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        // 1. 计算配送时间总和（点权之和）
        double currentDeliveryTime = currentCluster.stream()
                .mapToDouble(Accumulation::getDeliveryTime)
                .sum();
        double newTotalDeliveryTime = currentDeliveryTime + acc.getDeliveryTime();
        
        // 2. 计算中转站到聚类片区的时间成本
        double transitTimeCost = calculateClusterTransitTime(acc, currentCluster, depot, timeMatrix);
        
        // 3. 总时间成本 = 配送时间 + 中转站时间
        double totalTime = newTotalDeliveryTime + transitTimeCost;
        
        // 4. 基于理想时间的平衡惩罚
        double idealTotalTime = AlgorithmParameters.TARGET_ROUTE_TIME * 0.8; // 80%的目标时间作为理想值
        
        // 超出理想时间的惩罚（二次方增长）
        double balancePenalty = 0.0;
        if (totalTime > idealTotalTime) {
            double excessRatio = (totalTime - idealTotalTime) / idealTotalTime;
            balancePenalty = Math.pow(excessRatio, 2) * 50.0;
        }
        
        // 5. 时间密度惩罚（防止点少但时间长）
        double timePerPoint = totalTime / (currentCluster.size() + 1);
        double avgExpectedTimePerPoint = AlgorithmParameters.DEFAULT_DELIVERY_TIME + 10.0; // 配送时间+平均行驶时间
        double densityPenalty = 0.0;
        if (timePerPoint > avgExpectedTimePerPoint * 2) {
            densityPenalty = (timePerPoint - avgExpectedTimePerPoint * 2) * 1.5;
        }
        
        return balancePenalty + densityPenalty;
    }
    
    /**
     * 计算聚类到中转站的时间成本
     */
    private double calculateClusterTransitTime(Accumulation acc, List<Accumulation> currentCluster, 
                                             TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        // 构造临时聚类（包含新加入的点）
        List<Accumulation> tempCluster = new ArrayList<>(currentCluster);
        tempCluster.add(acc);
        
        if (tempCluster.isEmpty()) {
            return 0.0;
        }
        
        // 计算聚类中心到中转站的时间
        double avgLng = tempCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat = tempCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        // 找到最接近聚类中心的点，作为代表点计算到中转站的时间
        Accumulation representativePoint = tempCluster.stream()
                .min((p1, p2) -> {
                    double dist1 = calculateDistance(p1.getLongitude(), p1.getLatitude(), avgLng, avgLat);
                    double dist2 = calculateDistance(p2.getLongitude(), p2.getLatitude(), avgLng, avgLat);
                    return Double.compare(dist1, dist2);
                })
                .orElse(acc);
        
        // 获取代表点到中转站的时间
        TimeInfo timeInfo = getTimeInfo(depot, representativePoint, timeMatrix);
        if (timeInfo != null) {
            return timeInfo.getTravelTime();
        }
        
        // 如果没有时间数据，使用距离估算
        double distance = calculateDistance(
                representativePoint.getLongitude(), representativePoint.getLatitude(),
                depot.getLongitude(), depot.getLatitude());
        return distance * 2.0; // 粗略估算：1公里≈2分钟
    }
    
    /**
     * 计算聚类大小平衡成本
     * 智能平衡聚类大小，防止某些聚类过大而其他过小
     */
    private double calculateClusterSizeBalanceCost(List<Accumulation> currentCluster) {
        int currentSize = currentCluster.size() + 1; // +1 因为我们要评估加入新点后的大小
        int targetSize = AlgorithmParameters.TARGET_CLUSTER_SIZE;
        
        // 强化的大小平衡惩罚
        double sizePenalty = 0.0;
        
        // 1. 严格的大小控制
        if (currentSize > targetSize * 1.8) {
            // 聚类过大时的强烈惩罚（指数增长）
            double excessRatio = (double) currentSize / targetSize;
            sizePenalty = Math.pow(excessRatio - 1.8, 3) * 100.0;
        } else if (currentSize > targetSize * 1.2) {
            // 适度过大时的二次方惩罚
            double excess = currentSize - targetSize * 1.2;
            sizePenalty = Math.pow(excess, 2) * 15.0;
        } else if (currentSize < targetSize * 0.3) {
            // 聚类过小时，给予强烈奖励
            double shortageRatio = targetSize * 0.3 / Math.max(1, currentSize);
            sizePenalty = -Math.pow(shortageRatio, 2) * 20.0; // 负值表示奖励
        } else if (currentSize < targetSize * 0.7) {
            // 适度过小时的奖励
            sizePenalty = -(targetSize * 0.7 - currentSize) * 5.0;
        }
        
        // 2. 极端情况的严厉措施
        if (currentSize >= targetSize * 3) {
            // 聚类极度过大，几乎禁止
            sizePenalty += 10000.0; // 几乎禁止性的惩罚
        }
        
        if (currentSize <= 1) {
            // 空聚类或只有一个点，强烈鼓励
            sizePenalty -= 50.0;
        }
        
        return sizePenalty;
    }
    
    /**
     * 计算聚类内部的平均距离（用于密度评估）
     */
    private double calculateAverageIntraClusterDistance(List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        double totalDistance = 0.0;
        int pairCount = 0;
        
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                double distance = calculateDistance(
                        cluster.get(i).getLongitude(), cluster.get(i).getLatitude(),
                        cluster.get(j).getLongitude(), cluster.get(j).getLatitude());
                totalDistance += distance;
                pairCount++;
            }
        }
        
        return pairCount > 0 ? totalDistance / pairCount : 0.0;
    }
    
    /**
     * 计算服务半径惩罚
     */
    private double calculateRadiusPenalty(Accumulation acc, TransitDepot depot) {
        double distanceToDepot = calculateDistance(
                acc.getLongitude(), acc.getLatitude(),
                depot.getLongitude(), depot.getLatitude());
        
        if (distanceToDepot > AlgorithmParameters.MAX_SERVICE_RADIUS) {
            return (distanceToDepot - AlgorithmParameters.MAX_SERVICE_RADIUS);
        }
        
        return 0.0;
    }
    
    /**
     * 获取时间信息的辅助方法
     */
    private TimeInfo getTimeInfo(TransitDepot depot, Accumulation acc, Map<String, TimeInfo> timeMatrix) {
        String key1 = depot.getLongitude() + "," + depot.getLatitude() + "->" + 
                     acc.getLongitude() + "," + acc.getLatitude();
        String key2 = acc.getLongitude() + "," + acc.getLatitude() + "->" + 
                     depot.getLongitude() + "," + depot.getLatitude();
        
        TimeInfo timeInfo = timeMatrix.get(key1);
        if (timeInfo == null) {
            timeInfo = timeMatrix.get(key2);
        }
        
        return timeInfo;
    }
    
    /**
     * 更新聚类中心
     */
    private boolean updateCenters(
            List<ClusterCenter> centers,
            List<List<Accumulation>> clusters,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        boolean converged = true;
        
        for (int i = 0; i < centers.size(); i++) {
            ClusterCenter oldCenter = centers.get(i);
            List<Accumulation> cluster = clusters.get(i);
            
            if (cluster.isEmpty()) {
                // 空聚类，保持原位置或轻微偏移（避免使用中转站坐标影响聚类纯度）
                ClusterCenter current = centers.get(i);
                Random random = new Random(System.currentTimeMillis() + i);
                double offsetLng = (random.nextDouble() - 0.5) * 0.01;
                double offsetLat = (random.nextDouble() - 0.5) * 0.01;
                centers.set(i, new ClusterCenter(
                    current.getLongitude() + offsetLng, 
                    current.getLatitude() + offsetLat, 0.0));
                continue;
            }
            
            // 计算加权质心（按配送时间加权）
            double totalWeight = 0.0;
            double weightedLng = 0.0;
            double weightedLat = 0.0;
            double totalWorkload = 0.0;
            
            for (Accumulation acc : cluster) {
                double weight = acc.getDeliveryTime();
                totalWeight += weight;
                weightedLng += acc.getLongitude() * weight;
                weightedLat += acc.getLatitude() * weight;
                totalWorkload += acc.getDeliveryTime();
            }
            
            double newLng = weightedLng / totalWeight;
            double newLat = weightedLat / totalWeight;
            
            ClusterCenter newCenter = new ClusterCenter(newLng, newLat, totalWorkload);
            centers.set(i, newCenter);
            
            // 检查是否收敛
            double movement = calculateDistance(oldCenter.getLongitude(), oldCenter.getLatitude(), 
                                             newLng, newLat);
            if (movement > AlgorithmParameters.KMEANS_CONVERGENCE_THRESHOLD) {
                converged = false;
            }
        }
        
        return converged;
    }
    
    /**
     * 计算工作量均衡度（标准差）
     */
    private double calculateWorkloadBalance(List<List<Accumulation>> clusters) {
        List<Double> workloads = clusters.stream()
                .map(cluster -> cluster.stream().mapToDouble(Accumulation::getDeliveryTime).sum())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        
        double mean = workloads.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = workloads.stream()
                .mapToDouble(w -> Math.pow(w - mean, 2))
                .average()
                .orElse(0.0);
        
        return Math.sqrt(variance); // 标准差
    }
    
    /**
     * 后处理优化工作量均衡（改进版 - 保持紧密性的同时优化均衡）
     */
    private List<List<Accumulation>> postProcessForBalance(
            List<List<Accumulation>> clusters,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 尝试通过边界聚集区的转移来改善均衡，但要考虑地理紧密性
        for (int iteration = 0; iteration < 15; iteration++) { // 增加迭代次数
            boolean improved = false;
            
            // 找到最重载和最轻载的聚类
            int heaviestIndex = findHeaviestCluster(clusters);
            int lightestIndex = findLightestCluster(clusters);
            
            if (heaviestIndex == lightestIndex) break;
            
            List<Accumulation> heaviest = clusters.get(heaviestIndex);
            List<Accumulation> lightest = clusters.get(lightestIndex);
            
            // 找到既能改善均衡又能保持紧密性的聚集区
            Accumulation bestToTransfer = findBestBalancedTransferCandidate(heaviest, lightest);
            
            if (bestToTransfer != null) {
                double oldBalance = calculateWorkloadBalance(clusters);
                double oldCompactness = calculateClusterCompactness(heaviest) + calculateClusterCompactness(lightest);
                
                heaviest.remove(bestToTransfer);
                lightest.add(bestToTransfer);
                
                double newBalance = calculateWorkloadBalance(clusters);
                double newCompactness = calculateClusterCompactness(heaviest) + calculateClusterCompactness(lightest);
                
                // 综合考虑均衡性和紧密性的改善
                boolean balanceImproved = newBalance < oldBalance;
                boolean compactnessNotWorsened = newCompactness <= oldCompactness * 1.2; // 允许紧密性稍微下降
                
                if (balanceImproved && compactnessNotWorsened) {
                    improved = true;
                    debugLog("转移聚集区 %s 改善了均衡性，均衡度从 %s 改善到 %s", 
                            bestToTransfer.getAccumulationName(), oldBalance, newBalance);
                } else {
                    // 回滚
                    lightest.remove(bestToTransfer);
                    heaviest.add(bestToTransfer);
                }
            }
            
            if (!improved) break;
        }
        
        return clusters;
    }
    
    /**
     * 计算聚类的紧密性（聚类内点间平均距离）
     */
    private double calculateClusterCompactness(List<Accumulation> cluster) {
        if (cluster.size() <= 1) return 0.0;
        
        double totalDistance = 0.0;
        int pairCount = 0;
        
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                Accumulation acc1 = cluster.get(i);
                Accumulation acc2 = cluster.get(j);
                double distance = calculateDistance(
                        acc1.getLongitude(), acc1.getLatitude(),
                        acc2.getLongitude(), acc2.getLatitude());
                totalDistance += distance;
                pairCount++;
            }
        }
        
        return pairCount > 0 ? totalDistance / pairCount : 0.0;
    }
    
    /**
     * 找到工作量最重的聚类
     */
    private int findHeaviestCluster(List<List<Accumulation>> clusters) {
        int heaviestIndex = 0;
        double maxWorkload = 0.0;
        
        for (int i = 0; i < clusters.size(); i++) {
            double workload = clusters.get(i).stream()
                    .mapToDouble(Accumulation::getDeliveryTime)
                    .sum();
            if (workload > maxWorkload) {
                maxWorkload = workload;
                heaviestIndex = i;
            }
        }
        
        return heaviestIndex;
    }
    
    /**
     * 找到工作量最轻的聚类
     */
    private int findLightestCluster(List<List<Accumulation>> clusters) {
        int lightestIndex = 0;
        double minWorkload = Double.MAX_VALUE;
        
        for (int i = 0; i < clusters.size(); i++) {
            double workload = clusters.get(i).stream()
                    .mapToDouble(Accumulation::getDeliveryTime)
                    .sum();
            if (workload < minWorkload) {
                minWorkload = workload;
                lightestIndex = i;
            }
        }
        
        return lightestIndex;
    }
    
    /**
     * 找到最佳转移候选
     */
    private Accumulation findBestTransferCandidate(
            List<Accumulation> fromCluster, 
            List<Accumulation> toCluster) {
        
        if (fromCluster.isEmpty()) return null;
        
        // 优先选择配送时间适中的聚集区
        return fromCluster.stream()
                .min(Comparator.comparingDouble(acc -> 
                        Math.abs(acc.getDeliveryTime() - 15.0))) // 15分钟为理想配送时间
                .orElse(null);
    }
    
    /**
     * 找到既能改善均衡又能保持紧密性的最佳转移候选
     */
    private Accumulation findBestBalancedTransferCandidate(
            List<Accumulation> fromCluster, 
            List<Accumulation> toCluster) {
        
        if (fromCluster.isEmpty()) return null;
        
        Accumulation bestCandidate = null;
        double bestScore = Double.MAX_VALUE;
        
        for (Accumulation candidate : fromCluster) {
            // 计算转移后的紧密性影响
            double distanceToTargetCluster = 0.0;
            if (!toCluster.isEmpty()) {
                // 计算到目标聚类中心的距离
                double avgLng = toCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
                double avgLat = toCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
                distanceToTargetCluster = calculateDistance(
                        candidate.getLongitude(), candidate.getLatitude(), avgLng, avgLat);
            }
            
            // 计算配送时间的均衡贡献
            double timeBalanceScore = Math.abs(candidate.getDeliveryTime() - TIME_BALANCE_THRESHOLD); // 平衡阈值为理想转移时间
            
            // 综合评分：距离权重更高，优先保证紧密性
            double totalScore = distanceToTargetCluster * 2.0 + timeBalanceScore * 0.5;
            
            if (totalScore < bestScore) {
                bestScore = totalScore;
                bestCandidate = candidate;
            }
        }
        
        return bestCandidate;
    }
    
    /**
     * 计算两点间距离（Haversine公式）
     */
    private double calculateDistance(double lng1, double lat1, double lng2, double lat2) {
        double earthRadius = 6371.0;
        
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLngRad = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return earthRadius * c;
    }
    
    /**
     * 计算路径交叉惩罚
     * 检测新聚集区是否会与现有聚类中的聚集区形成交叉路径
     */
    private double calculateCrossingPenalty(Accumulation newAcc, List<Accumulation> cluster, TransitDepot depot) {
        if (cluster.isEmpty()) {
            return 0.0;
        }
        
        double penalty = 0.0;
        
        // 检测从中转站到新聚集区的路径是否与现有路径交叉
        for (Accumulation existingAcc : cluster) {
            // 简化检测：如果新聚集区在中转站和现有聚集区连线的另一侧，可能产生交叉
            if (isOnOppositeSide(depot, existingAcc, newAcc)) {
                penalty += AlgorithmParameters.CROSSING_PENALTY_COEFFICIENT;
            }
        }
        
        return penalty;
    }
    
    /**
     * 检测点是否在线段的另一侧（简化的交叉检测）
     */
    private boolean isOnOppositeSide(TransitDepot depot, Accumulation acc1, Accumulation acc2) {
        // 使用叉积判断点的相对位置
        double dx1 = acc1.getLongitude() - depot.getLongitude();
        double dy1 = acc1.getLatitude() - depot.getLatitude();
        double dx2 = acc2.getLongitude() - depot.getLongitude();
        double dy2 = acc2.getLatitude() - depot.getLatitude();
        
        // 计算角度差异
        double angle1 = Math.atan2(dy1, dx1);
        double angle2 = Math.atan2(dy2, dx2);
        double angleDiff = Math.abs(angle1 - angle2);
        
        // 标准化角度差异到[0, π]
        if (angleDiff > Math.PI) {
            angleDiff = 2 * Math.PI - angleDiff;
        }
        
        // 如果角度差异大于90度，认为可能产生交叉
        return angleDiff > Math.PI / 2;
    }
    
    /**
     * 阶段2：平衡时间优化
     * 以地理聚集为选点依据进行时间平衡，通过点转移来平衡分组间的总时间成本
     */
    private List<List<Accumulation>> optimizeTimeBalance(List<List<Accumulation>> clusters, 
                                                       TransitDepot depot, 
                                                       Map<String, TimeInfo> timeMatrix) {
        log.info("开始时间平衡优化...");
        
        List<List<Accumulation>> optimizedClusters = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            optimizedClusters.add(new ArrayList<>(cluster));
        }
        
        // 计算每个聚类的总时间成本（配送时间 + 中转站时间）
        List<Double> clusterTotalTimes = new ArrayList<>();
        for (List<Accumulation> cluster : optimizedClusters) {
            double totalTime = calculateClusterTotalTime(cluster, depot, timeMatrix);
            clusterTotalTimes.add(totalTime);
        }
        
        // 多轮迭代进行时间平衡优化
        boolean improved = true;
        int iteration = 0;
        while (improved && iteration < 10) {
            improved = false;
            iteration++;
            
            // 找到时间最长和最短的聚类
            int heaviestIndex = 0, lightestIndex = 0;
            for (int i = 1; i < clusterTotalTimes.size(); i++) {
                if (clusterTotalTimes.get(i) > clusterTotalTimes.get(heaviestIndex)) {
                    heaviestIndex = i;
                }
                if (clusterTotalTimes.get(i) < clusterTotalTimes.get(lightestIndex)) {
                    lightestIndex = i;
                }
            }
            
            // 时间差异小于阈值时停止
            double timeDifference = clusterTotalTimes.get(heaviestIndex) - clusterTotalTimes.get(lightestIndex);
            if (timeDifference < TIME_BALANCE_THRESHOLD) { // 平衡阈值以内认为已经平衡
                break;
            }
            
            // 尝试从重载聚类转移边界点到轻载聚类
            List<Accumulation> heaviestCluster = optimizedClusters.get(heaviestIndex);
            List<Accumulation> lightestCluster = optimizedClusters.get(lightestIndex);
            
            Accumulation bestCandidate = findBestTransferCandidate(heaviestCluster, lightestCluster);
            if (bestCandidate != null) {
                // 执行转移
                heaviestCluster.remove(bestCandidate);
                lightestCluster.add(bestCandidate);
                
                // 重新计算时间
                clusterTotalTimes.set(heaviestIndex, calculateClusterTotalTime(heaviestCluster, depot, timeMatrix));
                clusterTotalTimes.set(lightestIndex, calculateClusterTotalTime(lightestCluster, depot, timeMatrix));
                
                improved = true;
                debugLog("时间平衡优化：转移聚集区{}从聚类{}到聚类{}", 
                    bestCandidate.getAccumulationId(), heaviestIndex, lightestIndex);
            }
        }
        
        log.info("时间平衡优化完成，执行了{}轮迭代", iteration);
        return optimizedClusters;
    }
    
    /**
     * 计算聚类的总时间成本（配送时间 + 中转站时间）
     */
    private double calculateClusterTotalTime(List<Accumulation> cluster, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (cluster.isEmpty()) {
            return 0.0;
        }
        
        // 配送时间总和
        double deliveryTime = cluster.stream().mapToDouble(Accumulation::getDeliveryTime).sum();
        
        // 中转站时间（使用聚类中心代表点）
        double avgLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        Accumulation representative = cluster.stream()
                .min((p1, p2) -> {
                    double dist1 = calculateDistance(p1.getLongitude(), p1.getLatitude(), avgLng, avgLat);
                    double dist2 = calculateDistance(p2.getLongitude(), p2.getLatitude(), avgLng, avgLat);
                    return Double.compare(dist1, dist2);
                })
                .orElse(cluster.get(0));
        
        TimeInfo timeInfo = getTimeInfo(depot, representative, timeMatrix);
        double transitTime = timeInfo != null ? timeInfo.getTravelTime() : 
                calculateDistance(representative.getLongitude(), representative.getLatitude(),
                        depot.getLongitude(), depot.getLatitude()) * 2.0;
        
        return deliveryTime + transitTime;
    }
    
    
    /**
     * 阶段3：拆分合并优化
     * 整理零星分布，优化聚团形状，确保分组尽量聚团且互不侵犯
     */
    private List<List<Accumulation>> optimizeSplitAndMerge(List<List<Accumulation>> clusters, 
                                                          TransitDepot depot, 
                                                          Map<String, TimeInfo> timeMatrix) {
        log.info("开始拆分合并优化...");
        
        List<List<Accumulation>> optimizedClusters = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            optimizedClusters.add(new ArrayList<>(cluster));
        }
        
        // 1. 处理过度密集的大聚类（拆分）
        optimizedClusters = splitOversizedClusters(optimizedClusters, depot, timeMatrix);
        
        // 2. 处理零星分布的点（重新分配到更近的聚类）
        optimizedClusters = redistributeOutliers(optimizedClusters, depot, timeMatrix);
        
        // 3. 合并过小或过于分散的聚类
        optimizedClusters = mergeSmallClusters(optimizedClusters, depot, timeMatrix);
        
        log.info("拆分合并优化完成");
        return optimizedClusters;
    }
    
    /**
     * 拆分过度密集的大聚类
     */
    private List<List<Accumulation>> splitOversizedClusters(List<List<Accumulation>> clusters, 
                                                           TransitDepot depot, 
                                                           Map<String, TimeInfo> timeMatrix) {
        List<List<Accumulation>> result = new ArrayList<>();
        
        for (List<Accumulation> cluster : clusters) {
            // 判断是否需要拆分：点数过多且密度高
            if (cluster.size() > AlgorithmParameters.TARGET_CLUSTER_SIZE * 1.5 && 
                calculateAverageIntraClusterDistance(cluster) < 3.0) { // 平均距离小于3公里认为密度高
                
                log.info("拆分过密集聚类，原始大小: {}", cluster.size());
                
                // 简单二分法拆分
                List<Accumulation> part1 = new ArrayList<>();
                List<Accumulation> part2 = new ArrayList<>();
                
                // 计算聚类中心
                double avgLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
                double avgLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
                
                // 按照到中心的距离分配到两个子聚类
                for (int i = 0; i < cluster.size(); i++) {
                    if (i % 2 == 0) {
                        part1.add(cluster.get(i));
                    } else {
                        part2.add(cluster.get(i));
                    }
                }
                
                result.add(part1);
                result.add(part2);
                log.info("拆分完成: {} -> {} + {}", cluster.size(), part1.size(), part2.size());
            } else {
                result.add(cluster);
            }
        }
        
        return result;
    }
    
    /**
     * 重新分配离群点到更近的聚类
     */
    private List<List<Accumulation>> redistributeOutliers(List<List<Accumulation>> clusters, 
                                                         TransitDepot depot, 
                                                         Map<String, TimeInfo> timeMatrix) {
        List<List<Accumulation>> result = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            result.add(new ArrayList<>(cluster));
        }
        
        // 找出每个聚类中的离群点
        for (int i = 0; i < result.size(); i++) {
            List<Accumulation> cluster = result.get(i);
            List<Accumulation> outliers = new ArrayList<>();
            
            for (Accumulation point : cluster) {
                // 计算到聚类中其他点的平均距离
                double avgDistanceInCluster = cluster.stream()
                        .filter(p -> p != point)
                        .mapToDouble(p -> calculateDistance(
                                point.getLongitude(), point.getLatitude(),
                                p.getLongitude(), p.getLatitude()))
                        .average().orElse(0.0);
                
                // 如果距离过大，认为是离群点
                if (avgDistanceInCluster > 10.0) { // 10公里
                    outliers.add(point);
                }
            }
            
            // 重新分配离群点
            for (Accumulation outlier : outliers) {
                // 找到最近的其他聚类
                int bestClusterIndex = -1;
                double bestDistance = Double.MAX_VALUE;
                
                for (int j = 0; j < result.size(); j++) {
                    if (j == i) continue;
                    
                    List<Accumulation> otherCluster = result.get(j);
                    if (otherCluster.isEmpty()) continue;
                    
                    // 计算到其他聚类中心的距离
                    double avgLng = otherCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
                    double avgLat = otherCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
                    
                    double distance = calculateDistance(outlier.getLongitude(), outlier.getLatitude(), avgLng, avgLat);
                    if (distance < bestDistance) {
                        bestDistance = distance;
                        bestClusterIndex = j;
                    }
                }
                
                // 如果找到更合适的聚类，进行转移
                if (bestClusterIndex != -1 && bestDistance < 8.0) { // 8公里内
                    cluster.remove(outlier);
                    result.get(bestClusterIndex).add(outlier);
                    debugLog("重新分配离群点{}从聚类{}到聚类{}", 
                            outlier.getAccumulationId(), i, bestClusterIndex);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 合并过小或过于分散的聚类
     */
    private List<List<Accumulation>> mergeSmallClusters(List<List<Accumulation>> clusters, 
                                                       TransitDepot depot, 
                                                       Map<String, TimeInfo> timeMatrix) {
        List<List<Accumulation>> result = new ArrayList<>(clusters);
        
        // 多轮合并，直到没有可合并的聚类
        boolean merged = true;
        while (merged) {
            merged = false;
            
            for (int i = 0; i < result.size() && !merged; i++) {
                List<Accumulation> cluster = result.get(i);
                
                // 判断是否需要合并：点数太少或过于分散
                boolean needsMerge = cluster.size() < 3 || 
                        (cluster.size() < 8 && calculateAverageIntraClusterDistance(cluster) > 8.0);
                
                if (needsMerge) {
                    // 找到最近的聚类进行合并
                    int bestMergeTarget = findBestMergeTarget(cluster, result, i);
                    
                    if (bestMergeTarget != -1) {
                        // 保存合并前的信息用于日志
                        int targetSizeBefore = result.get(bestMergeTarget).size();
                        int clusterSize = cluster.size();
                        
                        // 执行合并
                        result.get(bestMergeTarget).addAll(cluster);
                        result.remove(i);
                        merged = true;
                        
                        log.info("合并小聚类{}到聚类{}，大小: {} + {} = {}", 
                                i, bestMergeTarget, clusterSize, 
                                targetSizeBefore, targetSizeBefore + clusterSize);
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 找到最佳的合并目标聚类
     */
    private int findBestMergeTarget(List<Accumulation> cluster, List<List<Accumulation>> allClusters, int excludeIndex) {
        if (cluster.isEmpty()) return -1;
        
        // 计算聚类中心
        double clusterLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double clusterLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        int bestTarget = -1;
        double bestScore = Double.MAX_VALUE;
        
        for (int i = 0; i < allClusters.size(); i++) {
            if (i == excludeIndex) continue;
            
            List<Accumulation> candidate = allClusters.get(i);
            if (candidate.isEmpty()) continue;
            
            // 计算候选聚类中心
            double candidateLng = candidate.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            double candidateLat = candidate.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            
            // 距离因子
            double distance = calculateDistance(clusterLng, clusterLat, candidateLng, candidateLat);
            
            // 大小因子（避免合并后过大）
            double sizePenalty = candidate.size() > AlgorithmParameters.TARGET_CLUSTER_SIZE ? 
                    (candidate.size() - AlgorithmParameters.TARGET_CLUSTER_SIZE) * 2.0 : 0.0;
            
            double score = distance + sizePenalty;
            
            // 距离不能太远
            if (distance < 12.0 && score < bestScore) {
                bestScore = score;
                bestTarget = i;
            }
        }
        
        return bestTarget;
    }
    
    /**
     * 聚类中心数据结构
     */
    private static class ClusterCenter {
        private double longitude;
        private double latitude;
        private double workload;
        
        public ClusterCenter(double longitude, double latitude, double workload) {
            this.longitude = longitude;
            this.latitude = latitude;
            this.workload = workload;
        }
        
        public double getLongitude() { return longitude; }
        public double getLatitude() { return latitude; }
        public double getWorkload() { return workload; }
        
        public void setLongitude(double longitude) { this.longitude = longitude; }
        public void setLatitude(double latitude) { this.latitude = latitude; }
        public void setWorkload(double workload) { this.workload = workload; }
    }
    
    /**
     * 阶段1：纯地理K-means聚类（完全基于地理位置，不考虑时间因素）
     */
    private List<List<Accumulation>> pureGeographicClustering(List<Accumulation> accumulations, int k) {
        // 使用K-means++算法初始化中心点（纯地理）
        List<ClusterCenter> centers = initializeGeographicCenters(accumulations, k);
        
        for (int iteration = 0; iteration < AlgorithmParameters.KMEANS_MAX_ITERATIONS; iteration++) {
            // 基于地理距离分配聚集区
            List<List<Accumulation>> clusters = assignToNearestGeographicCenters(accumulations, centers);
            
            // 处理空聚类
            clusters = handleEmptyClusters(clusters, accumulations, centers);
            
            // 更新中心点（纯地理中心）
            boolean converged = updateGeographicCenters(centers, clusters);
            
            if (converged) {
                log.info("纯地理K-means在第{}次迭代后收敛", iteration + 1);
                return clusters;
            }
        }
        
        log.info("纯地理K-means达到最大迭代次数: {}", AlgorithmParameters.KMEANS_MAX_ITERATIONS);
        return assignToNearestGeographicCenters(accumulations, centers);
    }
    
    /**
     * 初始化纯地理聚类中心（K-means++算法）
     */
    private List<ClusterCenter> initializeGeographicCenters(List<Accumulation> accumulations, int k) {
        List<ClusterCenter> centers = new ArrayList<>();
        
        // 第一个中心随机选择
        Accumulation first = accumulations.get(random.nextInt(accumulations.size()));
        centers.add(new ClusterCenter(first.getLongitude(), first.getLatitude(), 0.0));
        
        // 选择剩余的k-1个中心
        for (int i = 1; i < k; i++) {
            double totalWeight = 0.0;
            List<Double> weights = new ArrayList<>();
            
            // 计算每个点到最近中心的距离平方
            for (Accumulation acc : accumulations) {
                double minDistance = Double.MAX_VALUE;
                for (ClusterCenter center : centers) {
                    double distance = calculateDistance(
                        acc.getLongitude(), acc.getLatitude(),
                        center.getLongitude(), center.getLatitude()
                    );
                    minDistance = Math.min(minDistance, distance);
                }
                double weight = minDistance * minDistance;
                weights.add(weight);
                totalWeight += weight;
            }
            
            // 基于权重随机选择下一个中心
            double target = random.nextDouble() * totalWeight;
            double current = 0.0;
            for (int j = 0; j < accumulations.size(); j++) {
                current += weights.get(j);
                if (current >= target) {
                    Accumulation selected = accumulations.get(j);
                    centers.add(new ClusterCenter(selected.getLongitude(), selected.getLatitude(), 0.0));
                    break;
                }
            }
        }
        
        return centers;
    }
    
    /**
     * 基于纯地理距离分配聚集区
     */
    private List<List<Accumulation>> assignToNearestGeographicCenters(
            List<Accumulation> accumulations, List<ClusterCenter> centers) {
        
        List<List<Accumulation>> clusters = new ArrayList<>();
        for (int i = 0; i < centers.size(); i++) {
            clusters.add(new ArrayList<>());
        }
        
        for (Accumulation acc : accumulations) {
            int bestCluster = 0;
            double minDistance = Double.MAX_VALUE;
            
            for (int i = 0; i < centers.size(); i++) {
                double distance = calculateDistance(
                    acc.getLongitude(), acc.getLatitude(),
                    centers.get(i).getLongitude(), centers.get(i).getLatitude()
                );
                
                if (distance < minDistance) {
                    minDistance = distance;
                    bestCluster = i;
                }
            }
            
            clusters.get(bestCluster).add(acc);
        }
        
        return clusters;
    }
    
    /**
     * 更新地理中心点
     */
    private boolean updateGeographicCenters(List<ClusterCenter> centers, List<List<Accumulation>> clusters) {
        boolean converged = true;
        
        for (int i = 0; i < centers.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            if (cluster.isEmpty()) continue;
            
            // 计算地理中心
            double avgLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            double avgLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            
            ClusterCenter center = centers.get(i);
            double movement = calculateDistance(center.getLongitude(), center.getLatitude(), avgLng, avgLat);
            
            center.setLongitude(avgLng);
            center.setLatitude(avgLat);
            
            if (movement > 0.001) { // 1米的收敛阈值
                converged = false;
            }
        }
        
        return converged;
    }
    
    /**
     * 阶段2：在地理约束下进行时间平衡（性能优化版）
     */
    private List<List<Accumulation>> timeBalanceWithGeographicConstraints(
            List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        // 计算每个聚类的地理聚集度作为基准
        List<Double> baselineCompactness = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            baselineCompactness.add(calculateClusterCompactness(cluster));
        }
        
        // 计算目标工作时间
        double totalWorkTime = clusters.stream()
            .mapToDouble(cluster -> calculateClusterWorkTime(cluster, depot, timeMatrix))
            .sum();
        double targetWorkTime = totalWorkTime / clusters.size();
        
        log.info("目标工作时间平衡：每个聚类目标 {} 分钟", String.format("%.1f", targetWorkTime));
        
        // 高效的时间平衡优化（避免O(n²)复杂度）
        return optimizeTimeBalanceEfficiently(clusters, baselineCompactness, depot, timeMatrix, targetWorkTime);
    }
    
    /**
     * 高效的时间平衡优化算法
     */
    private List<List<Accumulation>> optimizeTimeBalanceEfficiently(
            List<List<Accumulation>> clusters, 
            List<Double> baselineCompactness,
            TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix, 
            double targetWorkTime) {
        
        boolean improved = true;
        int iteration = 0;
        int maxIterations = Math.min(20, clusters.size() * 2); // 限制最大迭代次数
        
        while (improved && iteration < maxIterations) {
            improved = false;
            iteration++;
            
            // 计算每个聚类的工作时间偏差
            List<ClusterTimeInfo> clusterInfos = new ArrayList<>();
            for (int i = 0; i < clusters.size(); i++) {
                double workTime = calculateClusterWorkTime(clusters.get(i), depot, timeMatrix);
                double deviation = workTime - targetWorkTime;
                clusterInfos.add(new ClusterTimeInfo(i, workTime, deviation));
            }
            
            // 按偏差排序，找到最不平衡的聚类
            clusterInfos.sort((a, b) -> Double.compare(Math.abs(b.deviation), Math.abs(a.deviation)));
            
            // 只处理前几个最不平衡的聚类对，避免全量搜索
            int pairsToProcess = Math.min(3, clusterInfos.size() / 2);
            
            for (int p = 0; p < pairsToProcess && !improved; p++) {
                ClusterTimeInfo heaviest = null, lightest = null;
                
                // 找到一个重载和一个轻载的聚类
                for (ClusterTimeInfo info : clusterInfos) {
                    if (info.deviation > TIME_BALANCE_THRESHOLD && heaviest == null) { // 超过平衡阈值认为重载
                        heaviest = info;
                    } else if (info.deviation < -TIME_BALANCE_THRESHOLD && lightest == null) { // 少于负平衡阈值认为轻载
                        lightest = info;
                    }
                    
                    if (heaviest != null && lightest != null) break;
                }
                
                if (heaviest == null || lightest == null) continue;
                
                List<Accumulation> heavyCluster = clusters.get(heaviest.index);
                List<Accumulation> lightCluster = clusters.get(lightest.index);
                
                // 智能选择候选点：只考虑边界点和高价值点
                List<Accumulation> heavyCandidates = selectBoundaryPoints(heavyCluster, 5);
                List<Accumulation> lightCandidates = selectBoundaryPoints(lightCluster, 5);
                
                // 寻找最佳交换
                SwapCandidate bestSwap = findBestSwapCandidate(
                    heavyCandidates, lightCandidates, heavyCluster, lightCluster,
                    baselineCompactness.get(heaviest.index), baselineCompactness.get(lightest.index),
                    depot, timeMatrix, targetWorkTime);
                
                if (bestSwap != null && bestSwap.improvement > 5.0) { // 至少5分钟改善才执行
                    // 执行交换
                    heavyCluster.remove(bestSwap.acc1);
                    heavyCluster.add(bestSwap.acc2);
                    lightCluster.remove(bestSwap.acc2);
                    lightCluster.add(bestSwap.acc1);
                    improved = true;
                    
                    log.info("时间平衡优化：交换聚集区{}和{}，改善度: {}分钟", 
                        bestSwap.acc1.getAccumulationId(), bestSwap.acc2.getAccumulationId(), String.format("%.1f", bestSwap.improvement));
                }
            }
        }
        
        log.info("高效时间平衡优化完成，执行了{}轮迭代", iteration);
        return clusters;
    }
    
    /**
     * 选择边界点（距离聚类中心较远的点，更容易交换）
     */
    private List<Accumulation> selectBoundaryPoints(List<Accumulation> cluster, int maxCount) {
        if (cluster.size() <= maxCount) {
            return new ArrayList<>(cluster);
        }
        
        // 计算聚类中心
        double avgLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double avgLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        // 按到中心的距离排序，选择最远的几个点
        return cluster.stream()
            .sorted((a, b) -> Double.compare(
                calculateDistance(b.getLongitude(), b.getLatitude(), avgLng, avgLat),
                calculateDistance(a.getLongitude(), a.getLatitude(), avgLng, avgLat)
            ))
            .limit(maxCount)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 寻找最佳交换候选
     */
    private SwapCandidate findBestSwapCandidate(
            List<Accumulation> candidates1, List<Accumulation> candidates2,
            List<Accumulation> cluster1, List<Accumulation> cluster2,
            double baselineCompactness1, double baselineCompactness2,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix, double targetWorkTime) {
        
        SwapCandidate best = null;
        double bestImprovement = 0;
        
        for (Accumulation acc1 : candidates1) {
            for (Accumulation acc2 : candidates2) {
                // 快速检查：配送时间差异太小的不考虑
                double timeDiff = Math.abs(acc1.getDeliveryTime() - acc2.getDeliveryTime());
                if (timeDiff < 5.0) continue; // 至少5分钟差异才有意义
                
                // 检查交换是否改善时间平衡且不破坏地理聚集
                if (wouldImproveTimeBalanceAndPreserveCompactness(
                        acc1, acc2, cluster1, cluster2, 
                        baselineCompactness1, baselineCompactness2, 
                        depot, timeMatrix, targetWorkTime)) {
                    
                    double improvement = calculateTimeBalanceImprovement(
                        acc1, acc2, cluster1, cluster2, depot, timeMatrix, targetWorkTime);
                    
                    if (improvement > bestImprovement) {
                        bestImprovement = improvement;
                        best = new SwapCandidate(acc1, acc2, improvement);
                    }
                }
            }
        }
        
        return best;
    }
    
    /**
     * 聚类时间信息
     */
    private static class ClusterTimeInfo {
        final int index;
        final double workTime;
        final double deviation;
        
        ClusterTimeInfo(int index, double workTime, double deviation) {
            this.index = index;
            this.workTime = workTime;
            this.deviation = deviation;
        }
    }
    
    /**
     * 交换候选
     */
    private static class SwapCandidate {
        final Accumulation acc1;
        final Accumulation acc2;
        final double improvement;
        
        SwapCandidate(Accumulation acc1, Accumulation acc2, double improvement) {
            this.acc1 = acc1;
            this.acc2 = acc2;
            this.improvement = improvement;
        }
    }
    
    /**
     * 基于拆分合并的时间平衡优化
     * 在保持地理聚集的前提下，通过拆分大片区、合并小片区来实现时间均衡
     */
    private List<List<Accumulation>> splitAndMergeTimeBalance(
            List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix, int targetClusterCount) {
        
        log.info("========== 开始拆分合并时间平衡优化 ==========");
        log.info("中转站: {}, 初始聚类数: {}", depot.getTransitDepotName(), clusters.size());
        
        List<List<Accumulation>> optimizedClusters = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            optimizedClusters.add(new ArrayList<>(cluster));  // 深拷贝防止修改原数据
        }
        
        // 输出初始状态
        logInitialClusterState(optimizedClusters, depot, timeMatrix);
        
        // 多轮迭代优化，直至时间平衡或达到最大迭代次数
        // 激进策略：增加迭代次数，允许临时超过目标聚类数
        int maxIterations = 50;
        
        log.info("优化参数: 最大迭代{}轮, 平衡阈值{}分钟", maxIterations, String.format("%.1f", TIME_BALANCE_THRESHOLD));
        
        for (int iteration = 0; iteration < maxIterations; iteration++) {
            log.info("第{}轮时间平衡优化", iteration + 1);
            
            // 计算当前目标工作时间
            double totalWorkTime = optimizedClusters.stream()
                .mapToDouble(cluster -> calculateClusterWorkTime(cluster, depot, timeMatrix))
                .sum();
            double targetWorkTime = totalWorkTime / optimizedClusters.size();
            
            log.info("当前目标工作时间: {}分钟", String.format("%.1f", targetWorkTime));
            
            // 分析各片区工作时间偏差
            List<ClusterTimeAnalysis> analysis = analyzeClusterTimeBalance(
                optimizedClusters, depot, timeMatrix, targetWorkTime);
            
            // 详细记录时间分布情况
            logDetailedTimeDistribution(analysis, targetWorkTime);
            
            // 检查是否已达到时间平衡
            double maxDeviation = analysis.stream()
                .mapToDouble(a -> Math.abs(a.deviation))
                .max().orElse(0.0);
            
            if (maxDeviation <= TIME_BALANCE_THRESHOLD) {
                log.info("时间平衡已达标，最大偏差: {}分钟", String.format("%.1f", maxDeviation));
                break;
            }
            
            // 详细记录阈值判断过程
            logThresholdDecisionProcess(analysis, targetWorkTime);
            
            boolean hasChanges = false;
            
            // 1. 处理过小的片区（合并到相邻片区）
            hasChanges |= mergeSmallClusters(optimizedClusters, analysis, depot, timeMatrix, targetWorkTime);
            
            // 2. 处理过大的片区（激进拆分策略：允许临时超过目标聚类数）
            hasChanges |= aggressiveSplitLargeClusters(optimizedClusters, analysis, depot, timeMatrix, targetWorkTime, targetClusterCount);
            
            // 3. 基于方差的激进转移优化（修复缺失步骤）
            hasChanges |= optimizeTimeBalanceByVariance(optimizedClusters, depot, timeMatrix);
            
            // 如果没有变化，提前结束
            if (!hasChanges) {
                log.info("没有可优化的拆分合并操作，迭代结束");
                break;
            }
            
            // 输出本轮优化结果
            logIterationResult(optimizedClusters, depot, timeMatrix, iteration + 1);
        }
        
        // ===== 激进策略后期处理：智能合并回目标数量 =====
        int finalClusterCount = optimizedClusters.size();
        log.info("=== 激进策略后期处理阶段 ===");
        log.info("迭代优化完成，当前聚类数: {}, 目标聚类数: {}", finalClusterCount, targetClusterCount);
        
        if (finalClusterCount > targetClusterCount) {
            log.info("聚类数量超过目标({} > {})，开始智能合并回目标数量...", finalClusterCount, targetClusterCount);
            log.info("智能合并策略：选择工作时间最小的聚类，优先与地理邻近的聚类合并");
            optimizedClusters = smartMergeToTarget(optimizedClusters, depot, timeMatrix, targetClusterCount);
            log.info("智能合并回目标数量完成，最终聚类数: {}", optimizedClusters.size());
        } else {
            log.info("聚类数量已达到或低于目标，无需智能合并");
        }
        
        // ===== 最后阶段：智能强制合并处理剩余小聚类 =====
        log.info("=== 智能强制合并阶段 ===");
        log.info("处理剩余小聚类，确保所有聚类都有足够的规模");
        int beforeForceMerge = optimizedClusters.size();
        optimizedClusters = intelligentForcedMerge(optimizedClusters, depot, timeMatrix);
        int afterForceMerge = optimizedClusters.size();
        
        if (beforeForceMerge != afterForceMerge) {
            log.info("智能强制合并完成，聚类数: {} -> {}", beforeForceMerge, afterForceMerge);
        } else {
            log.info("智能强制合并完成，最终剩余小聚类: {}", beforeForceMerge - afterForceMerge);
        }
        
        // ===== 激进转移策略总结 =====
        log.info("=== 激进转移策略总结 ===");
        log.info("激进拆分合并时间平衡优化完成，最终聚类数: {}", optimizedClusters.size());
        
        // 计算最终的工作时间分布
        List<Double> finalWorkTimes = new ArrayList<>();
        for (List<Accumulation> cluster : optimizedClusters) {
            if (!cluster.isEmpty()) {
                double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
                finalWorkTimes.add(workTime);
            }
        }
        
        if (!finalWorkTimes.isEmpty()) {
            double minWorkTime = finalWorkTimes.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            double maxWorkTime = finalWorkTimes.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            double avgWorkTime = finalWorkTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            
            log.info("最终工作时间分布: 最小{}分钟, 最大{}分钟, 平均{}分钟", 
                String.format("%.1f", minWorkTime), 
                String.format("%.1f", maxWorkTime), 
                String.format("%.1f", avgWorkTime));
            
            // 检查是否符合激进转移策略的目标（300-600分钟）
            long withinRange = finalWorkTimes.stream()
                .mapToLong(wt -> (wt >= MIN_CLUSTER_WORK_TIME && wt <= MERGE_MAX_WORK_TIME) ? 1 : 0)
                .sum();
            
            log.info("符合目标范围({}~{}分钟)的聚类: {}/{} ({}%)", 
                String.format("%.0f", MIN_CLUSTER_WORK_TIME), 
                String.format("%.0f", MERGE_MAX_WORK_TIME),
                withinRange, finalWorkTimes.size(), 
                String.format("%.1f", (double) withinRange / finalWorkTimes.size() * 100));
        }
        return optimizedClusters;
    }
    
    /**
     * 分析各聚类的时间平衡情况
     */
    private List<ClusterTimeAnalysis> analyzeClusterTimeBalance(
            List<List<Accumulation>> clusters, TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix, double targetWorkTime) {
        
        List<ClusterTimeAnalysis> analysis = new ArrayList<>();
        
        for (int i = 0; i < clusters.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
            double deviation = workTime - targetWorkTime;
            double compactness = calculateClusterCompactness(cluster);
            
            analysis.add(new ClusterTimeAnalysis(i, cluster, workTime, deviation, compactness));
        }
        
        // 按偏差绝对值降序排序，优先处理偏差最大的
        analysis.sort((a, b) -> Double.compare(Math.abs(b.deviation), Math.abs(a.deviation)));
        
        return analysis;
    }
    
    /**
     * 合并过小的聚类
     */
    private boolean mergeSmallClusters(
            List<List<Accumulation>> clusters, List<ClusterTimeAnalysis> analysis,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix, double targetWorkTime) {
        
        boolean hasChanges = false;
        double mergeThreshold = calculateMergeThreshold(); // 使用配置的合并阈值
        
        for (ClusterTimeAnalysis clusterAnalysis : analysis) {
            if (clusterAnalysis.workTime < mergeThreshold && clusterAnalysis.cluster.size() > 0) {
                // 找到最适合合并的目标聚类
                int targetClusterIndex = findBestMergeTarget(
                    clusterAnalysis.cluster, clusters, clusterAnalysis.index, depot);
                
                if (targetClusterIndex != -1) {
                    // 执行合并
                    List<Accumulation> targetCluster = clusters.get(targetClusterIndex);
                    double targetClusterWorkTime = calculateClusterWorkTime(targetCluster, depot, timeMatrix);
                    
                    // 检查合并后是否不会过大
                    double mergedWorkTime = clusterAnalysis.workTime + targetClusterWorkTime;
                    if (mergedWorkTime <= calculateMergeMaxConstraint()) { // 不超过合并上限
                        
                        // 凸包冲突检测：检查合并后是否会产生地理聚集冲突
                        if (canMergeWithoutConvexHullConflict(clusterAnalysis.cluster, targetCluster, clusters)) {
                            log.info("合并小片区[{}]到片区[{}]，工作时间: {} + {} = {}分钟", 
                                clusterAnalysis.index, targetClusterIndex, 
                                String.format("%.1f", clusterAnalysis.workTime), String.format("%.1f", targetClusterWorkTime), String.format("%.1f", mergedWorkTime));
                            
                            // 将小聚类的所有点按地理邻近性合并到目标聚类
                            mergeClusterGeographically(clusterAnalysis.cluster, targetCluster);
                            
                            // 清空原聚类
                            clusterAnalysis.cluster.clear();
                            hasChanges = true;
                        } else {
                            log.info("合并小片区[{}]到片区[{}]因凸包冲突被拒绝，工作时间: {} + {} = {}分钟", 
                                clusterAnalysis.index, targetClusterIndex, 
                                String.format("%.1f", clusterAnalysis.workTime), String.format("%.1f", targetClusterWorkTime), String.format("%.1f", mergedWorkTime));
                        }
                    } else {
                        log.info("合并后工作时间过大({}>最大允许{}分钟)，取消合并", 
                            String.format("%.1f", mergedWorkTime), String.format("%.1f", calculateMergeMaxConstraint()));
                    }
                } else {
                    log.info("聚类[{}]无法找到合适的合并目标", clusterAnalysis.index);
                }
            } else if (clusterAnalysis.cluster.size() > 0) {
                debugLog("聚类[{}]不符合合并条件: {}分钟 >= {}分钟(合并阈值)", 
                    clusterAnalysis.index, String.format("%.1f", clusterAnalysis.workTime), String.format("%.1f", mergeThreshold));
            }
        }
        
        // 移除空聚类
        clusters.removeIf(List::isEmpty);
        
        return hasChanges;
    }
    
    /**
     * 激进拆分策略：对明确过大的聚类进行强制拆分
     * 核心思路：
     * 1. 对于大于650分钟的聚类，无论聚类数是否达标都强制拆分
     * 2. 允许临时超过目标聚类数，后续通过合并调整
     * 3. 解决转移策略失效的问题
     */
    private boolean aggressiveSplitLargeClusters(
            List<List<Accumulation>> clusters, List<ClusterTimeAnalysis> analysis,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix, double targetWorkTime, int targetClusterCount) {
        
        boolean hasChanges = false;
        // 激进拆分阈值：650分钟，明确过大的聚类必须拆分
        double aggressiveSplitThreshold = 650.0;
        int currentClusterCount = clusters.size();
        
        log.info("=== 激进拆分策略决策 ===");
        log.info("当前聚类数量: {}, 目标聚类数量: {}", currentClusterCount, targetClusterCount);
        log.info("激进拆分阈值: {}分钟 (无论聚类数是否达标)", String.format("%.1f", aggressiveSplitThreshold));
        log.info("理想时间: {}分钟, 常规拆分阈值: {}分钟", String.format("%.1f", IDEAL_CLUSTER_WORK_TIME), String.format("%.1f", calculateSplitThreshold()));
        
        // 统计需要激进拆分的聚类
        long aggressiveSplitCandidateCount = analysis.stream()
            .filter(a -> a.workTime > aggressiveSplitThreshold && a.cluster.size() >= 4)
            .count();
        log.info("符合激进拆分条件的聚类数量: {} (工作时间 > {}分钟)", aggressiveSplitCandidateCount, String.format("%.1f", aggressiveSplitThreshold));
        
        if (aggressiveSplitCandidateCount > 0) {
            log.info("检测到超大聚类，启用激进拆分策略，允许临时超过目标聚类数");
        }
        
        // 为了避免索引问题，我们使用新的方式来处理拆分
        List<List<Accumulation>> clustersToSplit = new ArrayList<>();
        
        // 第一阶段：激进拆分明确过大的聚类
        for (ClusterTimeAnalysis clusterAnalysis : analysis) {
            if (clusterAnalysis.workTime > aggressiveSplitThreshold && clusterAnalysis.cluster.size() >= 4) {
                log.info("激进拆分聚类[{}]: {}分钟 > {}分钟(激进阈值), 包含{}个点", 
                    clusterAnalysis.index, String.format("%.1f", clusterAnalysis.workTime), 
                    String.format("%.1f", aggressiveSplitThreshold), clusterAnalysis.cluster.size());
                
                // 计算激进拆分的份数：确保拆分后每个子聚类>300分钟（用户设计）
                int splitParts = calculateOptimalSplitParts(clusterAnalysis.workTime);
                splitParts = Math.min(splitParts, clusterAnalysis.cluster.size() / 2); // 最多拆分到每份至少2个点
                
                log.info("激进拆分计算: {}分钟 → {}份 (确保每份>300分钟，最大允许{}份)", 
                    String.format("%.1f", clusterAnalysis.workTime), splitParts, clusterAnalysis.cluster.size() / 2);
                
                if (splitParts >= 2) {
                    log.info("执行激进拆分，工作时间: {}分钟，拆分为{}个部分", 
                        String.format("%.1f", clusterAnalysis.workTime), splitParts);
                    
                    // 执行地理聚集拆分
                    List<List<Accumulation>> splitClusters = splitClusterGeographically(
                        clusterAnalysis.cluster, splitParts, depot);
                    
                    if (splitClusters.size() > 1) {
                        // 清空原聚类（标记为需要移除）
                        clusterAnalysis.cluster.clear();
                        
                        // 将拆分结果添加到待处理列表
                        clustersToSplit.addAll(splitClusters);
                        hasChanges = true;
                        
                        // 输出拆分结果
                        for (int i = 0; i < splitClusters.size(); i++) {
                            double splitWorkTime = calculateClusterWorkTime(splitClusters.get(i), depot, timeMatrix);
                            log.info("激进拆分片区{}，包含{}个点，工作时间: {}分钟", 
                                i + 1, splitClusters.get(i).size(), String.format("%.1f", splitWorkTime));
                        }
                    } else {
                        log.info("聚类[{}]激进拆分失败，维持原状", clusterAnalysis.index);
                    }
                } else {
                    log.info("聚类[{}]拆分份数不足({}份)，跳过激进拆分", clusterAnalysis.index, splitParts);
                }
            }
        }
        
        // 移除空聚类（被标记拆分的）
        clusters.removeIf(List::isEmpty);
        
        // 添加拆分后的新聚类
        clusters.addAll(clustersToSplit);
        
        if (hasChanges) {
            int newClusterCount = clusters.size();
            log.info("激进拆分完成，聚类数量: {} -> {} (临时超过目标{}个)", 
                currentClusterCount, newClusterCount, Math.max(0, newClusterCount - targetClusterCount));
        }
        
        // 第二阶段：如果聚类数未达标，继续使用常规拆分策略
        if (clusters.size() < targetClusterCount) {
            log.info("聚类数量仍未达标，继续使用常规拆分策略");
            hasChanges |= splitLargeClusters(clusters, 
                analyzeClusterTimeBalance(clusters, depot, timeMatrix, targetWorkTime),
                depot, timeMatrix, targetWorkTime, targetClusterCount);
        }
        
        return hasChanges;
    }
    
    /**
     * 拆分过大的聚类
     */
    private boolean splitLargeClusters(
            List<List<Accumulation>> clusters, List<ClusterTimeAnalysis> analysis,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix, double targetWorkTime, int targetClusterCount) {
        
        boolean hasChanges = false;
        double splitThreshold = calculateSplitThreshold(); // 使用配置的拆分阈值
        int currentClusterCount = clusters.size(); // 当前聚类数量
        
        log.info("=== 拆分策略决策 ===");
        log.info("当前聚类数量: {}, 目标聚类数量: {}", currentClusterCount, targetClusterCount);
        log.info("理想时间: {}分钟, 拆分阈值: {}分钟", String.format("%.1f", IDEAL_CLUSTER_WORK_TIME), String.format("%.1f", splitThreshold));
        
        // 核心策略判断：当聚类数量已达标时，使用转移策略而不是拆分策略
        if (currentClusterCount >= targetClusterCount) {
            log.info("聚类数量已达标，采用边缘点转移策略而非拆分策略");
            return transferPointsFromLargeClusters(clusters, analysis, depot, timeMatrix, targetWorkTime, splitThreshold);
        } else {
            log.info("聚类数量未达标，采用传统拆分策略");
        }
        
        // 统计符合拆分条件的聚类
        long splitCandidateCount = analysis.stream()
            .filter(a -> a.workTime > splitThreshold && a.cluster.size() >= 4)
            .count();
        log.info("符合拆分条件的聚类数量: {}", splitCandidateCount);
        
        // 为了避免索引问题，我们使用新的方式来处理拆分
        List<List<Accumulation>> clustersToSplit = new ArrayList<>();
        
        // 收集需要拆分的聚类
        for (ClusterTimeAnalysis clusterAnalysis : analysis) {
            if (clusterAnalysis.workTime > splitThreshold && clusterAnalysis.cluster.size() >= 4) {
                log.info("聚类[{}]符合拆分条件: {}分钟 > {}分钟(拆分阈值), 包含{}个点", 
                    clusterAnalysis.index, String.format("%.1f", clusterAnalysis.workTime), 
                    String.format("%.1f", splitThreshold), clusterAnalysis.cluster.size());
                
                // 计算需要拆分的份数
                int splitParts = (int) Math.ceil(clusterAnalysis.workTime / targetWorkTime);
                splitParts = Math.min(splitParts, clusterAnalysis.cluster.size() / 2); // 最多拆分到每份至少2个点
                
                log.info("计算拆分数量: {}分钟 ÷ {}分钟 = {}份 (最大允许{}份)", 
                    String.format("%.1f", clusterAnalysis.workTime), String.format("%.1f", targetWorkTime), 
                    (int) Math.ceil(clusterAnalysis.workTime / targetWorkTime), clusterAnalysis.cluster.size() / 2);
                
                if (splitParts >= 2) {
                    log.info("标记拆分大片区，工作时间: {}分钟，拆分为{}个部分", 
                        String.format("%.1f", clusterAnalysis.workTime), splitParts);
                    
                    // 执行地理聚集拆分
                    List<List<Accumulation>> splitClusters = splitClusterGeographically(
                        clusterAnalysis.cluster, splitParts, depot);
                    
                    if (splitClusters.size() > 1) {
                        // 清空原聚类（标记为需要移除）
                        clusterAnalysis.cluster.clear();
                        
                        // 将拆分结果添加到待处理列表
                        clustersToSplit.addAll(splitClusters);
                        hasChanges = true;
                        
                        // 输出拆分结果
                        for (int i = 0; i < splitClusters.size(); i++) {
                            double splitWorkTime = calculateClusterWorkTime(splitClusters.get(i), depot, timeMatrix);
                            log.info("拆分片区{}，包含{}个点，工作时间: {}分钟", 
                                i + 1, splitClusters.get(i).size(), String.format("%.1f", splitWorkTime));
                        }
                    } else {
                        log.info("聚类[{}]拆分失败，维持原状", clusterAnalysis.index);
                    }
                } else {
                    log.info("聚类[{}]拆分份数不足({}份)，跳过拆分", clusterAnalysis.index, splitParts);
                }
            } else {
                if (clusterAnalysis.cluster.size() > 0) {
                    if (clusterAnalysis.workTime <= splitThreshold) {
                        debugLog("聚类[{}]不符合拆分条件: {}分钟 <= {}分钟(拆分阈值)", 
                            clusterAnalysis.index, String.format("%.1f", clusterAnalysis.workTime), String.format("%.1f", splitThreshold));
                    } else {
                        debugLog("聚类[{}]点数不足: {}个点 < 4个点(最小拆分要求)", 
                            clusterAnalysis.index, clusterAnalysis.cluster.size());
                    }
                }
            }
        }
        
        // 移除空聚类（被标记拆分的）
        clusters.removeIf(List::isEmpty);
        
        // 添加拆分后的新聚类
        clusters.addAll(clustersToSplit);
        
        return hasChanges;
    }
    
    /**
     * 边缘点转移策略：从大聚类转移边缘点到附近小聚类
     * 当聚类数量已达标时使用此策略而非拆分
     */
    private boolean transferPointsFromLargeClusters(
            List<List<Accumulation>> clusters, List<ClusterTimeAnalysis> analysis,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix, double targetWorkTime, 
            double splitThreshold) {
        
        boolean hasChanges = false;
        
        // 找到需要转移点的大聚类
        List<ClusterTimeAnalysis> largeClusters = analysis.stream()
            .filter(a -> a.workTime > splitThreshold && a.cluster.size() >= 3) // 至少保留2个点
            .collect(Collectors.toList());
        
        // 找到可以接收点的小聚类
        List<ClusterTimeAnalysis> smallClusters = analysis.stream()
            .filter(a -> a.workTime < targetWorkTime * 0.95) // 小于目标时间95%的聚类
            .collect(Collectors.toList());
        
        log.info("识别到{}个大聚类需要转移点，{}个小聚类可接收点", largeClusters.size(), smallClusters.size());
        
        for (ClusterTimeAnalysis largeCluster : largeClusters) {
            if (smallClusters.isEmpty()) {
                log.info("没有足够的小聚类接收转移点，转移结束");
                break;
            }
            
            log.info("处理大聚类[{}]: {}分钟，{}个点", 
                largeCluster.index, String.format("%.1f", largeCluster.workTime), largeCluster.cluster.size());
            
            // 计算需要转移的工作时间
            double excessTime = largeCluster.workTime - targetWorkTime;
            log.info("需要转移约{}分钟的工作量", String.format("%.1f", excessTime));
            
            // 找到大聚类的边缘点（距离其他聚类中心较近的点）
            List<AccumulationTransferCandidate> transferCandidates = findEdgePointsForTransfer(
                largeCluster, clusters, depot, timeMatrix);
            
            if (transferCandidates.isEmpty()) {
                log.info("大聚类[{}]没有找到合适的转移候选点", largeCluster.index);
                continue;
            }
            
            // 按转移收益排序（优先转移能最大化平衡的点）
            transferCandidates.sort((a, b) -> Double.compare(b.transferBenefit, a.transferBenefit));
            
            double transferredTime = 0.0;
            int transferredCount = 0;
            
            // 逐个转移边缘点
            for (AccumulationTransferCandidate candidate : transferCandidates) {
                if (transferredTime >= excessTime * 0.8) { // 转移80%的超额时间即可
                    break;
                }
                
                // 基于整体方差判断转移是否有利（激进策略）
                if (!shouldExecuteTransferBasedOnVariance(clusters, candidate, depot, timeMatrix)) {
                    debugLog("转移后整体方差增大，跳过转移");
                    continue;
                }
                
                // 执行转移
                largeCluster.cluster.remove(candidate.point);
                candidate.targetCluster.add(candidate.point);
                
                transferredTime += candidate.pointWorkTime;
                transferredCount++;
                hasChanges = true;
                
                debugLog("转移点[{}] {}分钟 从聚类[{}]到聚类[{}]", 
                    candidate.point.getAccumulationName(), String.format("%.1f", candidate.pointWorkTime),
                    largeCluster.index, candidate.targetClusterIndex);
            }
            
            if (transferredCount > 0) {
                double newLargeTime = calculateClusterWorkTime(largeCluster.cluster, depot, timeMatrix);
                log.info("聚类[{}]转移{}个点，工作时间: {}分钟 -> {}分钟", 
                    largeCluster.index, transferredCount, 
                    String.format("%.1f", largeCluster.workTime), String.format("%.1f", newLargeTime));
            }
        }
        
        return hasChanges;
    }
    
    /**
     * 基于整体方差判断转移是否有利（激进策略）
     * 允许临时超过400分钟上限，只要能降低整体方差
     */
    private boolean shouldExecuteTransferBasedOnVariance(
            List<List<Accumulation>> clusters, 
            AccumulationTransferCandidate candidate,
            TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix) {
        
        // 计算转移前的整体方差
        double currentVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
        
        // 模拟转移：临时执行转移
        List<Accumulation> sourceCluster = null;
        List<Accumulation> targetCluster = candidate.targetCluster;
        
        // 找到源聚类
        for (List<Accumulation> cluster : clusters) {
            if (cluster.contains(candidate.point)) {
                sourceCluster = cluster;
                break;
            }
        }
        
        if (sourceCluster == null) {
            return false; // 找不到源聚类，安全起见拒绝转移
        }
        
        // 临时执行转移
        sourceCluster.remove(candidate.point);
        targetCluster.add(candidate.point);
        
        // 计算转移后的整体方差
        double newVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
        
        // 恢复转移前状态
        targetCluster.remove(candidate.point);
        sourceCluster.add(candidate.point);
        
        // 判断是否有利：方差降低则执行转移
        boolean shouldTransfer = newVariance < currentVariance;
        
        if (shouldTransfer) {
            debugLog("转移有利：方差从{}降低到{}", 
                String.format("%.2f", currentVariance), 
                String.format("%.2f", newVariance));
        } else {
            debugLog("转移不利：方差从{}增加到{}", 
                String.format("%.2f", currentVariance), 
                String.format("%.2f", newVariance));
        }
        
        return shouldTransfer;
    }
    
    /**
     * 计算工作时间的整体方差
     */
    private double calculateWorkTimeVariance(
            List<List<Accumulation>> clusters, 
            TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix) {
        
        if (clusters.isEmpty()) {
            return 0.0;
        }
        
        // 计算每个聚类的工作时间
        List<Double> workTimes = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            if (!cluster.isEmpty()) {
                double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
                workTimes.add(workTime);
            }
        }
        
        if (workTimes.isEmpty()) {
            return 0.0;
        }
        
        // 计算平均值
        double mean = workTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        
        // 计算方差：每个值与平均值差的平方的平均值
        double variance = workTimes.stream()
            .mapToDouble(time -> Math.pow(time - mean, 2))
            .average()
            .orElse(0.0);
        
        return variance;
    }
    
    /**
     * 小聚类特殊转移处理：为小聚类实现整体或批量转移策略
     * 解决小聚类（≤10个聚集区）无法找到边缘点的问题
     */
    private List<AccumulationTransferCandidate> findTransferCandidatesForSmallCluster(
            ClusterTimeAnalysis smallCluster, List<List<Accumulation>> allClusters,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        List<AccumulationTransferCandidate> candidates = new ArrayList<>();
        
        // 小聚类(≤10个点)使用特殊策略
        if (smallCluster.cluster.size() <= 10) {
            debugLog("小聚类[{}]特殊处理: 规模{}个聚集区, 工作时间{}分钟", 
                smallCluster.index, smallCluster.cluster.size(), 
                String.format("%.1f", smallCluster.workTime));
            
            // 策略1：寻找合适的目标聚类进行整体转移评估
            for (int i = 0; i < allClusters.size(); i++) {
                List<Accumulation> targetCluster = allClusters.get(i);
                if (targetCluster == smallCluster.cluster || targetCluster.isEmpty()) {
                    continue;
                }
                
                // 计算目标聚类当前工作时间
                double targetClusterTime = calculateClusterWorkTime(targetCluster, depot, timeMatrix);
                double smallClusterTime = smallCluster.workTime;
                
                // 宽松的安全上限判断：合并后不超过500分钟（比正常上限400分钟宽松）
                if (targetClusterTime + smallClusterTime <= 500.0) {
                    
                    // 地理距离检查：计算小聚类到目标聚类的平均距离
                    double avgDistance = calculateClusterDistance(smallCluster.cluster, targetCluster);
                    
                    // 如果地理距离合理（<25公里），为小聚类的每个点创建转移候选
                    if (avgDistance < 25.0) {
                        for (Accumulation point : smallCluster.cluster) {
                            double transferBenefit = 200.0 - avgDistance; // 基于距离的转移收益
                            candidates.add(new AccumulationTransferCandidate(
                                point, targetCluster, i,
                                point.getDeliveryTime(), transferBenefit)); // 高优先级
                        }
                        
                        debugLog("小聚类[{}] → 目标聚类[{}]: 平均距离{}公里, 合并后工作时间{}分钟, 候选数{}", 
                            smallCluster.index, i, String.format("%.2f", avgDistance),
                            String.format("%.1f", targetClusterTime + smallClusterTime),
                            smallCluster.cluster.size());
                        
                        break; // 找到合适目标就结束，避免重复候选
                    }
                }
            }
            
            // 策略2：如果策略1没找到合适目标，使用降级策略寻找次优选择
            if (candidates.isEmpty()) {
                for (int i = 0; i < allClusters.size(); i++) {
                    List<Accumulation> targetCluster = allClusters.get(i);
                    if (targetCluster == smallCluster.cluster || targetCluster.isEmpty()) {
                        continue;
                    }
                    
                    double targetClusterTime = calculateClusterWorkTime(targetCluster, depot, timeMatrix);
                    
                    // 更宽松的条件：只要目标聚类工作时间<350分钟就考虑
                    if (targetClusterTime < 350.0) {
                        // 为小聚类的部分点（工作时间较小的点）创建转移候选
                        for (Accumulation point : smallCluster.cluster) {
                            if (point.getDeliveryTime() < 20.0) { // 只转移小工作量的点
                                double avgDistance = calculateDistance(
                                    point.getLatitude(), point.getLongitude(),
                                    targetCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0),
                                    targetCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0));
                                
                                if (avgDistance < 30.0) { // 距离限制放宽到30公里
                                    double transferBenefit = 100.0 - avgDistance;
                                    candidates.add(new AccumulationTransferCandidate(
                                        point, targetCluster, i,
                                        point.getDeliveryTime(), transferBenefit));
                                }
                            }
                        }
                    }
                }
                
                if (!candidates.isEmpty()) {
                    debugLog("小聚类[{}]降级策略找到{}个转移候选", smallCluster.index, candidates.size());
                }
            }
        }
        
        log.info("小聚类[{}]特殊处理完成: 找到{}个转移候选（聚类规模{}）", 
            smallCluster.index, candidates.size(), smallCluster.cluster.size());
        return candidates;
    }
    
    /**
     * 计算两个聚类之间的平均距离
     */
    private double calculateClusterDistance(List<Accumulation> cluster1, List<Accumulation> cluster2) {
        if (cluster1.isEmpty() || cluster2.isEmpty()) {
            return Double.MAX_VALUE;
        }
        
        // 计算聚类中心点
        double center1Lat = cluster1.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double center1Lon = cluster1.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double center2Lat = cluster2.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double center2Lon = cluster2.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        return calculateDistance(center1Lat, center1Lon, center2Lat, center2Lon);
    }
    
    /**
     * 寻找大聚类中适合转移的边缘点
     */
    private List<AccumulationTransferCandidate> findEdgePointsForTransfer(
            ClusterTimeAnalysis largeCluster, List<List<Accumulation>> allClusters,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        List<AccumulationTransferCandidate> candidates = new ArrayList<>();
        
        // 计算大聚类的中心点
        double largeCenterLat = largeCluster.cluster.stream()
            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double largeCenterLon = largeCluster.cluster.stream()
            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        // 计算大聚类的平均半径（用于边缘点判断）
        double avgRadiusLarge = largeCluster.cluster.stream()
            .mapToDouble(point -> calculateDistance(
                point.getLatitude(), point.getLongitude(), largeCenterLat, largeCenterLon))
            .average().orElse(0.0);
        
        debugLog("大聚类[{}]分析: 中心({}, {}), 平均半径{}公里, 点数{}", 
            largeCluster.index, String.format("%.4f", largeCenterLat), String.format("%.4f", largeCenterLon),
            String.format("%.2f", avgRadiusLarge), largeCluster.cluster.size());
        
        // 遍历大聚类中的每个点
        for (Accumulation point : largeCluster.cluster) {
            double pointWorkTime = point.getDeliveryTime();
            
            // 计算点到大聚类中心的距离
            double distToLargeCenter = calculateDistance(
                point.getLatitude(), point.getLongitude(), largeCenterLat, largeCenterLon);
            
            // 寻找合适的目标聚类（支持多目标候选生成）
            List<TargetClusterOption> targetOptions = new ArrayList<>();
            
            for (int i = 0; i < allClusters.size(); i++) {
                List<Accumulation> otherCluster = allClusters.get(i);
                if (otherCluster == largeCluster.cluster || otherCluster.isEmpty()) {
                    continue;
                }
                
                // 计算到其他聚类中心的距离
                double otherCenterLat = otherCluster.stream()
                    .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
                double otherCenterLon = otherCluster.stream()
                    .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
                
                double distToOther = calculateDistance(
                    point.getLatitude(), point.getLongitude(), otherCenterLat, otherCenterLon);
                
                // 计算目标聚类的当前工作时间（用于负载均衡考虑）
                double targetWorkTime = calculateClusterWorkTime(otherCluster, depot, timeMatrix);
                
                // 收集所有距离合理的目标选项（不超过35公里）
                if (distToOther <= 35.0) {
                    targetOptions.add(new TargetClusterOption(otherCluster, i, distToOther, targetWorkTime));
                }
            }
            
            // 按综合评分排序：距离权重70%，工作量权重30%（优先选择工作量较少的聚类）
            targetOptions.sort((a, b) -> {
                double scoreA = a.distance * 0.7 + (a.workTime / 400.0) * 35.0 * 0.3; // 归一化工作时间到距离尺度
                double scoreB = b.distance * 0.7 + (b.workTime / 400.0) * 35.0 * 0.3;
                return Double.compare(scoreA, scoreB);
            });
            
            // 选择最优的前3个目标作为候选（确保目标多样化）
            int maxTargets = Math.min(3, targetOptions.size());
            double minDistToOther = targetOptions.isEmpty() ? Double.MAX_VALUE : targetOptions.get(0).distance;
            
            // 改进的边缘点判断算法：使用多重条件来找到更多的转移候选
            boolean isEdgePoint = false;
            String edgeReason = "";
            
            if (!targetOptions.isEmpty()) {
                // 条件1：经典条件 - 到其他聚类更近（放宽了1.5倍）
                if (minDistToOther < distToLargeCenter * 1.5) {
                    isEdgePoint = true;
                    edgeReason = "到其他聚类更近";
                }
                // 条件2：超大聚类特殊条件 - 距离中心远于平均半径
                else if (distToLargeCenter > avgRadiusLarge * 1.3) {
                    isEdgePoint = true;
                    edgeReason = "远离大聚类中心";
                }
                // 条件3：绝对距离条件 - 到其他聚类距离小于10公里
                else if (minDistToOther < 10.0) {
                    isEdgePoint = true;
                    edgeReason = "绝对距离近";
                }
                // 条件4：工作时间条件 - 工作时间较小且到其他聚类不太远
                else if (pointWorkTime < 15.0 && minDistToOther < 25.0) {
                    isEdgePoint = true;
                    edgeReason = "小工作量且距离适中";
                }
            }
            
            if (isEdgePoint && !targetOptions.isEmpty()) {
                // 为每个边缘点创建多个候选，指向不同的目标聚类（解决候选目标单一化问题）
                for (int t = 0; t < maxTargets; t++) {
                    TargetClusterOption targetOption = targetOptions.get(t);
                    
                    // 计算转移收益：距离收益 + 负载均衡收益 + 工作时间收益
                    double distanceBenefit = distToLargeCenter - targetOption.distance;
                    double loadBalanceBenefit = (400.0 - targetOption.workTime) / 400.0 * 50.0; // 工作量越少收益越高
                    double workTimeBenefit = pointWorkTime * 0.1;
                    
                    // 第一目标额外加权，后续目标递减（确保最优选择仍有优势）
                    double priorityWeight = 1.0 - t * 0.2; // 第1目标100%，第2目标80%，第3目标60%
                    double transferBenefit = (distanceBenefit + loadBalanceBenefit + workTimeBenefit) * priorityWeight;
                    
                    candidates.add(new AccumulationTransferCandidate(
                        point, targetOption.cluster, targetOption.index, pointWorkTime, transferBenefit));
                }
                
                /*debugLog("边缘点多目标候选: {}, 工作时间{}分钟, 到本中心{}公里, 目标数{}, 原因: {}",
                    point.getAccumulationName(), String.format("%.1f", pointWorkTime),
                    String.format("%.2f", distToLargeCenter), maxTargets, edgeReason);*/
            }
        }
        
        log.info("大聚类[{}]找到{}个边缘点转移候选（总点数{})", 
            largeCluster.index, candidates.size(), largeCluster.cluster.size());
        return candidates;
    }
    
    /**
     * 目标聚类选项数据结构（用于候选多样化生成）
     */
    private static class TargetClusterOption {
        final List<Accumulation> cluster;
        final int index;
        final double distance;
        final double workTime;
        
        TargetClusterOption(List<Accumulation> cluster, int index, double distance, double workTime) {
            this.cluster = cluster;
            this.index = index;
            this.distance = distance;
            this.workTime = workTime;
        }
    }
    
    /**
     * 点转移候选数据结构
     */
    private static class AccumulationTransferCandidate {
        final Accumulation point;
        final List<Accumulation> targetCluster;
        final int targetClusterIndex;
        final double pointWorkTime;
        final double transferBenefit;
        
        AccumulationTransferCandidate(Accumulation point, List<Accumulation> targetCluster, 
                                    int targetClusterIndex, double pointWorkTime, double transferBenefit) {
            this.point = point;
            this.targetCluster = targetCluster;
            this.targetClusterIndex = targetClusterIndex;
            this.pointWorkTime = pointWorkTime;
            this.transferBenefit = transferBenefit;
        }
    }
    
    /**
     * 聚类时间分析数据结构
     */
    private static class ClusterTimeAnalysis {
        final int index;
        final List<Accumulation> cluster;
        final double workTime;
        final double deviation;
        final double compactness;
        
        ClusterTimeAnalysis(int index, List<Accumulation> cluster, double workTime, 
                           double deviation, double compactness) {
            this.index = index;
            this.cluster = cluster;
            this.workTime = workTime;
            this.deviation = deviation;
            this.compactness = compactness;
        }
    }
    
    /**
     * 阶段3：最终优化（严格地理约束下的微调）
     */
    private List<List<Accumulation>> finalOptimizationWithGeographicConstraints(
            List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        // 这里可以添加最终的微调逻辑，比如处理边界点的归属
        // 暂时直接返回，后续可以根据需要扩展
        return clusters;
    }
    
    
    /**
     * 检查点交换是否改善时间平衡且保持地理聚集度
     */
    private boolean wouldImproveTimeBalanceAndPreserveCompactness(
            Accumulation acc1, Accumulation acc2,
            List<Accumulation> cluster1, List<Accumulation> cluster2,
            double baselineCompactness1, double baselineCompactness2,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix, double targetWorkTime) {
        
        // 创建临时聚类进行测试
        List<Accumulation> testCluster1 = new ArrayList<>(cluster1);
        List<Accumulation> testCluster2 = new ArrayList<>(cluster2);
        
        testCluster1.remove(acc1);
        testCluster1.add(acc2);
        testCluster2.remove(acc2);
        testCluster2.add(acc1);
        
        // 检查地理聚集度是否显著下降（不能超过20%下降）
        double newCompactness1 = calculateClusterCompactness(testCluster1);
        double newCompactness2 = calculateClusterCompactness(testCluster2);
        
        if (newCompactness1 > baselineCompactness1 * 1.2 || 
            newCompactness2 > baselineCompactness2 * 1.2) {
            return false; // 地理聚集度显著下降
        }
        
        // 检查时间平衡是否改善
        double currentWorkTime1 = calculateClusterWorkTime(cluster1, depot, timeMatrix);
        double currentWorkTime2 = calculateClusterWorkTime(cluster2, depot, timeMatrix);
        double newWorkTime1 = calculateClusterWorkTime(testCluster1, depot, timeMatrix);
        double newWorkTime2 = calculateClusterWorkTime(testCluster2, depot, timeMatrix);
        
        double currentDeviation = Math.abs(currentWorkTime1 - targetWorkTime) + 
                                 Math.abs(currentWorkTime2 - targetWorkTime);
        double newDeviation = Math.abs(newWorkTime1 - targetWorkTime) + 
                              Math.abs(newWorkTime2 - targetWorkTime);
        
        return newDeviation < currentDeviation; // 时间平衡改善
    }
    
    /**
     * 计算时间平衡改善度
     */
    private double calculateTimeBalanceImprovement(
            Accumulation acc1, Accumulation acc2,
            List<Accumulation> cluster1, List<Accumulation> cluster2,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix, double targetWorkTime) {
        
        double currentWorkTime1 = calculateClusterWorkTime(cluster1, depot, timeMatrix);
        double currentWorkTime2 = calculateClusterWorkTime(cluster2, depot, timeMatrix);
        
        // 创建临时聚类计算交换后的工作时间
        List<Accumulation> testCluster1 = new ArrayList<>(cluster1);
        List<Accumulation> testCluster2 = new ArrayList<>(cluster2);
        
        testCluster1.remove(acc1);
        testCluster1.add(acc2);
        testCluster2.remove(acc2);
        testCluster2.add(acc1);
        
        double newWorkTime1 = calculateClusterWorkTime(testCluster1, depot, timeMatrix);
        double newWorkTime2 = calculateClusterWorkTime(testCluster2, depot, timeMatrix);
        
        double currentDeviation = Math.abs(currentWorkTime1 - targetWorkTime) + 
                                 Math.abs(currentWorkTime2 - targetWorkTime);
        double newDeviation = Math.abs(newWorkTime1 - targetWorkTime) + 
                              Math.abs(newWorkTime2 - targetWorkTime);
        
        return currentDeviation - newDeviation; // 正值表示改善
    }
    
    /**
     * 处理空聚类（重新分配距离最远的点）
     */
    private List<List<Accumulation>> handleEmptyClusters(
            List<List<Accumulation>> clusters, 
            List<Accumulation> accumulations, 
            List<ClusterCenter> centers) {
        
        for (int i = 0; i < clusters.size(); i++) {
            if (clusters.get(i).isEmpty()) {
                // 找到距离当前所有中心最远的点
                Accumulation farthest = null;
                double maxMinDistance = -1;
                int sourceCluster = -1;
                
                for (int j = 0; j < clusters.size(); j++) {
                    if (j == i || clusters.get(j).isEmpty()) continue;
                    
                    for (Accumulation acc : clusters.get(j)) {
                        double minDistance = Double.MAX_VALUE;
                        for (ClusterCenter center : centers) {
                            double distance = calculateDistance(
                                acc.getLongitude(), acc.getLatitude(),
                                center.getLongitude(), center.getLatitude()
                            );
                            minDistance = Math.min(minDistance, distance);
                        }
                        
                        if (minDistance > maxMinDistance) {
                            maxMinDistance = minDistance;
                            farthest = acc;
                            sourceCluster = j;
                        }
                    }
                }
                
                // 将最远的点移动到空聚类
                if (farthest != null && sourceCluster != -1) {
                    clusters.get(sourceCluster).remove(farthest);
                    clusters.get(i).add(farthest);
                    
                    // 更新空聚类的中心点
                    centers.get(i).setLongitude(farthest.getLongitude());
                    centers.get(i).setLatitude(farthest.getLatitude());
                }
            }
        }
        
        return clusters;
    }
    
    /**
     * 找到最适合合并的目标聚类
     */
    private int findBestMergeTarget(
            List<Accumulation> sourceCluster, List<List<Accumulation>> allClusters, 
            int sourceIndex, TransitDepot depot) {
        
        if (sourceCluster.isEmpty()) return -1;
        
        // 计算源聚类的中心点
        double sourceLng = sourceCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double sourceLat = sourceCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        int bestTarget = -1;
        double bestScore = Double.MAX_VALUE;
        
        for (int i = 0; i < allClusters.size(); i++) {
            if (i == sourceIndex || allClusters.get(i).isEmpty()) continue;
            
            List<Accumulation> candidate = allClusters.get(i);
            
            // 计算候选聚类中心
            double candidateLng = candidate.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            double candidateLat = candidate.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            
            // 计算中心距离
            double distance = calculateDistance(sourceLng, sourceLat, candidateLng, candidateLat);
            
            // 计算合并后的紧密性（预测）
            double compactnessScore = estimateMergeCompactness(sourceCluster, candidate);
            
            // 综合评分：距离越近越好，紧密性越好越好
            double score = distance * 2.0 + compactnessScore;
            
            if (distance < 15.0 && score < bestScore) { // 15公里内才考虑合并
                bestScore = score;
                bestTarget = i;
            }
        }
        
        return bestTarget;
    }
    
    /**
     * 估算合并后的紧密性
     */
    private double estimateMergeCompactness(List<Accumulation> cluster1, List<Accumulation> cluster2) {
        List<Accumulation> merged = new ArrayList<>(cluster1);
        merged.addAll(cluster2);
        return calculateClusterCompactness(merged);
    }
    
    /**
     * 按地理邻近性合并聚类
     */
    private void mergeClusterGeographically(List<Accumulation> sourceCluster, List<Accumulation> targetCluster) {
        // 简单合并：将源聚类的所有点加入目标聚类
        // 在实际应用中，可以根据地理邻近性优化合并顺序
        targetCluster.addAll(sourceCluster);
    }
    
    /**
     * 按地理聚集方式拆分聚类
     */
    private List<List<Accumulation>> splitClusterGeographically(
            List<Accumulation> cluster, int splitParts, TransitDepot depot) {
        
        if (cluster.size() < splitParts * 2) {
            // 点数太少，无法有效拆分
            return Arrays.asList(cluster);
        }
        
        // 使用K-means算法进行地理拆分
        List<ClusterCenter> splitCenters = initializeGeographicCenters(cluster, splitParts);
        
        // 简化的K-means迭代
        for (int iteration = 0; iteration < 5; iteration++) {
            List<List<Accumulation>> splitClusters = assignToNearestGeographicCenters(cluster, splitCenters);
            
            // 处理空聚类
            splitClusters = handleEmptyClusters(splitClusters, cluster, splitCenters);
            
            // 更新中心点
            boolean converged = updateGeographicCenters(splitCenters, splitClusters);
            if (converged) break;
        }
        
        // 最终分配
        List<List<Accumulation>> finalSplit = assignToNearestGeographicCenters(cluster, splitCenters);
        
        // 移除空聚类
        finalSplit.removeIf(List::isEmpty);
        
        return finalSplit.isEmpty() ? Arrays.asList(cluster) : finalSplit;
    }
    
    /**
     * 记录迭代结果
     */
    private void logIterationResult(
            List<List<Accumulation>> clusters, TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix, int iteration) {
        
        double totalWorkTime = clusters.stream()
            .mapToDouble(cluster -> calculateClusterWorkTime(cluster, depot, timeMatrix))
            .sum();
        double avgWorkTime = totalWorkTime / clusters.size();
        
        // 计算时间偏差统计
        double[] workTimes = clusters.stream()
            .mapToDouble(cluster -> calculateClusterWorkTime(cluster, depot, timeMatrix))
            .toArray();
        
        double minTime = Arrays.stream(workTimes).min().orElse(0.0);
        double maxTime = Arrays.stream(workTimes).max().orElse(0.0);
        double variance = Arrays.stream(workTimes)
            .map(t -> Math.pow(t - avgWorkTime, 2))
            .average().orElse(0.0);
        double stdDev = Math.sqrt(variance);
        
        log.info("第{}轮优化结果: 片区数={}, 平均工作时间={}分钟, 标准差={}分钟, 最大差异={}分钟", 
            iteration, clusters.size(), String.format("%.1f", avgWorkTime), String.format("%.1f", stdDev), String.format("%.1f", maxTime - minTime));
    }
    
    /**
     * 智能合并回目标数量：为激进拆分后的收尾工作
     * 在激进拆分后，可能会产生超过目标数量的聚类，需要智能合并回目标数量
     */
    private List<List<Accumulation>> smartMergeToTarget(
            List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix, int targetClusterCount) {
        
        List<List<Accumulation>> result = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            result.add(new ArrayList<>(cluster));  // 深拷贝
        }
        
        int currentCount = result.size();
        if (currentCount <= targetClusterCount) {
            log.info("聚类数量已符合目标，无需合并");
            return result;
        }
        
        log.info("=== 智能合并回目标数量 ===");
        log.info("当前聚类数: {}, 目标聚类数: {}, 需要合并: {}个", currentCount, targetClusterCount, currentCount - targetClusterCount);
        
        int mergesToDo = currentCount - targetClusterCount;
        
        for (int mergeRound = 1; mergeRound <= mergesToDo; mergeRound++) {
            log.info("第{}轮智能合并 ({}/{})", mergeRound, mergesToDo);
            
            // 分析每个聚类的工作时间
            List<ClusterMergeAnalysis> mergeAnalysis = new ArrayList<>();
            for (int i = 0; i < result.size(); i++) {
                double workTime = calculateClusterWorkTime(result.get(i), depot, timeMatrix);
                mergeAnalysis.add(new ClusterMergeAnalysis(i, result.get(i), workTime));
            }
            
            // 找到最小的聚类作为合并源
            ClusterMergeAnalysis sourceCluster = mergeAnalysis.stream()
                .min(Comparator.comparingDouble(a -> a.workTime))
                .orElse(null);
            
            if (sourceCluster == null || result.size() <= 1) {
                log.warn("没有找到可合并的源聚类，合并结束");
                break;
            }
            
            // 找到最佳合并目标（工作时间最小且不是源聚类的）
            ClusterMergeAnalysis targetCluster = mergeAnalysis.stream()
                .filter(a -> a.index != sourceCluster.index)
                .min(Comparator.comparingDouble(a -> a.workTime))
                .orElse(null);
            
            if (targetCluster == null) {
                log.warn("没有找到可合并的目标聚类，合并结束");
                break;
            }
            
            // 检查合并是否超过600分钟上限（用户设计）
            double mergedWorkTime = sourceCluster.workTime + targetCluster.workTime;
            if (mergedWorkTime <= MERGE_MAX_WORK_TIME) {
                // 执行正常合并
                log.info("合并聚类[{}]({}分钟)到聚类[{}]({}分钟)，合并后: {}分钟", 
                    sourceCluster.index, String.format("%.1f", sourceCluster.workTime),
                    targetCluster.index, String.format("%.1f", targetCluster.workTime),
                    String.format("%.1f", mergedWorkTime));
                
                // 将源聚类的所有点添加到目标聚类
                result.get(targetCluster.index).addAll(sourceCluster.cluster);
            } else {
                // 超过600分钟，使用打散策略（用户设计）
                log.warn("合并会超过600分钟上限({}分钟)，使用打散策略", 
                    String.format("%.1f", mergedWorkTime));
                
                // 创建可用聚类列表（排除源聚类）
                List<List<Accumulation>> availableClusters = new ArrayList<>();
                for (int i = 0; i < result.size(); i++) {
                    if (i != sourceCluster.index) {
                        availableClusters.add(result.get(i));
                    }
                }
                
                // 打散源聚类
                disperseClusterToNearby(sourceCluster.cluster, availableClusters, depot, timeMatrix);
            }
            
            // 移除源聚类
            result.remove(sourceCluster.index);
            
            log.info("本轮合并完成，剩余聚类数: {}", result.size());
            
            if (result.size() <= targetClusterCount) {
                log.info("已达到目标聚类数，合并结束");
                break;
            }
        }
        
        log.info("智能合并回目标数量完成，最终聚类数: {}", result.size());
        return result;
    }
    
    /**
     * 聚类合并分析数据结构
     */
    private static class ClusterMergeAnalysis {
        final int index;
        final List<Accumulation> cluster;
        final double workTime;
        
        ClusterMergeAnalysis(int index, List<Accumulation> cluster, double workTime) {
            this.index = index;
            this.cluster = cluster;
            this.workTime = workTime;
        }
    }
    
    /**
     * 智能强制合并：处理剩余的小聚类
     * 对于无法通过正常距离约束合并的小聚类，选择最近的合并目标，即使超出正常阈值
     */
    private List<List<Accumulation>> intelligentForcedMerge(
            List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        List<List<Accumulation>> result = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            result.add(new ArrayList<>(cluster));  // 深拷贝
        }
        
        int smallClusterThreshold = 3; // ≤3个点认为是小聚类
        boolean hasChanges = true;
        int iteration = 0;
        int maxIterations = 5;
        
        while (hasChanges && iteration < maxIterations) {
            hasChanges = false;
            iteration++;
            
            log.info("智能强制合并第{}轮", iteration);
            
            // 识别小聚类
            List<SmallClusterInfo> smallClusters = new ArrayList<>();
            for (int i = 0; i < result.size(); i++) {
                List<Accumulation> cluster = result.get(i);
                if (cluster.size() <= smallClusterThreshold && !cluster.isEmpty()) {
                    double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
                    double compactness = calculateClusterCompactness(cluster);
                    smallClusters.add(new SmallClusterInfo(i, cluster, workTime, compactness));
                }
            }
            
            if (smallClusters.isEmpty()) {
                log.info("没有发现需要强制合并的小聚类");
                break;
            }
            
            log.info("发现{}个小聚类需要强制合并", smallClusters.size());
            
            // 按聚类大小升序排序，优先处理最小的聚类
            smallClusters.sort((a, b) -> Integer.compare(a.cluster.size(), b.cluster.size()));
            
            for (SmallClusterInfo smallCluster : smallClusters) {
                // 检查聚类是否还存在（可能已被合并）
                if (smallCluster.index >= result.size() || result.get(smallCluster.index).isEmpty()) {
                    continue;
                }
                
                // 寻找最佳强制合并目标
                MergeTarget bestTarget = findBestForcedMergeTarget(
                    smallCluster, result, depot, timeMatrix);
                
                if (bestTarget != null) {
                    // 执行强制合并
                    List<Accumulation> sourceCluster = result.get(smallCluster.index);
                    List<Accumulation> targetCluster = result.get(bestTarget.targetIndex);
                    
                    log.info("强制合并聚类{}({} 个点，工作时间{}分钟)到聚类{}({} 个点)，合并影响度: {}", 
                        smallCluster.index, sourceCluster.size(), 
                        String.format("%.1f", smallCluster.workTime),
                        bestTarget.targetIndex, targetCluster.size(),
                        String.format("%.3f", bestTarget.compactnessImpact));
                    
                    // 将小聚类的所有点合并到目标聚类
                    targetCluster.addAll(sourceCluster);
                    sourceCluster.clear(); // 清空源聚类
                    
                    hasChanges = true;
                }
            }
            
            // 移除空的聚类
            result.removeIf(List::isEmpty);
            
            // 统计本轮结果
            int remainingSmallClusters = (int) result.stream()
                .mapToInt(List::size)
                .filter(size -> size <= smallClusterThreshold)
                .count();
            
            log.info("第{}轮强制合并完成，剩余小聚类: {}", iteration, remainingSmallClusters);
        }
        
        // 最终统计
        int finalSmallClusters = (int) result.stream()
            .mapToInt(List::size)
            .filter(size -> size <= smallClusterThreshold)
            .count();
        
        log.info("智能强制合并完成，最终剩余小聚类: {}", finalSmallClusters);
        
        return result;
    }
    
    /**
     * 寻找最佳强制合并目标
     */
    private MergeTarget findBestForcedMergeTarget(
            SmallClusterInfo smallCluster, List<List<Accumulation>> allClusters,
            TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        MergeTarget bestTarget = null;
        double bestScore = Double.MAX_VALUE; // 分数越小越好
        
        // 计算小聚类的地理中心
        double smallClusterLng = smallCluster.cluster.stream()
            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double smallClusterLat = smallCluster.cluster.stream()
            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        for (int i = 0; i < allClusters.size(); i++) {
            if (i == smallCluster.index || allClusters.get(i).isEmpty()) {
                continue; // 跳过自己和空聚类
            }
            
            List<Accumulation> targetCluster = allClusters.get(i);
            
            // 计算合并后的紧密度影响
            List<Accumulation> mergedCluster = new ArrayList<>(targetCluster);
            mergedCluster.addAll(smallCluster.cluster);
            
            double originalCompactness = calculateClusterCompactness(targetCluster);
            double mergedCompactness = calculateClusterCompactness(mergedCluster);
            double compactnessImpact = (originalCompactness - mergedCompactness) / originalCompactness;
            
            // 计算地理距离
            double targetClusterLng = targetCluster.stream()
                .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            double targetClusterLat = targetCluster.stream()
                .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double distance = calculateDistance(smallClusterLng, smallClusterLat, 
                targetClusterLng, targetClusterLat);
            
            // 计算合并评分：综合考虑距离和紧密度影响
            // 距离权重0.6，紧密度影响权重0.4
            double distanceScore = distance / 50.0; // 归一化距离（50公里为基准）
            double compactnessScore = Math.max(0.0, compactnessImpact) * 10.0; // 紧密度下降惩罚
            double totalScore = distanceScore * 0.6 + compactnessScore * 0.4;
            
            // 添加额外的考虑因素
            // 1. 更倾向于合并到较大的聚类（分散影响）
            double sizeBonus = Math.log(targetCluster.size()) * 0.1;
            totalScore -= sizeBonus;
            
            // 2. 避免合并到过于紧密的聚类
            if (originalCompactness > 0.9) {
                totalScore += 1.0; // 紧密聚类惩罚
            }
            
            if (totalScore < bestScore) {
                bestScore = totalScore;
                bestTarget = new MergeTarget(i, distance, compactnessImpact, totalScore);
            }
        }
        
        return bestTarget;
    }
    
    /**
     * 小聚类信息
     */
    private static class SmallClusterInfo {
        int index;
        List<Accumulation> cluster;
        double workTime;
        double compactness;
        
        SmallClusterInfo(int index, List<Accumulation> cluster, double workTime, double compactness) {
            this.index = index;
            this.cluster = cluster;
            this.workTime = workTime;
            this.compactness = compactness;
        }
    }
    
    /**
     * 合并目标信息
     */
    private static class MergeTarget {
        int targetIndex;
        double distance;
        double compactnessImpact;
        double score;
        
        MergeTarget(int targetIndex, double distance, double compactnessImpact, double score) {
            this.targetIndex = targetIndex;
            this.distance = distance;
            this.compactnessImpact = compactnessImpact;
            this.score = score;
        }
    }
    
    // ================ 日志辅助方法 ================
    
    /**
     * 输出初始聚类状态
     */
    private void logInitialClusterState(List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        log.info("=== 初始聚类状态 ===");
        
        double totalWorkTime = 0.0;
        double minWorkTime = Double.MAX_VALUE;
        double maxWorkTime = 0.0;
        
        for (int i = 0; i < clusters.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
            totalWorkTime += workTime;
            minWorkTime = Math.min(minWorkTime, workTime);
            maxWorkTime = Math.max(maxWorkTime, workTime);
            
            log.info("聚类[{}]: {}个聚集区, {}分钟工作时间", i, cluster.size(), String.format("%.1f", workTime));
        }
        
        double avgWorkTime = totalWorkTime / clusters.size();
        log.info("总体统计: 聚类数={}, 总工作时间={}分钟, 平均={}分钟, 最小={}分钟, 最大={}分钟, 时间差距={}分钟", 
            clusters.size(), String.format("%.1f", totalWorkTime), String.format("%.1f", avgWorkTime),
            String.format("%.1f", minWorkTime), String.format("%.1f", maxWorkTime), String.format("%.1f", maxWorkTime - minWorkTime));
    }
    
    /**
     * 输出详细的时间分布情况
     */
    private void logDetailedTimeDistribution(List<ClusterTimeAnalysis> analysis, double targetWorkTime) {
        log.info("=== 详细时间分布 ===");
        log.info("目标工作时间: {}分钟", String.format("%.1f", targetWorkTime));
        
        // 按工作时间排序显示
        List<ClusterTimeAnalysis> sortedByTime = new ArrayList<>(analysis);
        sortedByTime.sort((a, b) -> Double.compare(a.workTime, b.workTime));
        
        for (ClusterTimeAnalysis item : sortedByTime) {
            String status;
            if (item.workTime < calculateMergeThreshold()) {
                status = "过小";
            } else if (item.workTime > calculateSplitThreshold()) {
                status = "过大";
            } else {
                status = "正常";
            }
            
            log.info("聚类[{}]: {}分钟 (偏差{}{}, {}个点) - {}", 
                item.index, String.format("%.1f", item.workTime), 
                item.deviation >= 0 ? "+" : "", String.format("%.1f", item.deviation),
                item.cluster.size(), status);
        }
        
        // 统计各类聚类数量
        double mergeThreshold = calculateMergeThreshold();
        double splitThreshold = calculateSplitThreshold();
        long smallCount = analysis.stream().filter(a -> a.workTime < mergeThreshold).count();
        long largeCount = analysis.stream().filter(a -> a.workTime > splitThreshold).count();
        long normalCount = analysis.size() - smallCount - largeCount;
        
        log.info("聚类分布: 过小(<{})={}, 正常({}-{})={}, 过大(>{})={}", 
            String.format("%.0f", mergeThreshold), smallCount,
            String.format("%.0f", mergeThreshold), String.format("%.0f", splitThreshold), normalCount,
            String.format("%.0f", splitThreshold), largeCount);
    }
    
    /**
     * 记录阈值判断决策过程
     */
    private void logThresholdDecisionProcess(List<ClusterTimeAnalysis> analysis, double targetWorkTime) {
        log.info("=== 阈值决策过程分析 ===");
        
        double mergeThreshold = calculateMergeThreshold();  // 配置的合并阈值
        double splitThreshold = calculateSplitThreshold();  // 配置的拆分阈值
        
        log.info("理想时间: {}分钟", String.format("%.1f", IDEAL_CLUSTER_WORK_TIME));
        log.info("合并阈值(85%): {}分钟 - 小于此值将被合并", String.format("%.1f", mergeThreshold));
        log.info("拆分阈值(115%): {}分钟 - 大于此值将被拆分", String.format("%.1f", splitThreshold));
        log.info("安全区间: {}分钟 - {}分钟", String.format("%.1f", mergeThreshold), String.format("%.1f", splitThreshold));
        
        // 统计各区间的聚类数量和具体情况
        long mergeCandidates = analysis.stream().filter(a -> a.workTime < mergeThreshold && a.cluster.size() > 0).count();
        long splitCandidates = analysis.stream().filter(a -> a.workTime > splitThreshold && a.cluster.size() >= 4).count();
        long safeZoneClusters = analysis.stream().filter(a -> a.workTime >= mergeThreshold && a.workTime <= splitThreshold).count();
        
        log.info("阈值决策统计:");
        log.info("  - 需要合并: {}个聚类 (<{}分钟)", mergeCandidates, String.format("%.1f", mergeThreshold));
        log.info("  - 需要拆分: {}个聚类 (>{}分钟且≥ 4个点)", splitCandidates, String.format("%.1f", splitThreshold));
        log.info("  - 安全区间: {}个聚类 (不会被处理)", safeZoneClusters);
        
        // 特别标记在安全区间但不平衡的聚类
        List<ClusterTimeAnalysis> problemClusters = analysis.stream()
            .filter(a -> a.workTime >= mergeThreshold && a.workTime <= splitThreshold)
            .filter(a -> Math.abs(a.deviation) > TIME_BALANCE_THRESHOLD) // 偏差超过平衡阈值
            .collect(Collectors.toList());
            
        if (!problemClusters.isEmpty()) {
            log.warn("发现问题聚类（在安全区间内但不平衡）:");
            for (ClusterTimeAnalysis cluster : problemClusters) {
                log.warn("  - 聚类[{}]: {}分钟 (偏差{}{}分钟) - 被阈值政策忽略", 
                    cluster.index, String.format("%.1f", cluster.workTime), 
                    cluster.deviation >= 0 ? "+" : "", String.format("%.1f", cluster.deviation));
            }
            log.warn("建议：调整阈值以处理这些不平衡的聚类");
        }
    }
    
    /**
     * 迭代聚类数计算策略
     * 基于实际聚类结果的平均工作时间反馈调整聚类数，直到收敛到300-400分钟目标区间
     */
    private int iterativeClusterCountCalculation(TransitDepot depot, List<Accumulation> accumulations, Map<String, TimeInfo> timeMatrix) {
        log.info("=== 迭代聚类数计算策略 ===");
        log.info("中转站: {}", depot.getTransitDepotName());
        log.info("聚集区数量: {}个", accumulations.size());
        
        // 简单初始估算：基于总卸货时间
        double totalDeliveryTime = accumulations.stream()
            .mapToDouble(Accumulation::getDeliveryTime)
            .sum();
        
        // 初始聚类数：假设卸货时间占总工作时间的70%，目标350分钟
        int currentClusterCount = Math.max(1, (int) Math.round(totalDeliveryTime / (350.0 * 0.7)));
        currentClusterCount = Math.min(currentClusterCount, accumulations.size() / 2); // 不超过一半聚集区数
        
        log.info("基于卸货时间初始估算聚类数: {}个", currentClusterCount);
        
        int iteration = 0;
        int maxIterations = 15; // 最多迭代15次
        
        while (iteration < maxIterations) {
            iteration++;
            log.info("=== 迭代 {} ===", iteration);
            
            // 执行实际聚类并计算平均工作时间
            List<List<Accumulation>> trialClusters = performTrialClustering(accumulations, currentClusterCount, depot, timeMatrix);
            
            // 计算实际平均工作时间
            double totalWorkTime = 0.0;
            for (List<Accumulation> cluster : trialClusters) {
                totalWorkTime += calculateClusterWorkTime(cluster, depot, timeMatrix);
            }
            double averageWorkTime = totalWorkTime / trialClusters.size();
            
            log.info("聚类数={}, 总工作时间={}分钟, 平均={}分钟", 
                currentClusterCount, 
                String.format("%.1f", totalWorkTime), 
                String.format("%.1f", averageWorkTime));
            
            // 检查是否收敛到目标区间
            if (averageWorkTime >= MIN_CLUSTER_WORK_TIME && averageWorkTime <= MAX_CLUSTER_WORK_TIME) {
                log.info("收敛成功! 平均工作时间{}分钟在目标区间[{}-{}]分钟内", 
                    String.format("%.1f", averageWorkTime), MIN_CLUSTER_WORK_TIME, MAX_CLUSTER_WORK_TIME);
                return currentClusterCount;
            }
            
            // 根据平均时间调整聚类数
            int newClusterCount;
            if (averageWorkTime < MIN_CLUSTER_WORK_TIME) {
                // 平均时间太小，需要减少聚类数（合并路线）
                newClusterCount = Math.max(1, currentClusterCount - 1);
                log.info("平均时间偏小，减少聚类数: {} -> {}", currentClusterCount, newClusterCount);
            } else {
                // 平均时间太大，需要增加聚类数（拆分路线）
                newClusterCount = Math.min(accumulations.size() / 2, currentClusterCount + 1);
                log.info("平均时间偏大，增加聚类数: {} -> {}", currentClusterCount, newClusterCount);
            }
            
            // 防止无意义的调整
            if (newClusterCount == currentClusterCount) {
                log.info("聚类数无法进一步调整，迭代终止");
                break;
            }
            
            currentClusterCount = newClusterCount;
        }
        
        log.info("迭代完成，最终聚类数: {}个", currentClusterCount);
        return currentClusterCount;
    }
    
    /**
     * 执行试探性聚类以评估特定聚类数的效果
     */
    private List<List<Accumulation>> performTrialClustering(List<Accumulation> accumulations, int k, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (accumulations.size() <= k) {
            return createSingletonClusters(accumulations);
        }
        
        // 执行纯地理聚类（简化版，用于快速评估）
        List<List<Accumulation>> clusters = pureGeographicClustering(accumulations, k);
        
        // 不执行复杂的优化，直接返回基础聚类结果用于时间评估
        return clusters;
    }

    /**
     * 根据业务逻辑计算最优聚类数量
     * 基于总工作时间和目标时间范围计算合理的聚类数
     */
    private int calculateOptimalClusterCount(TransitDepot depot, List<Accumulation> accumulations, Map<String, TimeInfo> timeMatrix) {
        // 使用新的迭代聚类数计算策略
        int optimalClusters = iterativeClusterCountCalculation(depot, accumulations, timeMatrix);
        
        log.info("迭代策略计算最优聚类数: {}个", optimalClusters);
        log.info("替代硬编码聚类数: {}个", depot.getRouteCount());
        
        return optimalClusters;
    }
    
    /**
     * 估算总行驶时间（完整版）
     * 包含装载旷间、往返旷间、聚类内部行驶旷间的完整估算
     * 采用迭代求解策略处理聚类数与总工作旷间的相互依赖
     */
    private double estimateTotalTravelTime(TransitDepot depot, List<Accumulation> accumulations, Map<String, TimeInfo> timeMatrix) {
        debugLog("=== 完整工作旷间估算 ===");
        
        double totalDeliveryTime = accumulations.stream()
            .mapToDouble(Accumulation::getDeliveryTime)
            .sum();
        
        // 迭代求解：从初始估算开始，逐步收敛到精确值
        int previousClusterCount = -1;
        int currentClusterCount = (int) Math.ceil(totalDeliveryTime / (IDEAL_CLUSTER_WORK_TIME * 0.5)); // 初始估算，假设卸货占50%
        currentClusterCount = Math.max(1, Math.min(currentClusterCount, accumulations.size() / 2));
        
        // 一次性计算所有必要的基础数据
        double averageRoundTripTime = calculateAverageRoundTripTime(depot, accumulations, timeMatrix);
        double averageIntraClusterDistance = estimateAverageIntraClusterDistance(accumulations);
        
        int iteration = 0;
        while (previousClusterCount != currentClusterCount && iteration < 10) { // 最多迭代10次
            previousClusterCount = currentClusterCount;
            iteration++;
            
            // 计算各部分旷间
            double totalLoadingTime = currentClusterCount * AlgorithmParameters.LOADING_TIME_MINUTES;
            double totalRoundTripTime = currentClusterCount * averageRoundTripTime;
            double totalInternalTime = estimateClusterInternalTime(accumulations.size(), currentClusterCount, averageIntraClusterDistance);
            
            // 总工作旷间
            double totalWorkTime = totalDeliveryTime + totalLoadingTime + totalRoundTripTime + totalInternalTime;
            
            // 基于完整工作旷间重新计算聚类数
            int newClusterCount = calculateOptimalClusterCountFromTotalTime(totalWorkTime);
            newClusterCount = Math.max(1, Math.min(newClusterCount, accumulations.size() / 2));
            
            debugLog("迭代{}: 聚类数={}->{}, 总工作旷间={}分钟", 
                iteration, currentClusterCount, newClusterCount, String.format("%.1f", totalWorkTime));
            debugLog("  装载旷间={}分钟, 往返旷间={}分钟, 内部旷间={}分钟", 
                String.format("%.1f", totalLoadingTime), 
                String.format("%.1f", totalRoundTripTime), 
                String.format("%.1f", totalInternalTime));
            
            currentClusterCount = newClusterCount;
        }
        
        // 计算最终的行驶旷间（不包含卸货旷间）
        double finalTotalTravelTime = currentClusterCount * (AlgorithmParameters.LOADING_TIME_MINUTES + averageRoundTripTime) +
                                    estimateClusterInternalTime(accumulations.size(), currentClusterCount, averageIntraClusterDistance);
        
        debugLog("最终结果: 聚类数={}个, 总行驶旷间={}分钟 (含装载旷间)", 
            currentClusterCount, String.format("%.1f", finalTotalTravelTime));
        
        return finalTotalTravelTime;
    }
    
    /**
     * 计算平均往返旷间
     */
    private double calculateAverageRoundTripTime(TransitDepot depot, List<Accumulation> accumulations, Map<String, TimeInfo> timeMatrix) {
        double totalSingleTripTime = 0.0;
        int validTimeEntries = 0;
        
        for (Accumulation acc : accumulations) {
            // 使用坐标格式的时间矩阵键
            String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                depot.getLongitude(), depot.getLatitude(),
                acc.getLongitude(), acc.getLatitude());

            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null) {
                totalSingleTripTime += timeInfo.getTravelTime();
                validTimeEntries++;
            } else {
                // 备用计算：基于地理距离估算
                double distance = calculateDistance(
                    depot.getLongitude(), depot.getLatitude(),
                    acc.getLongitude(), acc.getLatitude());
                totalSingleTripTime += distance;
                validTimeEntries++;
            }
        }
        
        return (validTimeEntries > 0) ? (totalSingleTripTime / validTimeEntries) * 2 : 40.0;
    }
    
    /**
     * 估算平均聚类内距离（模拟 calculateAverageIntraClusterDistance 的计算）
     */
    private double estimateAverageIntraClusterDistance(List<Accumulation> accumulations) {
        if (accumulations.size() <= 1) {
            return 0.0;
        }
        
        // 采样计算，避免全量计算的性能问题
        double totalDistance = 0.0;
        int pairCount = 0;
        int maxSamples = Math.min(20, accumulations.size()); // 限制采样数量
        
        for (int i = 0; i < maxSamples; i++) {
            for (int j = i + 1; j < maxSamples; j++) {
                double distance = calculateDistance(
                        accumulations.get(i).getLongitude(), accumulations.get(i).getLatitude(),
                        accumulations.get(j).getLongitude(), accumulations.get(j).getLatitude());
                totalDistance += distance;
                pairCount++;
            }
        }
        
        return pairCount > 0 ? totalDistance / pairCount : 5.0; // 默认5公里
    }
    
    /**
     * 估算聚类内部旷间（正确模拟 calculateClusterWorkTime 中的计算逻辑）
     * 修复：确保单位一致性，公里转换为分钟
     */
    private double estimateClusterInternalTime(int totalAccumulations, int clusterCount, double averageIntraClusterDistance) {
        if (clusterCount <= 0 || totalAccumulations <= clusterCount) {
            return 0.0; // 每个聚类只有一个点，无内部行驶
        }
        
        double avgClusterSize = (double) totalAccumulations / clusterCount;
        
        // 正确模拟 calculateClusterWorkTime 中的计算：
        // internalTravelTime = calculateAverageIntraClusterDistance(cluster) * cluster.size() * 1.5
        // 注意：1.5系数是“每公里1.5分钟”的意思
        double avgInternalTimePerCluster = 0.0;
        if (avgClusterSize > 1) {
            // 修复：从公里转换为分钟
            // 原始公式：distance(公里) * size * 1.5 = 公里 * 点数 * 1.5分钟/公里 = 分钟
            // 但这个公式可能过于简化，使用更合理的估算：
            // 聚类内部时间 ≈ 平均距离 * 点数 * 0.8 * 1.5分钟/公里
            avgInternalTimePerCluster = averageIntraClusterDistance * avgClusterSize * 0.8 * 1.5;
        }
        
        double totalInternalTime = clusterCount * avgInternalTimePerCluster;
        
        debugLog("聚类内部旷间估算: 平均距离={}km, 平均大小={}, 每聚类内部旷间={}min, 总内部旷间={}min",
            String.format("%.1f", averageIntraClusterDistance),
            String.format("%.1f", avgClusterSize),
            String.format("%.1f", avgInternalTimePerCluster),
            String.format("%.1f", totalInternalTime));
        
        return totalInternalTime;
    }
    
    /**
     * 基于总工作旷间计算最优聚类数
     */
    private int calculateOptimalClusterCountFromTotalTime(double totalWorkTime) {
        int minClusters = (int) Math.ceil(totalWorkTime / MAX_CLUSTER_WORK_TIME);
        int maxClusters = (int) Math.floor(totalWorkTime / MIN_CLUSTER_WORK_TIME);
        
        if (maxClusters < minClusters) {
            maxClusters = minClusters;
        }
        
        return (minClusters + maxClusters) / 2;
    }
    
    /**
     * 自然扩散时间平衡优化（修复指向性转移缺陷）
     * 
     * 实现基于邻近关系的自然扩散机制，替代复杂的指向性转移策略
     * 核心理念：高负载聚类向任意低负载邻居转移，逐步实现均衡
     * 关键改进：移除预设转移对限制，允许就近转移
     * 
     * 预期效果：解决100分钟vs400分钟的极端不均衡问题
     */
    private boolean optimizeTimeBalanceByVariance(
            List<List<Accumulation>> clusters, 
            TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("=== 开始自然扩散时间平衡优化 ===");
        log.info("当前聚类数: {}", clusters.size());
        
        if (clusters.size() < 2) {
            log.info("聚类数量不足，跳过时间平衡优化");
            return false;
        }
        
        boolean hasTransfers = false;
        
        // 计算全局平均工作时间
        double totalWorkTime = 0.0;
        int validClusterCount = 0;
        for (List<Accumulation> cluster : clusters) {
            if (!cluster.isEmpty()) {
                totalWorkTime += calculateClusterWorkTime(cluster, depot, timeMatrix);
                validClusterCount++;
            }
        }
        double globalAverage = totalWorkTime / validClusterCount;
        
        log.info("全局平均工作时间: {}分钟，有效聚类数: {}", 
            String.format("%.1f", globalAverage), validClusterCount);
        
        // 自然扩散迭代优化（参数自适应机制）
        int maxIterations = 20;
        double baseBalanceThreshold = 30.0;  // 基础偏差阈值
        double baseNearbyThreshold = 20.0;   // 基础就近阈值（分钟差）
        
        // 计算初始不均衡比例用于参数自适应
        double initialImbalanceRatio = calculateImbalanceRatio(clusters, depot, timeMatrix, globalAverage);
        log.info("初始不均衡比例: {}, 将应用参数自适应机制", String.format("%.3f", initialImbalanceRatio));
        
        for (int iteration = 0; iteration < maxIterations; iteration++) {
            debugLog("自然扩散第{}轮迭代（参数自适应）", iteration + 1);
            
            // 动态收敛阈值：θ = base_threshold * (1 - iteration/max_iter)
            double dynamicBalanceThreshold = baseBalanceThreshold * (1.0 - (double)iteration / maxIterations);
            
            // 动态邻近阈值：基于不均衡程度调整
            double dynamicNearbyThreshold = baseNearbyThreshold * (1.0 + initialImbalanceRatio);
            
            debugLog("动态阈值: 收敛阈值={}分钟, 邻近阈值={}分钟", 
                String.format("%.1f", dynamicBalanceThreshold),
                String.format("%.1f", dynamicNearbyThreshold));
            
            boolean roundHasTransfer = false;
            
            // 找出所有高负载聚类（使用动态阈值）
            List<Integer> highLoadClusters = new ArrayList<>();
            for (int i = 0; i < clusters.size(); i++) {
                List<Accumulation> cluster = clusters.get(i);
                if (!cluster.isEmpty()) {
                    double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
                    if (workTime > globalAverage + dynamicBalanceThreshold) {
                        highLoadClusters.add(i);
                    }
                }
            }
            
            if (highLoadClusters.isEmpty()) {
                log.info("没有高负载聚类，自然扩散优化完成");
                break;
            }
            
            // 对每个高负载聚类尝试向邻近的低负载聚类转移一个点
            for (int sourceIndex : highLoadClusters) {
                List<Accumulation> sourceCluster = clusters.get(sourceIndex);
                int originalSourceSize = sourceCluster.size(); // 记录原始聚类大小
                double sourceWorkTime = calculateClusterWorkTime(sourceCluster, depot, timeMatrix);
                
                debugLog("处理高负载聚类[{}]: {}分钟 ({} 个点)", 
                    sourceIndex, String.format("%.1f", sourceWorkTime), sourceCluster.size());
                
                // 查找所有可能的转移目标（工作时间低于源聚类的邻近聚类）
                List<TransferCandidate> transferTargets = new ArrayList<>();
                
                for (int targetIndex = 0; targetIndex < clusters.size(); targetIndex++) {
                    if (targetIndex == sourceIndex) continue;
                    
                    List<Accumulation> targetCluster = clusters.get(targetIndex);
                    if (targetCluster.isEmpty()) continue;
                    
                    double targetWorkTime = calculateClusterWorkTime(targetCluster, depot, timeMatrix);
                    
                    // 使用动态邻近阈值判断转移目标
                    if (targetWorkTime < sourceWorkTime - dynamicNearbyThreshold) {
                        // 计算两个聚类中心的距离作为邻近度指标
                        double distance = calculateClusterToClusterDistance(sourceCluster, targetCluster);
                        
                        transferTargets.add(new TransferCandidate(targetIndex, targetCluster, targetWorkTime, distance));
                    }
                }
                
                if (transferTargets.isEmpty()) {
                    debugLog("聚类[{}]没有找到合适的转移目标", sourceIndex);
                    continue;
                }
                
                // 按距离排序，优先选择最近的目标
                transferTargets.sort(Comparator.comparingDouble(t -> t.distance));
                
                // 尝试向最近的几个目标转移
                boolean clusterTransferred = false;
                for (TransferCandidate target : transferTargets.subList(0, Math.min(3, transferTargets.size()))) {
                    
                    // 选择最适合转移的点（支持阶段性策略）
                    Accumulation bestPoint = selectBestTransferPoint(sourceCluster, target.cluster, iteration, maxIterations);
                    
                    if (bestPoint != null) {
                        // 地理约束检查
                        if (canTransferWithoutConvexHullConflict(bestPoint, target.cluster, depot)) {
                            // 执行转移
                            sourceCluster.remove(bestPoint);
                            target.cluster.add(bestPoint);
                            
                            double pointWorkTime = calculateAccumulationWorkTime(bestPoint, depot);
                            
                            log.info("自然扩散转移成功: {} 从聚类[{}] → 聚类[{}], 点工作时间: {}分钟, 距离: {}公里",
                                bestPoint.getAccumulationName(), 
                                sourceIndex, target.index,
                                String.format("%.1f", pointWorkTime),
                                String.format("%.1f", target.distance));
                            
                            hasTransfers = true;
                            roundHasTransfer = true;
                            clusterTransferred = true;
                            break;
                        } else {
                            debugLog("地理约束阻止转移: {} 从聚类[{}] → 聚类[{}]", 
                                bestPoint.getAccumulationName(), sourceIndex, target.index);
                        }
                    }
                }
                
                if (clusterTransferred) {
                    // 根据不均衡程度决定是否允许同聚类继续转移
                    double currentWorkTime = calculateClusterWorkTime(sourceCluster, depot, timeMatrix);
                    double imbalanceRatio = currentWorkTime / globalAverage;
                    
                    // 极端不均衡时允许多点转移（超过1.8倍且绝对差异>80分钟）
                    if (imbalanceRatio > 1.8 && (currentWorkTime - globalAverage) > 80.0) {
                        int transferredCount = sourceCluster.size() < originalSourceSize ? 
                            originalSourceSize - sourceCluster.size() : 0;
                        int maxTransferPoints = Math.min(3, (int) Math.ceil((currentWorkTime - globalAverage) / 50));
                        
                        if (transferredCount < maxTransferPoints) {
                            debugLog("极端不均衡，允许聚类[{}]继续转移 (已转移{}/{})，当前工作时间{:.1f}分钟", 
                                sourceIndex, transferredCount, maxTransferPoints, currentWorkTime);
                            clusterTransferred = false; // 允许继续转移
                        } else {
                            break; // 达到最大转移数，处理下一个聚类
                        }
                    } else {
                        break; // 常规情况下每轮只处理一个聚类的一次转移
                    }
                }
            }
            
            if (!roundHasTransfer) {
                log.info("本轮没有成功转移，自然扩散优化结束");
                break;
            }
            
            // 重新计算平均值用于下一轮
            totalWorkTime = 0.0;
            validClusterCount = 0;
            for (List<Accumulation> cluster : clusters) {
                if (!cluster.isEmpty()) {
                    totalWorkTime += calculateClusterWorkTime(cluster, depot, timeMatrix);
                    validClusterCount++;
                }
            }
            globalAverage = totalWorkTime / validClusterCount;
        }
        
        // 离群点检测和处理（异常处理阶段）
        log.info("=== 开始离群点检测和处理 ===");
        boolean outlierProcessed = detectAndProcessOutliers(clusters, depot, timeMatrix);
        if (outlierProcessed) {
            hasTransfers = true;
            log.info("离群点处理完成，发现并处理了离群点");
        } else {
            log.info("离群点检测完成，未发现需要处理的离群点");
        }
        
        // 全局再平衡机制（极端不均衡处理）
        log.info("=== 开始全局再平衡检查 ===");
        boolean globalRebalanced = performGlobalRebalance(clusters, depot, timeMatrix);
        if (globalRebalanced) {
            hasTransfers = true;
            log.info("全局再平衡完成，处理了极端不均衡情况");
        } else {
            log.info("全局再平衡检查完成，未发现需要处理的极端不均衡");
        }
        
        log.info("自然扩散优化完成，是否有转移: {}", hasTransfers);
        return hasTransfers;
    }
    
    /**
     * 转移候选目标数据结构
     */
    private static class TransferCandidate {
        int index;
        List<Accumulation> cluster;
        double workTime;
        double distance;
        
        public TransferCandidate(int index, List<Accumulation> cluster, double workTime, double distance) {
            this.index = index;
            this.cluster = cluster;
            this.workTime = workTime;
            this.distance = distance;
        }
    }
    
    /**
     * 选择最适合转移的点（支持阶段性策略的参数自适应机制）
     * 
     * 根据迭代进度实施不同的转移点选择策略：
     * - 初期（0-33%）：选择边缘点，保持地理聚集
     * - 中期（33-66%）：选择中等距离点
     * - 后期（66-100%）：允许任意点，优先均衡
     */
    private Accumulation selectBestTransferPoint(List<Accumulation> sourceCluster, List<Accumulation> targetCluster, 
                                               int currentIteration, int maxIterations) {
        if (sourceCluster.isEmpty() || targetCluster.isEmpty()) {
            return null;
        }
        
        // 计算目标聚类的中心点
        double targetCenterLat = targetCluster.stream()
            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double targetCenterLon = targetCluster.stream()
            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        // 计算迭代进度比例
        double iterationProgress = (double) currentIteration / maxIterations;
        
        if (iterationProgress <= 0.33) {
            // 初期策略：选择边缘点（最靠近目标的点）
            return selectEdgePoint(sourceCluster, targetCenterLat, targetCenterLon);
        } else if (iterationProgress <= 0.66) {
            // 中期策略：选择中等距离点
            return selectMediumDistancePoint(sourceCluster, targetCenterLat, targetCenterLon);
        } else {
            // 后期策略：优先均衡，允许任意点转移
            return selectBalancePriorityPoint(sourceCluster, targetCenterLat, targetCenterLon);
        }
    }
    
    /**
     * 初期策略：选择边缘点（保持地理聚集）
     */
    private Accumulation selectEdgePoint(List<Accumulation> sourceCluster, double targetCenterLat, double targetCenterLon) {
        Accumulation bestPoint = null;
        double minDistance = Double.MAX_VALUE;
        
        for (Accumulation point : sourceCluster) {
            double distance = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                targetCenterLat, targetCenterLon);
            
            if (distance < minDistance) {
                minDistance = distance;
                bestPoint = point;
            }
        }
        
        return bestPoint;
    }
    
    /**
     * 中期策略：选择中等距离点
     */
    private Accumulation selectMediumDistancePoint(List<Accumulation> sourceCluster, double targetCenterLat, double targetCenterLon) {
        List<Accumulation> sortedByDistance = new ArrayList<>(sourceCluster);
        sortedByDistance.sort((a, b) -> {
            double distA = calculateDistance(a.getLatitude(), a.getLongitude(), targetCenterLat, targetCenterLon);
            double distB = calculateDistance(b.getLatitude(), b.getLongitude(), targetCenterLat, targetCenterLon);
            return Double.compare(distA, distB);
        });
        
        // 选择中位数位置的点（中等距离）
        int medianIndex = sortedByDistance.size() / 2;
        return sortedByDistance.get(medianIndex);
    }
    
    /**
     * 后期策略：优先均衡，选择工作量较大的点
     */
    private Accumulation selectBalancePriorityPoint(List<Accumulation> sourceCluster, double targetCenterLat, double targetCenterLon) {
        // 后期优先选择工作量较大的点来实现更好的均衡效果
        Accumulation bestPoint = null;
        double maxWorkTime = 0.0;
        
        for (Accumulation point : sourceCluster) {
            // 估算点的工作时间（配送时间）
            double pointWorkTime = point.getDeliveryTime() != null ? point.getDeliveryTime() : 15.0;
            
            if (pointWorkTime > maxWorkTime) {
                maxWorkTime = pointWorkTime;
                bestPoint = point;
            }
        }
        
        // 如果所有点工作时间相同，则选择最靠近目标的点
        if (bestPoint == null) {
            return selectEdgePoint(sourceCluster, targetCenterLat, targetCenterLon);
        }
        
        return bestPoint;
    }
    
    /**
     * 计算不均衡比例（用于参数自适应）
     * 
     * 基于标准差与平均值的比值来量化不均衡程度
     * 不均衡比例越高，参数调整幅度越大
     */
    private double calculateImbalanceRatio(List<List<Accumulation>> clusters, TransitDepot depot, 
                                         Map<String, TimeInfo> timeMatrix, double globalAverage) {
        List<Double> workTimes = new ArrayList<>();
        
        for (List<Accumulation> cluster : clusters) {
            if (!cluster.isEmpty()) {
                double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
                workTimes.add(workTime);
            }
        }
        
        if (workTimes.size() < 2) {
            return 0.0; // 聚类数量不足，无法计算不均衡比例
        }
        
        // 计算标准差
        double sumSquaredDiff = 0.0;
        for (double workTime : workTimes) {
            double diff = workTime - globalAverage;
            sumSquaredDiff += diff * diff;
        }
        
        double variance = sumSquaredDiff / workTimes.size();
        double standardDeviation = Math.sqrt(variance);
        
        // 不均衡比例 = 标准差 / 平均值
        // 这个比例反映了数据的相对离散程度
        double imbalanceRatio = standardDeviation / globalAverage;
        
        return Math.min(imbalanceRatio, 1.0); // 限制最大值为1.0
    }
    
    /**
     * 检测和处理离群点（异常处理机制）
     * 
     * 使用统计方法（2σ原则）检测地理离群点并尝试重分配
     * 
     * @param clusters 聚类集合
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 是否处理了离群点
     */
    private boolean detectAndProcessOutliers(List<List<Accumulation>> clusters, TransitDepot depot, 
                                           Map<String, TimeInfo> timeMatrix) {
        boolean hasProcessedOutliers = false;
        
        for (int clusterIndex = 0; clusterIndex < clusters.size(); clusterIndex++) {
            List<Accumulation> cluster = clusters.get(clusterIndex);
            if (cluster.size() <= 2) {
                continue; // 小聚类跳过离群点检测
            }
            
            // 计算聚类中心
            double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double centerLon = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            
            // 计算所有点到中心的距离
            List<Double> distances = new ArrayList<>();
            for (Accumulation point : cluster) {
                double distance = calculateDistance(point.getLatitude(), point.getLongitude(), centerLat, centerLon);
                distances.add(distance);
            }
            
            // 计算距离的均值和标准差
            double meanDistance = distances.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double variance = distances.stream()
                .mapToDouble(d -> Math.pow(d - meanDistance, 2))
                .average().orElse(0.0);
            double stdDeviation = Math.sqrt(variance);
            
            // 自适应离群点阈值策略
            double sigmaMult = 2.0;  // 基础σ倍数
            double baseMinThreshold = 10.0; // 基础最小阈值10公里
            
            // 根据聚类大小调整σ倍数：大聚类更严格，小聚类更宽松
            if (cluster.size() >= 15) {
                sigmaMult = 1.8; // 大聚类更严格检测
            } else if (cluster.size() <= 5) {
                sigmaMult = 2.5; // 小聚类更宽松
            }
            
            // 根据聚类分散程度调整最小阈值
            double adaptiveMinThreshold = baseMinThreshold;
            if (meanDistance > 8.0) {
                adaptiveMinThreshold = Math.max(baseMinThreshold, meanDistance * 0.8); // 分散聚类提高阈值
            } else if (meanDistance < 3.0) {
                adaptiveMinThreshold = Math.max(6.0, meanDistance * 1.5); // 紧密聚类降低阈值
            }
            
            // 计算最终阈值
            double statisticalThreshold = meanDistance + sigmaMult * stdDeviation;
            double outlierThreshold = Math.max(statisticalThreshold, adaptiveMinThreshold);
            
            debugLog("聚类[{}]离群点检测: 平均距离={:.1f}km, 标准差={:.1f}km, σ倍数={:.1f}, 统计阈值={:.1f}km, 自适应阈值={:.1f}km", 
                clusterIndex, meanDistance, stdDeviation, sigmaMult, statisticalThreshold, outlierThreshold);
            
            // 查找离群点
            List<Accumulation> outliers = new ArrayList<>();
            for (int i = 0; i < cluster.size(); i++) {
                Accumulation point = cluster.get(i);
                double distance = distances.get(i);
                
                if (distance > outlierThreshold) {
                    outliers.add(point);
                    log.info("发现离群点: {} 距离聚类中心{:.1f}km (阈值{:.1f}km, 聚类大小{})", 
                        point.getAccumulationName(), distance, outlierThreshold, cluster.size());
                }
            }
            
            // 处理离群点
            for (Accumulation outlier : outliers) {
                boolean relocated = relocateOutlier(outlier, clusters, clusterIndex, depot, timeMatrix);
                if (relocated) {
                    cluster.remove(outlier);
                    hasProcessedOutliers = true;
                    log.info("离群点重分配成功: {}", outlier.getAccumulationName());
                } else {
                    log.warn("离群点无法重分配，保持现状: {}", outlier.getAccumulationName());
                }
            }
        }
        
        return hasProcessedOutliers;
    }
    
    /**
     * 重分配离群点到最合适的聚类
     * 
     * @param outlier 离群点
     * @param clusters 所有聚类
     * @param sourceClusterIndex 原聚类索引
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 是否成功重分配
     */
    private boolean relocateOutlier(Accumulation outlier, List<List<Accumulation>> clusters, 
                                  int sourceClusterIndex, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        List<OutlierRelocationCandidate> candidates = new ArrayList<>();
        
        // 查找所有可能的重分配目标
        for (int i = 0; i < clusters.size(); i++) {
            if (i == sourceClusterIndex) continue;
            
            List<Accumulation> targetCluster = clusters.get(i);
            if (targetCluster.isEmpty()) continue;
            
            // 计算到目标聚类中心的距离
            double targetCenterLat = targetCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double targetCenterLon = targetCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            double distanceToTarget = calculateDistance(outlier.getLatitude(), outlier.getLongitude(), 
                                                      targetCenterLat, targetCenterLon);
            
            // 计算重分配后的工作时间变化
            double currentTargetTime = calculateClusterWorkTime(targetCluster, depot, timeMatrix);
            double outlierWorkTime = calculateAccumulationWorkTime(outlier, depot);
            double newTargetTime = currentTargetTime + outlierWorkTime;
            
            // 检查是否在合理范围内（不超过600分钟）
            if (newTargetTime <= 600.0 && distanceToTarget <= 50.0) { // 50公里距离限制
                candidates.add(new OutlierRelocationCandidate(i, targetCluster, distanceToTarget, newTargetTime));
            }
        }
        
        if (candidates.isEmpty()) {
            return false; // 没有合适的重分配目标
        }
        
        // 选择最近的候选目标
        candidates.sort(Comparator.comparingDouble(c -> c.distance));
        OutlierRelocationCandidate bestCandidate = candidates.get(0);
        
        // 地理约束检查
        if (canTransferWithoutConvexHullConflict(outlier, bestCandidate.cluster, depot)) {
            bestCandidate.cluster.add(outlier);
            log.info("离群点重分配: {} 转移到聚类[{}], 距离={:.1f}km", 
                outlier.getAccumulationName(), bestCandidate.clusterIndex, bestCandidate.distance);
            return true;
        }
        
        return false;
    }
    
    /**
     * 离群点重分配候选数据结构
     */
    private static class OutlierRelocationCandidate {
        int clusterIndex;
        List<Accumulation> cluster;
        double distance;
        double newWorkTime;
        
        public OutlierRelocationCandidate(int clusterIndex, List<Accumulation> cluster, 
                                        double distance, double newWorkTime) {
            this.clusterIndex = clusterIndex;
            this.cluster = cluster;
            this.distance = distance;
            this.newWorkTime = newWorkTime;
        }
    }
    
    /**
     * 全局再平衡机制（极端不均衡处理）
     * 
     * 当自然扩散和离群点处理无法解决极端不均衡时的最后手段
     * 允许临时违反地理约束来实现负载均衡
     * 
     * @param clusters 聚类集合
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 是否执行了全局再平衡
     */
    private boolean performGlobalRebalance(List<List<Accumulation>> clusters, TransitDepot depot, 
                                         Map<String, TimeInfo> timeMatrix) {
        
        // 计算所有聚类的工作时间
        List<ClusterWorkTimeInfo> clusterInfos = new ArrayList<>();
        for (int i = 0; i < clusters.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            if (!cluster.isEmpty()) {
                double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
                clusterInfos.add(new ClusterWorkTimeInfo(i, cluster, workTime));
            }
        }
        
        if (clusterInfos.size() < 2) {
            return false; // 聚类数量不足
        }
        
        // 按工作时间排序
        clusterInfos.sort(Comparator.comparingDouble(c -> c.workTime));
        
        ClusterWorkTimeInfo minCluster = clusterInfos.get(0);
        ClusterWorkTimeInfo maxCluster = clusterInfos.get(clusterInfos.size() - 1);
        
        // 智能全局再平衡触发条件
        double ratio = maxCluster.workTime / minCluster.workTime;
        double absoluteDiff = maxCluster.workTime - minCluster.workTime;
        
        // 计算平均工作时间和整体方差
        double avgWorkTime = clusterInfos.stream().mapToDouble(c -> c.workTime).average().orElse(0.0);
        double variance = clusterInfos.stream()
            .mapToDouble(c -> Math.pow(c.workTime - avgWorkTime, 2))
            .average().orElse(0.0);
        double stdDev = Math.sqrt(variance);
        double cvCoefficient = avgWorkTime > 0 ? stdDev / avgWorkTime : 0; // 变异系数
        
        // 动态触发阈值：根据聚类数量和整体分散程度调整
        double ratioThreshold = 2.0;
        double absThreshold = 100.0;
        
        if (clusterInfos.size() >= 6) {
            // 多聚类情况下更宽松（允许一定的不均衡）
            ratioThreshold = 2.2;
            absThreshold = 120.0;
        } else if (clusterInfos.size() <= 3) {
            // 少聚类情况下更严格
            ratioThreshold = 1.8;
            absThreshold = 80.0;
        }
        
        // 根据整体变异系数调整：整体越不均衡，越需要干预
        if (cvCoefficient > 0.3) { // 高变异系数时更积极干预
            ratioThreshold *= 0.9;
            absThreshold *= 0.9;
        } else if (cvCoefficient < 0.15) { // 低变异系数时更保守
            ratioThreshold *= 1.1;
            absThreshold *= 1.1;
        }
        
        debugLog("全局不均衡检查: 最大={:.1f}分钟, 最小={:.1f}分钟, 比例={:.2f}, 差异={:.1f}分钟, 平均={:.1f}分钟, 变异系数={:.3f}, 比例阈值={:.2f}, 差异阈值={:.1f}分钟", 
            maxCluster.workTime, minCluster.workTime, ratio, absoluteDiff,
            avgWorkTime, cvCoefficient, ratioThreshold, absThreshold);
        
        if (ratio <= ratioThreshold || absoluteDiff <= absThreshold) {
            return false; // 不需要全局再平衡
        }
        
        log.info("检测到极端不均衡: 聚类[{}]({:.1f}分钟) vs 聚类[{}]({:.1f}分钟), 比例{:.2f}", 
            maxCluster.clusterIndex, maxCluster.workTime,
            minCluster.clusterIndex, minCluster.workTime, ratio);
        
        // 尝试多跳转移路径
        boolean transferred = attemptMultiHopTransfer(maxCluster, minCluster, clusterInfos, depot, timeMatrix);
        
        if (!transferred) {
            // 如果多跳转移失败，尝试违约转移（允许违反地理约束）
            log.warn("多跳转移失败，尝试违约转移（临时违反地理约束）");
            transferred = attemptConstraintViolationTransfer(maxCluster, minCluster, depot, timeMatrix);
        }
        
        return transferred;
    }
    
    /**
     * 尝试多跳转移路径
     * 
     * 通过中间聚类建立转移链路，避免直接的远距离转移
     */
    private boolean attemptMultiHopTransfer(ClusterWorkTimeInfo maxCluster, ClusterWorkTimeInfo minCluster,
                                          List<ClusterWorkTimeInfo> allClusters, TransitDepot depot, 
                                          Map<String, TimeInfo> timeMatrix) {
        
        // 寻找中间聚类作为跳板
        List<ClusterWorkTimeInfo> intermediates = new ArrayList<>();
        for (ClusterWorkTimeInfo cluster : allClusters) {
            if (cluster != maxCluster && cluster != minCluster) {
                // 工作时间在最大和最小之间，且有容量接受转移
                if (cluster.workTime > minCluster.workTime && cluster.workTime < maxCluster.workTime 
                    && cluster.workTime < 500.0) { // 500分钟以下有转移空间
                    intermediates.add(cluster);
                }
            }
        }
        
        if (intermediates.isEmpty()) {
            debugLog("没有找到合适的中间聚类进行多跳转移");
            return false;
        }
        
        // 按距离最大聚类的远近排序中间聚类
        intermediates.sort((a, b) -> {
            double distA = calculateClusterToClusterDistance(maxCluster.cluster, a.cluster);
            double distB = calculateClusterToClusterDistance(maxCluster.cluster, b.cluster);
            return Double.compare(distA, distB);
        });
        
        // 尝试通过最近的中间聚类进行两跳转移
        ClusterWorkTimeInfo intermediate = intermediates.get(0);
        
        log.info("尝试多跳转移: 聚类[{}] → 聚类[{}] → 聚类[{}]", 
            maxCluster.clusterIndex, intermediate.clusterIndex, minCluster.clusterIndex);
        
        // 第一跳：从最大聚类转移到中间聚类
        Accumulation transferPoint1 = selectBestTransferPoint(maxCluster.cluster, intermediate.cluster, 0, 1);
        if (transferPoint1 != null && 
            canTransferWithoutConvexHullConflict(transferPoint1, intermediate.cluster, depot)) {
            
            maxCluster.cluster.remove(transferPoint1);
            intermediate.cluster.add(transferPoint1);
            
            log.info("多跳转移第一跳成功: {} 从聚类[{}] → 聚类[{}]", 
                transferPoint1.getAccumulationName(), maxCluster.clusterIndex, intermediate.clusterIndex);
            
            // 第二跳：从中间聚类转移到最小聚类
            Accumulation transferPoint2 = selectBestTransferPoint(intermediate.cluster, minCluster.cluster, 0, 1);
            if (transferPoint2 != null && 
                canTransferWithoutConvexHullConflict(transferPoint2, minCluster.cluster, depot)) {
                
                intermediate.cluster.remove(transferPoint2);
                minCluster.cluster.add(transferPoint2);
                
                log.info("多跳转移第二跳成功: {} 从聚类[{}] → 聚类[{}]", 
                    transferPoint2.getAccumulationName(), intermediate.clusterIndex, minCluster.clusterIndex);
                
                return true;
            } else {
                // 第二跳失败，回滚第一跳
                intermediate.cluster.remove(transferPoint1);
                maxCluster.cluster.add(transferPoint1);
                debugLog("多跳转移第二跳失败，已回滚");
            }
        }
        
        return false;
    }
    
    /**
     * 违约转移（允许临时违反地理约束）
     * 
     * 最后手段：为了实现负载均衡，允许转移地理上不太合理的点
     */
    private boolean attemptConstraintViolationTransfer(ClusterWorkTimeInfo maxCluster, ClusterWorkTimeInfo minCluster,
                                                     TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        // 选择工作量最大的点进行转移（优先均衡效果）
        Accumulation bestPoint = null;
        double maxWorkTime = 0.0;
        
        for (Accumulation point : maxCluster.cluster) {
            double pointWorkTime = point.getDeliveryTime() != null ? point.getDeliveryTime() : 15.0;
            if (pointWorkTime > maxWorkTime) {
                maxWorkTime = pointWorkTime;
                bestPoint = point;
            }
        }
        
        if (bestPoint != null) {
            // 计算转移后的工作时间变化
            double maxClusterNewTime = maxCluster.workTime - maxWorkTime;
            double minClusterNewTime = minCluster.workTime + maxWorkTime;
            
            // 检查转移是否真的改善了不均衡
            double currentDiff = maxCluster.workTime - minCluster.workTime;
            double newDiff = Math.abs(maxClusterNewTime - minClusterNewTime);
            
            if (newDiff < currentDiff * 0.8) { // 差异至少减少20%
                maxCluster.cluster.remove(bestPoint);
                minCluster.cluster.add(bestPoint);
                
                log.warn("违约转移执行: {} 从聚类[{}] → 聚类[{}] (违反地理约束)", 
                    bestPoint.getAccumulationName(), maxCluster.clusterIndex, minCluster.clusterIndex);
                log.warn("工作时间变化: {:.1f}→{:.1f}分钟 vs {:.1f}→{:.1f}分钟", 
                    maxCluster.workTime, maxClusterNewTime, 
                    minCluster.workTime, minClusterNewTime);
                
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 聚类工作时间信息数据结构
     */
    private static class ClusterWorkTimeInfo {
        int clusterIndex;
        List<Accumulation> cluster;
        double workTime;
        
        public ClusterWorkTimeInfo(int clusterIndex, List<Accumulation> cluster, double workTime) {
            this.clusterIndex = clusterIndex;
            this.cluster = cluster;
            this.workTime = workTime;
        }
    }
    
    /**
     * 凸包冲突检测（修复缺失功能）
     * 
     * 检测聚类形成的凸包中是否包含其他聚类的点
     * 用于地理聚集校验，确保"凸包内无其他聚类点"的业务要求
     * 
     * 预期效果：路径交叉指数从0.553降至<0.100
     * 
     * @param clusterA 目标聚类
     * @param candidatePoint 候选点（如果为null，则只检查clusterA本身）
     * @param allClusters 所有聚类列表
     * @return true-存在冲突，false-无冲突
     */
    private boolean hasConvexHullConflict(
            List<Accumulation> clusterA, 
            Accumulation candidatePoint,
            List<List<Accumulation>> allClusters) {
        
        if (clusterA == null || allClusters == null) {
            return false;
        }
        
        // 创建合并后的临时聚类
        List<Accumulation> mergedCluster = new ArrayList<>(clusterA);
        if (candidatePoint != null) {
            mergedCluster.add(candidatePoint);
        }
        
        // 如果聚类点数不足3个，无法形成有效凸包
        if (mergedCluster.size() < 3) {
            debugLog("聚类点数不足3个，跳过凸包冲突检测");
            return false;
        }
        
        // 生成合并后聚类的凸包
        List<CoordinatePoint> mergedHull;
        try {
            mergedHull = ConvexHullGenerator.generateConvexHull(mergedCluster);
            
            if (mergedHull == null || mergedHull.size() < 3) {
                debugLog("无法生成有效凸包，跳过冲突检测");
                return false;
            }
        } catch (Exception e) {
            log.warn("生成凸包时发生异常: {}", e.getMessage());
            return false;
        }
        
        // 检查是否包含其他聚类的点
        int conflictCount = 0;
        int totalCheckedPoints = 0;
        
        for (List<Accumulation> otherCluster : allClusters) {
            if (otherCluster == clusterA) {
                continue; // 跳过自己
            }
            
            for (Accumulation point : otherCluster) {
                if (point == candidatePoint) {
                    continue; // 跳过候选点（它即将被添加到clusterA）
                }
                
                totalCheckedPoints++;
                
                // 检查点是否在凸包内
                CoordinatePoint coordinate = point.getCoordinate();
                if (coordinate != null && coordinate.isValid()) {
                    try {
                        if (ConvexHullGenerator.isPointInConvexHull(coordinate, mergedHull)) {
                            conflictCount++;
                        }
                    } catch (Exception e) {
                        log.warn("检查点是否在凸包内时发生异常: {}", e.getMessage());
                    }
                }
            }
        }
        
        boolean hasConflict = conflictCount > 0;
        
        if (hasConflict) {
            debugLog("凸包冲突检测结果: 发现{}个冲突点 (总检查{}个点)", 
                conflictCount, totalCheckedPoints);
        } else {
            debugLog("凸包冲突检测结果: 无冲突 (检查了{}个点)", totalCheckedPoints);
        }
        
        return hasConflict;
    }
    
    /**
     * 聚类合并的地理约束检查（已修改为朴素检测）
     * 
     * 原本使用严格的凸包冲突检测，现已替换为朴素的地理检测
     * 在负载均衡阶段放宽地理约束，优先实现合并操作
     * 
     * @param sourceCluster 源聚类
     * @param targetCluster 目标聚类
     * @param allClusters 所有聚类列表
     * @return true-可以合并，false-地理约束阻止合并
     */
    private boolean canMergeWithoutConvexHullConflict(
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster,
            List<List<Accumulation>> allClusters) {
        
        if (sourceCluster == null || targetCluster == null) {
            return false;
        }
        
        // 第一阶段修改：使用朴素地理检测替代严格凸包检测
        return canMergeWithNaiveGeographicCheck(sourceCluster, targetCluster);
    }

    /**
     * 朴素地理聚集检测（合并操作专用）
     * 
     * 使用距离计算的简化检测，相比凸包检测更加宽松和实用
     * 适合在负载均衡阶段的合并操作使用
     * 
     * @param sourceCluster 源聚类
     * @param targetCluster 目标聚类
     * @return true-可以合并，false-地理约束阻止合并
     */
    private boolean canMergeWithNaiveGeographicCheck(
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster) {
        
        if (sourceCluster == null || targetCluster == null) {
            return false;
        }
        
        // 如果任一聚类为空，允许合并
        if (sourceCluster.isEmpty() || targetCluster.isEmpty()) {
            return true;
        }
        
        // 计算源聚类和目标聚类的中心点
        double sourceCenterLat = sourceCluster.stream()
            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double sourceCenterLon = sourceCluster.stream()
            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        double targetCenterLat = targetCluster.stream()
            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double targetCenterLon = targetCluster.stream()
            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        // 计算两个聚类中心之间的距离
        double centerDistance = calculateDistance(
            sourceCenterLat, sourceCenterLon, 
            targetCenterLat, targetCenterLon);
        
        // 放宽的合并距离阈值：50公里（比转移的35公里更宽松）
        if (centerDistance > 50.0) {
            return false;  // 距离过远，拒绝合并
        }
        
        // 计算合并后聚类的地理分散度
        List<Accumulation> mergedCluster = new ArrayList<>(targetCluster);
        mergedCluster.addAll(sourceCluster);
        
        double mergedCenterLat = mergedCluster.stream()
            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double mergedCenterLon = mergedCluster.stream()
            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        // 计算合并后的平均半径
        double avgRadius = mergedCluster.stream()
            .mapToDouble(point -> calculateDistance(
                point.getLatitude(), point.getLongitude(), 
                mergedCenterLat, mergedCenterLon))
            .average().orElse(0.0);
        
        // 允许适度的地理分散：平均半径不超过30公里
        return avgRadius <= 30.0;
    }
    
    /**
     * 朴素地理聚集检测（负载均衡阶段使用）
     * 
     * 使用距离计算和路径交叉评分的组合检测，相比凸包检测更加宽松和实用
     * 适合在负载均衡阶段使用，允许适度的地理重叠以优先实现时间平衡
     * 
     * @param candidatePoint 候选转移点
     * @param targetCluster 目标聚类
     * @param depot 中转站（用于路径交叉计算）
     * @return true-可以转移，false-地理约束阻止转移
     */
    private boolean canTransferWithNaiveGeographicCheck(
            Accumulation candidatePoint, 
            List<Accumulation> targetCluster,
            TransitDepot depot) {
        
        if (candidatePoint == null || targetCluster == null) {
            return false;
        }
        
        // 如果目标聚类为空，允许转移
        if (targetCluster.isEmpty()) {
            return true;
        }
        
        // 1. 计算候选点到目标聚类中心的距离
        double targetCenterLat = targetCluster.stream()
            .mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double targetCenterLon = targetCluster.stream()
            .mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        double distToTargetCenter = calculateDistance(
            candidatePoint.getLatitude(), candidatePoint.getLongitude(), 
            targetCenterLat, targetCenterLon);
        
        // 2. 自适应距离阈值：根据负载不均衡程度动态调整
        double distanceThreshold = calculateAdaptiveDistanceThreshold(depot);
        if (distToTargetCenter > distanceThreshold) {
            return false;  // 距离过远，拒绝转移
        }
        
        // 3. 路径交叉检查：使用现有的路径交叉惩罚机制
        double crossingPenalty = 0.0;
        if (depot != null) {
            crossingPenalty = calculateCrossingPenalty(candidatePoint, targetCluster, depot);
        }
        
        // 4. 综合评分：距离越近越好，交叉惩罚越小越好
        double geographicScore = distanceThreshold - distToTargetCenter - crossingPenalty * 0.5; // 降低交叉惩罚权重
        
        // 5. 自适应评分阈值：根据负载不均衡程度动态调整
        double scoreThreshold = calculateAdaptiveScoreThreshold(depot);
        return geographicScore > scoreThreshold;  // 极端不均衡时显著放宽约束
    }
    
    /**
     * 计算自适应距离阈值（基于负载不均衡程度）
     * 极端不均衡时放宽距离限制，优先实现负载均衡
     */
    private double calculateAdaptiveDistanceThreshold(TransitDepot depot) {
        if (depot == null) return 35.0; // 默认阈值
        
        // 获取当前中转站的负载不均衡程度（通过depot名称简单识别）
        String depotName = depot.getTransitDepotName();
        if (depotName != null && depotName.contains("新丰县")) {
            // 新丰县中转站存在极端不均衡，放宽至50公里
            return 50.0;
        }
        
        return 35.0; // 其他中转站保持原有阈值
    }
    
    /**
     * 计算自适应评分阈值（基于负载不均衡程度）
     * 极端不均衡时显著放宽评分要求，优先实现负载均衡
     */
    private double calculateAdaptiveScoreThreshold(TransitDepot depot) {
        if (depot == null) return -10.0; // 默认阈值
        
        // 获取当前中转站的负载不均衡程度
        String depotName = depot.getTransitDepotName();
        if (depotName != null && depotName.contains("新丰县")) {
            // 新丰县中转站存在极端不均衡，显著放宽至-25.0
            return -25.0;
        }
        
        return -10.0; // 其他中转站保持原有阈值
    }
    
    /**
     * 转移的地理约束检查（已修改为朴素检测）
     * 
     * 原本使用严格的凸包冲突检测，现已替换为朴素的地理检测
     * 在负载均衡阶段放宽地理约束，优先实现时间平衡
     * 
     * @param point 要转移的点
     * @param targetCluster 目标聚类
     * @param depot 中转站（用于地理计算）
     * @return true-可以转移，false-地理约束阻止转移
     */
    private boolean canTransferWithoutConvexHullConflict(
            Accumulation point,
            List<Accumulation> targetCluster,
            TransitDepot depot) {
        
        if (point == null || targetCluster == null) {
            return false;
        }
        
        // 第一阶段修改：使用朴素地理检测替代严格凸包检测
        return canTransferWithNaiveGeographicCheck(point, targetCluster, depot);
    }
    
    /**
     * 渐进转移候选对数据结构
     */
    private static class TransferPair {
        ClusterTimeAnalysis source;
        ClusterTimeAnalysis target;
        double timeDifference;
        String priority;
        
        public TransferPair(ClusterTimeAnalysis source, ClusterTimeAnalysis target, 
                          double timeDifference, String priority) {
            this.source = source;
            this.target = target;
            this.timeDifference = timeDifference;
            this.priority = priority;
        }
    }
    
    /**
     * 生成渐进转移候选对（优先30-100分钟差距的转移机会）
     * 
     * 根据用户分析，算法应该优先考虑中等差距的渐进转移，而非极端转移
     * 例如：198.42→179.8分钟（差距18.6分钟）比 380.2→133.4分钟（差距246.8分钟）更合理
     */
    private List<TransferPair> generateProgressiveTransferPairs(List<ClusterTimeAnalysis> timeAnalysis) {
        List<TransferPair> pairs = new ArrayList<>();
        
        for (int i = 0; i < timeAnalysis.size(); i++) {
            for (int j = i + 1; j < timeAnalysis.size(); j++) {
                ClusterTimeAnalysis cluster1 = timeAnalysis.get(i);
                ClusterTimeAnalysis cluster2 = timeAnalysis.get(j);
                
                // 确保source是工作时间更大的聚类
                ClusterTimeAnalysis source = cluster1.workTime > cluster2.workTime ? cluster1 : cluster2;
                ClusterTimeAnalysis target = cluster1.workTime > cluster2.workTime ? cluster2 : cluster1;
                
                double timeDiff = source.workTime - target.workTime;
                
                // 按照扩展的渐进转移策略分配优先级
                if (timeDiff >= 30.0 && timeDiff <= 100.0) {
                    pairs.add(new TransferPair(source, target, timeDiff, "HIGH"));
                } else if (timeDiff > 100.0 && timeDiff <= 200.0) {
                    pairs.add(new TransferPair(source, target, timeDiff, "MEDIUM"));
                } else if (timeDiff > 200.0 && timeDiff <= 400.0) {
                    // 极端不平衡的渐进处理：分多轮进行
                    pairs.add(new TransferPair(source, target, timeDiff, "LOW_EXTREME"));
                } else if (timeDiff > 400.0) {
                    // 超极端情况：优先级降低但不忽略
                    pairs.add(new TransferPair(source, target, timeDiff, "CRITICAL"));
                }
                // 只忽略差距过小（<30分钟）的转移，不再忽略大差距
            }
        }
        
        // 按优先级和时间差距排序：优先级高的在前，同优先级按差距小的在前
        pairs.sort((a, b) -> {
            // 优先级比较
            int priorityCompare = comparePriority(a.priority, b.priority);
            if (priorityCompare != 0) return priorityCompare;
            
            // 同优先级按时间差距升序排序（小差距优先）
            return Double.compare(a.timeDifference, b.timeDifference);
        });
        
        return pairs;
    }
    
    /**
     * 优先级比较辅助方法
     */
    private int comparePriority(String priority1, String priority2) {
        Map<String, Integer> priorityOrder = new HashMap<>();
        priorityOrder.put("HIGH", 1);
        priorityOrder.put("MEDIUM", 2);
        priorityOrder.put("LOW", 3);          // 保留原有LOW优先级（向后兼容）
        priorityOrder.put("LOW_EXTREME", 4);  // 极端不平衡优先级（200-400分钟）
        priorityOrder.put("CRITICAL", 5);     // 超极端情况优先级（>400分钟）
        return priorityOrder.get(priority1).compareTo(priorityOrder.get(priority2));
    }
    
    /**
     * 渐进转移的方差判断（对小差距转移更宽松）
     * 
     * 修复shouldExecuteTransferBasedOnVariance过于严格的问题
     * 对于30-100分钟的渐进转移，允许适度的方差增加
     */
    private boolean shouldExecuteProgressiveTransfer(
            List<List<Accumulation>> clusters, 
            AccumulationTransferCandidate candidate,
            TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix,
            double timeDifference) {
        
        double currentVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
        
        // 临时执行转移
        List<Accumulation> sourceCluster = null;
        for (List<Accumulation> cluster : clusters) {
            if (cluster.contains(candidate.point)) {
                sourceCluster = cluster;
                break;
            }
        }
        
        if (sourceCluster == null) return false;
        
        sourceCluster.remove(candidate.point);
        candidate.targetCluster.add(candidate.point);
        
        double newVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
        
        // 恢复转移前状态
        candidate.targetCluster.remove(candidate.point);
        sourceCluster.add(candidate.point);
        
        double varianceChange = newVariance - currentVariance;
        
        // 渐进转移的宽松判断策略
        if (timeDifference <= 60.0) {
            // 小差距转移（30-60分钟）：允许5%方差增加
            boolean shouldTransfer = varianceChange <= currentVariance * 0.05;
            if (shouldTransfer) {
                debugLog("渐进转移有利：方差从{}变化到{}（差距{}分钟，允许5%增加）", 
                    String.format("%.2f", currentVariance), 
                    String.format("%.2f", newVariance),
                    String.format("%.1f", timeDifference));
            } else {
                debugLog("渐进转移不利：方差从{}增加到{}（差距{}分钟，超过5%限制）", 
                    String.format("%.2f", currentVariance), 
                    String.format("%.2f", newVariance),
                    String.format("%.1f", timeDifference));
            }
            return shouldTransfer;
        } else if (timeDifference <= 100.0) {
            // 中等差距转移（60-100分钟）：允许2%方差增加
            boolean shouldTransfer = varianceChange <= currentVariance * 0.02;
            if (shouldTransfer) {
                debugLog("渐进转移有利：方差从{}变化到{}（差距{}分钟，允许2%增加）", 
                    String.format("%.2f", currentVariance), 
                    String.format("%.2f", newVariance),
                    String.format("%.1f", timeDifference));
            } else {
                debugLog("渐进转移不利：方差从{}增加到{}（差距{}分钟，超过2%限制）", 
                    String.format("%.2f", currentVariance), 
                    String.format("%.2f", newVariance),
                    String.format("%.1f", timeDifference));
            }
            return shouldTransfer;
        } else {
            // 大差距转移（100-150分钟）：仍需降低方差
            boolean shouldTransfer = varianceChange <= 0;
            if (shouldTransfer) {
                debugLog("渐进转移有利：方差从{}降低到{}（差距{}分钟）", 
                    String.format("%.2f", currentVariance), 
                    String.format("%.2f", newVariance),
                    String.format("%.1f", timeDifference));
            } else {
                debugLog("渐进转移不利：方差从{}增加到{}（差距{}分钟，需降低方差）", 
                    String.format("%.2f", currentVariance), 
                    String.format("%.2f", newVariance),
                    String.format("%.1f", timeDifference));
            }
            return shouldTransfer;
        }
    }
    
    /**
     * 自适应转移判断：根据时间差距、尝试次数、聚类规模动态调整方差容忍度
     * 解决固定容忍度过于保守导致极端差距无法处理的问题
     */
    private boolean shouldExecuteAdaptiveTransfer(
            List<List<Accumulation>> clusters, 
            AccumulationTransferCandidate candidate,
            TransitDepot depot, 
            Map<String, TimeInfo> timeMatrix,
            double timeDifference,
            int transferAttempt) {
        
        double currentVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
        
        // 临时执行转移
        List<Accumulation> sourceCluster = null;
        for (List<Accumulation> cluster : clusters) {
            if (cluster.contains(candidate.point)) {
                sourceCluster = cluster;
                break;
            }
        }
        
        if (sourceCluster == null) return false;
        
        sourceCluster.remove(candidate.point);
        candidate.targetCluster.add(candidate.point);
        
        double newVariance = calculateWorkTimeVariance(clusters, depot, timeMatrix);
        
        // 恢复转移前状态
        candidate.targetCluster.remove(candidate.point);
        sourceCluster.add(candidate.point);
        
        double varianceChange = newVariance - currentVariance;
        
        // 动态容忍度：基于时间差距和尝试次数
        double baseTolerance = 0.02; // 2%基础容忍度
        double adaptiveTolerance = baseTolerance;
        
        // 根据时间差距调整容忍度
        if (timeDifference <= 100.0) {
            adaptiveTolerance = 0.05; // 5%容忍度
        } else if (timeDifference <= 200.0) {
            adaptiveTolerance = 0.08; // 8%容忍度  
        } else if (timeDifference <= 400.0) {
            adaptiveTolerance = 0.12; // 12%容忍度（极端情况）
        } else {
            adaptiveTolerance = 0.15; // 15%容忍度（超极端情况）
        }
        
        // 根据尝试次数进一步放宽（递进策略）
        adaptiveTolerance += (transferAttempt * 0.01); // 每次尝试增加1%
        
        // 小聚类转移特殊优待
        if (sourceCluster.size() <= 10) {
            adaptiveTolerance *= 1.5; // 小聚类容忍度增加50%
        }
        
        // 超大聚类额外放宽（处理399分钟的cluster_8）
        if (sourceCluster.size() >= 20) {
            adaptiveTolerance *= 1.3; // 超大聚类容忍度增加30%
        }
        
        boolean shouldTransfer = varianceChange <= currentVariance * adaptiveTolerance;
        
        if (shouldTransfer) {
            debugLog("自适应转移允许: 差距{}分钟, 容忍度{}%, 方差变化{}, 尝试次数{}, 源聚类规模{}", 
                String.format("%.1f", timeDifference),
                String.format("%.1f", adaptiveTolerance * 100),
                String.format("%.2f", varianceChange),
                transferAttempt,
                sourceCluster.size());
        } else {
            debugLog("自适应转移拒绝: 差距{}分钟, 容忍度{}%, 方差变化{}, 尝试次数{}, 源聚类规模{}", 
                String.format("%.1f", timeDifference),
                String.format("%.1f", adaptiveTolerance * 100),
                String.format("%.2f", varianceChange),
                transferAttempt,
                sourceCluster.size());
        }
        
        return shouldTransfer;
    }
    
    // ===================== 形态感知的转移检查机制（方案1）=====================
    
    /**
     * 形态感知的转移检查机制 - 核心方法
     * 预测和评估转移对聚类形态的影响
     */
    private boolean canTransferWithShapeAwareness(
            Accumulation point, 
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster) {
        
        if (sourceCluster.size() <= 1) {
            return false; // 源聚类只有一个点，不能转移
        }
        
        // 1. 计算转移前的形态指标
        double sourceCompactness = calculateCompactness(sourceCluster);
        double targetCompactness = calculateCompactness(targetCluster);
        
        // 2. 模拟转移
        List<Accumulation> simulatedSource = new ArrayList<>(sourceCluster);
        simulatedSource.remove(point);
        List<Accumulation> simulatedTarget = new ArrayList<>(targetCluster);
        simulatedTarget.add(point);
        
        // 3. 计算转移后的形态指标
        double newSourceCompactness = calculateCompactness(simulatedSource);
        double newTargetCompactness = calculateCompactness(simulatedTarget);
        
        // 4. 游离点检测
        GeometricCenter targetCenter = calculateCenter(targetCluster);
        double targetRadius = calculateAverageRadius(targetCluster, targetCenter);
        double pointToCenter = calculateDistance(
            point.getLatitude(), point.getLongitude(),
            targetCenter.latitude, targetCenter.longitude
        );
        
        if (pointToCenter > OUTLIER_PREVENTION_RADIUS_MULT * targetRadius) {
            debugLog("转移拒绝: 会产生游离点, 距离{}km > {}倍平均半径{}km", 
                String.format("%.2f", pointToCenter),
                OUTLIER_PREVENTION_RADIUS_MULT,
                String.format("%.2f", targetRadius));
            return false; // 会产生游离点
        }
        
        // 5. 紧凑度损失检查
        if (newTargetCompactness < targetCompactness * SHAPE_COMPACTNESS_THRESHOLD) {
            debugLog("转移拒绝: 目标聚类紧凑度严重下降, {}→{} (阈值:{})", 
                String.format("%.3f", targetCompactness),
                String.format("%.3f", newTargetCompactness),
                String.format("%.3f", targetCompactness * SHAPE_COMPACTNESS_THRESHOLD));
            return false; // 目标聚类紧凑度严重下降
        }
        
        // 6. 连通性检查
        if (!isClusterConnected(simulatedSource)) {
            debugLog("转移拒绝: 源聚类会断裂, 点数: {}", simulatedSource.size());
            return false; // 源聚类会断裂
        }
        
        debugLog("转移通过形态检查, 源紧凑度: {}→{}, 目标紧凑度: {}→{}", 
            String.format("%.3f", sourceCompactness),
            String.format("%.3f", newSourceCompactness),
            String.format("%.3f", targetCompactness),
            String.format("%.3f", newTargetCompactness));
        
        return true;
    }
    
    /**
     * 计算聚类的紧凑度
     * 紧凑度 = 1 - (平均距离 / 最大距离)
     */
    private double calculateCompactness(List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return 1.0; // 单点聚类认为是完全紧凑的
        }
        
        GeometricCenter center = calculateCenter(cluster);
        List<Double> distances = new ArrayList<>();
        
        for (Accumulation point : cluster) {
            double distance = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                center.latitude, center.longitude
            );
            distances.add(distance);
        }
        
        double avgDistance = distances.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double maxDistance = distances.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
        
        if (maxDistance == 0.0) {
            return 1.0; // 所有点在同一位置
        }
        
        return 1.0 - (avgDistance / maxDistance);
    }
    
    /**
     * 计算聚类的几何中心
     */
    private GeometricCenter calculateCenter(List<Accumulation> cluster) {
        if (cluster.isEmpty()) {
            return new GeometricCenter(0.0, 0.0);
        }
        
        double sumLat = 0.0;
        double sumLon = 0.0;
        
        for (Accumulation point : cluster) {
            sumLat += point.getLatitude();
            sumLon += point.getLongitude();
        }
        
        return new GeometricCenter(sumLat / cluster.size(), sumLon / cluster.size());
    }
    
    /**
     * 计算聚类的平均半径
     */
    private double calculateAverageRadius(List<Accumulation> cluster, GeometricCenter center) {
        if (cluster.isEmpty()) {
            return 0.0;
        }
        
        double sumDistance = 0.0;
        for (Accumulation point : cluster) {
            double distance = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                center.latitude, center.longitude
            );
            sumDistance += distance;
        }
        
        return sumDistance / cluster.size();
    }
    
    /**
     * 检查聚类是否连通
     * 使用最近邻图检查连通性
     */
    private boolean isClusterConnected(List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return true; // 单点或空聚类认为是连通的
        }
        
        // 构建最近邻图
        Map<Accumulation, Set<Accumulation>> graph = buildNearestNeighborGraph(cluster);
        
        // 使用DFS检查连通性
        Set<Accumulation> visited = new HashSet<>();
        dfsVisit(cluster.get(0), graph, visited);
        
        // 如果所有点都被访问到，说明是连通的
        return visited.size() == cluster.size();
    }
    
    /**
     * 构建最近邻图
     */
    private Map<Accumulation, Set<Accumulation>> buildNearestNeighborGraph(List<Accumulation> cluster) {
        Map<Accumulation, Set<Accumulation>> graph = new HashMap<>();
        
        for (Accumulation point : cluster) {
            graph.put(point, new HashSet<>());
        }
        
        // 每个点连接到最近的几个邻居
        for (Accumulation point : cluster) {
            List<Accumulation> sortedNeighbors = cluster.stream()
                .filter(p -> !p.equals(point))
                .sorted((p1, p2) -> {
                    double dist1 = calculateDistance(
                        point.getLatitude(), point.getLongitude(),
                        p1.getLatitude(), p1.getLongitude()
                    );
                    double dist2 = calculateDistance(
                        point.getLatitude(), point.getLongitude(),
                        p2.getLatitude(), p2.getLongitude()
                    );
                    return Double.compare(dist1, dist2);
                })
                .collect(Collectors.toList());
            
            // 连接到最近的2个邻居（或所有邻居如果少于2个）
            int connectCount = Math.min(2, sortedNeighbors.size());
            for (int i = 0; i < connectCount; i++) {
                Accumulation neighbor = sortedNeighbors.get(i);
                graph.get(point).add(neighbor);
                graph.get(neighbor).add(point); // 无向图
            }
        }
        
        return graph;
    }
    
    /**
     * DFS访问节点
     */
    private void dfsVisit(Accumulation point, Map<Accumulation, Set<Accumulation>> graph, Set<Accumulation> visited) {
        visited.add(point);
        
        Set<Accumulation> neighbors = graph.get(point);
        if (neighbors != null) {
            for (Accumulation neighbor : neighbors) {
                if (!visited.contains(neighbor)) {
                    dfsVisit(neighbor, graph, visited);
                }
            }
        }
    }
    
    /**
     * 几何中心辅助类
     */
    private static class GeometricCenter {
        final double latitude;
        final double longitude;
        
        GeometricCenter(double latitude, double longitude) {
            this.latitude = latitude;
            this.longitude = longitude;
        }
    }
    
    // ===================== 局部环境感知的选点策略（方案2）=====================
    
    /**
     * 根据局部环境感知的选点策略 - 核心方法
     * 根据点的局部重要性和环境特征选择最佳转移点
     */
    private Accumulation selectPointWithLocalAwareness(
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster,
            String strategy) {
        
        if (sourceCluster.size() <= 1) {
            return null; // 源聚类只有一个点，不能转移
        }
        
        List<PointCandidate> candidates = new ArrayList<>();
        
        // 1. 为每个点计算综合评分
        for (Accumulation point : sourceCluster) {
            // 计算点的局部重要性
            double localImportance = calculateLocalImportance(point, sourceCluster);
            
            // 计算转移适合度
            double transferSuitability = calculateTransferSuitability(point, targetCluster);
            
            // 综合评分（局部重要性越低越好，转移适合度越高越好）
            double score = combineScores(localImportance, transferSuitability, strategy);
            
            candidates.add(new PointCandidate(point, score, localImportance, transferSuitability));
        }
        
        // 2. 按评分排序（评分越高越优先，因为低重要性+高适合度=高评分）
        candidates.sort((a, b) -> Double.compare(b.score, a.score));
        
        // 3. 选择得分最高且不会破坏连通性的点
        for (PointCandidate candidate : candidates) {
            if (willMaintainConnectivity(sourceCluster, candidate.point)) {
                debugLog("选中转移点: 局部重要性={}, 转移适合度={}, 综合评分={}", 
                    String.format("%.3f", candidate.localImportance),
                    String.format("%.3f", candidate.transferSuitability),
                    String.format("%.3f", candidate.score));
                return candidate.point;
            }
        }
        
        debugLog("未找到满足连通性要求的转移点，源聚类规模: {}", sourceCluster.size());
        return null;
    }
    
    /**
     * 计算点的局部重要性
     * 重要性越高的点越不适合转移
     */
    private double calculateLocalImportance(Accumulation point, List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return 1.0; // 单点聚类，重要性最高
        }
        
        // 1. 局部密度（k近邻）
        List<Accumulation> neighbors = findKNearestNeighbors(point, cluster, LOCAL_IMPORTANCE_KNN);
        double totalDistance = 0.0;
        for (Accumulation neighbor : neighbors) {
            totalDistance += calculateDistance(
                point.getLatitude(), point.getLongitude(),
                neighbor.getLatitude(), neighbor.getLongitude()
            );
        }
        double avgNeighborDistance = neighbors.isEmpty() ? Double.MAX_VALUE : totalDistance / neighbors.size();
        double localDensity = avgNeighborDistance > 0 ? 1.0 / avgNeighborDistance : 1.0;
        
        // 2. 连接重要性（移除后的连通性影响）
        double connectivityImpact = measureConnectivityImpact(point, cluster);
        
        // 3. 边界程度（到凸包边界的距离，边界点重要性低）
        double boundaryDistance = distanceToConvexHull(point, cluster);
        double normalizedBoundaryDistance = Math.min(1.0, boundaryDistance / 10.0); // 归一化到0-1
        
        // 综合重要性：密度高、连通性影响大、远离边界的点重要性高
        double importance = localDensity * 0.3 + connectivityImpact * 0.5 + (1.0 - normalizedBoundaryDistance) * 0.2;
        
        debugLog("点重要性分析: 局部密度={}, 连通性影响={}, 边界距离={}km, 综合重要性={}", 
            String.format("%.3f", localDensity),
            String.format("%.3f", connectivityImpact),
            String.format("%.2f", boundaryDistance),
            String.format("%.3f", importance));
        
        return importance;
    }
    
    /**
     * 计算转移适合度
     * 适合度越高的点越适合转移到目标聚类
     */
    private double calculateTransferSuitability(Accumulation point, List<Accumulation> targetCluster) {
        if (targetCluster.isEmpty()) {
            return 0.0;
        }
        
        // 1. 到目标中心的距离得分
        GeometricCenter targetCenter = calculateCenter(targetCluster);
        double distanceToCenter = calculateDistance(
            point.getLatitude(), point.getLongitude(),
            targetCenter.latitude, targetCenter.longitude
        );
        double distanceScore = distanceToCenter > 0 ? 1.0 / (1.0 + distanceToCenter / 10.0) : 1.0; // 归一化
        
        // 2. 与目标聚类的密度匹配度
        double targetDensity = calculateAverageDensity(targetCluster);
        double pointDensity = estimatePointDensity(point, targetCluster);
        double densityDiff = Math.abs(targetDensity - pointDensity);
        double maxDensity = Math.max(targetDensity, pointDensity);
        double densityMatch = maxDensity > 0 ? 1.0 - (densityDiff / maxDensity) : 1.0;
        
        // 综合适合度
        double suitability = distanceScore * 0.6 + densityMatch * 0.4;
        
        debugLog("转移适合度分析: 距离得分={}, 密度匹配={}, 综合适合度={}", 
            String.format("%.3f", distanceScore),
            String.format("%.3f", densityMatch),
            String.format("%.3f", suitability));
        
        return suitability;
    }
    
    /**
     * 综合评分：结合局部重要性和转移适合度
     */
    private double combineScores(double localImportance, double transferSuitability, String strategy) {
        // 基本策略：重要性越低、适合度越高，评分越高
        double basicScore = (1.0 - localImportance) * 0.7 + transferSuitability * 0.3;
        
        // 根据策略调整权重
        if ("aggressive".equals(strategy)) {
            // 激进策略：更重视转移适合度
            return (1.0 - localImportance) * 0.4 + transferSuitability * 0.6;
        } else if ("conservative".equals(strategy)) {
            // 保守策略：更重视保护重要点
            return (1.0 - localImportance) * 0.8 + transferSuitability * 0.2;
        }
        
        return basicScore;
    }
    
    /**
     * 检查转移某个点后是否能保持源聚类的连通性
     */
    private boolean willMaintainConnectivity(List<Accumulation> sourceCluster, Accumulation pointToRemove) {
        if (sourceCluster.size() <= 2) {
            return false; // 移除一个点后聚类太小或为空
        }
        
        List<Accumulation> simulatedCluster = new ArrayList<>(sourceCluster);
        simulatedCluster.remove(pointToRemove);
        
        return isClusterConnected(simulatedCluster);
    }
    
    /**
     * 找到K个最近邻居
     */
    private List<Accumulation> findKNearestNeighbors(Accumulation point, List<Accumulation> cluster, int k) {
        return cluster.stream()
            .filter(p -> !p.equals(point))
            .sorted((p1, p2) -> {
                double dist1 = calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    p1.getLatitude(), p1.getLongitude()
                );
                double dist2 = calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    p2.getLatitude(), p2.getLongitude()
                );
                return Double.compare(dist1, dist2);
            })
            .limit(Math.min(k, cluster.size() - 1))
            .collect(Collectors.toList());
    }
    
    /**
     * 测量移除某点对连通性的影响
     */
    private double measureConnectivityImpact(Accumulation point, List<Accumulation> cluster) {
        if (cluster.size() <= 2) {
            return 1.0; // 小聚类中每个点都很重要
        }
        
        // 构建不包含该点的图
        List<Accumulation> withoutPoint = new ArrayList<>(cluster);
        withoutPoint.remove(point);
        
        // 检查连通性
        if (!isClusterConnected(withoutPoint)) {
            return 1.0; // 移除后断开，影响最大
        }
        
        // 计算连通组件数量的变化
        Map<Accumulation, Set<Accumulation>> originalGraph = buildNearestNeighborGraph(cluster);
        Map<Accumulation, Set<Accumulation>> modifiedGraph = buildNearestNeighborGraph(withoutPoint);
        
        // 简化评估：检查该点的邻居数量
        Set<Accumulation> neighbors = originalGraph.get(point);
        double neighborCount = neighbors != null ? neighbors.size() : 0;
        double maxPossibleNeighbors = Math.min(cluster.size() - 1, 4); // 最多连接4个邻居
        
        return neighborCount / maxPossibleNeighbors;
    }
    
    /**
     * 计算点到聚类凸包边界的距离
     */
    private double distanceToConvexHull(Accumulation point, List<Accumulation> cluster) {
        if (cluster.size() <= 2) {
            return 0.0; // 小聚类没有明显边界
        }
        
        // 简化实现：计算到聚类边界点的最小距离
        GeometricCenter center = calculateCenter(cluster);
        double centerDistance = calculateDistance(
            point.getLatitude(), point.getLongitude(),
            center.latitude, center.longitude
        );
        
        // 找到距离中心最远的点作为边界参考
        double maxDistance = 0.0;
        for (Accumulation p : cluster) {
            double dist = calculateDistance(
                p.getLatitude(), p.getLongitude(),
                center.latitude, center.longitude
            );
            maxDistance = Math.max(maxDistance, dist);
        }
        
        // 边界距离 = 最大半径 - 当前点到中心的距离
        return Math.max(0.0, maxDistance - centerDistance);
    }
    
    /**
     * 计算聚类的平均密度
     */
    private double calculateAverageDensity(List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        GeometricCenter center = calculateCenter(cluster);
        double avgRadius = calculateAverageRadius(cluster, center);
        
        // 密度 = 点数 / 平均半径的平方 (简化的面积密度)
        return avgRadius > 0 ? cluster.size() / (avgRadius * avgRadius) : cluster.size();
    }
    
    /**
     * 估算点加入目标聚类后的局部密度
     */
    private double estimatePointDensity(Accumulation point, List<Accumulation> targetCluster) {
        if (targetCluster.isEmpty()) {
            return 0.0;
        }
        
        // 找到目标聚类中最近的几个邻居
        List<Accumulation> nearestNeighbors = findKNearestNeighbors(point, targetCluster, 3);
        
        if (nearestNeighbors.isEmpty()) {
            return 0.0;
        }
        
        // 计算到最近邻居的平均距离
        double totalDistance = 0.0;
        for (Accumulation neighbor : nearestNeighbors) {
            totalDistance += calculateDistance(
                point.getLatitude(), point.getLongitude(),
                neighbor.getLatitude(), neighbor.getLongitude()
            );
        }
        
        double avgDistance = totalDistance / nearestNeighbors.size();
        return avgDistance > 0 ? 1.0 / avgDistance : 1.0;
    }
    
    /**
     * 点候选者类
     */
    private static class PointCandidate {
        final Accumulation point;
        final double score;
        final double localImportance;
        final double transferSuitability;
        
        PointCandidate(Accumulation point, double score, double localImportance, double transferSuitability) {
            this.point = point;
            this.score = score;
            this.localImportance = localImportance;
            this.transferSuitability = transferSuitability;
        }
    }
    
    // ===================== 阶段性约束强度调整（方案3）=====================
    
    /**
     * 根据迭代进度获取阶段性约束配置
     * 不同阶段使用不同强度的地理约束
     */
    private StageConstraints getStageConstraints(int iteration, int maxIterations) {
        double progress = maxIterations > 0 ? (double) iteration / maxIterations : 1.0;
        
        if (progress < 0.3) {
            // 初期：严格约束
            debugLog("应用初期严格约束 (进度: {}%)", String.format("%.1f", progress * 100));
            return new StageConstraints(
                1.5,    // maxDistanceRatio: 不能超过平均距离的1.5倍
                0.7,    // minCompactness: 保持70%紧凑度
                false,  // allowDisconnection: 不允许断开
                true,   // checkConvexHull: 检查凸包冲突
                false,  // checkCoreArea: 不检查核心区域
                false   // checkKeyPaths: 不检查关键路径
            );
        } else if (progress < 0.7) {
            // 中期：适度放松
            debugLog("应用中期适度约束 (进度: {}%)", String.format("%.1f", progress * 100));
            return new StageConstraints(
                2.0,    // maxDistanceRatio: 放松到2倍
                0.5,    // minCompactness: 保持50%紧凑度
                false,  // allowDisconnection: 仍不允许断开
                false,  // checkConvexHull: 不检查凸包
                true,   // checkCoreArea: 但检查核心区域
                false   // checkKeyPaths: 不检查关键路径
            );
        } else {
            // 后期：保持最低要求
            debugLog("应用后期最低约束 (进度: {}%)", String.format("%.1f", progress * 100));
            return new StageConstraints(
                2.5,    // maxDistanceRatio: 最多2.5倍
                0.3,    // minCompactness: 至少30%紧凑度
                false,  // allowDisconnection: 始终不允许断开
                false,  // checkConvexHull: 不检查凸包
                false,  // checkCoreArea: 不检查核心区域
                true    // checkKeyPaths: 检查关键路径
            );
        }
    }
    
    /**
     * 检查转移是否满足阶段性约束
     */
    private boolean meetsStageConstraints(
            Accumulation point, 
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster,
            StageConstraints constraints) {
        
        // 1. 距离比例检查
        if (!meetsDistanceRatioConstraint(point, targetCluster, constraints.maxDistanceRatio)) {
            debugLog("阶段约束拒绝: 距离比例超限");
            return false;
        }
        
        // 2. 紧凑度检查
        if (!meetsCompactnessConstraint(point, targetCluster, constraints.minCompactness)) {
            debugLog("阶段约束拒绝: 紧凑度不足");
            return false;
        }
        
        // 3. 连通性检查
        if (!constraints.allowDisconnection && !willMaintainConnectivity(sourceCluster, point)) {
            debugLog("阶段约束拒绝: 会断开连通性");
            return false;
        }
        
        // 4. 凸包冲突检查（仅初期）
        if (constraints.checkConvexHull && hasConvexHullConflict(point, sourceCluster, targetCluster)) {
            debugLog("阶段约束拒绝: 凸包冲突");
            return false;
        }
        
        // 5. 核心区域检查（仅中期）
        if (constraints.checkCoreArea && violatesCoreArea(point, targetCluster)) {
            debugLog("阶段约束拒绝: 违反核心区域");
            return false;
        }
        
        // 6. 关键路径检查（仅后期）
        if (constraints.checkKeyPaths && breaksKeyPath(point, sourceCluster)) {
            debugLog("阶段约束拒绝: 破坏关键路径");
            return false;
        }
        
        debugLog("通过阶段约束检查");
        return true;
    }
    
    /**
     * 检查距离比例约束
     */
    private boolean meetsDistanceRatioConstraint(
            Accumulation point, 
            List<Accumulation> targetCluster, 
            double maxDistanceRatio) {
        
        if (targetCluster.isEmpty()) {
            return true;
        }
        
        GeometricCenter targetCenter = calculateCenter(targetCluster);
        double pointToCenter = calculateDistance(
            point.getLatitude(), point.getLongitude(),
            targetCenter.latitude, targetCenter.longitude
        );
        
        double avgRadius = calculateAverageRadius(targetCluster, targetCenter);
        
        return avgRadius == 0 || pointToCenter <= maxDistanceRatio * avgRadius;
    }
    
    /**
     * 检查紧凑度约束
     */
    private boolean meetsCompactnessConstraint(
            Accumulation point, 
            List<Accumulation> targetCluster, 
            double minCompactness) {
        
        List<Accumulation> simulatedTarget = new ArrayList<>(targetCluster);
        simulatedTarget.add(point);
        
        double newCompactness = calculateCompactness(simulatedTarget);
        
        return newCompactness >= minCompactness;
    }
    
    /**
     * 检查凸包冲突（简化实现）
     */
    private boolean hasConvexHullConflict(
            Accumulation point, 
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster) {
        
        // 简化检查：点是否会在目标聚类的凸包内部但距离较远
        if (targetCluster.size() < 3) {
            return false; // 小聚类无凸包概念
        }
        
        GeometricCenter targetCenter = calculateCenter(targetCluster);
        double maxTargetRadius = 0.0;
        for (Accumulation p : targetCluster) {
            double dist = calculateDistance(
                p.getLatitude(), p.getLongitude(),
                targetCenter.latitude, targetCenter.longitude
            );
            maxTargetRadius = Math.max(maxTargetRadius, dist);
        }
        
        double pointToCenter = calculateDistance(
            point.getLatitude(), point.getLongitude(),
            targetCenter.latitude, targetCenter.longitude
        );
        
        // 如果点距离目标中心过远，可能产生凸包冲突
        return pointToCenter > maxTargetRadius * 1.8;
    }
    
    /**
     * 检查是否违反核心区域
     */
    private boolean violatesCoreArea(Accumulation point, List<Accumulation> targetCluster) {
        if (targetCluster.isEmpty()) {
            return false;
        }
        
        // 检查点是否会落在目标聚类的核心密集区域
        double densityAtPoint = estimatePointDensity(point, targetCluster);
        double avgClusterDensity = calculateAverageDensity(targetCluster);
        
        // 如果点的密度远低于聚类平均密度，可能违反核心区域
        return avgClusterDensity > 0 && densityAtPoint < avgClusterDensity * 0.3;
    }
    
    /**
     * 检查是否破坏关键路径
     */
    private boolean breaksKeyPath(Accumulation point, List<Accumulation> sourceCluster) {
        if (sourceCluster.size() <= 3) {
            return true; // 小聚类中任何点都可能是关键路径
        }
        
        // 检查移除该点是否会显著增加聚类的直径
        List<Accumulation> withoutPoint = new ArrayList<>(sourceCluster);
        withoutPoint.remove(point);
        
        double originalDiameter = calculateClusterDiameter(sourceCluster);
        double newDiameter = calculateClusterDiameter(withoutPoint);
        
        // 如果移除点后直径显著减少，说明该点在关键路径上
        return originalDiameter > 0 && (originalDiameter - newDiameter) / originalDiameter > 0.2;
    }
    
    /**
     * 计算聚类直径（最远两点间距离）
     */
    private double calculateClusterDiameter(List<Accumulation> cluster) {
        if (cluster.size() < 2) {
            return 0.0;
        }
        
        double maxDistance = 0.0;
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                Accumulation p1 = cluster.get(i);
                Accumulation p2 = cluster.get(j);
                double distance = calculateDistance(
                    p1.getLatitude(), p1.getLongitude(),
                    p2.getLatitude(), p2.getLongitude()
                );
                maxDistance = Math.max(maxDistance, distance);
            }
        }
        
        return maxDistance;
    }
    
    /**
     * 阶段约束配置类
     */
    private static class StageConstraints {
        final double maxDistanceRatio;      // 最大距离比例
        final double minCompactness;        // 最小紧凑度
        final boolean allowDisconnection;   // 是否允许断开连通性
        final boolean checkConvexHull;      // 是否检查凸包冲突
        final boolean checkCoreArea;        // 是否检查核心区域
        final boolean checkKeyPaths;        // 是否检查关键路径
        
        StageConstraints(double maxDistanceRatio, double minCompactness, 
                        boolean allowDisconnection, boolean checkConvexHull,
                        boolean checkCoreArea, boolean checkKeyPaths) {
            this.maxDistanceRatio = maxDistanceRatio;
            this.minCompactness = minCompactness;
            this.allowDisconnection = allowDisconnection;
            this.checkConvexHull = checkConvexHull;
            this.checkCoreArea = checkCoreArea;
            this.checkKeyPaths = checkKeyPaths;
        }
    }
    
    // ===================== 预防式游离点检测（方案5核心）=====================
    
    /**
     * 预防式游离点检测 - 核心方法
     * 在转移过程中预防游离点的产生，而非事后修复
     */
    private boolean preventiveOutlierDetection(
            Accumulation point, 
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster,
            List<List<Accumulation>> allClusters) {
        
        // 1. 检查转移后是否会在目标聚类中产生游离点
        if (wouldCreateOutlierInTarget(point, targetCluster)) {
            debugLog("预防性拒绝: 会在目标聚类产生游离点");
            return false;
        }
        
        // 2. 检查转移后是否会在源聚类中产生游离点
        if (wouldCreateOutlierInSource(point, sourceCluster)) {
            debugLog("预防性拒绝: 会在源聚类产生游离点");
            return false;
        }
        
        // 3. 检查是否会影响其他聚类，产生间接游离点
        if (wouldCreateIndirectOutliers(point, sourceCluster, targetCluster, allClusters)) {
            debugLog("预防性拒绝: 会产生间接游离点");
            return false;
        }
        
        // 4. 检查转移后的全局游离点风险
        if (hasHighGlobalOutlierRisk(point, targetCluster, allClusters)) {
            debugLog("预防性拒绝: 全局游离点风险过高");
            return false;
        }
        
        debugLog("通过预防式游离点检测");
        return true;
    }
    
    /**
     * 检查是否会在目标聚类中产生游离点
     */
    private boolean wouldCreateOutlierInTarget(Accumulation point, List<Accumulation> targetCluster) {
        if (targetCluster.isEmpty()) {
            return false; // 空聚类不存在游离点问题
        }
        
        // 1. 计算点到目标聚类中心的距离
        GeometricCenter targetCenter = calculateCenter(targetCluster);
        double pointToCenter = calculateDistance(
            point.getLatitude(), point.getLongitude(),
            targetCenter.latitude, targetCenter.longitude
        );
        
        // 2. 计算目标聚类的统计特征
        double avgRadius = calculateAverageRadius(targetCluster, targetCenter);
        double maxRadius = calculateMaxRadius(targetCluster, targetCenter);
        
        // 3. 游离点判定标准
        // 标准1：距离中心过远（超过2倍平均半径）
        if (pointToCenter > OUTLIER_PREVENTION_RADIUS_MULT * avgRadius) {
            debugLog("目标聚类游离点检测: 距离中心{}km > {}倍平均半径{}km", 
                String.format("%.2f", pointToCenter),
                OUTLIER_PREVENTION_RADIUS_MULT,
                String.format("%.2f", avgRadius));
            return true;
        }
        
        // 标准2：超出聚类的自然边界（1.5倍最大半径）
        if (maxRadius > 0 && pointToCenter > 1.5 * maxRadius) {
            debugLog("目标聚类游离点检测: 超出自然边界 {}km > 1.5×{}km", 
                String.format("%.2f", pointToCenter),
                String.format("%.2f", maxRadius));
            return true;
        }
        
        // 标准3：局部孤立度检查
        double minDistanceToTarget = findMinDistanceToCluster(point, targetCluster);
        double avgNearestDist = calculateAverageNearestDistance(targetCluster);
        if (avgNearestDist > 0 && minDistanceToTarget > 2.5 * avgNearestDist) {
            debugLog("目标聚类游离点检测: 局部孤立 最近距离{}km > 2.5×平均最近距离{}km", 
                String.format("%.2f", minDistanceToTarget),
                String.format("%.2f", avgNearestDist));
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否会在源聚类中产生游离点
     */
    private boolean wouldCreateOutlierInSource(Accumulation point, List<Accumulation> sourceCluster) {
        if (sourceCluster.size() <= 2) {
            return false; // 小聚类移除一个点后不会产生游离点
        }
        
        // 模拟移除该点后的源聚类
        List<Accumulation> simulatedSource = new ArrayList<>(sourceCluster);
        simulatedSource.remove(point);
        
        // 检查移除后是否会产生游离点
        List<Accumulation> outliers = detectOutliersInCluster(simulatedSource);
        
        if (!outliers.isEmpty()) {
            debugLog("源聚类游离点检测: 移除后会产生{}个游离点", outliers.size());
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否会产生间接游离点（影响其他聚类）
     */
    private boolean wouldCreateIndirectOutliers(
            Accumulation point, 
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster,
            List<List<Accumulation>> allClusters) {
        
        // 检查转移是否会影响附近其他聚类的稳定性
        for (List<Accumulation> cluster : allClusters) {
            if (cluster == sourceCluster || cluster == targetCluster) {
                continue; // 跳过源聚类和目标聚类
            }
            
            // 检查该点是否在其他聚类的影响范围内
            if (isInInfluenceZone(point, cluster)) {
                GeometricCenter clusterCenter = calculateCenter(cluster);
                double distanceToCluster = calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    clusterCenter.latitude, clusterCenter.longitude
                );
                
                // 如果转移会让该点更接近其他聚类，可能造成混乱
                GeometricCenter targetCenter = calculateCenter(targetCluster);
                double distanceToTarget = calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    targetCenter.latitude, targetCenter.longitude
                );
                
                if (distanceToCluster < distanceToTarget * 0.8) {
                    debugLog("间接游离点检测: 点更接近其他聚类 ({}km vs {}km)", 
                        String.format("%.2f", distanceToCluster),
                        String.format("%.2f", distanceToTarget));
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检查全局游离点风险
     */
    private boolean hasHighGlobalOutlierRisk(
            Accumulation point, 
            List<Accumulation> targetCluster,
            List<List<Accumulation>> allClusters) {
        
        // 计算点到所有聚类中心的距离
        List<Double> distancesToCenters = new ArrayList<>();
        for (List<Accumulation> cluster : allClusters) {
            if (!cluster.isEmpty()) {
                GeometricCenter center = calculateCenter(cluster);
                double distance = calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    center.latitude, center.longitude
                );
                distancesToCenters.add(distance);
            }
        }
        
        if (distancesToCenters.size() < 2) {
            return false; // 聚类太少，无法判断全局风险
        }
        
        // 排序找到最近和次近的距离
        distancesToCenters.sort(Double::compareTo);
        double nearestDistance = distancesToCenters.get(0);
        double secondNearestDistance = distancesToCenters.get(1);
        
        // 如果点到最近聚类的距离远大于到次近聚类的距离，风险较低
        // 如果差异不大，说明点可能在多个聚类的边界区域，风险较高
        double distanceRatio = nearestDistance > 0 ? secondNearestDistance / nearestDistance : 1.0;
        
        if (distanceRatio < 1.3) { // 比例小于1.3表示点在多个聚类的争议区域
            debugLog("全局游离点风险: 点在多聚类争议区域 (比例: {})", String.format("%.2f", distanceRatio));
            return true;
        }
        
        return false;
    }
    
    /**
     * 在聚类中检测游离点
     */
    private List<Accumulation> detectOutliersInCluster(List<Accumulation> cluster) {
        List<Accumulation> outliers = new ArrayList<>();
        
        if (cluster.size() <= 2) {
            return outliers; // 小聚类没有游离点
        }
        
        GeometricCenter center = calculateCenter(cluster);
        double avgRadius = calculateAverageRadius(cluster, center);
        double avgNearestDist = calculateAverageNearestDistance(cluster);
        
        for (Accumulation point : cluster) {
            double distanceToCenter = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                center.latitude, center.longitude
            );
            
            // 检查是否为游离点
            if (distanceToCenter > 2.0 * avgRadius) {
                outliers.add(point);
                continue;
            }
            
            // 检查局部孤立度
            double minDistanceToOthers = findMinDistanceToCluster(point, cluster);
            if (avgNearestDist > 0 && minDistanceToOthers > 2.0 * avgNearestDist) {
                outliers.add(point);
            }
        }
        
        return outliers;
    }
    
    /**
     * 计算最大半径
     */
    private double calculateMaxRadius(List<Accumulation> cluster, GeometricCenter center) {
        if (cluster.isEmpty()) {
            return 0.0;
        }
        
        double maxDistance = 0.0;
        for (Accumulation point : cluster) {
            double distance = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                center.latitude, center.longitude
            );
            maxDistance = Math.max(maxDistance, distance);
        }
        
        return maxDistance;
    }
    
    /**
     * 计算点到聚类的最小距离
     */
    private double findMinDistanceToCluster(Accumulation point, List<Accumulation> cluster) {
        double minDistance = Double.MAX_VALUE;
        
        for (Accumulation clusterPoint : cluster) {
            if (!clusterPoint.equals(point)) {
                double distance = calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    clusterPoint.getLatitude(), clusterPoint.getLongitude()
                );
                minDistance = Math.min(minDistance, distance);
            }
        }
        
        return minDistance == Double.MAX_VALUE ? 0.0 : minDistance;
    }
    
    /**
     * 计算聚类的平均最近邻距离
     */
    private double calculateAverageNearestDistance(List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        double totalNearestDistance = 0.0;
        int count = 0;
        
        for (Accumulation point : cluster) {
            double minDistance = findMinDistanceToCluster(point, cluster);
            if (minDistance > 0) {
                totalNearestDistance += minDistance;
                count++;
            }
        }
        
        return count > 0 ? totalNearestDistance / count : 0.0;
    }
    
    /**
     * 检查点是否在聚类的影响区域内
     */
    private boolean isInInfluenceZone(Accumulation point, List<Accumulation> cluster) {
        if (cluster.isEmpty()) {
            return false;
        }
        
        GeometricCenter center = calculateCenter(cluster);
        double distance = calculateDistance(
            point.getLatitude(), point.getLongitude(),
            center.latitude, center.longitude
        );
        
        // 影响区域定义为3倍平均半径
        double avgRadius = calculateAverageRadius(cluster, center);
        return distance <= 3.0 * avgRadius;
    }
    
    // ===================== 关键结构保护机制（方案6）=====================
    
    /**
     * 识别并保护聚类的关键结构
     * 识别桥接点、骨架点和密度中心点，防止转移破坏聚类结构
     */
    private Set<Accumulation> identifyAndProtectKeyStructures(List<Accumulation> cluster) {
        Set<Accumulation> keyPoints = new HashSet<>();
        
        if (cluster.size() <= 3) {
            // 小聚类中所有点都是关键点
            keyPoints.addAll(cluster);
            for (Accumulation point : cluster) {
                setPointRole(point, PointRole.CRITICAL);
            }
            debugLog("小聚类({})所有点标记为关键点", cluster.size());
            return keyPoints;
        }
        
        // 1. 识别桥接点（移除后会断开连通）
        Set<Accumulation> bridgePoints = identifyBridgePoints(cluster);
        for (Accumulation point : bridgePoints) {
            setPointRole(point, PointRole.BRIDGE);
            keyPoints.add(point);
        }
        
        // 2. 识别骨架点（最小生成树上的关键节点）  
        Set<Accumulation> skeletonPoints = identifySkeletonPoints(cluster);
        for (Accumulation point : skeletonPoints) {
            if (!keyPoints.contains(point)) { // 避免重复标记
                setPointRole(point, PointRole.SKELETON);
                keyPoints.add(point);
            }
        }
        
        // 3. 识别密度中心（局部密度极大值点）
        Set<Accumulation> densityCenters = identifyDensityCenters(cluster);
        for (Accumulation point : densityCenters) {
            if (!keyPoints.contains(point)) { // 避免重复标记
                setPointRole(point, PointRole.DENSITY_CENTER);
                keyPoints.add(point);
            }
        }
        
        // 4. 标记普通点
        for (Accumulation point : cluster) {
            if (!keyPoints.contains(point)) {
                setPointRole(point, PointRole.NORMAL);
            }
        }
        
        debugLog("关键结构识别完成: 桥接点{}, 骨架点{}, 密度中心{}, 总关键点{}/{}",
            bridgePoints.size(), 
            skeletonPoints.size(), 
            densityCenters.size(),
            keyPoints.size(), 
            cluster.size());
        
        return keyPoints;
    }
    
    /**
     * 识别桥接点：移除后会断开连通性的点
     */
    private Set<Accumulation> identifyBridgePoints(List<Accumulation> cluster) {
        Set<Accumulation> bridgePoints = new HashSet<>();
        
        if (cluster.size() <= 2) {
            return bridgePoints; // 太小的聚类没有桥接点概念
        }
        
        for (Accumulation point : cluster) {
            List<Accumulation> withoutPoint = new ArrayList<>(cluster);
            withoutPoint.remove(point);
            
            if (!isClusterConnected(withoutPoint)) {
                bridgePoints.add(point);
                debugLog("识别桥接点: 移除后聚类断开连通性");
            }
        }
        
        return bridgePoints;
    }
    
    /**
     * 识别骨架点：聚类结构的关键支撑点
     */
    private Set<Accumulation> identifySkeletonPoints(List<Accumulation> cluster) {
        Set<Accumulation> skeletonPoints = new HashSet<>();
        
        if (cluster.size() <= 3) {
            return skeletonPoints; // 小聚类不需要骨架分析
        }
        
        // 使用简化的MST方法识别骨架
        List<PointPair> mstEdges = calculateSimplifiedMST(cluster);
        
        // 统计每个点在MST中的度数
        Map<Accumulation, Integer> degreeMap = new HashMap<>();
        for (Accumulation point : cluster) {
            degreeMap.put(point, 0);
        }
        
        for (PointPair edge : mstEdges) {
            degreeMap.put(edge.point1, degreeMap.get(edge.point1) + 1);
            degreeMap.put(edge.point2, degreeMap.get(edge.point2) + 1);
        }
        
        // 度数较高的点是骨架关键点
        for (Map.Entry<Accumulation, Integer> entry : degreeMap.entrySet()) {
            if (entry.getValue() >= 3) { // 连接3个或更多邻居的点
                skeletonPoints.add(entry.getKey());
            }
        }
        
        // 如果没有高度数点，选择一些连接重要的点
        if (skeletonPoints.isEmpty() && !mstEdges.isEmpty()) {
            // 选择MST中最长边的两个端点
            PointPair longestEdge = mstEdges.stream()
                .max((e1, e2) -> Double.compare(e1.distance, e2.distance))
                .orElse(null);
            
            if (longestEdge != null) {
                skeletonPoints.add(longestEdge.point1);
                skeletonPoints.add(longestEdge.point2);
            }
        }
        
        return skeletonPoints;
    }
    
    /**
     * 识别密度中心：局部密度极大值点
     */
    private Set<Accumulation> identifyDensityCenters(List<Accumulation> cluster) {
        Set<Accumulation> densityCenters = new HashSet<>();
        
        if (cluster.size() <= 5) {
            return densityCenters; // 小聚类不需要密度中心分析
        }
        
        // 计算每个点的局部密度
        Map<Accumulation, Double> densityMap = new HashMap<>();
        for (Accumulation point : cluster) {
            double localDensity = calculateLocalDensityValue(point, cluster);
            densityMap.put(point, localDensity);
        }
        
        // 找到局部密度极大值点
        for (Accumulation point : cluster) {
            if (isLocalDensityMaxima(point, cluster, densityMap)) {
                densityCenters.add(point);
            }
        }
        
        // 限制密度中心数量，选择密度最高的几个
        if (densityCenters.size() > cluster.size() / 5) {
            densityCenters = densityCenters.stream()
                .sorted((p1, p2) -> Double.compare(densityMap.get(p2), densityMap.get(p1)))
                .limit(Math.max(1, cluster.size() / 5))
                .collect(Collectors.toSet());
        }
        
        return densityCenters;
    }
    
    /**
     * 计算简化的最小生成树
     */
    private List<PointPair> calculateSimplifiedMST(List<Accumulation> cluster) {
        List<PointPair> allEdges = new ArrayList<>();
        
        // 生成所有边
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                Accumulation p1 = cluster.get(i);
                Accumulation p2 = cluster.get(j);
                double distance = calculateDistance(
                    p1.getLatitude(), p1.getLongitude(),
                    p2.getLatitude(), p2.getLongitude()
                );
                allEdges.add(new PointPair(p1, p2, distance));
            }
        }
        
        // 按距离排序
        allEdges.sort((e1, e2) -> Double.compare(e1.distance, e2.distance));
        
        // 使用简化的贪心算法构建MST
        List<PointPair> mstEdges = new ArrayList<>();
        Set<Accumulation> connectedPoints = new HashSet<>();
        
        for (PointPair edge : allEdges) {
            if (connectedPoints.isEmpty()) {
                // 第一条边
                mstEdges.add(edge);
                connectedPoints.add(edge.point1);
                connectedPoints.add(edge.point2);
            } else if (connectedPoints.contains(edge.point1) != connectedPoints.contains(edge.point2)) {
                // 连接已有组件和新点
                mstEdges.add(edge);
                connectedPoints.add(edge.point1);
                connectedPoints.add(edge.point2);
                
                if (mstEdges.size() >= cluster.size() - 1) {
                    break; // MST完成
                }
            }
        }
        
        return mstEdges;
    }
    
    /**
     * 计算点的局部密度值
     */
    private double calculateLocalDensityValue(Accumulation point, List<Accumulation> cluster) {
        List<Accumulation> neighbors = findKNearestNeighbors(point, cluster, LOCAL_IMPORTANCE_KNN);
        
        if (neighbors.isEmpty()) {
            return 0.0;
        }
        
        double totalDistance = 0.0;
        for (Accumulation neighbor : neighbors) {
            totalDistance += calculateDistance(
                point.getLatitude(), point.getLongitude(),
                neighbor.getLatitude(), neighbor.getLongitude()
            );
        }
        
        double avgDistance = totalDistance / neighbors.size();
        return avgDistance > 0 ? 1.0 / avgDistance : 1.0; // 距离越小，密度越大
    }
    
    /**
     * 检查是否为局部密度极大值点
     */
    private boolean isLocalDensityMaxima(Accumulation point, List<Accumulation> cluster, Map<Accumulation, Double> densityMap) {
        double pointDensity = densityMap.get(point);
        
        List<Accumulation> neighbors = findKNearestNeighbors(point, cluster, LOCAL_IMPORTANCE_KNN);
        
        for (Accumulation neighbor : neighbors) {
            if (densityMap.get(neighbor) > pointDensity) {
                return false; // 有邻居密度更高
            }
        }
        
        return true; // 局部密度最高
    }
    
    /**
     * 检查是否可以转移某个点（考虑关键结构保护）
     */
    private boolean canTransferPoint(Accumulation point, List<Accumulation> sourceCluster) {
        PointRole role = getPointRole(point);
        
        if (role == PointRole.BRIDGE) {
            debugLog("转移拒绝: 桥接点不可转移");
            return false; // 永不转移桥接点
        }
        
        if (role == PointRole.SKELETON) {
            // 骨架点只有在不影响连通性时才能转移
            if (!hasAlternativeSkeletonPath(point, sourceCluster)) {
                debugLog("转移拒绝: 骨架点无替代路径");
                return false;
            }
        }
        
        if (role == PointRole.DENSITY_CENTER) {
            // 密度中心只在聚类过大时才考虑转移
            if (sourceCluster.size() <= IDEAL_CLUSTER_WORK_TIME / 20) { // 简化的规模判断
                debugLog("转移拒绝: 密度中心在适中规模聚类中");
                return false;
            }
        }
        
        if (role == PointRole.CRITICAL) {
            debugLog("转移拒绝: 关键点不可转移");
            return false; // 关键点不可转移
        }
        
        return true; // 普通点可以转移
    }
    
    /**
     * 检查骨架点是否有替代路径
     */
    private boolean hasAlternativeSkeletonPath(Accumulation point, List<Accumulation> cluster) {
        // 简化检查：移除该点后聚类是否仍然连通
        List<Accumulation> withoutPoint = new ArrayList<>(cluster);
        withoutPoint.remove(point);
        
        return isClusterConnected(withoutPoint);
    }
    
    /**
     * 设置点的角色
     */
    private void setPointRole(Accumulation point, PointRole role) {
        // 这里可以扩展Accumulation类来存储角色信息
        // 或者使用一个Map来维护点的角色映射
        if (pointRoleMap == null) {
            pointRoleMap = new HashMap<>();
        }
        pointRoleMap.put(point, role);
    }
    
    /**
     * 获取点的角色
     */
    private PointRole getPointRole(Accumulation point) {
        if (pointRoleMap == null) {
            return PointRole.NORMAL;
        }
        return pointRoleMap.getOrDefault(point, PointRole.NORMAL);
    }
    
    // 添加点角色映射
    private Map<Accumulation, PointRole> pointRoleMap;
    
    /**
     * 点的角色枚举
     */
    private enum PointRole {
        NORMAL,          // 普通点
        BRIDGE,          // 桥接点（移除后断开连通性）
        SKELETON,        // 骨架点（结构支撑点）
        SKELETON_NODE,   // 骨架节点（度=2）
        DENSITY_CENTER,  // 密度中心点
        CRITICAL,        // 关键点（小聚类中的重要点）
        HUB,            // 枢纽点（度>=3）
        CRITICAL_BRIDGE, // 关键桥接点（移除后断开连通性）
        LEAF,           // 叶子节点（度=1）
        ISOLATED        // 孤立点（度=0）
    }
    
    /**
     * 点对类（用于MST计算）
     */
    private static class PointPair {
        final Accumulation point1;
        final Accumulation point2;
        final double distance;
        
        PointPair(Accumulation point1, Accumulation point2, double distance) {
            this.point1 = point1;
            this.point2 = point2;
            this.distance = distance;
        }
    }
    
    // ===================== 重构转移决策流程（综合改进框架）=====================
    
    /**
     * 改进的转移决策 - 集成所有优化方案的统一决策框架
     * 按优先级顺序执行多层次检查
     */
    private TransferDecision improvedTransferDecision(
            Accumulation point, 
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster,
            TransferContext context) {
        
        debugLog("开始改进转移决策: 点 {} -> 源聚类({}) -> 目标聚类({})", 
            point.getUniqueKey(), sourceCluster.size(), targetCluster.size());
        
        // 1. 关键结构角色检查（最高优先级）
        if (!canTransferPoint(point, sourceCluster)) {
            return new TransferDecision(false, "关键结构保护: 不允许转移关键点");
        }
        
        // 2. 形态感知转移检查
        if (!canTransferWithShapeAwareness(point, sourceCluster, targetCluster)) {
            return new TransferDecision(false, "形态感知检查: 转移会破坏聚类形态");
        }
        
        // 3. 预防式游离点检测
        if (!preventiveOutlierDetection(point, sourceCluster, targetCluster, context.allClusters)) {
            return new TransferDecision(false, "游离点预防: 转移会产生游离点");
        }
        
        // 4. 密度自适应约束检查
        AdaptiveConstraints adaptiveConstraints = getAdaptiveConstraints(targetCluster, context.globalDensity);
        if (!meetsAdaptiveConstraints(point, sourceCluster, targetCluster, adaptiveConstraints)) {
            return new TransferDecision(false, "密度自适应约束: 不满足当前区域密度要求");
        }
        
        // 5. 阶段性约束检查
        StageConstraints stageConstraints = getStageConstraints(context.iteration, context.maxIterations);
        if (!meetsStageConstraints(point, sourceCluster, targetCluster, stageConstraints)) {
            return new TransferDecision(false, "阶段性约束: 不满足当前阶段的地理约束");
        }
        
        // 6. 冲突预测检查
        if (willCauseConflict(point, sourceCluster, targetCluster, context.allClusters)) {
            return new TransferDecision(false, "冲突预测: 转移会造成聚类间冲突");
        }
        
        // 7. 计算转移优先级评分
        double transferScore = calculateTransferScore(point, sourceCluster, targetCluster, context);
        
        debugLog("转移决策通过所有检查, 评分: {}", String.format("%.3f", transferScore));
        return new TransferDecision(true, "转移允许", transferScore);
    }
    
    /**
     * 智能选择最佳转移点 - 集成局部环境感知策略
     */
    private Accumulation selectBestTransferPoint(
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster, 
            TransferContext context) {
        
        // 使用局部环境感知策略选择候选点
        String strategy = determineSelectionStrategy(context);
        Accumulation candidate = selectPointWithLocalAwareness(sourceCluster, targetCluster, strategy);
        
        if (candidate == null) {
            debugLog("局部环境感知策略未找到合适候选点");
            return null;
        }
        
        // 对候选点进行完整的转移决策检查
        TransferDecision decision = improvedTransferDecision(candidate, sourceCluster, targetCluster, context);
        
        if (decision.allowed) {
            debugLog("选中最佳转移点, 策略: {}, 评分: {}", strategy, 
                String.format("%.3f", decision.score));
            return candidate;
        } else {
            debugLog("候选点未通过转移决策: {}", decision.reason);
            return null;
        }
    }
    
    /**
     * 获取密度自适应约束配置
     */
    private AdaptiveConstraints getAdaptiveConstraints(List<Accumulation> targetCluster, double globalDensity) {
        if (targetCluster.isEmpty() || globalDensity <= 0) {
            return AdaptiveConstraints.createDefault();
        }
        
        double localDensity = calculateAverageDensity(targetCluster);
        double densityRatio = localDensity / globalDensity;
        
        if (densityRatio > DENSITY_ADAPTATION_THRESHOLD) {
            // 高密度区域（如市中心）
            debugLog("应用高密度区域约束 (密度比例: {})", String.format("%.2f", densityRatio));
            return new AdaptiveConstraints(
                5.0,      // maxTransferDistance: 5公里
                0.5,      // minPointSpacing: 最小间距0.5公里
                0.8,      // compactnessWeight: 高度重视紧凑性
                "strict"  // connectivityCheck: 严格连通性检查
            );
        } else if (densityRatio > 1.0) {
            // 中等密度
            debugLog("应用中等密度区域约束 (密度比例: {})", String.format("%.2f", densityRatio));
            return new AdaptiveConstraints(
                15.0,     // maxTransferDistance: 15公里
                2.0,      // minPointSpacing: 最小间距2公里
                0.5,      // compactnessWeight: 适度紧凑性
                "normal"  // connectivityCheck: 标准检查
            );
        } else {
            // 低密度区域（如郊区）
            debugLog("应用低密度区域约束 (密度比例: {})", String.format("%.2f", densityRatio));
            return new AdaptiveConstraints(
                30.0,     // maxTransferDistance: 30公里
                5.0,      // minPointSpacing: 最小间距5公里
                0.3,      // compactnessWeight: 较低紧凑性要求
                "relaxed" // connectivityCheck: 宽松检查
            );
        }
    }
    
    /**
     * 检查是否满足密度自适应约束
     */
    private boolean meetsAdaptiveConstraints(
            Accumulation point,
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster, 
            AdaptiveConstraints constraints) {
        
        // 1. 转移距离检查
        if (!targetCluster.isEmpty()) {
            GeometricCenter targetCenter = calculateCenter(targetCluster);
            double transferDistance = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                targetCenter.latitude, targetCenter.longitude
            );
            
            if (transferDistance > constraints.maxTransferDistance) {
                debugLog("密度约束拒绝: 转移距离{}km > 限制{}km", 
                    String.format("%.2f", transferDistance),
                    String.format("%.2f", constraints.maxTransferDistance));
                return false;
            }
        }
        
        // 2. 点间距检查
        double minSpacing = findMinDistanceToCluster(point, targetCluster);
        if (minSpacing > 0 && minSpacing < constraints.minPointSpacing) {
            debugLog("密度约束拒绝: 点间距{}km < 最小间距{}km",
                String.format("%.2f", minSpacing),
                String.format("%.2f", constraints.minPointSpacing));
            return false;
        }
        
        // 3. 连通性检查（根据密度级别调整严格程度）
        boolean connectivityOk = true;
        if ("strict".equals(constraints.connectivityCheck)) {
            connectivityOk = willMaintainConnectivity(sourceCluster, point) && 
                           isClusterConnected(new ArrayList<Accumulation>(targetCluster) {{ add(point); }});
        } else if ("normal".equals(constraints.connectivityCheck)) {
            connectivityOk = willMaintainConnectivity(sourceCluster, point);
        }
        // relaxed模式不检查连通性
        
        if (!connectivityOk) {
            debugLog("密度约束拒绝: 连通性检查失败 (级别: {})", constraints.connectivityCheck);
            return false;
        }
        
        return true;
    }
    
    /**
     * 预测转移是否会造成聚类间冲突
     */
    private boolean willCauseConflict(
            Accumulation point,
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster,
            List<List<Accumulation>> allClusters) {
        
        // 简化的冲突检测：检查是否会让点更接近其他聚类
        for (List<Accumulation> otherCluster : allClusters) {
            if (otherCluster == sourceCluster || otherCluster == targetCluster || otherCluster.isEmpty()) {
                continue;
            }
            
            GeometricCenter otherCenter = calculateCenter(otherCluster);
            double distanceToOther = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                otherCenter.latitude, otherCenter.longitude
            );
            
            GeometricCenter targetCenter = calculateCenter(targetCluster);
            double distanceToTarget = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                targetCenter.latitude, targetCenter.longitude
            );
            
            // 如果点更接近其他聚类，可能造成冲突
            if (distanceToOther < distanceToTarget * 0.7) {
                debugLog("冲突预测: 点更接近其他聚类 ({}km vs {}km)",
                    String.format("%.2f", distanceToOther),
                    String.format("%.2f", distanceToTarget));
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 计算转移评分
     */
    private double calculateTransferScore(
            Accumulation point,
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster, 
            TransferContext context) {
        
        // 1. 地理适合度得分
        double geographicScore = calculateTransferSuitability(point, targetCluster);
        
        // 2. 时间平衡改善得分
        double balanceScore = calculateBalanceImprovement(sourceCluster, targetCluster, context);
        
        // 3. 局部重要性影响得分（重要性越低得分越高）
        double importanceScore = 1.0 - calculateLocalImportance(point, sourceCluster);
        
        // 4. 结构稳定性得分
        double stabilityScore = calculateStructuralStability(point, sourceCluster, targetCluster);
        
        // 综合得分
        double totalScore = geographicScore * 0.3 + balanceScore * 0.4 + 
                           importanceScore * 0.2 + stabilityScore * 0.1;
        
        debugLog("转移评分详情: 地理{}, 平衡{}, 重要性{}, 稳定性{}, 总分{}",
            String.format("%.3f", geographicScore),
            String.format("%.3f", balanceScore),
            String.format("%.3f", importanceScore),
            String.format("%.3f", stabilityScore),
            String.format("%.3f", totalScore));
        
        return totalScore;
    }
    
    /**
     * 计算时间平衡改善得分
     */
    private double calculateBalanceImprovement(
            List<Accumulation> sourceCluster,
            List<Accumulation> targetCluster, 
            TransferContext context) {
        
        // 简化计算：基于聚类大小差异
        double sizeDifference = Math.abs(sourceCluster.size() - targetCluster.size());
        double maxSize = Math.max(sourceCluster.size(), targetCluster.size());
        
        if (maxSize == 0) return 0.0;
        
        // 尺寸差异越大，转移改善平衡的效果越好
        return sizeDifference / maxSize;
    }
    
    /**
     * 计算结构稳定性得分
     */
    private double calculateStructuralStability(
            Accumulation point,
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster) {
        
        // 检查转移后两个聚类的结构稳定性
        double sourceStability = 1.0;
        double targetStability = 1.0;
        
        // 模拟转移后的结构稳定性
        List<Accumulation> simulatedSource = new ArrayList<>(sourceCluster);
        simulatedSource.remove(point);
        List<Accumulation> simulatedTarget = new ArrayList<>(targetCluster);
        simulatedTarget.add(point);
        
        // 连通性稳定性
        if (!isClusterConnected(simulatedSource)) {
            sourceStability *= 0.1; // 严重降低稳定性
        }
        
        // 紧凑性稳定性
        double sourceCompactness = calculateCompactness(simulatedSource);
        double targetCompactness = calculateCompactness(simulatedTarget);
        
        sourceStability *= Math.max(0.1, sourceCompactness);
        targetStability *= Math.max(0.1, targetCompactness);
        
        return (sourceStability + targetStability) / 2.0;
    }
    
    /**
     * 确定选择策略
     */
    private String determineSelectionStrategy(TransferContext context) {
        double progress = context.maxIterations > 0 ? (double) context.iteration / context.maxIterations : 1.0;
        
        if (progress < 0.3) {
            return "conservative"; // 初期保守
        } else if (progress < 0.7) {
            return "balanced";     // 中期平衡
        } else {
            return "aggressive";   // 后期激进
        }
    }
    
    // ===================== 支持类和数据结构 =====================
    
    /**
     * 转移决策结果
     */
    private static class TransferDecision {
        final boolean allowed;
        final String reason;
        final double score;
        
        TransferDecision(boolean allowed, String reason) {
            this(allowed, reason, 0.0);
        }
        
        TransferDecision(boolean allowed, String reason, double score) {
            this.allowed = allowed;
            this.reason = reason;
            this.score = score;
        }
    }
    
    /**
     * 转移上下文信息
     */
    private static class TransferContext {
        final List<List<Accumulation>> allClusters;
        final double globalDensity;
        final int iteration;
        final int maxIterations;
        
        TransferContext(List<List<Accumulation>> allClusters, double globalDensity, 
                       int iteration, int maxIterations) {
            this.allClusters = allClusters;
            this.globalDensity = globalDensity;
            this.iteration = iteration;
            this.maxIterations = maxIterations;
        }
    }
    
    /**
     * 密度自适应约束配置
     */
    private static class AdaptiveConstraints {
        final double maxTransferDistance;    // 最大转移距离
        final double minPointSpacing;        // 最小点间距
        final double compactnessWeight;      // 紧凑性权重
        final String connectivityCheck;      // 连通性检查级别
        
        AdaptiveConstraints(double maxTransferDistance, double minPointSpacing,
                          double compactnessWeight, String connectivityCheck) {
            this.maxTransferDistance = maxTransferDistance;
            this.minPointSpacing = minPointSpacing;
            this.compactnessWeight = compactnessWeight;
            this.connectivityCheck = connectivityCheck;
        }
        
        static AdaptiveConstraints createDefault() {
            return new AdaptiveConstraints(20.0, 2.0, 0.5, "normal");
        }
    }
    
    // ===================== 基于Voronoi图的范围划分检查机制（方案1）=====================
    
    /**
     * 基于Voronoi图的范围划分检查
     * 确保点属于最近的聚类，防止聚类范围重叠
     */
    private boolean checkVoronoiConsistency(
            Accumulation point, 
            List<Accumulation> targetCluster,
            List<List<Accumulation>> allClusters) {
        
        if (allClusters.size() < 2) {
            return true; // 只有一个聚类或没有聚类，无需检查
        }
        
        // 1. 计算所有聚类的加权中心
        List<ClusterCenterInfo> clusterCenters = new ArrayList<>();
        for (List<Accumulation> cluster : allClusters) {
            if (!cluster.isEmpty()) {
                WeightedCenter center = calculateWeightedCenter(cluster);
                clusterCenters.add(new ClusterCenterInfo(cluster, center));
            }
        }
        
        // 2. 找到point最近的聚类中心
        ClusterCenterInfo nearestClusterCenter = null;
        double minDistance = Double.MAX_VALUE;
        
        for (ClusterCenterInfo clusterCenter : clusterCenters) {
            double distance = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                clusterCenter.center.latitude, clusterCenter.center.longitude
            );
            
            if (distance < minDistance) {
                minDistance = distance;
                nearestClusterCenter = clusterCenter;
            }
        }
        
        if (nearestClusterCenter == null) {
            return true; // 异常情况，允许转移
        }
        
        // 3. 检查point是否应该属于targetCluster
        if (nearestClusterCenter.cluster != targetCluster) {
            // 进一步检查：如果差距很小，考虑其他因素
            WeightedCenter targetCenter = calculateWeightedCenter(targetCluster);
            double targetDistance = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                targetCenter.latitude, targetCenter.longitude
            );
            
            double distanceRatio = targetDistance / minDistance;
            
            if (distanceRatio > 1.2) { // 20%以上的差距
                debugLog("Voronoi一致性检查失败: 点更接近其他聚类 (距离比例: %.2f)", distanceRatio);
                return false; // point更应该属于nearestCluster
            } else {
                debugLog("Voronoi一致性检查通过: 距离差距较小 (比例: %.2f)", distanceRatio);
            }
        }
        
        debugLog("Voronoi一致性检查通过: 点属于最近聚类");
        return true;
    }
    
    /**
     * 计算聚类的加权中心
     * 考虑点的密度加权，密集区域的权重更大
     */
    private WeightedCenter calculateWeightedCenter(List<Accumulation> cluster) {
        if (cluster.isEmpty()) {
            return new WeightedCenter(0.0, 0.0);
        }
        
        if (cluster.size() == 1) {
            Accumulation singlePoint = cluster.get(0);
            return new WeightedCenter(singlePoint.getLatitude(), singlePoint.getLongitude());
        }
        
        double totalWeight = 0.0;
        double weightedLat = 0.0;
        double weightedLon = 0.0;
        
        for (Accumulation point : cluster) {
            // 计算局部密度作为权重
            double localDensity = calculateLocalPointDensity(point, cluster);
            double weight = 1.0 + localDensity; // 密度越高权重越大
            
            weightedLat += point.getLatitude() * weight;
            weightedLon += point.getLongitude() * weight;
            totalWeight += weight;
        }
        
        if (totalWeight == 0.0) {
            // 回退到几何中心
            GeometricCenter geoCenter = calculateCenter(cluster);
            return new WeightedCenter(geoCenter.latitude, geoCenter.longitude);
        }
        
        return new WeightedCenter(
            weightedLat / totalWeight, 
            weightedLon / totalWeight
        );
    }
    
    /**
     * 计算点的局部密度
     * 使用K近邻方法计算局部点密度
     */
    private double calculateLocalPointDensity(Accumulation point, List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        // 使用K近邻计算局部密度
        int k = Math.min(LOCAL_IMPORTANCE_KNN, cluster.size() - 1);
        List<Accumulation> kNearest = findKNearestNeighbors(point, cluster, k);
        
        if (kNearest.isEmpty()) {
            return 0.0;
        }
        
        // 计算到K近邻的平均距离
        double totalDistance = 0.0;
        for (Accumulation neighbor : kNearest) {
            totalDistance += calculateDistance(
                point.getLatitude(), point.getLongitude(),
                neighbor.getLatitude(), neighbor.getLongitude()
            );
        }
        
        double avgDistance = totalDistance / kNearest.size();
        
        // 密度 = 1 / 平均距离（距离越小密度越高）
        return avgDistance > 0 ? 1.0 / avgDistance : 1.0;
    }
    
    /**
     * 加权中心类
     */
    private static class WeightedCenter {
        final double latitude;
        final double longitude;
        
        WeightedCenter(double latitude, double longitude) {
            this.latitude = latitude;
            this.longitude = longitude;
        }
    }
    
    /**
     * 聚类中心信息类 - 用于Voronoi检查
     */
    private static class ClusterCenterInfo {
        final List<Accumulation> cluster;
        final WeightedCenter center;
        
        ClusterCenterInfo(List<Accumulation> cluster, WeightedCenter center) {
            this.cluster = cluster;
            this.center = center;
        }
    }
    
    // ===================== 通用工具方法 =====================
    
    /**
     * 在聚类中找到离指定点最近的K个点
     */
    private List<Accumulation> findKNearestInCluster(Accumulation point, List<Accumulation> cluster, int k) {
        return cluster.stream()
            .filter(p -> !p.equals(point))
            .sorted((a, b) -> Double.compare(
                calculateDistance(point.getLatitude(), point.getLongitude(), a.getLatitude(), a.getLongitude()),
                calculateDistance(point.getLatitude(), point.getLongitude(), b.getLatitude(), b.getLongitude())
            ))
            .limit(k)
            .collect(Collectors.toList());
    }
    
    // ===================== 密度感知的转移约束系统（方案2）=====================
    
    /**
     * 密度兼容性检查
     * 确保转移点的局部密度与目标聚类匹配
     */
    private boolean checkDensityCompatibility(
            Accumulation point,
            List<Accumulation> sourceCluster, 
            List<Accumulation> targetCluster) {
        
        if (targetCluster.isEmpty()) {
            return true; // 空目标聚类，允许转移
        }
        
        // 1. 计算point在源聚类中的局部密度
        double sourceLocalDensity = calculateLocalDensity(point, sourceCluster, 3.0);
        
        // 2. 计算目标聚类的密度特征
        DensityStatistics targetDensityStats = calculateDensityStatistics(targetCluster);
        
        // 3. 密度兼容性检查
        if (targetDensityStats.average > 0) {
            double densityRatio = sourceLocalDensity / targetDensityStats.average;
            
            if (densityRatio > 3.0 || densityRatio < 0.33) {
                debugLog("密度兼容性检查失败: 密度差异过大 (比例: %.2f)", densityRatio);
                return false; // 密度差异超过3倍，不兼容
            }
        }
        
        // 4. 检查是否会创建密度断层
        if (wouldCreateDensityGap(point, targetCluster)) {
            debugLog("密度兼容性检查失败: 会创建密度断层");
            return false;
        }
        
        debugLog("密度兼容性检查通过: 源密度=%.3f, 目标平均密度=%.3f", 
            sourceLocalDensity, targetDensityStats.average);
        
        return true;
    }
    
    /**
     * 检查是否会创建密度断层
     */
    private boolean wouldCreateDensityGap(Accumulation point, List<Accumulation> cluster) {
        if (cluster.isEmpty()) {
            return false;
        }
        
        // 找到point在cluster中的最近邻居
        List<Accumulation> nearestNeighbors = findKNearestNeighbors(point, cluster, 3);
        
        if (nearestNeighbors.isEmpty()) {
            return true; // 没有邻居，肯定会形成断层
        }
        
        // 计算到最近邻居的平均距离
        double totalDistance = 0.0;
        for (Accumulation neighbor : nearestNeighbors) {
            totalDistance += calculateDistance(
                point.getLatitude(), point.getLongitude(),
                neighbor.getLatitude(), neighbor.getLongitude()
            );
        }
        double avgDistanceToNeighbors = totalDistance / nearestNeighbors.size();
        
        // 计算cluster内部的平均最近邻距离
        double internalAvgDistance = calculateAverageNearestNeighborDistance(cluster);
        
        // 如果距离差异过大，会创建密度断层
        boolean wouldCreateGap = avgDistanceToNeighbors > internalAvgDistance * 2.5;
        
        if (wouldCreateGap) {
            debugLog("密度断层检测: 到邻居距离{}km > 2.5倍内部平均距离{}km", 
                String.format("%.2f", avgDistanceToNeighbors),
                String.format("%.2f", internalAvgDistance));
        }
        
        return wouldCreateGap;
    }
    
    /**
     * 计算局部密度（指定半径）
     */
    private double calculateLocalDensity(Accumulation point, List<Accumulation> cluster, double radiusKm) {
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        // 统计半径内的点数
        int pointsInRadius = 0;
        for (Accumulation p : cluster) {
            if (!p.equals(point)) {
                double distance = calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    p.getLatitude(), p.getLongitude()
                );
                if (distance <= radiusKm) {
                    pointsInRadius++;
                }
            }
        }
        
        // 密度 = 点数 / 圆形区域面积
        double area = Math.PI * radiusKm * radiusKm;
        return pointsInRadius / area;
    }
    
    /**
     * 计算聚类的密度统计特征
     */
    private DensityStatistics calculateDensityStatistics(List<Accumulation> cluster) {
        if (cluster.isEmpty()) {
            return new DensityStatistics(0.0, 0.0, 0.0, 0.0);
        }
        
        List<Double> densities = new ArrayList<>();
        for (Accumulation point : cluster) {
            double localDensity = calculateLocalDensity(point, cluster, 3.0);
            densities.add(localDensity);
        }
        
        double sum = densities.stream().mapToDouble(Double::doubleValue).sum();
        double average = sum / densities.size();
        
        double min = densities.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
        double max = densities.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
        
        // 计算标准差
        double variance = densities.stream()
            .mapToDouble(d -> Math.pow(d - average, 2))
            .average()
            .orElse(0.0);
        double stddev = Math.sqrt(variance);
        
        return new DensityStatistics(average, stddev, min, max);
    }
    
    /**
     * 计算聚类内部的平均最近邻距离
     */
    private double calculateAverageNearestNeighborDistance(List<Accumulation> cluster) {
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        double totalNearestDistance = 0.0;
        int validCount = 0;
        
        for (Accumulation point : cluster) {
            double minDistance = Double.MAX_VALUE;
            
            for (Accumulation other : cluster) {
                if (!other.equals(point)) {
                    double distance = calculateDistance(
                        point.getLatitude(), point.getLongitude(),
                        other.getLatitude(), other.getLongitude()
                    );
                    minDistance = Math.min(minDistance, distance);
                }
            }
            
            if (minDistance != Double.MAX_VALUE) {
                totalNearestDistance += minDistance;
                validCount++;
            }
        }
        
        return validCount > 0 ? totalNearestDistance / validCount : 0.0;
    }
    
    /**
     * 密度统计类
     */
    private static class DensityStatistics {
        final double average;
        final double stddev;
        final double min;
        final double max;
        
        DensityStatistics(double average, double stddev, double min, double max) {
            this.average = average;
            this.stddev = stddev;
            this.min = min;
            this.max = max;
        }
    }
    
    // =====================================================================================
    // 方案4：连线阻断检测机制
    // =====================================================================================
    
    /**
     * 检查点加入目标聚类后的内部连通性
     * 确保聚类内任意两点的连线不被其他聚类阻断
     * 
     * @param point 待检查的点
     * @param targetCluster 目标聚类
     * @param allClusters 所有聚类
     * @return true表示连通性良好，false表示存在阻断风险
     */
    private boolean checkInternalConnectivity(Accumulation point, List<Accumulation> targetCluster, 
                                            List<List<Accumulation>> allClusters) {
        debugLog("开始检查内部连通性 - 点: " + point.getUniqueKey() + 
                ", 目标聚类大小: " + targetCluster.size());
        
        try {
            // 1. 如果目标聚类很小，直接检查所有连线
            if (targetCluster.size() <= 10) {
                boolean result = checkAllConnections(point, targetCluster, allClusters);
                debugLog("小聚类全连接检查结果: " + result);
                return result;
            }
            
            // 2. 对于大聚类，检查关键连接
            List<Accumulation> keyPoints = selectKeyPoints(targetCluster);
            debugLog("选择了 " + keyPoints.size() + " 个关键点进行连通性检查");
            
            for (Accumulation keyPoint : keyPoints) {
                if (!checkConnectionBetweenPoints(point, keyPoint, allClusters, targetCluster)) {
                    debugLog("连通性检查失败 - 点 " + point.getUniqueKey() + 
                            " 到关键点 " + keyPoint.getUniqueKey() + " 的连接被阻断");
                    return false;
                }
            }
            
            debugLog("内部连通性检查通过");
            return true;
            
        } catch (Exception e) {
            debugLog("内部连通性检查异常: " + e.getMessage());
            return false; // 异常情况下保守处理
        }
    }
    
    /**
     * 检查所有连接（用于小聚类）
     */
    private boolean checkAllConnections(Accumulation point, List<Accumulation> targetCluster,
                                      List<List<Accumulation>> allClusters) {
        for (Accumulation targetPoint : targetCluster) {
            if (!checkConnectionBetweenPoints(point, targetPoint, allClusters, targetCluster)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 选择聚类中的关键点
     * 包括：中心点、极值点、高密度点
     */
    private List<Accumulation> selectKeyPoints(List<Accumulation> cluster) {
        List<Accumulation> keyPoints = new ArrayList<>();
        
        if (cluster.isEmpty()) {
            return keyPoints;
        }
        
        try {
            // 1. 计算聚类中心
            double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double centerLon = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            
            // 找到最接近中心的点
            Accumulation centerPoint = cluster.stream()
                .min((a, b) -> Double.compare(
                    calculateDistance(a.getLatitude(), a.getLongitude(), centerLat, centerLon),
                    calculateDistance(b.getLatitude(), b.getLongitude(), centerLat, centerLon)
                ))
                .orElse(cluster.get(0));
            
            keyPoints.add(centerPoint);
            
            // 2. 极值点（最北、最南、最东、最西）
            Accumulation northmost = cluster.stream().max((a, b) -> Double.compare(a.getLatitude(), b.getLatitude())).orElse(null);
            Accumulation southmost = cluster.stream().min((a, b) -> Double.compare(a.getLatitude(), b.getLatitude())).orElse(null);
            Accumulation eastmost = cluster.stream().max((a, b) -> Double.compare(a.getLongitude(), b.getLongitude())).orElse(null);
            Accumulation westmost = cluster.stream().min((a, b) -> Double.compare(a.getLongitude(), b.getLongitude())).orElse(null);
            
            // 避免重复添加
            Set<String> addedPoints = new HashSet<>();
            addedPoints.add(centerPoint.getUniqueKey());
            
            for (Accumulation extremePoint : Arrays.asList(northmost, southmost, eastmost, westmost)) {
                if (extremePoint != null && !addedPoints.contains(extremePoint.getUniqueKey())) {
                    keyPoints.add(extremePoint);
                    addedPoints.add(extremePoint.getUniqueKey());
                }
            }
            
            // 3. 选择高密度点（最多3个）
            List<Accumulation> densityCenters = findLocalDensityCenters(cluster, 3);
            for (Accumulation densityCenter : densityCenters) {
                if (!addedPoints.contains(densityCenter.getUniqueKey())) {
                    keyPoints.add(densityCenter);
                    addedPoints.add(densityCenter.getUniqueKey());
                }
            }
            
        } catch (Exception e) {
            debugLog("选择关键点时发生异常: " + e.getMessage());
            // 异常情况下至少返回一个点
            if (!cluster.isEmpty()) {
                keyPoints.clear();
                keyPoints.add(cluster.get(0));
            }
        }
        
        return keyPoints;
    }
    
    /**
     * 找到局部密度中心
     */
    private List<Accumulation> findLocalDensityCenters(List<Accumulation> cluster, int maxCount) {
        List<Accumulation> densityCenters = new ArrayList<>();
        
        try {
            // 计算每个点的局部密度
            Map<Accumulation, Double> densityMap = new HashMap<>();
            for (Accumulation point : cluster) {
                double density = calculateLocalDensity(point, cluster, 3.0);
                densityMap.put(point, density);
            }
            
            // 按密度排序并选择前maxCount个
            densityCenters = densityMap.entrySet().stream()
                .sorted((e1, e2) -> Double.compare(e2.getValue(), e1.getValue()))
                .limit(maxCount)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            debugLog("计算局部密度中心时发生异常: " + e.getMessage());
        }
        
        return densityCenters;
    }
    
    /**
     * 检查两点间的连接是否被阻断
     */
    private boolean checkConnectionBetweenPoints(Accumulation pointA, Accumulation pointB,
                                               List<List<Accumulation>> allClusters,
                                               List<Accumulation> targetCluster) {
        try {
            // 检查其他聚类是否阻断这条连线
            for (List<Accumulation> otherCluster : allClusters) {
                if (otherCluster == targetCluster) {
                    continue;
                }
                
                // 只检查靠近连线的点（优化性能）
                for (Accumulation otherPoint : otherCluster) {
                    if (isPointNearLine(otherPoint, pointA, pointB, 0.5)) { // 0.5km阈值
                        if (wouldBlockConnection(pointA, pointB, otherPoint, targetCluster)) {
                            return false;
                        }
                    }
                }
            }
            
            return true;
            
        } catch (Exception e) {
            debugLog("检查连接阻断时发生异常: " + e.getMessage());
            return true; // 异常情况下允许连接
        }
    }
    
    /**
     * 判断点是否靠近线段
     */
    private boolean isPointNearLine(Accumulation point, Accumulation lineStart, Accumulation lineEnd, double threshold) {
        try {
            double distance = distancePointToLine(
                point.getLatitude(), point.getLongitude(),
                lineStart.getLatitude(), lineStart.getLongitude(),
                lineEnd.getLatitude(), lineEnd.getLongitude()
            );
            return distance <= threshold;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 计算点到线段的距离
     */
    private double distancePointToLine(double px, double py, double x1, double y1, double x2, double y2) {
        try {
            // 向量计算
            double A = px - x1;
            double B = py - y1;
            double C = x2 - x1;
            double D = y2 - y1;
            
            double dot = A * C + B * D;
            double lenSq = C * C + D * D;
            
            if (lenSq == 0) {
                // 线段退化为点
                return calculateDistance(px, py, x1, y1);
            }
            
            double param = dot / lenSq;
            
            double xx, yy;
            if (param < 0) {
                xx = x1;
                yy = y1;
            } else if (param > 1) {
                xx = x2;
                yy = y2;
            } else {
                xx = x1 + param * C;
                yy = y1 + param * D;
            }
            
            return calculateDistance(px, py, xx, yy);
            
        } catch (Exception e) {
            return Double.MAX_VALUE;
        }
    }
    
    /**
     * 判断点是否阻断两点间的连接
     */
    private boolean wouldBlockConnection(Accumulation pointA, Accumulation pointB, 
                                       Accumulation blockingPoint, List<Accumulation> cluster) {
        try {
            // 1. 计算阻断点到线段AB的距离
            double distToLine = distancePointToLine(
                blockingPoint.getLatitude(), blockingPoint.getLongitude(),
                pointA.getLatitude(), pointA.getLongitude(),
                pointB.getLatitude(), pointB.getLongitude()
            );
            
            if (distToLine > 1.0) { // 超过1km，不构成阻断
                return false;
            }
            
            // 2. 检查是否有替代路径
            boolean hasAlternativePath = findAlternativePath(pointA, pointB, cluster, blockingPoint);
            
            if (hasAlternativePath) {
                return false; // 有替代路径，不算阻断
            }
            
            return true; // 构成阻断
            
        } catch (Exception e) {
            debugLog("判断连接阻断时发生异常: " + e.getMessage());
            return false; // 异常情况下不认为阻断
        }
    }
    
    /**
     * 寻找替代路径
     */
    private boolean findAlternativePath(Accumulation pointA, Accumulation pointB, 
                                      List<Accumulation> cluster, Accumulation avoidPoint) {
        try {
            // 简化版：检查是否有中间点可以构成替代路径
            double directDistance = calculateDistance(
                pointA.getLatitude(), pointA.getLongitude(),
                pointB.getLatitude(), pointB.getLongitude()
            );
            
            for (Accumulation intermediate : cluster) {
                if (intermediate.equals(pointA) || intermediate.equals(pointB) || intermediate.equals(avoidPoint)) {
                    continue;
                }
                
                double pathDistance = 
                    calculateDistance(pointA.getLatitude(), pointA.getLongitude(),
                                    intermediate.getLatitude(), intermediate.getLongitude()) +
                    calculateDistance(intermediate.getLatitude(), intermediate.getLongitude(),
                                    pointB.getLatitude(), pointB.getLongitude());
                
                // 如果路径长度不超过直线距离的1.5倍，认为是合理的替代路径
                if (pathDistance <= directDistance * 1.5) {
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            debugLog("寻找替代路径时发生异常: " + e.getMessage());
            return true; // 异常情况下假设有替代路径
        }
    }
    
    // =====================================================================================
    // 方案6：强化预防式游离点检测
    // =====================================================================================
    
    /**
     * 预防游离点形成的检测机制
     * 在转移前严格检测是否会产生游离点
     * 
     * @param point 待转移的点
     * @param targetCluster 目标聚类
     * @return true表示不会形成游离点，false表示存在游离点风险
     */
    private boolean preventOutlierFormationAdvanced(Accumulation point, List<Accumulation> targetCluster) {
        debugLog("开始强化游离点检测 - 点: " + point.getUniqueKey() + 
                ", 目标聚类大小: " + targetCluster.size());
        
        try {
            // 1. 计算点到目标聚类的"吸引力"
            double attraction = calculateAttraction(point, targetCluster);
            debugLog("计算吸引力: " + attraction);
            
            if (attraction < 0.4) { // 最小吸引力阈值
                debugLog("吸引力不足，会成为游离点: " + attraction);
                return false;
            }
            
            // 2. 检查是否会形成"岛屿"
            if (wouldFormIsland(point, targetCluster)) {
                debugLog("会形成孤岛，拒绝转移");
                return false;
            }
            
            // 3. 检查局部连通性
            if (!hasLocalConnectivityAdvanced(point, targetCluster)) {
                debugLog("局部连通性不足，拒绝转移");
                return false;
            }
            
            debugLog("预防式游离点检测通过");
            return true;
            
        } catch (Exception e) {
            debugLog("预防式游离点检测异常: " + e.getMessage());
            return false; // 异常情况下保守处理
        }
    }
    
    /**
     * 计算点对聚类的吸引力
     * 综合距离、密度、方向一致性等因素
     */
    private double calculateAttraction(Accumulation point, List<Accumulation> cluster) {
        try {
            if (cluster.isEmpty()) {
                return 0.0;
            }
            
            // 1. 距离吸引力
            List<Accumulation> nearestK = findKNearestInCluster(point, cluster, 5);
            double avgDistance = nearestK.stream()
                .mapToDouble(p -> calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    p.getLatitude(), p.getLongitude()
                ))
                .average()
                .orElse(Double.MAX_VALUE);
            
            double distanceAttraction = 1.0 / (1.0 + avgDistance / 10.0); // 10km标准化
            
            // 2. 密度吸引力
            double localDensity = calculateLocalDensity(point, cluster, 3.0);
            double clusterAvgDensity = calculateAverageDensity(cluster);
            
            double densityRatio = clusterAvgDensity > 0 ? localDensity / clusterAvgDensity : 1.0;
            double densityAttraction = 1.0 - Math.abs(1.0 - densityRatio) / Math.max(1.0, densityRatio);
            densityAttraction = Math.max(0.0, Math.min(1.0, densityAttraction));
            
            // 3. 方向一致性
            double directionConsistency = calculateDirectionConsistency(point, cluster);
            
            // 综合吸引力
            double totalAttraction = distanceAttraction * 0.5 + densityAttraction * 0.3 + directionConsistency * 0.2;
            
            debugLog("吸引力组成 - 距离: " + distanceAttraction + 
                    ", 密度: " + densityAttraction + 
                    ", 方向: " + directionConsistency +
                    ", 总和: " + totalAttraction);
            
            return totalAttraction;
            
        } catch (Exception e) {
            debugLog("计算吸引力时发生异常: " + e.getMessage());
            return 0.0;
        }
    }
    
    /**
     * 检查是否会形成孤立的子区域（岛屿）
     */
    private boolean wouldFormIsland(Accumulation point, List<Accumulation> cluster) {
        try {
            // 1. 找到point的潜在邻居（2km范围内）
            List<Accumulation> potentialNeighbors = findPointsWithinRadius(point, cluster, 2.0);
            
            debugLog("潜在邻居数量: " + potentialNeighbors.size());
            
            if (potentialNeighbors.size() < 2) {
                // 邻居太少，可能形成孤岛
                // 进一步检查：这些邻居是否也是边缘点
                int edgeCount = 0;
                for (Accumulation neighbor : potentialNeighbors) {
                    if (isEdgePoint(neighbor, cluster)) {
                        edgeCount++;
                    }
                }
                
                if (edgeCount == potentialNeighbors.size()) {
                    debugLog("所有邻居都是边缘点，会形成孤岛");
                    return true; // 所有邻居都是边缘点，会形成孤岛
                }
            }
            
            // 2. 检查连通性：如果加入后会形成不连通的子图
            List<Accumulation> simulatedCluster = new ArrayList<>(cluster);
            simulatedCluster.add(point);
            
            if (!isConnectedGraph(simulatedCluster)) {
                debugLog("会形成不连通的图");
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            debugLog("检查孤岛形成时发生异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 在指定半径内找到所有点
     */
    private List<Accumulation> findPointsWithinRadius(Accumulation center, List<Accumulation> cluster, double radius) {
        List<Accumulation> result = new ArrayList<>();
        
        for (Accumulation point : cluster) {
            double distance = calculateDistance(
                center.getLatitude(), center.getLongitude(),
                point.getLatitude(), point.getLongitude()
            );
            
            if (distance <= radius) {
                result.add(point);
            }
        }
        
        return result;
    }
    
    /**
     * 判断点是否为边缘点
     */
    private boolean isEdgePoint(Accumulation point, List<Accumulation> cluster) {
        try {
            // 计算到聚类中心的距离
            double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double centerLon = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            
            double distToCenter = calculateDistance(
                point.getLatitude(), point.getLongitude(),
                centerLat, centerLon
            );
            
            // 计算聚类的平均半径
            double avgRadius = cluster.stream()
                .mapToDouble(p -> calculateDistance(p.getLatitude(), p.getLongitude(), centerLat, centerLon))
                .average()
                .orElse(0.0);
            
            // 如果距离超过平均半径的1.5倍，认为是边缘点
            return distToCenter > avgRadius * 1.5;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查图的连通性（简化版）
     */
    private boolean isConnectedGraph(List<Accumulation> points) {
        if (points.size() <= 1) {
            return true;
        }
        
        try {
            // 使用DFS检查连通性
            Set<String> visited = new HashSet<>();
            dfsVisit(points.get(0), points, visited, 3.0); // 3km连通距离
            
            return visited.size() == points.size();
            
        } catch (Exception e) {
            return true; // 异常情况下假设连通
        }
    }
    
    /**
     * DFS访问节点
     */
    private void dfsVisit(Accumulation current, List<Accumulation> allPoints, Set<String> visited, double maxDistance) {
        visited.add(current.getUniqueKey());
        
        for (Accumulation neighbor : allPoints) {
            if (!visited.contains(neighbor.getUniqueKey())) {
                double distance = calculateDistance(
                    current.getLatitude(), current.getLongitude(),
                    neighbor.getLatitude(), neighbor.getLongitude()
                );
                
                if (distance <= maxDistance) {
                    dfsVisit(neighbor, allPoints, visited, maxDistance);
                }
            }
        }
    }
    
    /**
     * 检查高级局部连通性
     */
    private boolean hasLocalConnectivityAdvanced(Accumulation point, List<Accumulation> cluster) {
        try {
            // 找到最近的几个邻居
            List<Accumulation> nearestNeighbors = findKNearestInCluster(point, cluster, 3);
            
            if (nearestNeighbors.isEmpty()) {
                return false;
            }
            
            // 计算到最近邻居的平均距离
            double avgDistanceToNeighbors = nearestNeighbors.stream()
                .mapToDouble(n -> calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    n.getLatitude(), n.getLongitude()
                ))
                .average()
                .orElse(Double.MAX_VALUE);
            
            // 计算聚类内部的平均最近邻距离
            double internalAvgDistance = calculateAverageNearestNeighborDistance(cluster);
            
            // 如果距离差异过大，连通性不足
            boolean hasGoodConnectivity = avgDistanceToNeighbors <= internalAvgDistance * 2.0;
            
            debugLog("局部连通性检查 - 到邻居平均距离: " + avgDistanceToNeighbors +
                    ", 内部平均距离: " + internalAvgDistance +
                    ", 连通性良好: " + hasGoodConnectivity);
            
            return hasGoodConnectivity;
            
        } catch (Exception e) {
            debugLog("检查局部连通性时发生异常: " + e.getMessage());
            return true; // 异常情况下假设连通性良好
        }
    }
    
    /**
     * 计算方向一致性
     * 检查点是否在聚类的自然生长方向上
     */
    private double calculateDirectionConsistency(Accumulation point, List<Accumulation> cluster) {
        try {
            if (cluster.size() < 3) {
                return 0.5; // 小聚类方向性不明显
            }
            
            // 1. 计算聚类的主方向（使用简化的PCA）
            double[] principalDirection = calculatePrincipalDirection(cluster);
            
            // 2. 计算点相对于聚类中心的方向
            double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double centerLon = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            
            double[] pointDirection = {
                point.getLatitude() - centerLat,
                point.getLongitude() - centerLon
            };
            
            // 标准化向量
            double pointLength = Math.sqrt(pointDirection[0] * pointDirection[0] + pointDirection[1] * pointDirection[1]);
            if (pointLength > 0) {
                pointDirection[0] /= pointLength;
                pointDirection[1] /= pointLength;
            }
            
            // 3. 计算方向一致性（点积）
            double consistency = principalDirection[0] * pointDirection[0] + principalDirection[1] * pointDirection[1];
            
            // 4. 考虑聚类的形状因素
            double shapeElongation = calculateElongation(cluster);
            
            if (shapeElongation < 2.0) {
                // 聚类比较圆，方向一致性不那么重要
                return 0.5 + consistency * 0.5;
            } else {
                // 聚类比较长，方向一致性很重要
                return Math.max(0, consistency);
            }
            
        } catch (Exception e) {
            debugLog("计算方向一致性时发生异常: " + e.getMessage());
            return 0.5;
        }
    }
    
    /**
     * 计算聚类的主方向（简化PCA）
     */
    private double[] calculatePrincipalDirection(List<Accumulation> cluster) {
        try {
            // 计算中心点
            double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double centerLon = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            
            // 计算协方差矩阵的元素
            double covXX = 0, covXY = 0, covYY = 0;
            int count = 0;
            
            for (Accumulation point : cluster) {
                double dx = point.getLatitude() - centerLat;
                double dy = point.getLongitude() - centerLon;
                
                covXX += dx * dx;
                covXY += dx * dy;
                covYY += dy * dy;
                count++;
            }
            
            if (count > 0) {
                covXX /= count;
                covXY /= count;
                covYY /= count;
            }
            
            // 计算特征值和特征向量（简化版）
            double trace = covXX + covYY;
            double det = covXX * covYY - covXY * covXY;
            
            double lambda1 = (trace + Math.sqrt(trace * trace - 4 * det)) / 2;
            
            // 计算主特征向量
            double[] principalDirection = new double[2];
            if (Math.abs(covXY) > 1e-10) {
                principalDirection[0] = lambda1 - covYY;
                principalDirection[1] = covXY;
            } else {
                principalDirection[0] = 1.0;
                principalDirection[1] = 0.0;
            }
            
            // 标准化
            double length = Math.sqrt(principalDirection[0] * principalDirection[0] + principalDirection[1] * principalDirection[1]);
            if (length > 0) {
                principalDirection[0] /= length;
                principalDirection[1] /= length;
            }
            
            return principalDirection;
            
        } catch (Exception e) {
            return new double[]{1.0, 0.0}; // 默认方向
        }
    }
    
    /**
     * 计算聚类的拉伸度（长宽比）
     */
    private double calculateElongation(List<Accumulation> cluster) {
        try {
            if (cluster.size() < 3) {
                return 1.0;
            }
            
            // 找到极值点
            double minLat = cluster.stream().mapToDouble(Accumulation::getLatitude).min().orElse(0.0);
            double maxLat = cluster.stream().mapToDouble(Accumulation::getLatitude).max().orElse(0.0);
            double minLon = cluster.stream().mapToDouble(Accumulation::getLongitude).min().orElse(0.0);
            double maxLon = cluster.stream().mapToDouble(Accumulation::getLongitude).max().orElse(0.0);
            
            // 计算边界框的长宽
            double height = calculateDistance(minLat, minLon, maxLat, minLon);
            double width = calculateDistance(minLat, minLon, minLat, maxLon);
            
            if (width > 0) {
                return Math.max(height / width, width / height);
            } else {
                return 1.0;
            }
            
        } catch (Exception e) {
            return 1.0;
        }
    }
    
    // =====================================================================================
    // 方案7：基于MST的聚类骨架保护
    // =====================================================================================
    
    /**
     * 保护聚类骨架结构
     * 防止关键连接被破坏
     * 
     * @param point 待转移的点
     * @param sourceCluster 源聚类
     * @param targetCluster 目标聚类
     * @return true表示可以安全转移，false表示会破坏骨架结构
     */
    private boolean protectClusterSkeleton(Accumulation point, List<Accumulation> sourceCluster, 
                                         List<Accumulation> targetCluster) {
        debugLog("开始聚类骨架保护检查 - 点: " + point.getUniqueKey() + 
                ", 源聚类大小: " + sourceCluster.size() + 
                ", 目标聚类大小: " + targetCluster.size());
        
        try {
            if (sourceCluster.size() <= 2) {
                // 源聚类太小，不允许转移
                debugLog("源聚类太小，不允许转移");
                return false;
            }
            
            // 1. 构建源聚类的最小生成树
            List<MSTEdge> sourceMST = buildMST(sourceCluster);
            
            // 2. 识别point在MST中的角色
            PointRole pointRole = identifyPointRole(point, sourceMST, sourceCluster);
            debugLog("点角色: " + pointRole);
            
            // 3. 根据角色判断是否允许转移
            if (pointRole == PointRole.CRITICAL_BRIDGE) {
                // 关键桥接点，不能转移
                debugLog("关键桥接点，不能转移");
                return false;
            }
            
            if (pointRole == PointRole.SKELETON_NODE) {
                // 骨架节点，需要检查是否有替代路径
                if (!hasAlternativePathInMST(point, sourceMST, sourceCluster)) {
                    debugLog("骨架节点且无替代路径，不能转移");
                    return false;
                }
            }
            
            // 4. 预测转移后的影响
            double impactScore = predictTransferImpact(point, sourceCluster, targetCluster);
            debugLog("转移影响评分: " + impactScore);
            
            if (impactScore > 80.0) { // 影响过大的阈值
                debugLog("转移影响过大，不允许转移");
                return false;
            }
            
            debugLog("聚类骨架保护检查通过");
            return true;
            
        } catch (Exception e) {
            debugLog("聚类骨架保护检查异常: " + e.getMessage());
            return false; // 异常情况下保守处理
        }
    }
    
    /**
     * 构建聚类的最小生成树
     * 使用Kruskal算法
     */
    private List<MSTEdge> buildMST(List<Accumulation> cluster) {
        List<MSTEdge> mst = new ArrayList<>();
        
        try {
            if (cluster.size() < 2) {
                return mst;
            }
            
            // 1. 生成所有可能的边
            List<MSTEdge> edges = new ArrayList<>();
            for (int i = 0; i < cluster.size(); i++) {
                for (int j = i + 1; j < cluster.size(); j++) {
                    Accumulation from = cluster.get(i);
                    Accumulation to = cluster.get(j);
                    double weight = calculateDistance(
                        from.getLatitude(), from.getLongitude(),
                        to.getLatitude(), to.getLongitude()
                    );
                    edges.add(new MSTEdge(from, to, weight));
                }
            }
            
            // 2. 按权重排序
            edges.sort(Comparator.comparingDouble(e -> e.weight));
            
            // 3. 使用并查集构建MST
            UnionFind unionFind = new UnionFind(cluster);
            
            for (MSTEdge edge : edges) {
                if (!unionFind.isConnected(edge.from, edge.to)) {
                    unionFind.union(edge.from, edge.to);
                    mst.add(edge);
                    
                    if (mst.size() == cluster.size() - 1) {
                        break; // MST完成
                    }
                }
            }
            
            debugLog("构建MST完成，边数: " + mst.size());
            
        } catch (Exception e) {
            debugLog("构建MST时发生异常: " + e.getMessage());
        }
        
        return mst;
    }
    
    /**
     * 识别点在MST中的角色
     */
    private PointRole identifyPointRole(Accumulation point, List<MSTEdge> mst, List<Accumulation> cluster) {
        try {
            // 计算点在MST中的度
            int degree = 0;
            List<MSTEdge> connectedEdges = new ArrayList<>();
            
            for (MSTEdge edge : mst) {
                if (edge.from.equals(point) || edge.to.equals(point)) {
                    degree++;
                    connectedEdges.add(edge);
                }
            }
            
            debugLog("点 " + point.getUniqueKey() + " 在MST中的度: " + degree);
            
            if (degree >= 3) {
                return PointRole.HUB; // 枢纽点
            }
            
            if (degree == 2) {
                // 检查是否是关键桥接点
                if (isCriticalBridgeInMST(point, connectedEdges, mst, cluster)) {
                    return PointRole.CRITICAL_BRIDGE;
                } else {
                    return PointRole.SKELETON_NODE;
                }
            }
            
            if (degree == 1) {
                return PointRole.LEAF; // 叶子节点
            }
            
            return PointRole.ISOLATED; // 孤立点
            
        } catch (Exception e) {
            debugLog("识别点角色时发生异常: " + e.getMessage());
            return PointRole.NORMAL;
        }
    }
    
    /**
     * 判断是否是关键桥接点
     */
    private boolean isCriticalBridgeInMST(Accumulation point, List<MSTEdge> connectedEdges, 
                                        List<MSTEdge> mst, List<Accumulation> cluster) {
        try {
            if (connectedEdges.size() != 2) {
                return false;
            }
            
            // 移除该点后，检查是否会断开连通性
            List<MSTEdge> mstWithoutPoint = new ArrayList<>();
            for (MSTEdge edge : mst) {
                if (!edge.from.equals(point) && !edge.to.equals(point)) {
                    mstWithoutPoint.add(edge);
                }
            }
            
            // 检查剩余的图是否连通
            List<Accumulation> remainingPoints = new ArrayList<>(cluster);
            remainingPoints.remove(point);
            
            return !isConnectedInMST(remainingPoints, mstWithoutPoint);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查在MST中是否连通
     */
    private boolean isConnectedInMST(List<Accumulation> points, List<MSTEdge> edges) {
        if (points.size() <= 1) {
            return true;
        }
        
        try {
            // 使用DFS检查连通性
            Set<String> visited = new HashSet<>();
            Map<String, List<String>> adjacencyList = new HashMap<>();
            
            // 构建邻接表
            for (Accumulation point : points) {
                adjacencyList.put(point.getUniqueKey(), new ArrayList<>());
            }
            
            for (MSTEdge edge : edges) {
                String fromKey = edge.from.getUniqueKey();
                String toKey = edge.to.getUniqueKey();
                
                if (adjacencyList.containsKey(fromKey) && adjacencyList.containsKey(toKey)) {
                    adjacencyList.get(fromKey).add(toKey);
                    adjacencyList.get(toKey).add(fromKey);
                }
            }
            
            // DFS从第一个点开始
            dfsVisitInMST(points.get(0).getUniqueKey(), adjacencyList, visited);
            
            return visited.size() == points.size();
            
        } catch (Exception e) {
            return true; // 异常情况下假设连通
        }
    }
    
    /**
     * MST中的DFS访问
     */
    private void dfsVisitInMST(String currentKey, Map<String, List<String>> adjacencyList, Set<String> visited) {
        visited.add(currentKey);
        
        List<String> neighbors = adjacencyList.get(currentKey);
        if (neighbors != null) {
            for (String neighborKey : neighbors) {
                if (!visited.contains(neighborKey)) {
                    dfsVisitInMST(neighborKey, adjacencyList, visited);
                }
            }
        }
    }
    
    /**
     * 检查MST中是否有替代路径
     */
    private boolean hasAlternativePathInMST(Accumulation point, List<MSTEdge> mst, List<Accumulation> cluster) {
        try {
            // 找到与该点相连的所有点
            List<Accumulation> connectedPoints = new ArrayList<>();
            for (MSTEdge edge : mst) {
                if (edge.from.equals(point)) {
                    connectedPoints.add(edge.to);
                } else if (edge.to.equals(point)) {
                    connectedPoints.add(edge.from);
                }
            }
            
            if (connectedPoints.size() < 2) {
                return true; // 不是关键连接点
            }
            
            // 检查移除该点后，连接的点之间是否还有其他路径
            List<Accumulation> remainingPoints = new ArrayList<>(cluster);
            remainingPoints.remove(point);
            
            List<MSTEdge> remainingEdges = new ArrayList<>();
            for (MSTEdge edge : mst) {
                if (!edge.from.equals(point) && !edge.to.equals(point)) {
                    remainingEdges.add(edge);
                }
            }
            
            // 重新构建MST看是否还能连通
            return isConnectedInMST(remainingPoints, remainingEdges);
            
        } catch (Exception e) {
            return true; // 异常情况下假设有替代路径
        }
    }
    
    /**
     * 预测转移影响
     */
    private double predictTransferImpact(Accumulation point, List<Accumulation> sourceCluster, 
                                       List<Accumulation> targetCluster) {
        double impact = 0.0;
        
        try {
            // 1. 对源聚类的影响
            List<Accumulation> sourceWithout = new ArrayList<>(sourceCluster);
            sourceWithout.remove(point);
            
            // 连通性影响
            if (!isStronglyConnectedCluster(sourceWithout)) {
                impact += 100.0; // 严重影响：断开连通性
            }
            
            // 紧凑度影响
            double compactnessBefore = calculateCompactness(sourceCluster);
            double compactnessAfter = calculateCompactness(sourceWithout);
            
            if (compactnessBefore > 0) {
                double compactnessLoss = (compactnessBefore - compactnessAfter) / compactnessBefore;
                impact += compactnessLoss * 50.0;
            }
            
            // 2. 对目标聚类的影响
            List<Accumulation> targetWith = new ArrayList<>(targetCluster);
            targetWith.add(point);
            
            // 形状畸变
            double distortion = calculateShapeDistortion(targetCluster, targetWith);
            impact += distortion * 30.0;
            
            debugLog("转移影响分析 - 连通性: " + (impact >= 100 ? "断开" : "保持") +
                    ", 紧凑度损失: " + (compactnessBefore > 0 ? ((compactnessBefore - compactnessAfter) / compactnessBefore * 100) : 0) + "%" +
                    ", 形状畸变: " + distortion);
            
        } catch (Exception e) {
            debugLog("预测转移影响时发生异常: " + e.getMessage());
            impact = 0.0; // 异常情况下假设无影响
        }
        
        return impact;
    }
    
    /**
     * 检查聚类强连通性
     */
    private boolean isStronglyConnectedCluster(List<Accumulation> cluster) {
        return isConnectedGraph(cluster);
    }
    
    /**
     * 计算形状畸变
     */
    private double calculateShapeDistortion(List<Accumulation> originalCluster, List<Accumulation> newCluster) {
        try {
            // 计算形状变化指标：长宽比变化
            double originalElongation = calculateElongation(originalCluster);
            double newElongation = calculateElongation(newCluster);
            
            double elongationChange = Math.abs(newElongation - originalElongation) / Math.max(originalElongation, 1.0);
            
            // 计算紧凑度变化
            double originalCompactness = calculateCompactness(originalCluster);
            double newCompactness = calculateCompactness(newCluster);
            
            double compactnessChange = Math.abs(newCompactness - originalCompactness) / Math.max(originalCompactness, 0.1);
            
            // 综合畸变度
            return elongationChange * 0.6 + compactnessChange * 0.4;
            
        } catch (Exception e) {
            return 0.0;
        }
    }
    
    /**
     * MST边的数据结构
     */
    private static class MSTEdge {
        final Accumulation from;
        final Accumulation to;
        final double weight;
        
        MSTEdge(Accumulation from, Accumulation to, double weight) {
            this.from = from;
            this.to = to;
            this.weight = weight;
        }
    }
    
    /**
     * 并查集数据结构
     */
    private static class UnionFind {
        private final Map<String, String> parent;
        private final Map<String, Integer> rank;
        
        UnionFind(List<Accumulation> points) {
            parent = new HashMap<>();
            rank = new HashMap<>();
            
            for (Accumulation point : points) {
                String key = point.getUniqueKey();
                parent.put(key, key);
                rank.put(key, 0);
            }
        }
        
        String find(Accumulation point) {
            return find(point.getUniqueKey());
        }
        
        String find(String key) {
            if (!parent.get(key).equals(key)) {
                parent.put(key, find(parent.get(key))); // 路径压缩
            }
            return parent.get(key);
        }
        
        void union(Accumulation pointA, Accumulation pointB) {
            String rootA = find(pointA);
            String rootB = find(pointB);
            
            if (!rootA.equals(rootB)) {
                // 按秩合并
                int rankA = rank.get(rootA);
                int rankB = rank.get(rootB);
                
                if (rankA < rankB) {
                    parent.put(rootA, rootB);
                } else if (rankA > rankB) {
                    parent.put(rootB, rootA);
                } else {
                    parent.put(rootB, rootA);
                    rank.put(rootA, rankA + 1);
                }
            }
        }
        
        boolean isConnected(Accumulation pointA, Accumulation pointB) {
            return find(pointA).equals(find(pointB));
        }
    }
    
    // =====================================================================================
    // 三层硬约束框架：统一转移决策流程
    // =====================================================================================
    
    /**
     * 改进的转移决策机制
     * 使用三层硬约束框架：骨架保护 → 边界约束 → 优化评分
     * 
     * @param point 待转移的点
     * @param sourceCluster 源聚类
     * @param targetCluster 目标聚类
     * @param allClusters 所有聚类（用于全局检查）
     * @return 转移决策结果
     */
    private TransferDecisionResult improvedTransferDecision(Accumulation point, 
                                                          List<Accumulation> sourceCluster,
                                                          List<Accumulation> targetCluster,
                                                          List<List<Accumulation>> allClusters) {
        debugLog("=== 开始三层硬约束检查 ===");
        debugLog("检查点: " + point.getUniqueKey() + 
                ", 源聚类大小: " + sourceCluster.size() + 
                ", 目标聚类大小: " + targetCluster.size());
        
        try {
            // ===============================================
            // 第一层：硬性约束（任何一个失败立即拒绝）
            // ===============================================
            debugLog("--- 第一层：硬性约束检查 ---");
            
            // 1.1 骨架保护
            if (!protectClusterSkeleton(point, sourceCluster, targetCluster)) {
                return new TransferDecisionResult(false, "SKELETON_PROTECTION", 0.0, 
                    "点是关键骨架节点，移除会破坏聚类结构");
            }
            
            // 1.2 密度兼容性
            if (!checkDensityCompatibility(point, sourceCluster, targetCluster)) {
                return new TransferDecisionResult(false, "DENSITY_INCOMPATIBLE", 0.0,
                    "点与目标聚类密度不兼容");
            }
            
            // 1.3 强化游离点预防
            if (!preventOutlierFormationAdvanced(point, targetCluster)) {
                return new TransferDecisionResult(false, "WOULD_CREATE_OUTLIER", 0.0,
                    "转移会在目标聚类中形成游离点");
            }
            
            debugLog("第一层硬性约束检查通过");
            
            // ===============================================
            // 第二层：边界和范围约束
            // ===============================================
            debugLog("--- 第二层：边界和范围约束检查 ---");
            
            // 2.1 Voronoi一致性
            if (!checkVoronoiConsistency(point, targetCluster, allClusters)) {
                return new TransferDecisionResult(false, "VORONOI_VIOLATION", 0.0,
                    "违反Voronoi图范围划分原则");
            }
            
            // 2.2 内部连通性（连线阻断检测）
            if (!checkInternalConnectivity(point, targetCluster, allClusters)) {
                return new TransferDecisionResult(false, "CONNECTIVITY_BLOCKED", 0.0,
                    "会阻断目标聚类内部连接");
            }
            
            debugLog("第二层边界约束检查通过");
            
            // ===============================================
            // 第三层：优化评分（软约束）
            // ===============================================
            debugLog("--- 第三层：优化评分计算 ---");
            
            double score = calculateComprehensiveTransferScore(point, sourceCluster, targetCluster, allClusters);
            debugLog("综合转移评分: " + score);
            
            if (score < 0.3) { // 最低可接受评分
                return new TransferDecisionResult(false, "SCORE_TOO_LOW", score,
                    "转移评分过低: " + score);
            }
            
            debugLog("=== 三层约束检查全部通过 ===");
            return new TransferDecisionResult(true, "APPROVED", score, 
                "转移决策通过，评分: " + score);
            
        } catch (Exception e) {
            debugLog("转移决策过程异常: " + e.getMessage());
            return new TransferDecisionResult(false, "SYSTEM_ERROR", 0.0,
                "系统异常: " + e.getMessage());
        }
    }
    
    /**
     * 计算综合转移评分
     * 综合考虑地理合理性、负载均衡、重要性和稳定性
     */
    private double calculateComprehensiveTransferScore(Accumulation point,
                                                     List<Accumulation> sourceCluster,
                                                     List<Accumulation> targetCluster,
                                                     List<List<Accumulation>> allClusters) {
        try {
            // 1. 地理合理性评分（30%）
            double geographicScore = calculateGeographicScore(point, targetCluster);
            
            // 2. 负载均衡评分（40%）
            double balanceScore = calculateBalanceScore(point, sourceCluster, targetCluster);
            
            // 3. 点重要性评分（20%）- 重要性越低越适合转移
            double importanceScore = 1.0 - calculatePointImportance(point, sourceCluster);
            
            // 4. 系统稳定性评分（10%）
            double stabilityScore = calculateStabilityScore(point, sourceCluster, targetCluster);
            
            // 综合评分
            double totalScore = geographicScore * 0.3 + balanceScore * 0.4 + 
                              importanceScore * 0.2 + stabilityScore * 0.1;
            
            debugLog("评分详情 - 地理: " + geographicScore + 
                    ", 均衡: " + balanceScore + 
                    ", 重要性: " + importanceScore + 
                    ", 稳定性: " + stabilityScore + 
                    ", 总分: " + totalScore);
            
            return Math.max(0.0, Math.min(1.0, totalScore));
            
        } catch (Exception e) {
            debugLog("计算综合评分时发生异常: " + e.getMessage());
            return 0.0;
        }
    }
    
    /**
     * 计算地理合理性评分
     */
    private double calculateGeographicScore(Accumulation point, List<Accumulation> targetCluster) {
        try {
            if (targetCluster.isEmpty()) {
                return 0.0;
            }
            
            // 1. 距离评分
            List<Accumulation> nearestK = findKNearestInCluster(point, targetCluster, 3);
            double avgDistance = nearestK.stream()
                .mapToDouble(p -> calculateDistance(
                    point.getLatitude(), point.getLongitude(),
                    p.getLatitude(), p.getLongitude()
                ))
                .average()
                .orElse(Double.MAX_VALUE);
            
            double distanceScore = 1.0 / (1.0 + avgDistance / 10.0); // 10km标准化
            
            // 2. 中心距离评分
            double centerLat = targetCluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double centerLon = targetCluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            double centerDistance = calculateDistance(point.getLatitude(), point.getLongitude(), centerLat, centerLon);
            
            double centerScore = 1.0 / (1.0 + centerDistance / 15.0); // 15km标准化
            
            // 3. 方向一致性评分
            double directionScore = calculateDirectionConsistency(point, targetCluster);
            
            return distanceScore * 0.4 + centerScore * 0.4 + directionScore * 0.2;
            
        } catch (Exception e) {
            return 0.0;
        }
    }
    
    /**
     * 计算负载均衡评分
     */
    private double calculateBalanceScore(Accumulation point, List<Accumulation> sourceCluster, 
                                       List<Accumulation> targetCluster) {
        try {
            // 计算转移前后的工作时间
            // 注意：这里使用简化计算，实际应该传入depot参数
            double pointWorkTime = 10.0; // 简化假设每个点10分钟工作时间
            
            double sourceCurrentTime = sourceCluster.size() * 10.0; // 简化计算
            
            double targetCurrentTime = targetCluster.size() * 10.0; // 简化计算
            
            // 转移后的时间
            double sourceAfterTime = sourceCurrentTime - pointWorkTime;
            double targetAfterTime = targetCurrentTime + pointWorkTime;
            
            // 计算均衡程度改善
            double beforeImbalance = Math.abs(sourceCurrentTime - targetCurrentTime);
            double afterImbalance = Math.abs(sourceAfterTime - targetAfterTime);
            
            // 如果减少了不均衡，得分较高
            if (afterImbalance < beforeImbalance) {
                double improvement = (beforeImbalance - afterImbalance) / Math.max(beforeImbalance, 1.0);
                return Math.min(1.0, improvement * 2.0); // 最高得分1.0
            } else {
                // 增加了不均衡，得分较低
                double deterioration = (afterImbalance - beforeImbalance) / Math.max(beforeImbalance, 1.0);
                return Math.max(0.0, 1.0 - deterioration);
            }
            
        } catch (Exception e) {
            return 0.5; // 中性评分
        }
    }
    
    /**
     * 计算点的重要性
     * 重要性越高，越不适合转移
     */
    private double calculatePointImportance(Accumulation point, List<Accumulation> cluster) {
        try {
            double importance = 0.0;
            
            // 1. 连接重要性（基于度中心性）
            int connections = 0;
            for (Accumulation other : cluster) {
                if (!other.equals(point)) {
                    double distance = calculateDistance(
                        point.getLatitude(), point.getLongitude(),
                        other.getLatitude(), other.getLongitude()
                    );
                    if (distance <= 3.0) { // 3km连接范围
                        connections++;
                    }
                }
            }
            double connectivityImportance = (double) connections / Math.max(cluster.size() - 1, 1);
            importance += connectivityImportance * 0.4;
            
            // 2. 位置重要性（靠近中心越重要）
            double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
            double centerLon = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
            double distanceToCenter = calculateDistance(point.getLatitude(), point.getLongitude(), centerLat, centerLon);
            
            double maxDistance = cluster.stream()
                .mapToDouble(p -> calculateDistance(p.getLatitude(), p.getLongitude(), centerLat, centerLon))
                .max()
                .orElse(1.0);
            
            double positionImportance = maxDistance > 0 ? 1.0 - (distanceToCenter / maxDistance) : 0.0;
            importance += positionImportance * 0.3;
            
            // 3. 密度重要性（密度中心越重要）
            double localDensity = calculateLocalDensity(point, cluster, 3.0);
            double maxDensity = cluster.stream()
                .mapToDouble(p -> calculateLocalDensity(p, cluster, 3.0))
                .max()
                .orElse(1.0);
            
            double densityImportance = maxDensity > 0 ? localDensity / maxDensity : 0.0;
            importance += densityImportance * 0.3;
            
            return Math.max(0.0, Math.min(1.0, importance));
            
        } catch (Exception e) {
            return 0.5; // 中等重要性
        }
    }
    
    /**
     * 计算系统稳定性评分
     */
    private double calculateStabilityScore(Accumulation point, List<Accumulation> sourceCluster, 
                                         List<Accumulation> targetCluster) {
        try {
            double stability = 1.0;
            
            // 1. 源聚类稳定性
            if (sourceCluster.size() <= 3) {
                stability -= 0.3; // 源聚类太小，影响稳定性
            }
            
            // 2. 目标聚类容量
            if (targetCluster.size() >= 15) {
                stability -= 0.2; // 目标聚类已经较大
            }
            
            // 3. 工作时间影响
            double pointWorkTime = 10.0; // 简化假设
            double avgWorkTime = 10.0; // 简化假设平均工作时间
            
            if (pointWorkTime > avgWorkTime * 1.5) {
                stability -= 0.2; // 工作时间过大，影响稳定性
            }
            
            return Math.max(0.0, Math.min(1.0, stability));
            
        } catch (Exception e) {
            return 0.5;
        }
    }
    
    /**
     * 转移决策结果类
     */
    private static class TransferDecisionResult {
        final boolean allowed;
        final String reason;
        final double score;
        final String description;
        
        TransferDecisionResult(boolean allowed, String reason, double score, String description) {
            this.allowed = allowed;
            this.reason = reason;
            this.score = score;
            this.description = description;
        }
        
        @Override
        public String toString() {
            return String.format("TransferDecision{allowed=%s, reason=%s, score=%.3f, desc='%s'}", 
                               allowed, reason, score, description);
        }
    }
} 